# DiscountController API 验收测试说明

## 概述

本文档说明了为 `DiscountController` API 创建的验收测试，用于确保重构前后API功能的一致性。

## 测试文件

### 1. DiscountControllerBasicTest.java
- **位置**: `pad-resource-admin-api/src/test/java/cmc/pad/resource/admin/api/controller/DiscountControllerBasicTest.java`
- **作用**: 折扣控制器的基础验收测试
- **作者**: fu.wei
- **创建日期**: 2025/09/10

### 2. 测试套件更新
- **AllControllerTestSuite**: 已添加 `DiscountControllerBasicTest`
- **LeasingControllerTestSuite**: 已添加 `DiscountControllerBasicTest`

## 测试覆盖范围

### 1. 参数验证测试 (`testInvalidParams`)
- 空参数测试
- 无效业务类型测试
- 缺少必要参数测试

### 2. 有效业务类型测试 (`testValidBusinessTypes`)
测试所有支持的业务类型：
- YX (影讯)
- WZ (物资)
- GD (广告)
- XC (宣传)
- YT (影厅)
- GMT (贵宾厅)

### 3. 折扣方式测试 (`testSpecificDiscountMethod`)
- 时长折扣 (DURATION)
- 面积折扣 (AREA)
- 无效折扣方式

### 4. 边界值测试 (`testBoundaryValues`)
- 最小值测试 (0.1)
- 最大值测试 (1000.0)

### 5. 大小写敏感性测试
- 业务类型大小写不敏感 (`testCaseInsensitiveBusinessType`)
- 折扣方式大小写不敏感 (`testCaseInsensitiveDiscountMethod`)

### 6. 多重折扣因子测试 (`testMultipleDiscountFactors`)
- 同时提供面积和时长参数

### 7. 特殊情况测试 (`testSpecialCases`)
- 指定折扣方式但缺少对应参数
- 参数不匹配的情况

### 8. 响应数据结构测试 (`testResponseDataStructure`)
- 单个折扣方式返回结构
- 多个折扣方式返回结构

## API 接口

### 接口路径
- GET: `/discount/coefficient/match`
- POST: `/discount/coefficient/match`

### 请求参数
- `businessType` (必填): 广告业务类型
- `discountMethod` (可选): 折扣方式 (DURATION/AREA)
- `area` (可选): 面积
- `duration` (可选): 时长

### 响应格式
```json
{
  "status": 0,
  "msg": "OK",
  "data": [
    {
      "discount_method": "DURATION",
      "coefficient": 0.8
    }
  ]
}
```

## 错误码对照

| 错误码 | 错误信息 |
|--------|----------|
| 6001 | 没有匹配的折扣系数 |
| 6002 | 不支持的折扣类别 |
| 6003 | 广告业务类型不能为空 / 查询面积折扣系数时，面积数不能为空 |
| 6004 | 查询时长折扣系数时，时长不能为空 |
| 6005 | 查询折扣系数时，时长和面积数不能同时为空 |

## 运行测试

### 方法一：使用脚本运行
```bash
./run-discount-test.sh
```

### 方法二：使用Maven运行
```bash
cd pad-resource-admin-api
mvn test -Dtest=DiscountControllerBasicTest
```

### 方法三：运行测试套件
```bash
cd pad-resource-admin-api
mvn test -Dtest=LeasingControllerTestSuite
```

## 前置条件

1. **服务启动**: 确保 `pad-resource-admin-api` 服务在端口 8125 运行
2. **数据库连接**: 确保数据库连接正常
3. **测试数据**: 确保数据库中有相应的折扣配置数据

## 注意事项

1. **测试环境**: 测试默认连接 `http://localhost:8125`
2. **数据依赖**: 测试依赖数据库中的折扣规则数据
3. **并发测试**: 同时测试 GET 和 POST 方法
4. **错误处理**: 测试覆盖各种异常情况

## 重构指导

在进行 DiscountController 重构时：

1. **保持接口不变**: 确保 API 路径和参数格式不变
2. **保持响应格式**: 确保响应数据结构不变
3. **保持错误码**: 确保错误码和错误信息不变
4. **运行验收测试**: 重构后运行测试确保功能一致性

## 测试数据要求

为了测试正常运行，数据库中应包含：

1. **Discount 表**: 包含各种业务类型的折扣配置
2. **DiscountRule 表**: 包含具体的折扣规则
3. **测试业务类型**: YX, WZ, GD, XC, YT, GMT

## 联系信息

- **作者**: fu.wei
- **创建日期**: 2025/09/10
- **版本**: 1.0