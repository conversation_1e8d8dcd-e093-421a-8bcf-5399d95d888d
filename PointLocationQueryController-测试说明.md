# PointLocationQueryController 验收测试说明

## 测试概述
本文档描述了 `PointLocationQueryController` 的验收测试覆盖范围和测试用例。

## API 信息
- **控制器**: `PointLocationQueryController`
- **请求路径**: `/point-location/query`
- **请求方法**: POST（仅支持POST方法）
- **请求格式**: JSON
- **响应格式**: JSON (PageResult<QueryPointLocationUsableAreaData>)

## 必需参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| start | LocalDate | 是 | 查询开始日期，格式：YYYY-MM-DD |
| end | LocalDate | 是 | 查询结束日期，格式：YYYY-MM-DD |

## 可选参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| page_index | int | 否 | 页码，从0开始，默认0 |
| page_size | int | 否 | 页面大小，默认10 |
| code | String | 否 | 点位编码过滤 |
| cinema_inner_code | String | 否 | 影院内部编码过滤 |
| business_type_code | String | 否 | 业务类型编码过滤 |

## 测试用例覆盖

### 1. 必需参数验证测试 (`testValidRequiredParams`, `testMissingRequiredParams`)
- ✅ 验证必需的 start 和 end 参数
- ✅ 测试缺少必需参数时的错误响应
- ✅ 测试空参数的处理

### 2. 可选参数测试 (`testOptionalParams`)
- ✅ 测试点位编码过滤功能
- ✅ 测试影院内部编码过滤功能
- ✅ 测试业务类型编码过滤功能
- ✅ 测试多个过滤条件组合使用

### 3. 分页功能测试 (`testPaginationParams`)
- ✅ 测试不同页码和页面大小
- ✅ 测试默认分页参数
- ✅ 验证分页参数的正确处理

### 4. 日期验证测试 (`testDateRangeValidation`, `testDateRangeLogic`)
- ✅ 测试有效的日期格式 (YYYY-MM-DD)
- ✅ 测试无效日期格式的错误处理
- ✅ 测试无效日期值的错误处理
- ✅ 测试日期范围逻辑（开始日期不能晚于结束日期）
- ✅ 测试跨年和长时间范围查询

### 5. 响应数据结构测试 (`testResponseDataStructure`)
- ✅ 验证分页响应结构 (total, pageIndex, pageSize, data)
- ✅ 验证数据字段完整性 (id, code, usableArea, business_type_code 等)

### 6. 过滤功能测试 (`testFilterFunctionality`)
- ✅ 验证各种过滤条件的有效性
- ✅ 测试过滤结果的正确性

### 7. 特殊字符处理测试 (`testSpecialCharactersInParams`)
- ✅ 测试特殊字符在参数中的处理
- ✅ 测试中文字符支持（如果适用）

### 8. 边界值测试 (`testBoundaryValues`)
- ✅ 测试分页参数的边界值
- ✅ 测试负数页码的处理
- ✅ 测试过大页面大小的限制
- ✅ 测试零页面大小的处理

### 9. 空结果测试 (`testEmptyResults`)
- ✅ 测试不存在的过滤条件返回空结果
- ✅ 验证空结果时的正常状态码

## 响应数据结构

### 成功响应格式
```json
{
  "status": 0,
  "msg": "OK",
  "data": {
    "total": 100,
    "pageIndex": 0,
    "pageSize": 10,
    "data": [
      {
        "id": 1,
        "code": "P001",
        "usableArea": 25.5,
        "business_type_code": "YX",
        "cinema_inner_code": "C001",
        "resourceOwnershipCode": "OWN001",
        "locationDesc": "大厅入口处",
        "planUse": "广告展示",
        "landingMode": "落地式"
      }
    ]
  }
}
```

### 错误响应格式
```json
{
  "status": 400,
  "msg": "start不能为空",
  "data": null
}
```

## 运行测试

### 前置条件
1. 确保 pad-resource-admin-api 服务运行在端口 8125
2. 服务健康检查接口 `/health` 可访问

### 执行命令
```bash
# 运行完整验收测试
./run-point-location-query-test.sh

# 或者直接运行Maven测试
cd pad-resource-admin-api
mvn test -Dtest=PointLocationQueryControllerBasicTest
```

## 测试环境配置
- 测试基础URL在 `BaseControllerTest` 中配置
- 当前配置为 QAS 环境：`http://pad-resource-admin-api-qas-cmc.cmc.com`
- 本地测试可修改为：`http://localhost:8125`

## 注意事项
1. 该API仅支持POST方法，不支持GET请求
2. 日期参数必须使用 ISO 格式 (YYYY-MM-DD)
3. 分页参数 page_index 从 0 开始计数
4. 所有过滤参数都是可选的，可以单独使用或组合使用
5. 测试会验证响应的数据结构和业务逻辑正确性