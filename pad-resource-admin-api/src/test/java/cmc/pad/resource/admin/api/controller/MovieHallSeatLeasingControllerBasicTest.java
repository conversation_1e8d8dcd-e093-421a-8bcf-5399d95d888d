package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 影厅租赁报价控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/09
 * @Version 1.0
 */
public class MovieHallSeatLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 空参数和无效参数测试
        testBothMethods("", 3006, "城市级别不存在");
        testBothMethods("city_level=1", 101, "city_level城市级别错误");
        testBothMethods("city_level=L1", 2006, "影城级别不存在");
        testBothMethods("cinema_level=a", 3006, "城市级别不存在");
        testBothMethods("cinema_level=A", 3006, "城市级别不存在");
    }

    @Test
    public void testValidCinemaCode() throws Exception {
        // 有效影城代码测试
        testBothMethods("cinema_code=849", 0, "OK", "[]");
        testBothMethods("cinema_code=304", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"N\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"FD\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"M\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0}]");
    }

    @Test
    public void testValidLevels() throws Exception {
        // 有效城市级别和影院级别测试
        testBothMethods("city_level=L1&cinema_level=A", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"N\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"FD\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"M\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0}]");
        testBothMethods("city_level=L2&cinema_level=B", 3006, "城市级别不存在");
        testBothMethods("city_level=L3&cinema_level=C", 0, "OK", "[]");
    }

    @Test
    public void testWithMovieHallType() throws Exception {
        // 包含影厅类型的测试
        testBothMethods("city_level=L1&cinema_level=A&movie_hall_type=DO", 0, "OK", "[]");
        testBothMethods("city_level=L1&cinema_level=A&movie_hall_type=N", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"N\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0}]");
    }

    @Test
    public void testInvalidMovieHallType() throws Exception {
        // 无效影厅类型测试
        testBothMethods("city_level=L1&cinema_level=A&movie_hall_type=INVALID_TYPE", 5006, "影厅类型编码不存在");
    }

    @Test
    public void testNonExistentCinemaCode() throws Exception {
        // 不存在的影院编码测试
        testBothMethods("cinema_code=999999", 2007, "影城没有对应的影城级别");
    }

    @Test
    public void testCinemaCodePriority() throws Exception {
        // 测试影院编码优先级（当影院编码存在时，城市级别和影院级别参数失效）
        testBothMethods("cinema_code=849&cinema_level=a&city_level=INVALID", 101, "city_level城市级别错误");
    }

    @Test
    public void testDifferentCityLevels() throws Exception {
        // 测试不同城市级别
        testBothMethods("city_level=L1&cinema_level=A", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"N\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"FD\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"M\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0}]");
        testBothMethods("city_level=L2&cinema_level=A", 3006, "城市级别不存在");
        testBothMethods("city_level=L3&cinema_level=A", 0, "OK", "[]");
        testBothMethods("city_level=L4&cinema_level=A", 0, "OK", "[]");
        testBothMethods("city_level=L5&cinema_level=A", 3006, "城市级别不存在");
    }

    @Test
    public void testDifferentCinemaLevels() throws Exception {
        // 测试不同影院级别
        testBothMethods("city_level=L1&cinema_level=A", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"N\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"FD\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"movie_hall_type\":\"M\",\"base_price\":11.0,\"base_duration\":1,\"extended_price\":3.0}]");
        testBothMethods("city_level=L1&cinema_level=B", 0, "OK", "[]");
        testBothMethods("city_level=L1&cinema_level=C", 0, "OK", "[]");
        testBothMethods("city_level=L1&cinema_level=D", 0, "OK", "[]");
    }

    @Test
    public void testFallbackQuery() throws Exception {
        // 测试兜底查询逻辑（使用可能没有具体数据的组合）
        testBothMethods("city_level=L4&cinema_level=D", 0, "OK", "[]");
    }

    @Test
    public void testResponseDataStructure() throws Exception {
        // 测试响应数据结构
        String response = getApi("city_level=L1&cinema_level=A");
        assertResponseIsArray(response);

        response = postApi("city_level=L1&cinema_level=A");
        assertResponseIsArray(response);
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws
            Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/movie-hall-seat-leasing/price/query?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/movie-hall-seat-leasing/price/query";
        return httpPost(path, param);
    }

    /**
     * 验证响应是否为数组格式
     */
    private void assertResponseIsArray(String response) throws Exception {
        // 解析响应，验证data字段是否为数组
        if (response.contains("\"code\":0")) {
            // 成功响应应该包含data字段且为数组格式
            if (!response.contains("\"data\":[")) {
                throw new AssertionError("成功响应的data字段应该是数组格式");
            }
        }
    }
}