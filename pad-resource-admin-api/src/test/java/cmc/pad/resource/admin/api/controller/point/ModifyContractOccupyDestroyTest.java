package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state.NewContractApproved;
import org.junit.Test;

import static cmc.pad.resource.admin.api.controller.point.test_dsl.ActualContractDetail.PointLocationDetailAssert.detailPid;
import static cmc.pad.resource.admin.api.controller.point.test_dsl.ActualInventory.PointLocationInventoryAssert.inventoryPid;
import static cmc.pad.resource.admin.api.controller.point.test_dsl.DSLImports.*;
import static cmc.pad.resource.domain.inventory.point.AlterStatus.DESTROY;
import static cmc.pad.resource.domain.inventory.point.AlterStatus.OCCUPY;
import static cmc.pad.resource.domain.inventory.point.ContractStatus.APPROVAL;
import static cmc.pad.resource.domain.inventory.point.ContractStatus.SUBMIT;
import static cmc.pad.resource.domain.inventory.point.ContractType.ALTER_CONTRACT;

public class ModifyContractOccupyDestroyTest extends PointLocationInventoryBaseTest {
    @Test
    public void testModifyContractSubmittedPointLocationOccupy() {
        new NewContractApproved()
                .submit(ALTER_CONTRACT, PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))//测试幂等
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ALTER_CONTRACT)
                            .statusIs(SUBMIT);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//提交时废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testModifyContractSubmittedPointLocationDestroyFinish() {
        new NewContractApproved()
                .submit(ALTER_CONTRACT, PointLocation(32).status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .approval()
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ALTER_CONTRACT)
                            .statusIs(APPROVAL);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(DESTROY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//提交时废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(0f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testModifyApprovedContractSubmittedPointLocationReDestroy() {
        new NewContractApproved()
                .submit(ALTER_CONTRACT, PointLocation(32).status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .approval()
                .submit(PointLocation(32).status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ALTER_CONTRACT)
                            .statusIs(SUBMIT);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(DESTROY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//变更合同重复提交废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(0f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testModifyApprovedContractSubmittedPointLocationDestroy() {
        new NewContractApproved()
                .submit(ALTER_CONTRACT, PointLocation(32).status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .approval()
                .submit(PointLocation(32).status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ALTER_CONTRACT)
                            .statusIs(SUBMIT);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(DESTROY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//变更合同重复提交废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(0f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testModifyApprovedContractSubmittedPointLocationDestroyToOccupy() {
        new NewContractApproved()
                .submit(ALTER_CONTRACT, PointLocation(32).status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .approval()
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualInventory//变更合同审批后废弃点位库存恢复
                            .pointLocation(inventoryPid(32).occupyAreaIs(0f).date("2024-01-01", "2024-12-31"));
                })
                .submit(PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualInventory//变更合同重复提交废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                })
                .approval()
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//变更合同审批后废弃点位库存恢复
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                })
                .submit(PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//变更合同重复提交废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                })
                .approval()
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ALTER_CONTRACT)
                            .statusIs(APPROVAL);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory//变更合同重复提交废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                })
        ;
    }

    @Test
    public void testModifyApprovedContractSubmittedPointLocationDestroyAddOccupy() {
        new NewContractApproved()
                .submit(ALTER_CONTRACT, PointLocation(32).detailId("DETAIL_32").status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .approval()
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualInventory//变更合同重复提交废弃点位库存不变
                            .pointLocation(inventoryPid(32).occupyAreaIs(0f).date("2024-01-01", "2024-12-31"));
                })
                .submit(PointLocation(32).detailId("DETAIL_32").status(Destroy).amount(5.01f).date("2024-01-01", "2024-12-31"),
                        PointLocation(32).detailId("DETAIL_32_1").status(Add).amount(5.02f).date("2024-01-01", "2024-12-31"))
                .then((apiResponse, actualContract, actualContractDetail, actualInventory) -> {
                    //作废后，在用相同时间范围追加明细会报错，这种情况同一点位作废应该改成变更
                    assertResponseStatusAndMsgV2(apiResponse, -1, "点位id:32, msg:2024-01-01存在交集, 请将时间段拆分", null);
                });
    }
}