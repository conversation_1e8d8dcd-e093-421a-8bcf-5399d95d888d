package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 灯箱价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/04
 * @Version 1.0
 */
public class LightBoxPriceControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 无效参数测试
        testBothMethods("city_level=1", 101, "city_level城市级别错误");
        testBothMethods("cinema_level=a", 2006, "影城级别不存在");
        testBothMethods("cinema_level=A", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0},{\"city_level\":\"L2\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0},{\"city_level\":\"L3\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0},{\"city_level\":\"L4\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        testBothMethods("city_level=L99&cinema_level=A", 3006, "城市级别不存在");
        testBothMethods("cinema_code=304&cinema_level=INVALID", 101, "cinema_level影城级别错误");
    }

    @Test
    public void testInvalidCinemaCode() throws Exception {
        // 无效影城代码测试
        testBothMethods("cinema_code=99999", 2007, "影城没有对应的影城级别");
        testBothMethods("cinema_code=INVALID", 101, "cinema_code影城编码错误");
    }

    @Test
    public void testValidCinemaCode() throws Exception {
        // 有效影城代码测试
        testBothMethods("cinema_code=849", 0, "OK",
                "[{\"city_level\":\"L1\",\"cinema_level\":\"B\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        testBothMethods("cinema_code=849&city_level=L1&cinema_level=A", 0, "OK",
                "[{\"city_level\":\"L1\",\"cinema_level\":\"B\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        testBothMethods("cinema_code=304", 0, "OK",
                "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
    }

    @Test
    public void testValidCityLevelAndCinemaLevel() throws Exception {
        // 有效城市级别和影城级别测试
        testBothMethods("city_level=L1&cinema_level=A", 0, "OK",
                "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        testBothMethods("city_level=L3&cinema_level=C", 0, "OK",
                "[{\"city_level\":\"L3\",\"cinema_level\":\"C\",\"base_price\":1000.0,\"base_area\":10,\"extended_price\":100.0}]");
        testBothMethods("city_level=L1&cinema_level=S", 0, "OK",
                "[{\"city_level\":\"L1\",\"cinema_level\":\"S\",\"base_price\":88.45,\"base_area\":10,\"extended_price\":65.56}]");
        testBothMethods("city_level=L1&cinema_level=E", 2006, "影城级别不存在");
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/light-box/price/query?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/light-box/price/query";
        return httpPost(path, param);
    }
}
