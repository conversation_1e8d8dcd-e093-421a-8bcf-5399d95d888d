package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import com.alibaba.fastjson.JSON;
import org.junit.Test;

import static cmc.pad.resource.domain.inventory.point.ContractStatus.CANCEL;

/**
 * 点位库存控制器验收测试
 * 用于验证点位库存管理API的功能正确性
 *
 * <AUTHOR>
 * @Date 2025/09/17
 * @Version 1.0
 */
public class PointLocationInventoryControllerBasicTest extends PointLocationInventoryBaseTest {

    //测试占用面积过大
    @Test
    public void testOccupyAreaTooLarge() throws Exception {
        contract.getDetails().get(0).setAmount(10000f);
        testOccupyMethodByJson(JSON.toJSONString(contract), -1, "id:32点位库存面积不足, 当前库存可使用面积:100.0", null);
    }

    @Test
    public void testOccupyApproval() throws Exception {
        testOccupyMethodByJson(JSON.toJSONString(contract), 0, "OK", true);
        assertSubmit();
        //1:审批扣减
        testUpdateStatusMethod("contract_no=" + contract.getContractNo() + "&status=" + UpdateStatus.Approval.value, 0, "OK", true);
        assertApproval();
    }

    @Test
    public void testOccupyCancel() throws Exception {
        testOccupyMethodByJson(JSON.toJSONString(contract), 0, "OK", true);
        assertSubmit();
        //1:扣减 2:取消
        testUpdateStatusMethod("contract_no=" + contract.getContractNo() + "&status=" + UpdateStatus.Cancel.value, 0, "OK", true);
        assertCancel();
    }

    //测试变更合同占用
    @Test
    public void testUpdateContractOccupy() throws Exception {
        testOccupyMethodByJson(JSON.toJSONString(contract), 0, "OK", true);
        assertSubmit();
        testUpdateStatusMethod("contract_no=" + contract.getContractNo() + "&status=" + UpdateStatus.Approval.value, 0, "OK", true);

        contract.setContractType(ContractType.ALTER_CONTRACT.value());
        testOccupyMethodByJson(JSON.toJSONString(contract), 0, "OK", true);
        assertSubmit();
    }

    //测试变更合同占用作废无效
    @Test
    public void testUpdateContractOccupyDestroyNotModifyAdd() throws Exception {
        testOccupyMethodByJson(JSON.toJSONString(contract), 0, "OK", true);
        assertSubmit();
        testUpdateStatusMethod("contract_no=" + contract.getContractNo() + "&status=" + UpdateStatus.Approval.value, 0, "OK", true);
        assertApproval();

        contract.setContractType(ContractType.ALTER_CONTRACT.value());
        contract.getDetails().get(0).setAlterStatus(PointLocationOccupationContractDetailTest.TestAlterStatus.DESTROY.value());
        testOccupyMethodByJson(JSON.toJSONString(contract), 0, "OK", true);
        assertSubmit();
        testUpdateStatusMethod("contract_no=" + contract.getContractNo() + "&status=" + UpdateStatus.Approval.value, 0, "OK", true);
        assertApproval();

        contract.getDetails().get(0).setAlterStatus(PointLocationOccupationContractDetailTest.TestAlterStatus.ADD.value());
        testOccupyMethodByJson(JSON.toJSONString(contract), -1, "变更合同提交的 ADD 明细(id:AMOUNT_MIN)数据点位id:32，在上一版本存在，不是新加明细");
    }

    private void assertSubmit() {
        assertActualDBData(contract, ContractType.valueOf(contract.getContractType()), ContractStatus.SUBMIT);
    }

    private void assertApproval() {
        assertActualDBData(contract, ContractType.valueOf(contract.getContractType()), ContractStatus.APPROVAL);
    }

    private void assertCancel() {
        assertActualDBData(contract, ContractType.valueOf(contract.getContractType()), CANCEL);
    }

    enum UpdateStatus {
        Approval(1),
        Cancel(2);
        final int value;

        UpdateStatus(int value) {
            this.value = value;
        }

        public int value() {
            return value;
        }
    }

    private void testOccupyMethodByJson(String param, int expectedCode, String expectedMsg) throws Exception {
        assertResponseStatusAndMsgV2(postOccupyJsonApi(param), expectedCode, expectedMsg, null);
    }

    private void testOccupyMethodByJson(String param, int expectedCode, String expectedMsg, Boolean data) throws Exception {
        assertResponseStatusAndMsgV2(postOccupyJsonApi(param), expectedCode, expectedMsg, data);
    }

    private void testUpdateStatusMethod(String param, int expectedCode, String expectedMsg, boolean result) throws Exception {
        assertResponseStatusAndMsgV2(postUpdateStatusApi(param), expectedCode, expectedMsg, result);
    }

    @Override
    protected String getApi(String param) throws Exception {
        // 点位库存API只支持POST方法
        throw new UnsupportedOperationException("点位库存API不支持GET方法，请使用POST方法");
    }

    protected String postApi(String param) throws Exception {
        String path = "/point-location/query";
        return httpPost(path, param);
    }

    /**
     * 调用占用接口
     */
    protected String postOccupyApi(String param) throws Exception {
        String path = "/point-location/inventory/occupy";
        return httpPost(path, param);
    }

    protected String postOccupyJsonApi(String body) throws Exception {
        String path = "/point-location/inventory/occupy";
        return postJson(path, body);
    }

    /**
     * 调用状态更新接口
     */
    protected String postUpdateStatusApi(String param) throws Exception {
        String path = "/point-location/inventory/updateStatus";
        return httpPost(path, param);
    }
}