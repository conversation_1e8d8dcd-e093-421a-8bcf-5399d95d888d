package cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state;

import cmc.pad.resource.admin.api.controller.point.test_dsl.PointLocation;
import cmc.pad.resource.domain.inventory.point.ContractType;

/**
 * 已审批新合同状态
 */
public class NewContractApproved extends ContractState {
    public NewContractApproved() {
        new NewContractSubmitted()
                .approval();
    }

    public ContractState submit(ContractType contractType, PointLocation... pointLocations) {
        testContract.setContractType(contractType.value());
        return super.submit(pointLocations);
    }
}