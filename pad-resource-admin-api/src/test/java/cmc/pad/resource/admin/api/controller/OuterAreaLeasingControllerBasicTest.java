package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 外租区域租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/09
 * @Version 1.0
 */
public class OuterAreaLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 空参数测试
        testBothMethods("", 3006, "城市级别不存在");
        
        // 无效城市级别测试
        testBothMethods("city_level=1", 101, "city_level城市级别错误");
        testBothMethods("city_level=INVALID", 101, "city_level城市级别错误");
        testBothMethods("city_level=", 3006, "城市级别不存在");
        
        // 无效影城级别测试
        testBothMethods("city_level=L1", 2006, "影城级别不存在");
        testBothMethods("cinema_level=1", 101, "cinema_level影城级别错误");
        testBothMethods("cinema_level=", 3006, "城市级别不存在");
        testBothMethods("cinema_level=A", 3006, "城市级别不存在");
        testBothMethods("cinema_level=a", 3006, "城市级别不存在");

        // 城市级别有效但影城级别无效
        testBothMethods("city_level=L1&cinema_level=INVALID", 101, "cinema_level影城级别错误");

        // 影城级别有效但城市级别无效
        testBothMethods("city_level=INVALID&cinema_level=A", 101, "city_level城市级别错误");
        testBothMethods("cinema_level=INVALID", 101, "cinema_level影城级别错误");
    }

    @Test
    public void testValidCinemaCode() throws Exception {
        // 有效影城代码测试
        testBothMethods("cinema_code=849", 0, "OK", "[]");
        testBothMethods("cinema_code=304", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":100.0}]");

        // 影城代码优先级测试 - 当提供影城代码时，城市级别和影城级别参数应该被忽略
        testBothMethods("cinema_code=849&city_level=INVALID&cinema_level=a", 101, "city_level城市级别错误");
    }

    @Test
    public void testInvalidCinemaCode() throws Exception {
        // 无效影城代码测试
        testBothMethods("cinema_code=999999", 2007, "影城没有对应的影城级别");
        testBothMethods("cinema_code=INVALID", 101, "cinema_code影城编码错误");
        testBothMethods("cinema_code=", 3006, "城市级别不存在");
    }

    @Test
    public void testValidCityAndCinemaLevel() throws Exception {
        // 有效的城市级别和影城级别组合测试
        testBothMethods("city_level=L1&cinema_level=A", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":100.0}]");
        testBothMethods("city_level=L2&cinema_level=A", 3006, "城市级别不存在");
        
        // 测试可能存在的其他级别组合
        testBothMethods("city_level=L1&cinema_level=B", 0, "OK", "[]");
        testBothMethods("city_level=L1&cinema_level=C", 0, "OK", "[]");
    }

    @Test
    public void testEmptyResult() throws Exception {
        // 测试可能返回空结果的有效参数组合
        testBothMethods("city_level=L3&cinema_level=A", 0, "OK", "[]");
        testBothMethods("city_level=L1&cinema_level=D", 0, "OK", "[]");
    }

    @Test
    public void testParameterCombinations() throws Exception {
        // 测试参数组合的边界情况
        
        // 只提供城市级别，缺少影城级别
        testBothMethods("city_level=L1", 2006, "影城级别不存在");
        
        // 只提供影城级别，缺少城市级别
        testBothMethods("cinema_level=A", 3006, "城市级别不存在");
        
        // 同时提供影城代码和其他参数（影城代码应该优先）
        testBothMethods("cinema_code=304&city_level=L1&cinema_level=A", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":100.0}]");
    }

    @Test
    public void testSpecialCharacters() throws Exception {
        // 测试特殊字符参数
        testBothMethods("city_level=L1&cinema_level=A&extra_param=test", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":100.0}]");
        testBothMethods("cinema_code=304&unused_param=value", 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":100.0}]");
    }

    @Test
    public void testCaseSensitivity() throws Exception {
        // 测试大小写敏感性
        testBothMethods("city_level=l1&cinema_level=a", 3006, "城市级别不存在");
    }

    @Test
    public void testDataFormat() throws Exception {
        // 测试返回数据格式 - 验证有数据返回的情况
        String response = getApi("cinema_code=304");
        System.out.println("Response for cinema_code=304: " + response);
        
        // 验证返回的数据结构包含必要字段
        if (response.contains("\"status\":0")) {
            // 如果有数据，验证数据格式
            if (!response.contains("\"data\":[]")) {
                assertResponseContainsFields(response, "city_level", "cinema_level", "base_price");
            }
        }
    }

    /**
     * 验证响应中包含指定字段
     */
    private void assertResponseContainsFields(String response, String... fields) {
        for (String field : fields) {
            if (!response.contains("\"" + field + "\"")) {
                System.out.println("Warning: Field '" + field + "' not found in response: " + response);
            }
        }
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/outer-area-leasing/price/query?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/outer-area-leasing/price/query";
        return httpPost(path, param);
    }
}