package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 营销点位租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/05
 * @Version 1.0
 */
public class MarketingPointLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 无效参数测试
        testBothMethods("", 3006, "城市级别不存在");
        testBothMethods("city_level=1", 101, "city_level城市级别错误");
        testBothMethods("city_level=L1", 2006, "影城级别不存在");
        testBothMethods("cinema_level=a", 3006, "城市级别不存在");
        testBothMethods("cinema_level=A", 3006, "城市级别不存在");
        testBothMethods("city_level=L1&cinema_level=A&lease_method=INVALID", 4006, "租赁方式编码不存在");
        testBothMethods("city_level=L99&cinema_level=A", 3006, "城市级别不存在");
        testBothMethods("city_level=L1&cinema_level=Z", 2006, "影城级别不存在");
        testBothMethods("city_level=L1&cinema_level=A", 4006, "租赁方式编码不存在");
    }

    @Test
    public void testInvalidCinemaCode() throws Exception {
        // 无效影城代码测试
        testBothMethods("cinema_code=INVALID", 101, "cinema_code影城编码错误");
        testBothMethods("cinema_code=99999", 2007, "影城没有对应的影城级别");
        testBothMethods("cinema_code=1234567", 101, "cinema_code影城编码错误");
        testBothMethods("cinema_code=304&cinema_level=INVALID", 101, "cinema_level影城级别错误");
        testBothMethods("cinema_code=304&city_level=INVALID", 101, "city_level城市级别错误");
        testBothMethods("cinema_code=304&lease_method=1", 101, "lease_method城市级别错误");
    }

    @Test
    public void testValidCinemaCode() throws Exception {
        // 有效影城代码测试
        testBothMethods("cinema_code=849", 0, "OK", "[]");
        testBothMethods("cinema_code=304", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0},{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
    }

    @Test
    public void testLeaseMethodParams() throws Exception {
        // 租赁方式参数测试
        testBothMethods("city_level=L1&cinema_level=A&lease_method=AREA", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        testBothMethods("city_level=L1&cinema_level=A&lease_method=QUANTITY", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
        testBothMethods("cinema_code=304&lease_method=AREA", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        testBothMethods("cinema_code=304&lease_method=QUANTITY", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
    }

    @Test
    public void testCaseInsensitiveLeaseMethod() throws Exception {
        // 租赁方式大小写不敏感测试
        testBothMethods("city_level=L1&cinema_level=A&lease_method=area", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
        testBothMethods("city_level=L1&cinema_level=A&lease_method=quantity", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"QUANTITY\",\"base_price\":20.0}]");
        testBothMethods("city_level=L1&cinema_level=A&lease_method=Area", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"lease_method\":\"AREA\",\"base_price\":15.0}]");
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/marketing-point-leasing/price/query?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/marketing-point-leasing/price/query";
        return httpPost(path, param);
    }
}
