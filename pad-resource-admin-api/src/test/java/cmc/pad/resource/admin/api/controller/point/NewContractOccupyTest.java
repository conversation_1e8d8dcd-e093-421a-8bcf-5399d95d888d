package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state.InitState;
import cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state.NewContractCancelled;
import cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state.NewContractSubmitted;
import cmc.pad.resource.domain.inventory.point.ContractType;
import org.junit.Test;

import static cmc.pad.resource.admin.api.controller.point.test_dsl.ActualContractDetail.PointLocationDetailAssert.detailPid;
import static cmc.pad.resource.admin.api.controller.point.test_dsl.ActualInventory.PointLocationInventoryAssert.inventoryPid;
import static cmc.pad.resource.admin.api.controller.point.test_dsl.DSLImports.Occup;
import static cmc.pad.resource.admin.api.controller.point.test_dsl.DSLImports.PointLocation;
import static cmc.pad.resource.domain.inventory.point.AlterStatus.OCCUPY;
import static cmc.pad.resource.domain.inventory.point.ContractStatus.*;

public class NewContractOccupyTest extends PointLocationInventoryBaseTest {
    @Test
    public void initContractStateSubmit() {
        new InitState()
                .submit(PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))
                .submit(PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))//测试幂等
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ContractType.NEW_CONTRACT)
                            .statusIs(SUBMIT);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testNewContractStateApproval() {
        new NewContractSubmitted()
                .approval()
                .approval()//测试幂等
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ContractType.NEW_CONTRACT)
                            .statusIs(APPROVAL);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testNewApprovedContractSubmittedCancel() {
        new NewContractSubmitted()
                .cancel()
                .cancel()//测试幂等
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ContractType.NEW_CONTRACT)
                            .statusIs(CANCEL);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory
                            .pointLocation(inventoryPid(32).occupyAreaIs(0f).date("2024-01-01", "2024-12-31"));
                });
    }

    @Test
    public void testNewApprovedContractCancelledSubmit() {
        new NewContractCancelled()
                .submit(PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))//测试幂等
                .then((actualContract, actualContractDetail, actualInventory) -> {
                    actualContract
                            .typeIs(ContractType.NEW_CONTRACT)
                            .statusIs(SUBMIT);
                    actualContractDetail
                            .numIs(1)
                            .pointLocation(detailPid(32).statusIs(OCCUPY).amountIs(5.01f).dateIs("2024-01-01", "2024-12-31"));
                    actualInventory
                            .pointLocation(inventoryPid(32).occupyAreaIs(5.01f).date("2024-01-01", "2024-12-31"));
                });
    }
}