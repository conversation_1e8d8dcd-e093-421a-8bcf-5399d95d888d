package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.domain.inventory.point.ContractType;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Transient;
import java.util.List;

/**
 * Created by fuyuanpu on 2022/2/26.
 */
@Getter
@Setter
@ToString
public class PointLocationOccupationContractTest {
    @JSONField(name = "contract_no")
    private String contractNo;
    @JSONField(name = "contract_type")
    private int contractType;
    @JSONField(name = "business_type_code")
    private String businessTypeCode;
    @Transient
    private List<PointLocationOccupationContractDetailTest> details;

    public PointLocationOccupationContractTest(ContractType contractType, String contractNo, String businessTypeCode, PointLocationOccupationContractDetailTest... occupationDetailList) {
        this.contractNo = contractNo;
        this.contractType = contractType.value();
        this.businessTypeCode = businessTypeCode;
        this.details = Lists.newArrayList(occupationDetailList);
    }

    public PointLocationOccupationContractTest(ContractType contractType, String contractNo, String businessTypeCode) {
        this.contractNo = contractNo;
        this.contractType = contractType.value();
        this.businessTypeCode = businessTypeCode;
    }

}