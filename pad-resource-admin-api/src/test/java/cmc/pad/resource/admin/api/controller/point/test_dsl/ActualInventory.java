package cmc.pad.resource.admin.api.controller.point.test_dsl;

import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.junit.Assert;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.function.Consumer;

import static cmc.pad.resource.domain.inventory.point.PointLocationInventory.TABLE_NAME;
import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.FilterType.LTE;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 实际库存断言对象
 */
public class ActualInventory {
    private final String contractNo;

    public ActualInventory(String contractNo) {
        this.contractNo = contractNo;
    }

    protected static Database db() {
        return DatabaseFactory.open("PadResource");
    }

    /**
     * 断言点位库存 - DSL风格
     */
    public ActualInventory pointLocation(PointLocationInventoryAssert asserter) {
//        await().atMost(5, SECONDS).until(() -> db().select(PointLocationOccupationContract.class).where(f("contract_no", this.contractNo)).result().one(PointLocationOccupationContract.class).getProcessStatus() == ProcessStatus.SUCESS);
        // 执行断言
        System.out.println(">>>检查库存:");
        asserter.execute();
        System.out.println(">>>检查库存完成");
        return this;
    }

    /**
     * 断言点位库存 - 传统Consumer风格（保持兼容）
     */
    public ActualInventory pointLocations(Consumer<PointLocationInventoryAsserter> asserter) {
        PointLocationInventoryAsserter inventoryAsserter = new PointLocationInventoryAsserter();
        asserter.accept(inventoryAsserter);
        return this;
    }

    /**
     * 点位库存断言器
     */
    public static class PointLocationInventoryAsserter {
        public final PointLocationInventoryArrayAccess pointLocation;

        public PointLocationInventoryAsserter() {
            this.pointLocation = new PointLocationInventoryArrayAccess();
        }
    }

    /**
     * 支持数组访问语法的辅助类
     */
    public static class PointLocationInventoryArrayAccess {
        public PointLocationInventoryAssert get(int index) {
            return new PointLocationInventoryAssert(index);
        }
    }

    /**
     * 单个点位库存断言
     */
    public static class PointLocationInventoryAssert {
        private final Integer pointLocationId;
        private LocalDate startDate;
        private LocalDate endDate;
        private Float expectedOccupyArea;

        public static ActualInventory.PointLocationInventoryAssert inventoryPid(Integer pointLocationId) {
            return new ActualInventory.PointLocationInventoryAssert(pointLocationId);
        }

        public PointLocationInventoryAssert(Integer pointLocationId) {
            this.pointLocationId = pointLocationId;
        }

        public PointLocationInventoryAssert occupyAreaIs(Float expectedOccupyArea) {
            this.expectedOccupyArea = expectedOccupyArea;
            return this;
        }

        public PointLocationInventoryAssert date(String startDate, String endDate) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            this.startDate = LocalDate.parse(startDate, formatter);
            this.endDate = LocalDate.parse(endDate, formatter);
            return this;
        }

        /**
         * 执行断言
         */
        public void execute() {
            if (expectedOccupyArea != null && startDate != null && endDate != null) {
                List<PointLocationInventory> inventories = getInventories();

                inventories.forEach(inventory -> {
                    float actualOccupyArea = subtract(inventory.getSellArea(), inventory.getNotSellArea());
                    PointLocationInfo pointLocationInfo = db().select(PointLocationInfo.class).where(f("id", inventory.getPointLocationId())).result().one(PointLocationInfo.class);
                    Assert.assertEquals("点位 " + pointLocationId + " 锁状态 " + pointLocationInfo.getInventoryStatus().name() + " 日期 " + inventory.getDate() + " 占用面积不匹配",
                            expectedOccupyArea.toString(), String.valueOf(actualOccupyArea));
                });
            }
        }

        private List<PointLocationInventory> getInventories() {
            return db().table(TABLE_NAME, pointLocationId)
                    .select(PointLocationInventory.class)
                    .where(
                            f("date", GTE, startDate)
                                    .add("date", LTE, endDate)
                                    .add("point_location_id", pointLocationId)
                    )
                    .result()
                    .all(PointLocationInventory.class);
        }

        private static float subtract(float a, float b) {
            BigDecimal aDecimal = BigDecimal.valueOf(a);
            BigDecimal bDecimal = BigDecimal.valueOf(b);
            return aDecimal.subtract(bDecimal).setScale(2, java.math.RoundingMode.HALF_UP).floatValue();
        }
    }
}