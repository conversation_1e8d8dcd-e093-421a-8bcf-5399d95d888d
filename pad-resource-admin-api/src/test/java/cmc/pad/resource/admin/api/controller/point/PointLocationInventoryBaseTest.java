package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.controller.BaseControllerTest;
import cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state.ContractState;
import cmc.pad.resource.domain.inventory.point.*;
import com.alibaba.fastjson.JSON;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cmc.pad.resource.domain.inventory.point.ContractStatus.CANCEL;
import static cmc.pad.resource.domain.inventory.point.PointLocationInventory.TABLE_NAME;
import static java.util.concurrent.TimeUnit.SECONDS;
import static mtime.lark.db.jsd.FilterType.*;
import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;
import static mtime.lark.db.jsd.UpdateType.XP;
import static org.awaitility.Awaitility.await;

public class PointLocationInventoryBaseTest extends BaseControllerTest {
    PointLocationOccupationContractTest contract = new PointLocationOccupationContractTest(
            ContractType.NEW_CONTRACT,
            ContractState.contractNo,
            "GD",
            PointLocationOccupationContractDetailTest.builder()
                    .id("AMOUNT_MIN")
                    .cinemaInnerCode("111")
                    .pointLocationId(32)
                    .amount(5.01f)
                    .startDate(LocalDate.of(2024, 1, 1))
                    .endDate(LocalDate.of(2024, 12, 31))
                    .alterStatus(PointLocationOccupationContractDetailTest.TestAlterStatus.OCCUPY.value())
                    .build()
    );

    @Before
    public void init() {
        clearAndRevertInventory(ContractState.contractNo);
    }

    //清理方法
    @After
    public void destroy() {
//        clearAndRevertInventory(ContractState.contractNo);
    }

    protected void assertActualDBData(PointLocationOccupationContractTest newContract, ContractType expectedType, ContractStatus expectedStatus) {
        assertContract(newContract.getContractNo(), expectedType, expectedStatus);
        assertPointLocationOccupationContractDetail(newContract, expectedStatus);
        assertInventory(newContract, expectedStatus);
    }

    protected void assertPointLocationOccupationContractDetail(PointLocationOccupationContractTest testNewContract, ContractStatus expectContractStatus) {
        List<PointLocationOccupationContractDetail> detailList = db()
                .select(PointLocationOccupationContractDetail.class)
                .where(f("contract_no", testNewContract.getContractNo()))
                .result()
                .all(PointLocationOccupationContractDetail.class);
        Assert.assertEquals(testNewContract.getDetails().size(), detailList.size());
        Map<String, List<PointLocationOccupationContractDetail>> actualDetailMap = detailList.stream().collect(Collectors.groupingBy(PointLocationOccupationContractDetail::getDetailId));
        Map<String, PointLocationOccupationContractDetailTest> testlDetailMap = testNewContract.getDetails().stream()
                .collect(Collectors.toMap(PointLocationOccupationContractDetailTest::getId, detail -> detail));

        detailList.forEach(actualDetail -> {
            PointLocationOccupationContractDetailTest testDetail = testlDetailMap.get(actualDetail.getDetailId());
            Assert.assertEquals(1, actualDetailMap.get(actualDetail.getDetailId()).size());
            Assert.assertEquals(testDetail.getAmount().toString(), actualDetail.getAmount().toString());
            if (PointLocationOccupationContractDetailTest.TestAlterStatus.DESTROY.value() == testDetail.getAlterStatus())
                Assert.assertEquals(AlterStatus.DESTROY.value(), actualDetail.getAlterStatus().value());
            else
                Assert.assertEquals(testDetail.getAlterStatus(), actualDetail.getAlterStatus().value());
            Assert.assertEquals(expectContractStatus.value(), actualDetail.getContractStatus().value());

            Assert.assertEquals(testDetail.getCinemaInnerCode(), actualDetail.getCinemaCode());
            Assert.assertEquals(testDetail.getPointLocationId().intValue(), actualDetail.getPointLocationId().intValue());
            Assert.assertEquals(testDetail.getId(), actualDetail.getDetailId());
            Assert.assertEquals(testDetail.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), actualDetail.getStartDate().toString());
            Assert.assertEquals(testDetail.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), actualDetail.getEndDate().toString());
        });
    }

    protected void assertContract(String contractNo, ContractType expectedType, ContractStatus expectedStatus) {
        PointLocationOccupationContract contract = db()
                .select(PointLocationOccupationContract.class)
                .where(f("contract_no", contractNo))
                .limit(0, 1)
                .result().
                one(PointLocationOccupationContract.class);
        System.out.println(JSON.toJSONString(contract));
        Assert.assertEquals(expectedType.value(), contract.getContractType().value());
        Assert.assertEquals(expectedStatus.value(), contract.getStatus().value());
        Assert.assertNotNull(contract);
    }

    protected void clearAndRevertInventory(String contractNo) {
        PointLocationOccupationContract testContract = db().select(PointLocationOccupationContract.class).where(f("contract_no", contractNo)).result().one(PointLocationOccupationContract.class);
        if (testContract == null)
            return;
        List<PointLocationOccupationContractDetail> testContractDetails = db().select(PointLocationOccupationContractDetail.class).where(f("contract_no", contractNo)).result().all(PointLocationOccupationContractDetail.class);
        List<PointLocationOccupationContractDetail> alreadyExistDetails = db().select(PointLocationOccupationContractDetail.class)
                .where(
                        f("point_location_id", IN, testContractDetails.stream().map(PointLocationOccupationContractDetail::getPointLocationId).toArray())
                                .add("contract_no", NE, contractNo))
                .result()
                .all(PointLocationOccupationContractDetail.class);
        if (!alreadyExistDetails.isEmpty())
            throw new RuntimeException("除" + contractNo + "合同外,还有其他合同正在占用, 请更换其他点位测试id");
        testContractDetails.forEach(detail -> {
            db()
                    .table("point_location_inventory", detail.getPointLocationId())
                    .update(uv("not_sell_area", XP, "sell_area"))
                    .where(f("point_location_id", detail.getPointLocationId())
                            .add("date", GTE, detail.getStartDate())
                            .add("date", LTE, detail.getEndDate())
                    ).result();
            System.out.println("还原pid:" + detail.getPointLocationId() + ", date:" + detail.getStartDate() + " - " + detail.getEndDate());
        });
        db().delete("point_location_occupation_contract").where(f("contract_no", contractNo)).result();
        db().delete("point_location_occupation_contract_detail").where(f("contract_no", contractNo)).result();
        db().delete("point_location_occupation_log").where(f("contract_no", contractNo)).result();
        System.out.println(">>>清理 + 还原 数据 完成<<<");
    }

    private static void assertInventory(PointLocationOccupationContractTest newContract, ContractStatus expectedStatus) {
        await().atMost(5, SECONDS).until(() -> db().select(PointLocationOccupationContract.class).where(f("contract_no", newContract.getContractNo())).result().one(PointLocationOccupationContract.class).getProcessStatus() == ProcessStatus.SUCESS);
        newContract.getDetails().forEach(detail -> {
            db().table(TABLE_NAME, detail.getPointLocationId())
                    .select(PointLocationInventory.class)
                    .where(
                            f("date", GTE, detail.getStartDate())
                                    .add("date", LTE, detail.getEndDate())
                                    .add("point_location_id", detail.getPointLocationId())
                    )
                    .result()
                    .all(PointLocationInventory.class)
                    .forEach(inventory -> {
                        if (CANCEL == expectedStatus) {
                            Assert.assertEquals(inventory.getSellArea(), inventory.getNotSellArea());
                        } else {
                            if (detail.getAlterStatus() == PointLocationOccupationContractDetailTest.TestAlterStatus.DESTROY.value() && expectedStatus == ContractStatus.APPROVAL) {
                                Assert.assertEquals(inventory.getSellArea(), inventory.getNotSellArea());
                            } else {
                                float actualOcpArea = subtract(inventory.getSellArea(), inventory.getNotSellArea());
//                        System.out.println("pointId:" + detail.getPointLocationId() + " date:" + inventory.getDate() + " ocp_area:" + detail.getAmount() + " sell_area:" + inventory.getSellArea() + " not_sell_area:" + inventory.getNotSellArea() + " actOcpArea:" + actualOcpArea);
                                Assert.assertEquals(detail.getAmount().toString(), String.valueOf(actualOcpArea));
                            }
                        }
                    });
        });
    }

    private static float subtract(float a, float b) {
        BigDecimal aDecimal = BigDecimal.valueOf(a);
        BigDecimal bDecimal = BigDecimal.valueOf(b);
        return aDecimal.subtract(bDecimal).setScale(2, java.math.RoundingMode.HALF_UP).floatValue();
    }

    @Override
    protected String getApi(String param) throws Exception {
        return "";
    }
}
