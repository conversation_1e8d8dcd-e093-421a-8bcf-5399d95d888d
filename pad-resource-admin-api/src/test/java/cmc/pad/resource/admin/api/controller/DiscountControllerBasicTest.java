package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 折扣控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/10
 * @Version 1.0
 */
public class DiscountControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 空参数测试
        testBothMethods("", 101, "business_type不能为空");
        // 无效业务类型测试
        testBothMethods("business_type=INVALID", 6002, "不支持的折扣类别");
        testBothMethods("business_type=XX", 6002, "不支持的折扣类别");
        // 缺少必要参数测试
        testBothMethods("business_type=YX", 6005, "查询折扣系数时，时长和面积数不能同时为空");
        testBothMethods("business_type=WZ", 6005, "查询折扣系数时，时长和面积数不能同时为空");
        testBothMethods("business_type=CMDX", 6005, "查询折扣系数时，时长和面积数不能同时为空");
    }

    @Test
    public void testValidBusinessTypes() throws Exception {
        // 测试所有支持的业务类型
        testBothMethods("business_type=YX&area=10.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.95}]");
        testBothMethods("business_type=WZ&duration=30.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.6}]");
        testBothMethods("business_type=GD&area=5.0&duration=15.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.67},{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=XC&area=20.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.75}]");
        testBothMethods("business_type=YT&duration=60.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.5}]");
        testBothMethods("business_type=GMT&area=8.0&duration=45.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.8},{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=CMDX&area=8.0&duration=45.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":1.0},{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=CMDX&area=8.0&duration=1.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":1.2},{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=CMDX&area=8.0&duration=2.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":1.2},{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
    }

    @Test
    public void testSpecificDiscountMethod() throws Exception {
        // 测试指定折扣方式 - 时长
        testBothMethods("business_type=YX&discount_method=DURATION&duration=30.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.88}]");
        testBothMethods("business_type=WZ&discount_method=DURATION&duration=60.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.6}]");

        // 测试指定折扣方式 - 面积
        testBothMethods("business_type=GD&discount_method=AREA&area=10.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.85}]");
        testBothMethods("business_type=XC&discount_method=AREA&area=25.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.75}]");

        // 测试无效折扣方式
        testBothMethods("business_type=YX&discount_method=INVALID&duration=30.0", 6002, "不支持的折扣类别");
    }

    @Test
    public void testBoundaryValues() throws Exception {
        // 测试边界值
        testBothMethods("business_type=YX&area=0.1", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=YX&area=11", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.95}]");
        testBothMethods("business_type=YX&area=20", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.8}]");

        testBothMethods("business_type=YX&duration=1", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":1.0}]");
        testBothMethods("business_type=YX&duration=30", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.88}]");
        testBothMethods("business_type=YX&duration=60", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.75}]");

        testBothMethods("business_type=WZ&duration=0.1", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":1.0}]");
        testBothMethods("business_type=GD&area=1000.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.75}]");
        testBothMethods("business_type=XC&duration=1000.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.5}]");
    }

    @Test
    public void testCaseInsensitiveBusinessType() throws Exception {
        // 测试业务类型大小写不敏感
        testBothMethods("business_type=yx&area=10.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":0.95}]");
        testBothMethods("business_type=wz&duration=30.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.6}]");
        testBothMethods("business_type=gd&area=5.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=xc&duration=15.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.5}]");
        testBothMethods("business_type=yt&area=8.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
        testBothMethods("business_type=gmt&duration=45.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.8}]");
        testBothMethods("business_type=gmt&area=45.0", 0, "OK", "[{\"discount_method\":\"AREA\",\"coefficient\":1.0}]");
    }

    @Test
    public void testCaseInsensitiveDiscountMethod() throws Exception {
        // 测试折扣方式大小写不敏感
        testBothMethods("business_type=YX&discount_method=duration&duration=30.0", 0, "OK", "[{\"discount_method\":\"duration\",\"coefficient\":0.88}]");
        testBothMethods("business_type=WZ&discount_method=area&area=10.0", 0, "OK", "[{\"discount_method\":\"area\",\"coefficient\":0.9}]");
        testBothMethods("business_type=GD&discount_method=Duration&duration=45.0", 0, "OK", "[{\"discount_method\":\"Duration\",\"coefficient\":0.67}]");
        testBothMethods("business_type=XC&discount_method=Area&area=15.0", 0, "OK", "[{\"discount_method\":\"Area\",\"coefficient\":0.75}]");
    }

    @Test
    public void testMultipleDiscountFactors() throws Exception {
        // 测试同时提供面积和时长参数（不指定折扣方式）
        testBothMethods("business_type=YX&area=10.0&duration=30.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.88},{\"discount_method\":\"AREA\",\"coefficient\":0.95}]");
        testBothMethods("business_type=WZ&area=15.0&duration=45.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.6},{\"discount_method\":\"AREA\",\"coefficient\":0.9}]");
        testBothMethods("business_type=GD&area=20.0&duration=60.0", 0, "OK", "[{\"discount_method\":\"DURATION\",\"coefficient\":0.67},{\"discount_method\":\"AREA\",\"coefficient\":0.75}]");
    }

    @Test
    public void testSpecialCases() throws Exception {
        // 测试特殊情况
        // 指定折扣方式但缺少对应参数
        testBothMethods("business_type=YX&discount_method=DURATION", 6004, "查询时长折扣系数时，时长不能为空");
        testBothMethods("business_type=WZ&discount_method=AREA", 6003, "查询面积折扣系数时，面积数不能为空");

        // 指定折扣方式但提供了错误的参数
        testBothMethods("business_type=YX&discount_method=DURATION&area=10.0", 6004, "查询时长折扣系数时，时长不能为空");
        testBothMethods("business_type=WZ&discount_method=AREA&duration=30.0", 6003, "查询面积折扣系数时，面积数不能为空");
    }

    @Test
    public void testResponseDataStructure() throws Exception {
        // 测试返回数据结构 - 单个折扣方式
        testBothMethods("business_type=YX&discount_method=DURATION&duration=30.0", 0, "OK",
                "[{\"discount_method\":\"DURATION\",\"coefficient\":0.88}]");

        testBothMethods("business_type=WZ&discount_method=AREA&area=10.0", 0, "OK",
                "[{\"discount_method\":\"AREA\",\"coefficient\":0.9}]");

        // 测试返回数据结构 - 多个折扣方式
        testBothMethods("business_type=GD&area=10.0&duration=30.0", 0, "OK",
                "[{\"discount_method\":\"DURATION\",\"coefficient\":0.67},{\"discount_method\":\"AREA\",\"coefficient\":0.85}]");
        testBothMethods("business_type=GD&area=10.0&duration=30.0", 0, "OK",
                "[{\"discount_method\":\"DURATION\",\"coefficient\":0.67},{\"discount_method\":\"AREA\",\"coefficient\":0.85}]");
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/discount/coefficient/match?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/discount/coefficient/match";
        return httpPost(path, param);
    }
}