package cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state;

import cmc.pad.resource.admin.api.controller.point.PointLocationOccupationContractTest;
import cmc.pad.resource.domain.inventory.point.ContractType;

public class InitState extends ContractState {
    public InitState() {
        testContract = new PointLocationOccupationContractTest(
                ContractType.NEW_CONTRACT,
                ContractState.contractNo,
                "GD"
        );
    }
}