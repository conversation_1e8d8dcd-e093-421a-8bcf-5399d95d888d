package cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state;

import static cmc.pad.resource.admin.api.controller.point.test_dsl.DSLImports.Occup;
import static cmc.pad.resource.admin.api.controller.point.test_dsl.DSLImports.PointLocation;

/**
 * 已提交新合同状态
 */
public class NewContractSubmitted extends ContractState {
    public NewContractSubmitted() {
        new InitState()
                .submit(PointLocation(32).status(Occup).amount(5.01f).date("2024-01-01", "2024-12-31"))
        ;
    }
}