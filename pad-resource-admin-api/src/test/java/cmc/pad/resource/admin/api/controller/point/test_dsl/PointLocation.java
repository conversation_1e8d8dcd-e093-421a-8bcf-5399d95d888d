package cmc.pad.resource.admin.api.controller.point.test_dsl;

import cmc.pad.resource.admin.api.controller.point.PointLocationOccupationContractDetailTest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * PointLocation构建器，支持DSL风格的链式调用
 */
public class PointLocation {
    private String detailId;
    private Integer pointLocationId;
    private PointLocationStatus status;
    private Float amount;
    private LocalDate startDate;
    private LocalDate endDate;
    private String cinemaInnerCode = "111"; // 默认值

    /**
     * 点位状态枚举
     */
    public enum PointLocationStatus {
        Occup(PointLocationOccupationContractDetailTest.TestAlterStatus.OCCUPY),
        Add(PointLocationOccupationContractDetailTest.TestAlterStatus.ADD),
        Destroy(PointLocationOccupationContractDetailTest.TestAlterStatus.DESTROY);

        private final PointLocationOccupationContractDetailTest.TestAlterStatus alterStatus;

        PointLocationStatus(PointLocationOccupationContractDetailTest.TestAlterStatus alterStatus) {
            this.alterStatus = alterStatus;
        }

        public int value() {
            return alterStatus.value();
        }
    }

    private PointLocation(Integer pointLocationId) {
        this.pointLocationId = pointLocationId;
    }

    /**
     * 创建PointLocation构建器
     */
    public static PointLocation PointLocation(Integer pointLocationId) {
        return new PointLocation(pointLocationId);
    }

    public PointLocation status(PointLocationStatus status) {
        this.status = status;
        return this;
    }

    public PointLocation amount(Float amount) {
        this.amount = amount;
        return this;
    }

    public PointLocation date(String startDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        this.startDate = LocalDate.parse(startDate, formatter);
        this.endDate = LocalDate.parse(endDate, formatter);
        return this;
    }

    public PointLocation cinemaInnerCode(String cinemaInnerCode) {
        this.cinemaInnerCode = cinemaInnerCode;
        return this;
    }

    public PointLocation detailId(String detailId) {
        this.detailId = detailId;
        return this;
    }

    /**
     * 转换为测试用的合同明细对象
     */
    public PointLocationOccupationContractDetailTest toContractDetail() {
        PointLocationOccupationContractDetailTest build = PointLocationOccupationContractDetailTest.builder()
                .id("DETAIL_" + pointLocationId)
                .cinemaInnerCode(cinemaInnerCode)
                .pointLocationId(pointLocationId)
                .amount(amount)
                .startDate(startDate)
                .endDate(endDate)
                .alterStatus(status.value())
                .build();
        if (detailId != null) {
            build.setId(detailId);
        }
        return build;
    }
}