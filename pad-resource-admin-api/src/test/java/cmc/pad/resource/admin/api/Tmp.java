package cmc.pad.resource.admin.api;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import cmc.pad.resource.admin.api.model.point.PointLocationParamModel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.*;
import mtime.lark.util.lang.FaultException;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterDataChecker.dateRangeAndAreaAllIsExpand;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterDataChecker.isExpand;

/**
 * Created by fu.wei on 2022/1/28.
 */
public class Tmp {
    static List<DateRange> dateRangeList = Lists.newArrayList();

    static {
        dateRangeList.add(
                new DateRange(
                        LocalDate.of(2020, 2, 9),
                        LocalDate.of(2020, 2, 15)
                )
        );
    }

    @Test
    public void test1() {
        LocalDate start = LocalDate.of(2022, 1, 1);
        LocalDate end = LocalDate.of(2025, 2, 28);
        System.out.printf(" >>> " + JSON.toJSONString(splitYearRange(start, end), true));
    }

    private List<DateRange> splitYearRange(LocalDate start, LocalDate end) {
        Period p = Period.between(start, end);
        int years = p.getYears();
        List<DateRange> list = Lists.newArrayList();
        if (years == 0) {
            list.add(new DateRange(start, end));
        } else {
            for (int i = 0; i <= years; i++) {
                if (i == 0) {
                    list.add(new DateRange(start, LocalDate.of(start.getYear(), 12, 31)));
                }
                if (i != 0 && i != years) {
                    start = LocalDate.of(start.getYear() + 1, 1, 1);
                    list.add(new DateRange(start, LocalDate.of(start.getYear(), 12, 31)));
                }
                if (i == years) {
                    list.add(new DateRange(LocalDate.of(end.getYear(), 1, 1), end));
                }
            }
        }
        return list;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRange {
        LocalDate start;
        LocalDate end;
    }

    @Test
    public void tt() {
        PointLocationParamModel.InventoryOccupationContractParam param = new PointLocationParamModel.InventoryOccupationContractParam();
        param.setContractNo("contract_code_xxx");
        param.setBusinessTypeCode("YX");
        PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("001");
        detail1.setPointLocationId(1);
        detail1.setAmount(10f);
        detail1.setStartDate(LocalDate.of(2022, 2, 1));
        detail1.setEndDate(LocalDate.of(2022, 2, 10));

        PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("001");
        detail2.setPointLocationId(1);
        detail2.setAmount(20F);
        detail2.setStartDate(LocalDate.of(2022, 2, 11));
        detail2.setEndDate(LocalDate.of(2022, 2, 20));

        PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("002");
        detail3.setPointLocationId(2);
        detail3.setAmount(15F);
        detail3.setStartDate(LocalDate.of(2022, 1, 1));
        detail3.setEndDate(LocalDate.of(2023, 1, 1));

        List<PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail> details = Lists.newArrayList();
        details.add(detail1);
        details.add(detail2);
        details.add(detail3);
        param.setDetails(details);

        System.out.println(JSON.toJSONString(param, true));
    }

    @Test
    public void test() {
        LocalDate start = LocalDate.of(2020, 2, 7);
        LocalDate end = LocalDate.of(2020, 2, 10);
        checkPointLocationDateIntersection(start, end);
    }

    private void checkPointLocationDateIntersection(LocalDate start, LocalDate end) {
        dateRangeList.stream().forEach(dateRange -> {
            if (start.isEqual(dateRange.start) || start.isEqual(dateRange.end)) {
                throw new FaultException(start + "存在交集");
            }
            if (end.isEqual(dateRange.end) || end.isEqual(dateRange.start)) {
                throw new FaultException(end + "存在交集");
            }
            if (start.isAfter(dateRange.start) && end.isBefore(dateRange.end)) {
                throw new FaultException(start + " - " + end + "存在交集");
            }
            if (start.isAfter(dateRange.start) && start.isBefore(dateRange.end)) {
                throw new FaultException(start + "存在交集");
            }
            if (end.isAfter(dateRange.start) && end.isBefore(dateRange.end)) {
                throw new FaultException(end + "存在交集");
            }
        });
        dateRangeList.add(new DateRange(start, end));
    }

    private void collectPointLocationIntersectionDateArea(LocalDate start, LocalDate end, Float area) {
        dateRangeList.stream().forEach(dateRange -> {
            if (start.isEqual(dateRange.start) || start.isEqual(dateRange.end)) {
                throw new FaultException(start + "存在交集");
            }
            if (end.isEqual(dateRange.end) || end.isEqual(dateRange.start)) {
                throw new FaultException(end + "存在交集");
            }
            if (start.isAfter(dateRange.start) && end.isBefore(dateRange.end)) {
                throw new FaultException(start + " - " + end + "存在交集");
            }
            if (start.isAfter(dateRange.start) && start.isBefore(dateRange.end)) {
                throw new FaultException(start + "存在交集");
            }
            if (end.isAfter(dateRange.start) && end.isBefore(dateRange.end)) {
                throw new FaultException(end + "存在交集");
            }
        });
        dateRangeList.add(new DateRange(start, end));
    }



    public LocalDate date(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        String[] splitDate = date.split("-");
//        return LocalDate.of(Integer.valueOf(splitDate[0]), Integer.valueOf(splitDate[1]), Integer.valueOf(splitDate[2]));
    }

    @Test
    public void testIsExpand() {
        RealOccupyData.DateOccupyArea oldData = new RealOccupyData.DateOccupyArea(date("2022-3-1"), date("2022-3-10"), 10f);
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-3-1"), date("2022-3-11"), 10f),
                oldData
        ));
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-2-1"), date("2022-3-10"), 10f),
                oldData
        ));
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-2-1"), date("2022-3-11"), 10f),
                oldData
        ));
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-3-1"), date("2022-3-10"), 11f),
                oldData
        ));
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-3-1"), date("2022-3-11"), 11f),
                oldData
        ));
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-2-1"), date("2022-3-10"), 11f),
                oldData
        ));
        Assert.assertTrue(isExpand(
                new RealOccupyData.DateOccupyArea(date("2022-2-1"), date("2022-3-11"), 11f),
                oldData
        ));
    }

    @Test
    public void testIsShrink() {
        RealOccupyData.DateOccupyArea oldData = new RealOccupyData.DateOccupyArea(date("2022-3-1"), date("2022-3-10"), 10f);
        Assert.assertTrue(dateRangeAndAreaAllIsExpand(
                new RealOccupyData.DateOccupyArea(date("2022-3-1"), date("2022-3-1"), 20f),
                oldData
        ));

    }

    @Test
    public void testDateRangeAndAreaAllIsShrink() {
//        Assert.assertTrue(dateRangeAndAreaAllIsExpand());
    }

    @Test
    public void testDateRangeIsExpandButAreaIsShrink() {
//        Assert.assertTrue(dateRangeAndAreaAllIsExpand());
    }

    @Test
    public void testDateRangeIsShrinkButAreaIsExpand() {
//        Assert.assertTrue(dateRangeAndAreaAllIsExpand());
    }



}