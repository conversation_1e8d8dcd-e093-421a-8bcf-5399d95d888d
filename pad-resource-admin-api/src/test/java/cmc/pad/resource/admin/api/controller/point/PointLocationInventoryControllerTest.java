package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.HttpUtil;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.PublishUtil;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.OccupyOrCancelParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Data;
import org.junit.Test;

import java.util.List;

/**
 * Created by fuwei on 2022/2/23.
 */
public class PointLocationInventoryControllerTest {

    private static final String TEST_POINT_LOCATION_200 = "test-point-location-200";
    private static final String TEST_POINT_LOCATION_201 = "test-point-location-201";
    private static final String TEST_POINT_LOCATION_202 = "test-point-location-202";
    private static final String TEST_POINT_LOCATION_204 = "test-point-location-204";

    @Test
    public void testSend() {
        PublishUtil.send(new OccupyOrCancelParam(ContractType.NEW_CONTRACT.value(), TEST_POINT_LOCATION_200, ContractStatus.CANCEL.value()));
    }

    @Test
    public void testOccupy() {
        SaveOccupationParams params = new SaveOccupationParams();
        params.setContractNo(TEST_POINT_LOCATION_204);
        params.setBusinessType("GD");
        params.setContractType(ContractType.ALTER_CONTRACT.value());
        SaveOccupationParams.OccupationDetail detail1 = new SaveOccupationParams.OccupationDetail();
        detail1.setPointLocationId(8);
        detail1.setCinemaCode("111");
        detail1.setAmount(10F);
        detail1.setStartDate("2022-03-01");
        detail1.setEndDate("2022-03-05");
        detail1.setStatus(1);
        detail1.setId("21_112");
        params.setDetails(Lists.newArrayList(detail1));

        HttpUtil.postJson("point-location/inventory/occupy", params);
    }

    @Test
    public void testUpdateStatus() {
        UpdateStatusParam param = new UpdateStatusParam();
        param.setContractNo(TEST_POINT_LOCATION_204);
        param.setStatus(2);
        HttpUtil.postJson("point-location/inventory/updateStatus", param);
    }

    @Test
    public void testQuery() {
        PointLocationQueryParam param = new PointLocationQueryParam();
        param.setStart("2022-05-01");
        param.setEnd("2022-05-26");
        HttpUtil.postJson("point-location/query", param);
    }

    @Data
    public class PointLocationQueryParam {
        @JsonProperty("page_index")
        private int pageIndex = 1;
        @JsonProperty("page_size")
        private int pageSize = 10;
        private String cinemaInnerCode;
        @JsonProperty("business_type_code")
        private String businessTypeCode;
        @JsonProperty("start")
        private String start;
        @JsonProperty("end")
        private String end;
    }

    @Data
    public static class UpdateStatusParam {
        @JsonProperty("contract_no")
        private String contractNo;
        private int status;//1:扣减 2:取消
    }

    @Data
    public static class SaveOccupationParams {
        @JsonProperty("contract_type")
        private int contractType;
        @JsonProperty("contract_no")
        private String contractNo;
        @JsonProperty("business_type_code")
        private String businessType;
        private List<OccupationDetail> details;

        @Data
        public static class OccupationDetail {
            @JsonProperty("point_location_id")
            private int pointLocationId;
            @JsonProperty("cinema_inner_code")
            private String cinemaCode;
            private Float amount;
            @JsonProperty("start_date")
            private String startDate;
            @JsonProperty("end_date")
            private String endDate;
            @JsonProperty("status")
            private int status;
            @JsonProperty("id")
            private String id;
        }
    }
}