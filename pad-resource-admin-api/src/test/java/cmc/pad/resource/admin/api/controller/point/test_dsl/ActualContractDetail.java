package cmc.pad.resource.admin.api.controller.point.test_dsl;

import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.junit.Assert;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 实际合同明细断言对象
 */
public class ActualContractDetail {
    private final String contractNo;

    public ActualContractDetail(String contractNo) {
        this.contractNo = contractNo;
    }

    protected static Database db() {
        return DatabaseFactory.open("PadResource");
    }

    /**
     * 断言明细数量
     */
    public ActualContractDetail numIs(int expectedNum) {
        List<PointLocationOccupationContractDetail> details = getContractDetails();
        Assert.assertEquals("合同明细数量不匹配", expectedNum, details.size());
        return this;
    }

    /**
     * 断言点位明细 - DSL风格
     */
    public ActualContractDetail pointLocation(PointLocationDetailAssert asserter) {
        // 执行断言
        if (asserter.detailId != null) {
            asserter.execute2(getContractDetailsMapByDetailId());
        } else {
            asserter.execute1(getContractDetailsMap());
        }
        return this;
    }

    /**
     * 断言点位明细 - 传统Consumer风格（保持兼容）
     */
    public ActualContractDetail pointLocations(Consumer<PointLocationDetailAsserter> asserter) {
        List<PointLocationOccupationContractDetail> details = getContractDetails();
        Map<Integer, PointLocationOccupationContractDetail> detailMap = details.stream()
                .collect(Collectors.toMap(
                        PointLocationOccupationContractDetail::getPointLocationId,
                        detail -> detail
                ));

        PointLocationDetailAsserter pointLocationAsserter = new PointLocationDetailAsserter(detailMap);
        asserter.accept(pointLocationAsserter);
        return this;
    }

    private Map<Integer, PointLocationOccupationContractDetail> getContractDetailsMap() {
        return getContractDetails().stream()
                .collect(Collectors.toMap(
                        PointLocationOccupationContractDetail::getPointLocationId,
                        detail -> detail
                ));
    }

    private Map<String, PointLocationOccupationContractDetail> getContractDetailsMapByDetailId() {
        return getContractDetails().stream()
                .collect(Collectors.toMap(
                        PointLocationOccupationContractDetail::getDetailId,
                        detail -> detail
                ));
    }

    private List<PointLocationOccupationContractDetail> getContractDetails() {
        return db()
                .select(PointLocationOccupationContractDetail.class)
                .where(f("contract_no", contractNo))
                .result()
                .all(PointLocationOccupationContractDetail.class);
    }

    /**
     * 点位明细断言器
     */
    public static class PointLocationDetailAsserter {
        private final Map<Integer, PointLocationOccupationContractDetail> detailMap;
        public final PointLocationDetailArrayAccess pointLocation;

        public PointLocationDetailAsserter(Map<Integer, PointLocationOccupationContractDetail> detailMap) {
            this.detailMap = detailMap;
            this.pointLocation = new PointLocationDetailArrayAccess(this);
        }

        /**
         * 获取指定点位ID的断言器
         */
        public PointLocationDetailAssert get(int pointLocationId) {
            PointLocationOccupationContractDetail detail = detailMap.get(pointLocationId);
            Assert.assertNotNull("点位ID " + pointLocationId + " 的明细不存在", detail);
            return new PointLocationDetailAssert(pointLocationId);
        }
    }

    /**
     * 支持数组访问语法的辅助类
     */
    public static class PointLocationDetailArrayAccess {
        private final PointLocationDetailAsserter asserter;

        public PointLocationDetailArrayAccess(PointLocationDetailAsserter asserter) {
            this.asserter = asserter;
        }

        public PointLocationDetailAssert get(int index) {
            return asserter.get(index);
        }
    }

    /**
     * 单个点位明细断言
     */
    public static class PointLocationDetailAssert {
        private String detailId;
        private Integer pointLocationId;
        private AlterStatus expectedStatus;
        private Float expectedAmount;
        private String expectedStartDate;
        private String expectedEndDate;

        public static ActualContractDetail.PointLocationDetailAssert detailPid(Integer pointLocationId) {
            return new ActualContractDetail.PointLocationDetailAssert(pointLocationId);
        }

        public static ActualContractDetail.PointLocationDetailAssert detailId(String detailId) {
            return new ActualContractDetail.PointLocationDetailAssert(detailId);
        }

        public PointLocationDetailAssert(Integer pointLocationId) {
            this.pointLocationId = pointLocationId;
        }

        public PointLocationDetailAssert(String detailId) {
            this.detailId = detailId;
        }

        public PointLocationDetailAssert statusIs(AlterStatus expectedStatus) {
            this.expectedStatus = expectedStatus;
            return this;
        }

        public PointLocationDetailAssert amountIs(Float expectedAmount) {
            this.expectedAmount = expectedAmount;
            return this;
        }

        public PointLocationDetailAssert dateIs(String expectedStartDate, String expectedEndDate) {
            this.expectedStartDate = expectedStartDate;
            this.expectedEndDate = expectedEndDate;
            return this;
        }

        /**
         * 执行断言
         */
        public void execute1(Map<Integer, PointLocationOccupationContractDetail> detailMap) {
            PointLocationOccupationContractDetail detail = detailMap.get(pointLocationId);
            Assert.assertNotNull("点位ID " + pointLocationId + " 的明细不存在", detail);

            if (expectedStatus != null) {
                Assert.assertEquals("点位状态不匹配", expectedStatus.value(), detail.getAlterStatus().value());
            }

            if (expectedAmount != null) {
                Assert.assertEquals("点位面积不匹配", expectedAmount.toString(), detail.getAmount().toString());
            }

            if (expectedStartDate != null && expectedEndDate != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate expectedStart = LocalDate.parse(expectedStartDate, formatter);
                LocalDate expectedEnd = LocalDate.parse(expectedEndDate, formatter);

                Assert.assertEquals("开始日期不匹配", expectedStart.toString(), detail.getStartDate().toString());
                Assert.assertEquals("结束日期不匹配", expectedEnd.toString(), detail.getEndDate().toString());
            }
        }

        public void execute2(Map<String, PointLocationOccupationContractDetail> detailMap) {
            PointLocationOccupationContractDetail detail = detailMap.get(detailId);
            Assert.assertNotNull("点位ID " + pointLocationId + " 的明细不存在", detail);

            if (expectedStatus != null) {
                Assert.assertEquals("点位状态不匹配", expectedStatus.value(), detail.getAlterStatus().value());
            }

            if (expectedAmount != null) {
                Assert.assertEquals("点位面积不匹配", expectedAmount.toString(), detail.getAmount().toString());
            }

            if (expectedStartDate != null && expectedEndDate != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                LocalDate expectedStart = LocalDate.parse(expectedStartDate, formatter);
                LocalDate expectedEnd = LocalDate.parse(expectedEndDate, formatter);

                Assert.assertEquals("开始日期不匹配", expectedStart.toString(), detail.getStartDate().toString());
                Assert.assertEquals("结束日期不匹配", expectedEnd.toString(), detail.getEndDate().toString());
            }
        }
    }
}