package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 冠名厅租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/08
 * @Version 1.0
 */
public class MovieHallNamingLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 无效参数测试
        testBothMethods("cinema_code=INVALID", 101, "cinema_code影城编码错误");
        testBothMethods("cinema_code=", 2004, "影城不存在");
        testBothMethods("cinema_code=@#$", 101, "cinema_code影城编码错误");
        testBothMethods("cinema_code=99999", 2004, "影城不存在");
        testBothMethods("movie_hall_type=W9", 101, "cinema_code不能为null");
    }

    @Test
    public void testValidCinemaCode() throws Exception {
        // 有效影城代码测试
        testBothMethods("cinema_code=849", 0, "OK", "[]");
        testBothMethods("cinema_code=304", 0, "OK",
                "[{\"cinema_code\":\"304\",\"movie_hall_type\":\"W9\",\"base_price\":12345678.90},{\"cinema_code\":\"304\",\"movie_hall_type\":\"W9\",\"base_price\":1234567.89}]");
    }

    @Test
    public void testMovieHallTypeParams() throws Exception {
        // 影厅类型参数测试
        testBothMethods("cinema_code=304&movie_hall_type=INVALID", 0, "OK", "[]");
        testBothMethods("cinema_code=304&movie_hall_type=123", 0, "OK", "[]");
        testBothMethods("cinema_code=304&movie_hall_type=W8", 0, "OK", "[]");
        testBothMethods("cinema_code=304&movie_hall_type=I", 0, "OK", "[]");
        testBothMethods("cinema_code=304&movie_hall_type=S", 0, "OK", "[]");
        testBothMethods("cinema_code=304&movie_hall_type=", 0, "OK",
                "[{\"cinema_code\":\"304\",\"movie_hall_type\":\"W9\",\"base_price\":12345678.90},{\"cinema_code\":\"304\",\"movie_hall_type\":\"W9\",\"base_price\":1234567.89}]");
        testBothMethods("cinema_code=304&movie_hall_type=W9", 0, "OK",
                "[{\"cinema_code\":\"304\",\"movie_hall_type\":\"W9\",\"base_price\":12345678.90},{\"cinema_code\":\"304\",\"movie_hall_type\":\"W9\",\"base_price\":1234567.89}]");
    }

    @Test
    public void testTestEndpoint() throws Exception {
        // 测试专用端点
        String response = postTestApi();
        assert response.contains("test") : "Test endpoint should return 'test'";
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/named-movie-hall-leasing/price/query?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/named-movie-hall-leasing/price/query";
        return httpPost(path, param);
    }

    protected String postTestApi() throws Exception {
        String path = "/named-movie-hall-leasing/price/test";
        return httpPost(path, "{}");
    }
}
