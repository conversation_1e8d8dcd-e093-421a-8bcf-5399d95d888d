package cmc.pad.resource.admin.api;

import com.alibaba.fastjson.JSON;
import org.junit.Test;

import java.util.Map;

import static org.junit.Assert.*;

public class QueryStringToJsonTest {
    
    @Test
    public void testSimpleQueryStringToJson() {
        String queryString = "xx=1&ss=2";
        String json = HttpUtil.queryStringToJson(queryString);
        Map<String, String> parsed = JSON.parseObject(json, Map.class);
        
        assertEquals("1", parsed.get("xx"));
        assertEquals("2", parsed.get("ss"));
    }
    
    @Test
    public void testEmptyValueQueryStringToJson() {
        String queryString = "xx=1&empty=&ss=2";
        String json = HttpUtil.queryStringToJson(queryString);
        Map<String, String> parsed = JSON.parseObject(json, Map.class);
        
        assertEquals("1", parsed.get("xx"));
        assertEquals("", parsed.get("empty"));
        assertEquals("2", parsed.get("ss"));
    }
    
    @Test
    public void testNoValueQueryStringToJson() {
        String queryString = "xx=1&novalue&ss=2";
        String json = HttpUtil.queryStringToJson(queryString);
        Map<String, String> parsed = JSON.parseObject(json, Map.class);
        
        assertEquals("1", parsed.get("xx"));
        assertEquals("", parsed.get("novalue"));
        assertEquals("2", parsed.get("ss"));
    }
    
    @Test
    public void testSpecialCharactersQueryStringToJson() {
        String queryString = "name=%E4%B8%AD%E6%96%87&value=hello%20world";
        String json = HttpUtil.queryStringToJson(queryString);
        Map<String, String> parsed = JSON.parseObject(json, Map.class);
        
        assertEquals("中文", parsed.get("name"));
        assertEquals("hello world", parsed.get("value"));
    }
    
    @Test
    public void testEmptyQueryStringToJson() {
        String queryString = "";
        String json = HttpUtil.queryStringToJson(queryString);
        Map<String, String> parsed = JSON.parseObject(json, Map.class);
        
        assertTrue(parsed.isEmpty());
    }
    
    @Test
    public void testNullQueryStringToJson() {
        String json = HttpUtil.queryStringToJson(null);
        Map<String, String> parsed = JSON.parseObject(json, Map.class);
        
        assertTrue(parsed.isEmpty());
    }
}