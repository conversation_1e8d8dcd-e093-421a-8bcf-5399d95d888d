package cmc.pad.resource.admin.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import okhttp3.*;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

import static cmc.pad.resource.admin.api.controller.UrlConstant.HOST;

/**
 * Created by fuwei on 2021/12/10.
 */
@Slf4j
public class HttpUtil {
    private HttpUtil() {
    }

    public static String login() {
        return "";
//        Map<String, String> param = Maps.newHashMap();
//        param.put("loginName", "admin1");
//        param.put("password", "123456");
//        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSON.toJSONString(param));
//        Request request = new Request.Builder()
//                .url(DOMAIN + "portal/auth/login")
//                .post(requestBody)
//                .build();
//        String token = "";
//        try {
//            Response response = new OkHttpClient().newCall(request).execute();
//            JSONObject jsonObject = JSON.parseObject(response.body().string());
//            JSONObject dataJsonObj = JSON.parseObject(jsonObject.get("data").toString());
//            token = dataJsonObj.get("token").toString();
//            System.out.println(token);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        return token;
    }
    
    /**
     * 将查询字符串(xx=1&ss=2)转换为JSON格式
     * @param queryString 查询字符串
     * @return JSON字符串
     */
    public static String queryStringToJson(String queryString) {
        try {
            Map<String, Object> params = parseQueryStringAsObject(queryString);
            return JSON.toJSONString(params);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 解析查询字符串为Map，其中JSON字符串会被解析为对应的对象
     * @param queryString 查询字符串
     * @return 参数Map
     */
    public static Map<String, Object> parseQueryStringAsObject(String queryString) throws UnsupportedEncodingException {
        Map<String, Object> params = new HashMap<>();
        if (queryString == null || queryString.isEmpty()) {
            return params;
        }
        
        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                String key = URLDecoder.decode(pair.substring(0, idx), String.valueOf(StandardCharsets.UTF_8));
                String value = URLDecoder.decode(pair.substring(idx + 1), String.valueOf(StandardCharsets.UTF_8));
                
                // 尝试解析JSON数组或对象
                Object parsedValue = value;
                if (value.startsWith("[") && value.endsWith("]")) {
                    try {
                        parsedValue = JSON.parseArray(value);
                    } catch (Exception e) {
                        // 如果解析失败，保持原字符串值
                        parsedValue = value;
                    }
                } else if (value.startsWith("{") && value.endsWith("}")) {
                    try {
                        parsedValue = JSON.parseObject(value);
                    } catch (Exception e) {
                        // 如果解析失败，保持原字符串值
                        parsedValue = value;
                    }
                }
                
                params.put(key, parsedValue);
            } else if (idx == 0) {
                // 只有值没有键的情况，跳过
                continue;
            } else {
                // 没有等号的情况，将整个作为键，值为空字符串
                String key = URLDecoder.decode(pair, String.valueOf(StandardCharsets.UTF_8));
                params.put(key, "");
            }
        }
        return params;
    }

    /**
     * 解析查询字符串为Map
     * @param queryString 查询字符串
     * @return 参数Map
     */
    public static Map<String, String> parseQueryString(String queryString) throws UnsupportedEncodingException {
        Map<String, String> params = new HashMap<>();
        if (queryString == null || queryString.isEmpty()) {
            return params;
        }
        
        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            if (idx > 0) {
                String key = URLDecoder.decode(pair.substring(0, idx), String.valueOf(StandardCharsets.UTF_8));
                String value = URLDecoder.decode(pair.substring(idx + 1), String.valueOf(StandardCharsets.UTF_8));
                params.put(key, value);
            } else if (idx == 0) {
                // 只有值没有键的情况，跳过
                continue;
            } else {
                // 没有等号的情况，将整个作为键，值为空字符串
                String key = URLDecoder.decode(pair, String.valueOf(StandardCharsets.UTF_8));
                params.put(key, "");
            }
        }
        return params;
    }

    public static void get(String path) {
        String url = HOST + path;
        log.info(">>>get url:{}", url);
        Request request = new Request.Builder()
                .headers(Headers.of("Authorization", login()))
                .url(url)
                .get()
                .build();
        try {
            Response response = new OkHttpClient().newCall(request).execute();
            System.out.println(JSON.toJSONString(JSONObject.parse(response.body().string()), true));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void post(String path, Map<String, String> param) {
        String url = HOST + path;
        FormBody.Builder builder = new FormBody.Builder();
        param.forEach((k, v) -> builder.add(k, v));
        log.info(">>>post url:{}, param:{}", url, JSON.toJSONString(param));
        Request request = new Request.Builder()
                .headers(Headers.of("Authorization", login()))
                .url(url)
                .post(builder.build())
                .build();
        try {
            Response response = new OkHttpClient().newCall(request).execute();
            System.out.println(JSON.toJSONString(JSONObject.parse(response.body().string()), true));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    static ObjectMapper mapper = new ObjectMapper();

    public static void postJson(String path, Object jsonParam) {
        String json = null;
        try {
            json = mapper.writeValueAsString(jsonParam);
        } catch (JsonProcessingException e) {
            throw new FaultException(e);
        }
        String url = HOST + path;
        System.out.println(">>>post json url:" + url + ", param:" + json);
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json);
        Request request = new Request.Builder()
                .headers(Headers.of("Authorization", login()))
                .url(url)
                .post(requestBody)
                .build();
        try {
            Response response = new OkHttpClient().newBuilder().build().newCall(request).execute();
            System.out.println(JSON.toJSONString(JSONObject.parse(response.body().string()), true));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
