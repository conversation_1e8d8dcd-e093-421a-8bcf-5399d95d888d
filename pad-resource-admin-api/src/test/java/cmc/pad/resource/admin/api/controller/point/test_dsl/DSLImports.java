package cmc.pad.resource.admin.api.controller.point.test_dsl;

import cmc.pad.resource.admin.api.controller.point.test_dsl.PointLocation.PointLocationStatus;

/**
 * DSL静态导入类，提供便捷的静态方法访问
 */
public class DSLImports {
    
    // PointLocation构建器
    public static PointLocation PointLocation(Integer pointLocationId) {
        return PointLocation.PointLocation(pointLocationId);
    }
    
    // 点位状态常量
    public static final PointLocationStatus Occup = PointLocationStatus.Occup;
    public static final PointLocationStatus Add = PointLocationStatus.Add;
    public static final PointLocationStatus Destroy = PointLocationStatus.Destroy;
}