package cmc.pad.resource.admin.api.controller;

import org.junit.Test;

/**
 * 宣传点位租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/01
 * @Version 1.0
 */
public class AdvertisingPointLeasingControllerBasicTest extends BaseControllerTest {

    @Test
    public void testInvalidParams() throws Exception {
        // 空参数和无效参数测试
        testBothMethods("", 3006, "城市级别不存在");
        testBothMethods("city_level=1", 101, "city_level城市级别错误");
        testBothMethods("city_level=L1", 2006, "影城级别不存在");
        testBothMethods("cinema_level=a", 3006, "城市级别不存在");
    }

    @Test
    public void testValidCinemaCode() throws Exception {
        // 有效影城代码测试
        testBothMethods("cinema_code=849", 0, "OK");
        testBothMethods("cinema_code=304", 0, "OK", 
            "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":2.35,\"base_area\":1,\"extended_price\":2.33}]");
    }

    /**
     * 同时测试GET和POST方法
     */
    private void testBothMethods(String param, int expectedCode, String expectedMsg) throws Exception {
        testBothMethods(param, expectedCode, expectedMsg, null);
    }

    private void testBothMethods(String param, int expectedCode, String expectedMsg, String expectedData) throws Exception {
        if (expectedData == null) {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg);
        } else {
            assertResponseStatusAndMsg(getApi(param), expectedCode, expectedMsg, expectedData);
            assertResponseStatusAndMsg(postApi(param), expectedCode, expectedMsg, expectedData);
        }
    }

    @Override
    protected String getApi(String param) throws Exception {
        String path = "/advertising-point-leasing/price/query?" + param;
        return httpGet(path);
    }

    protected String postApi(String param) throws Exception {
        String path = "/advertising-point-leasing/price/query";
        return httpPost(path, param);
    }
}