package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.HttpUtil;
import cmc.pad.resource.admin.api.model.CancelOccupationParams;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.SneakyThrows;
import org.junit.Test;

import java.util.List;

/**
 * Created by fuwei on 2020/12/7.
 */
public class InventoryOccupationControllerTest {
    String LOCAL = "http://localhost:8125/";
    String QAS = "http://pad-resource-admin-api-qas-cmc.cmc.com/";
    String STG = "http://pad-resource-admin-api-stg-cmc.wandafilm.com:8081/";
    String HOST = QAS;

    @Test
    @SneakyThrows
    public void test() {
        String HOST = "http://localhost:8125/test/testLock";
        HttpUtil.get(HOST);
    }

    @Test
    @SneakyThrows
    public void testCancelOccupationByGet() {
//        HttpUtil.get(HOST + "inventory/occupation/cancel?contract_no=");
        SaveOccupationParams params = new SaveOccupationParams();
        params.setContractNo("test_contract_111");
        HttpUtil.postJson("inventory/occupation/cancel", params);
    }

    @Test
    public void testSaveOccupation() {
        SaveOccupationParams params = new SaveOccupationParams();
        params.setContractNo("test_contract_111");
        params.setBusinessType("GD");

        SaveOccupationParams.OccupationDetail detail1 = new SaveOccupationParams.OccupationDetail();
        detail1.setCinemaCode("001");
        detail1.setAmount(10F);
        detail1.setStartDate("2019-04-06");
        detail1.setEndDate("2019-04-06");

        params.setDetails(Lists.newArrayList(detail1));

        HttpUtil.postJson("inventory/occupation/save", params);
    }

    @Data
    public static class SaveOccupationParams {

        @JsonProperty("contract_no")
        private String contractNo;

        @JsonProperty("business_type")
        private String businessType;

        private List<OccupationDetail> details;

        @Data
        public static class OccupationDetail {

            @JsonProperty("cinema_code")
            private String cinemaCode;

            private Float amount;

            @JsonProperty("start_date")
            private String startDate;

            @JsonProperty("end_date")
            private String endDate;
        }
    }
}