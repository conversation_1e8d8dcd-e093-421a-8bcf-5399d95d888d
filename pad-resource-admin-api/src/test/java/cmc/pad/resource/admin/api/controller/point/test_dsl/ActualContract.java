package cmc.pad.resource.admin.api.controller.point.test_dsl;

import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.junit.Assert;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 实际合同断言对象
 */
@RequiredArgsConstructor
public class ActualContract {
    private final String contractNo;
    
    protected static Database db() {
        return DatabaseFactory.open("PadResource");
    }
    
    /**
     * 断言合同类型
     */
    public ActualContract typeIs(ContractType expectedType) {
        PointLocationOccupationContract contract = getContract();
        Assert.assertEquals("合同类型不匹配", expectedType.value(), contract.getContractType().value());
        return this;
    }
    
    /**
     * 断言合同状态
     */
    public ActualContract statusIs(ContractStatus expectedStatus) {
        PointLocationOccupationContract contract = getContract();
        Assert.assertEquals("合同状态不匹配", expectedStatus.value(), contract.getStatus().value());
        return this;
    }
    
    private PointLocationOccupationContract getContract() {
        return db()
                .select(PointLocationOccupationContract.class)
                .where(f("contract_no", contractNo))
                .limit(0, 1)
                .result()
                .one(PointLocationOccupationContract.class);
    }
}