package cmc.pad.resource.admin.api.controller.point;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.util.lang.EnumValueSupport;

import java.time.LocalDate;

/**
 * 点位合同明细
 */
@Getter
@Setter
@Builder
@ToString
public class PointLocationOccupationContractDetailTest {
    private String id;//明细唯一标识

    @JSONField(name = "cinema_inner_code")
    private String cinemaInnerCode;

    @JSONField(name = "point_location_id")
    private Integer pointLocationId;

    private Float amount;

    @JSONField(name = "start_date")
    private LocalDate startDate;

    @JSONField(name = "end_date")
    private LocalDate endDate;

    @JSONField(name = "status")
    private int alterStatus;//点位明细变更状态

    public enum TestAlterStatus implements EnumValueSupport {

        OCCUPY(1, "占用/变更"),
        ADD(2, "新增"),
        DESTROY(3, "作废");

        private final int value;
        private final String name;

        TestAlterStatus(int value, String name) {
            this.value = value;
            this.name = name;
        }

        @Override
        public int value() {
            return this.value;
        }
    }
}