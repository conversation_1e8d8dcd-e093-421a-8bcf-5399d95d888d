package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.controller.BaseControllerTest;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 点位库存控制器验收测试
 * 用于验证点位库存管理API的功能正确性
 *
 * <AUTHOR>
 * @Date 2025/09/17
 * @Version 1.0
 */
public class PointLocationInventoryControllerBasicParamCheckTest extends BaseControllerTest {

    @Test
    public void testOccupyInValidRequiredParams() throws Exception {
        String validOccupyParams = "contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]";
        testOccupyMethod(validOccupyParams, -1, "id:21064点位库存面积不足, 当前库存可使用面积:10.0");

        String changeContractParams = "contract_type=2&contract_no=TEST_CONTRACT_002&business_type_code=YX&details=[{\"cinema_inner_code\":\"304\",\"point_location_id\":20957,\"amount\":50.0,\"start_date\":\"2024-02-01\",\"end_date\":\"2024-11-30\",\"id\":\"DETAIL_002\",\"status\":2}]";
        testOccupyMethod(changeContractParams, -1, "该合同不是变更合同,不能执行此操作");

        // 测试不同状态的占用详情
        String mixedStatusParams = "contract_type=1&contract_no=TEST_CONTRACT_STATUS&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":16,\"amount\":70.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_S001\",\"status\":1},{\"cinema_inner_code\":\"111\",\"point_location_id\":199,\"amount\":80.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_S002\",\"status\":3}]";
        testOccupyMethod(mixedStatusParams, -1, "提交新合同明细id:DETAIL_S002占用状态错误");
    }

    @Test
    public void testIgnoreBusinessTypeCode() throws Exception {
        ignoreBusinessTypeCode().forEach(ignoreBusinessTypeCode -> {
            System.out.println(">>>测试ignoreBusinessTypeCode:" + ignoreBusinessTypeCode);
            PointLocationInfo pointLocationInfo = db().select(PointLocationInfo.class).where(f("business_type_code", ignoreBusinessTypeCode).add("cinema_inner_code is not null")).limit(0, 1).result().one(PointLocationInfo.class);
            if (pointLocationInfo == null) {
                System.out.println("无" + ignoreBusinessTypeCode + "业务类型编码");
                return;
            }
            System.out.println("p_code:" + pointLocationInfo.getCode() + " cinema:" + pointLocationInfo.getCinemaInnerCode() + " bTypeCode:" + pointLocationInfo.getBusinessTypeCode());
            String cinemaInnerCode = pointLocationInfo.getCinemaInnerCode();
            int id = pointLocationInfo.getId().intValue();
            String multipleDetailsParams = "contract_type=1&contract_no=TEST_CONTRACT_MULTI&business_type_code=" + ignoreBusinessTypeCode + "&details=[" +
                    "{\"cinema_inner_code\":\"" + cinemaInnerCode + "\",\"point_location_id\":" + id + ",\"amount\":30.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-06-30\",\"id\":\"DETAIL_M001\",\"status\":1}" +
                    ",{\"cinema_inner_code\":\"" + cinemaInnerCode + "\",\"point_location_id\":" + id + ",\"amount\":40.0,\"start_date\":\"2024-07-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_M002\",\"status\":2}" +
                    "]";
            try {
                testOccupyMethod(multipleDetailsParams, 0, "OK");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        Assert.assertNull(db()
                .select(PointLocationOccupationContract.class)
                .where(f("contract_no", "TEST_CONTRACT_MULTI"))
                .result()
                .one(PointLocationOccupationContract.class));
    }

    /**
     * && !businessType.equalsIgnoreCase("YX")
     * && !businessType.equalsIgnoreCase("WZ")
     * && !businessType.equalsIgnoreCase("GD")
     * && !businessType.equalsIgnoreCase("XC")
     * && !businessType.equalsIgnoreCase("QT")
     * && !businessType.equalsIgnoreCase("CMDX")
     * && !businessType.equalsIgnoreCase("WBZY")
     *
     * @return
     */
    private List<String> ignoreBusinessTypeCode() {
        return Lists.newArrayList("QT", "CMDX", "WBZY");
    }

    @Test
    public void testOccupyMissingRequiredParams() throws Exception {
        // 测试缺少contract_no参数
        testOccupyMethod("contract_type=1&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "contract_no不能为null");

        // 测试缺少business_type_code参数
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "business_type_code不能为null");

        // 测试缺少details参数
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD", 101, "details不能为null");

        // 测试空的details数组
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD", 101, "details不能为null");
    }

    @Test
    public void testOccupyDetailValidation() throws Exception {
        // 测试详情中缺少cinema_inner_code
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "details[0].cinema_inner_code不能为null");

        // 测试详情中缺少point_location_id
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "details[0].point_location_id不能为null");

        // 测试详情中缺少amount
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "details[0].amount不能为null");

        // 测试详情中缺少start_date
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "details[0].start_date不能为null");

        // 测试详情中缺少end_date
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "details[0].end_date不能为null");

        // 测试详情中缺少id
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"status\":1}]", 101, "details[0].id不能为null");

        // 测试详情中缺少status
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\"}]", 101, "details[0].status不能为null");
    }

    @Test
    public void testOccupyInvalidBusinessType() throws Exception {
        // 测试无效的业务类型代码
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=INVALID_TYPE&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "business_type_code不支持的业务类型");
        // 测试空的业务类型代码
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", -1, "点位id:[21064]业务类型和入参定义不一致");
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=YX&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", -1, "点位id:[21064]业务类型和入参定义不一致");
    }

    @Test
    public void testOccupyInvalidCinemaCode() throws Exception {
        // 测试无效的影城内码
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"INVALID_CINEMA\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 101, "details[0].cinema_inner_code影城编码错误");
    }

    @Test
    public void testOccupyDateValidation() throws Exception {
        // 测试无效的日期格式
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024/01/01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 301,
                "Could not read document: Text '2024/01/01' could not be parsed at index 4 (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"start_date\\\"]); nested exception is com.fasterxml.jackson.databind.JsonMappingException: Text '2024/01/01' could not be parsed at index 4 (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"start_date\\\"])");

        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024/12/31\",\"id\":\"DETAIL_001\",\"status\":1}]", 301,
                "Could not read document: Text '2024/12/31' could not be parsed at index 4 (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"end_date\\\"]); nested exception is com.fasterxml.jackson.databind.JsonMappingException: Text '2024/12/31' could not be parsed at index 4 (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"end_date\\\"])");

        // 测试无效的日期值
        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-13-01\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 301,
                "Could not read document: Text '2024-13-01' could not be parsed: Invalid value for MonthOfYear (valid values 1 - 12): 13 (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"start_date\\\"]); nested exception is com.fasterxml.jackson.databind.JsonMappingException: Text '2024-13-01' could not be parsed: Invalid value for MonthOfYear (valid values 1 - 12): 13 (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"start_date\\\"])");

        testOccupyMethod("contract_type=1&contract_no=TEST_CONTRACT_001&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-02-30\",\"end_date\":\"2024-12-31\",\"id\":\"DETAIL_001\",\"status\":1}]", 301,
                "Could not read document: Text '2024-02-30' could not be parsed: Invalid date 'FEBRUARY 30' (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"start_date\\\"]); nested exception is com.fasterxml.jackson.databind.JsonMappingException: Text '2024-02-30' could not be parsed: Invalid date 'FEBRUARY 30' (through reference chain: cmc.pad.resource.admin.api.model.point.InventoryOccupationContractParam[\\\"details\\\"]->java.util.ArrayList[0]->cmc.pad.resource.admin.api.model.point.OccupationDetail[\\\"start_date\\\"])");
    }

    @Test
    public void testContractNoNotExistSoUpdateStatusValidRequiredParams() throws Exception {
        // 测试扣减状态更新
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_STATUS_001&status=1", -1, "合同编号不存在,不能操作");
        // 测试取消状态更新
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_STATUS_002&status=2", -1, "合同编号不存在,不能操作");
        testUpdateStatusMethod("contract_no=TEST-CONTRACT_001@2024&status=1", -1, "合同编号不存在,不能操作");
        testUpdateStatusMethod("contract_no=合同编号_测试&status=2", -1, "合同编号不存在,不能操作");
    }

    @Test
    public void testUpdateStatusMissingRequiredParams() throws Exception {
        // 测试缺少contract_no参数
        testUpdateStatusMethod("status=1", 101, "contract_no不能为null");

        // 测试缺少status参数
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_001", 101, "status不能为null");

        // 测试空的contract_no
        testUpdateStatusMethod("contract_no=&status=1", -1, "合同编号不存在,不能操作");
    }

    @Test
    public void testUpdateStatusInvalidStatus() throws Exception {
        // 测试无效的状态值（只支持1和2）
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_001&status=0", -1, "status参数错误");
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_001&status=3", -1, "status参数错误");
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_001&status=-1", -1, "status参数错误");
        testUpdateStatusMethod("contract_no=TEST_CONTRACT_001&status=999", -1, "status参数错误");
    }

    @Test
    public void testOccupyStatusValues() throws Exception {
        // 测试无效状态值
        testOccupyMethod("contract_type=1&contract_no=TEST_STATUS_INVALID&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"STATUS_INVALID\",\"status\":0}]",
                -1, "提交新合同明细id:STATUS_INVALID占用状态错误");
        testOccupyMethod("contract_type=1&contract_no=TEST_STATUS_INVALID2&business_type_code=GD&details=[{\"cinema_inner_code\":\"111\",\"point_location_id\":21064,\"amount\":100.0,\"start_date\":\"2024-01-01\",\"end_date\":\"2024-12-31\",\"id\":\"STATUS_INVALID2\",\"status\":4}]",
                -1, "提交新合同明细id:STATUS_INVALID2占用状态错误");
    }

    /**
     * 测试占用接口POST方法
     */
    private void testOccupyMethod(String param, int expectedCode, String expectedMsg, String... data) throws Exception {
        assertResponseStatusAndMsg(postOccupyApi(param), expectedCode, expectedMsg, data);
    }

    private void testOccupyMethodByJson(String param, int expectedCode, String expectedMsg, String... data) throws Exception {
        assertResponseStatusAndMsg(postOccupyJsonApi(param), expectedCode, expectedMsg, data);
    }

    private void testOccupyMethodByJson(String param, int expectedCode, String expectedMsg, boolean data) throws Exception {
        assertResponseStatusAndMsgV2(postOccupyJsonApi(param), expectedCode, expectedMsg, data);
    }

    /**
     * 测试状态更新接口POST方法
     */
    private void testUpdateStatusMethod(String param, int expectedCode, String expectedMsg, String... data) throws Exception {
        assertResponseStatusAndMsg(postUpdateStatusApi(param), expectedCode, expectedMsg, data);
    }

    private void testUpdateStatusMethod(String param, int expectedCode, String expectedMsg, boolean result) throws Exception {
        assertResponseStatusAndMsgV2(postUpdateStatusApi(param), expectedCode, expectedMsg, result);
    }

    @Override
    protected String getApi(String param) throws Exception {
        // 点位库存API只支持POST方法
        throw new UnsupportedOperationException("点位库存API不支持GET方法，请使用POST方法");
    }

    protected String postApi(String param) throws Exception {
        String path = "/point-location/query";
        return httpPost(path, param);
    }

    /**
     * 调用占用接口
     */
    protected String postOccupyApi(String param) throws Exception {
        String path = "/point-location/inventory/occupy";
        return httpPost(path, param);
    }

    protected String postOccupyJsonApi(String body) throws Exception {
        String path = "/point-location/inventory/occupy";
        return postJson(path, body);
    }

    /**
     * 调用状态更新接口
     */
    protected String postUpdateStatusApi(String param) throws Exception {
        String path = "/point-location/inventory/updateStatus";
        return httpPost(path, param);
    }
}