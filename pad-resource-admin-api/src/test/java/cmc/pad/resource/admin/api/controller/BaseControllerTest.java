package cmc.pad.resource.admin.api.controller;

import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

import static cmc.pad.resource.admin.api.HttpUtil.queryStringToJson;

/**
 * 控制器基础测试类
 * 提供通用的HTTP请求和响应断言方法
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
public abstract class BaseControllerTest {

//    protected static final String BASE_URL = "http://pad-resource-admin-api-qas-cmc.cmc.com";
    protected static final String BASE_URL = "http://localhost:8125";

    protected static Database db() {
        return DatabaseFactory.open("PadResource");
    }

    /**
     * 发送HTTP GET请求
     *
     * @param path 请求路径
     * @return 响应内容
     * @throws Exception 请求异常
     */
    protected String httpGet(String path) throws Exception {
        URL url = new URL(BASE_URL + path);
        System.out.println("GET " + url);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);

        int responseCode = conn.getResponseCode();
        BufferedReader reader;

        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        conn.disconnect();

        return response.toString();
    }

    /**
     * 发送HTTP POST请求
     *
     * @param path 请求路径
     * @param body 请求体
     * @return 响应内容
     * @throws Exception 请求异常
     */
    public static String httpPost(String path, String body) throws Exception {
        body = queryStringToJson(body);
        return postJson(path, body);
    }

    @NotNull
    public static String postJson(String path, String body) throws IOException {
        URL url = new URL(BASE_URL + path);
        System.out.println("POST " + url + ", body: " + body);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);
        conn.setDoOutput(true);

        if (body != null && !body.isEmpty()) {
            conn.getOutputStream().write(body.getBytes("UTF-8"));
        }

        int responseCode = conn.getResponseCode();
        BufferedReader reader;

        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        } else {
            System.err.println(">>> error: vpn global");
            reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        conn.disconnect();

        return response.toString();
    }

    /**
     * 断言响应状态码和消息
     *
     * @param response 响应内容
     * @param status   期望的状态码
     * @param msg      期望的消息
     * @param data     期望的数据（可选）
     */
    protected void assertResponseStatusAndMsg(String response, int status, String msg, String... data) {
        System.out.println(response);
        Assert.assertTrue("status:" + status + " msg: " + (msg == null ? "null" : msg) + ", but response: " + response,
                response.contains("\"status\":" + status));
        if (msg != null)
            Assert.assertTrue("msg: " + msg + ", but response: " + response,
                    response.contains("\"msg\":\"" + msg + "\""));
        if (data != null && data.length != 0) {
            Assert.assertTrue("data: " + data[0] + ", but response: " + response,
                    response.contains("\"data\":" + data[0]));
        }
    }

    protected void assertResponseStatusAndMsgV2(String response, int status, String msg, Boolean data) {
        System.out.println(response);
        Assert.assertTrue("status:" + status + " msg: " + (msg == null ? "null" : msg) + ", but response: " + response,
                response.contains("\"status\":" + status));
        if (msg != null)
            Assert.assertTrue("msg: " + msg + ", but response: " + response,
                    response.contains("\"msg\":\"" + msg + "\""));
        if (data != null)
            Assert.assertTrue("data: " + data + ", but response: " + response,
                    response.contains("\"data\":" + data));
    }

    /**
     * 测试参数cinema_code为空但city_level和cinema_level不为空的情况
     * 子类需要实现具体的API调用
     */
    protected abstract String getApi(String param) throws Exception;
}