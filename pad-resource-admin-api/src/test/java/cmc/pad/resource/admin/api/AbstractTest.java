package cmc.pad.resource.admin.api;

import mtime.lark.util.test.TestBase;
import mtime.lark.util.test.spring.SpringJUnit;
import org.junit.BeforeClass;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

/**
 *  fuwei on 2017/8/23.
 */
public abstract class AbstractTest extends TestBase {

    @BeforeClass
    public static void init() {
        SpringJUnit.boot(Dummy.class, AdminApiBootstrap.class);

    }

    /**
     * HACK: LOOP ENDLESS IF ANNOTATE ON AbstractTest DIRECTLY
     */
    @SpringBootApplication(exclude = {MongoAutoConfiguration.class,
            MongoDataAutoConfiguration.class})
    public static class Dummy {
    }
}

