package cmc.pad.resource.admin.api.controller.point.test_dsl.contract_state;

import cmc.pad.resource.admin.api.controller.point.PointLocationOccupationContractDetailTest;
import cmc.pad.resource.admin.api.controller.point.PointLocationOccupationContractTest;
import cmc.pad.resource.admin.api.controller.point.test_dsl.ActualContract;
import cmc.pad.resource.admin.api.controller.point.test_dsl.ActualContractDetail;
import cmc.pad.resource.admin.api.controller.point.test_dsl.ActualInventory;
import cmc.pad.resource.admin.api.controller.point.test_dsl.PointLocation;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import com.alibaba.fastjson.JSON;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.junit.Assert;

import java.util.Arrays;

import static cmc.pad.resource.admin.api.controller.BaseControllerTest.httpPost;
import static cmc.pad.resource.admin.api.controller.BaseControllerTest.postJson;
import static java.util.concurrent.TimeUnit.SECONDS;
import static mtime.lark.db.jsd.Shortcut.f;
import static org.awaitility.Awaitility.await;

public abstract class ContractState {
    public static PointLocationOccupationContractTest testContract;
    public final static String contractNo = "contract_001";
    private String apiResponse;

    /**
     * 构造方法，用于构建测试数据
     */
    public ContractState(PointLocation... pointLocations) {

    }

    protected static Database db() {
        return DatabaseFactory.open("PadResource");
    }

    public ContractState submit(PointLocation... pointLocations) {
        // 如果提供了新的点位数据，更新合同明细
        if (pointLocations.length > 0) {
            testContract = new PointLocationOccupationContractTest(
                    ContractType.valueOf(testContract.getContractType()),
                    testContract.getContractNo(),
                    testContract.getBusinessTypeCode(),
                    Arrays.stream(pointLocations)
                            .map(PointLocation::toContractDetail)
                            .toArray(PointLocationOccupationContractDetailTest[]::new)
            );
        }
        try {
            System.out.println("提交contract:" + contractNo);
            apiResponse = postOccupyJsonApi(JSON.toJSONString(testContract));
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            throw new RuntimeException(e);
        } finally {
            awaitInventoryDone();
        }
        return this;
    }

    private void awaitInventoryDone() {
//        System.out.println("awaitInventoryDone contract await ...:" + contractNo);
        await().atMost(10, SECONDS).until(() -> {
            ProcessStatus processStatus = db().select(PointLocationOccupationContract.class).where(f("contract_no", this.contractNo)).result().one(PointLocationOccupationContract.class).getProcessStatus();
//            System.out.println("awaitInventoryDone contract await ...:processStatus:" + processStatus);
            return processStatus == ProcessStatus.SUCESS;
        });
//        System.out.println("awaitInventoryDone contract awaited " + contractNo);
    }

    public ContractState approval() {
        try {
            System.out.println("审批contract:" + contractNo);
            apiResponse = postUpdateStatusApi("contract_no=" + contractNo + "&status=1");
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            throw new RuntimeException(e);
        } finally {
            awaitInventoryDone();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return this;
    }

    public ContractState cancel() {
        try {
            System.out.println("撤销contract:" + contractNo);
            apiResponse = postUpdateStatusApi("contract_no=" + contractNo + "&status=2");
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            throw new RuntimeException(e);
        } finally {
            awaitInventoryDone();
        }
        return this;
    }

    public ContractState then(TriConsumer<ActualContract, ActualContractDetail, ActualInventory> asserter) {
        System.out.println("then contract:" + contractNo);
        assertResponseStatusAndMsgV2(apiResponse, 0, "OK", true);
        ActualContract actualContract = new ActualContract(contractNo);
        ActualContractDetail actualContractDetail = new ActualContractDetail(contractNo);
        ActualInventory actualInventory = new ActualInventory(contractNo);
        asserter.accept(actualContract, actualContractDetail, actualInventory);
        return this;
    }

    /**
     * DSL风格的断言方法
     */
    public ContractState then(QuadConsumer<String, ActualContract, ActualContractDetail, ActualInventory> asserter) {
        ActualContract actualContract = new ActualContract(contractNo);
        ActualContractDetail actualContractDetail = new ActualContractDetail(contractNo);
        ActualInventory actualInventory = new ActualInventory(contractNo);
        asserter.accept(apiResponse, actualContract, actualContractDetail, actualInventory);
        return this;
    }

    /**
     * 三参数Consumer接口
     */
    @FunctionalInterface
    public interface TriConsumer<T, U, V> {
        void accept(T t, U u, V v);
    }

    /**
     * 四参数Consumer接口
     */
    @FunctionalInterface
    public interface QuadConsumer<T, U, V, W> {
        void accept(T t, U u, V v, W w);
    }


    private void testOccupyMethodByJson(String param, int expectedCode, String expectedMsg, Boolean data) throws Exception {
        assertResponseStatusAndMsgV2(postOccupyJsonApi(param), expectedCode, expectedMsg, data);
    }

    protected String postOccupyJsonApi(String body) throws Exception {
        String path = "/point-location/inventory/occupy";
        return postJson(path, body);
    }

    private void testUpdateStatusMethod(String param, int expectedCode, String expectedMsg, boolean result) throws Exception {
        assertResponseStatusAndMsgV2(postUpdateStatusApi(param), expectedCode, expectedMsg, result);
    }

    protected String postUpdateStatusApi(String param) throws Exception {
        String path = "/point-location/inventory/updateStatus";
        return httpPost(path, param);
    }


    private void assertResponseStatusAndMsgV2(String response, int status, String msg, Boolean data) {
        System.out.println(response);
        Assert.assertTrue("status:" + status + " msg: " + (msg == null ? "null" : msg) + ", but response: " + response,
                response.contains("\"status\":" + status));
        if (msg != null)
            Assert.assertTrue("msg: " + msg + ", but response: " + response,
                    response.contains("\"msg\":\"" + msg + "\""));
        if (data != null)
            Assert.assertTrue("data: " + data + ", but response: " + response,
                    response.contains("\"data\":" + data));
    }
}