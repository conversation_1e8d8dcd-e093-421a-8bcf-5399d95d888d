package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.controller.*;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

@RunWith(Suite.class)
@Suite.SuiteClasses({
        ModifyContractOccupyDestroyTest.class,
        NewContractOccupyTest.class,
        PointLocationInventoryControllerBasicTest.class,
        PointLocationInventoryControllerBasicParamCheckTest.class
})
public class PointLocationTestSuite {
    // 测试套件类，无需实现任何方法
    // 通过注解配置要运行的测试类
}