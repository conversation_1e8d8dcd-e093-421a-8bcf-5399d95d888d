package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.HttpUtil;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import org.junit.Test;

import java.util.Map;

/**
 * Created by fuwei on 2022/1/28.
 */
public class ResourceControllerTest {
    @Test
    @SneakyThrows
    public void query() {
        Map<String, String> param = Maps.newHashMap();
        param.put("cinema_code", "849");
        HttpUtil.postJson("resource/query", param);
    }
}
