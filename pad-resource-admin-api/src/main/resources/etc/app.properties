server.port=8125
service.name=cmc.pad.resource.admin.api
service.debug=true
app.global.env=cmc
app.auth.enabled=false

log.date.split=true
#æ§å¶å°æ¥å¿éç½®
console.levelMin=DEBUG
console.levelMax=INFO

#traceréç½®
tracer.enabled=true
tracer.payload.enabled=true
#lark.rest.error.bad_request.code=10000
#lark.rest.error.bad_request.description=nothing wrong
# mysqlæ°æ®åºéç½®
mx.mysql.port=3306
mx.mysql.host=**************
mx.mysql.db=cmc_pad_resource
mx.mysql.pass.wr=mxuser123456
mx.mysql.user.wr=mxuser

#redisèç¹éç½®
cmc.redis.host.node1=**************
cmc.redis.port.node1=26309
cmc.redis.host.node2=**************
cmc.redis.port.node2=26309
cmc.redis.host.node3=**************
cmc.redis.port.node3=26309
cmc.redis.master.name=cmc-6309,cmc-6319
cmc.redis.password=9bA+7KctA2628w=Tb