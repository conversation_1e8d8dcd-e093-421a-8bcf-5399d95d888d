# é»è®¤éç½®
app.platform=cmc
lark.global.profile=cmc-qas
app.auth.enabled=true
#
# çå¬ç«¯å£
server.port=8125
service.name=cmc.pad.resource.admin.api
#globaléç½®æä»¶æå®
cmc.global.env=cmc
#
# Spring ç»æ
#
# èªå®ä¹éç½®å±æ§ å¼å§
#
cms.web.debug=true
# å¯ç¨æå¡åç°
cmc.rpc.discovery.enabled=true
# åå°éæèµæºå°å
cmc.admin.static.url=http://static.qas.cmc.com
#ç»ä¸è®¤è¯ç»å½å°å
cmc.passport.url=http://passport-qas.mx.com
#portalå°å
cmc.admin.portal.url=http://portal.qas.cmc.com

