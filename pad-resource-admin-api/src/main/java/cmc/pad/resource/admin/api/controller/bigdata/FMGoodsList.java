
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 卖品
 */

@Getter
@Setter
public class FMGoodsList {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    //商品名称
    @DSFiled(order = 5)
    private String goodsName;
        
    //品项名称
    @DSFiled(order = 6)
    private String goodsItemName;
        
    //规格型号
    @DSFiled(order = 7)
    private String specsModel;
        
    //数量
    @DSFiled(order = 8)
    private String quantity;
        
    //单价
    @DSFiled(order = 9)
    private String unitPrice;
        
    //总价
    @DSFiled(order = 10)
    private String totalPrice;
        
    //在途开票金额
    @DSFiled(order = 11)
    private String invoicingAmount;
        
    //已开票金额
    @DSFiled(order = 12)
    private String invoicedAmount;
        
    //已认领合同金额
    @DSFiled(order = 13)
    private String claimedAmount;
        
    //已提成基数
    @DSFiled(order = 14)
    private String commissionBase;
        

}
    