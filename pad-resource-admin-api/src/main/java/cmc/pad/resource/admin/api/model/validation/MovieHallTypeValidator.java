package cmc.pad.resource.admin.api.model.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @Date 2019/4/1 15:06
 * @Version 1.0
 */
class MovieHallTypeValidator implements ConstraintValidator<MovieHallType, String> {
//    private static final Pattern numberPattern = Pattern.compile("^[a-zA-Z]+$");

    @Override
    public void initialize(MovieHallType constraintAnnotation) {

    }

    @Override
    public boolean isValid(String movieHallType, ConstraintValidatorContext constraintValidatorContext) {
        return true;
    }
}
