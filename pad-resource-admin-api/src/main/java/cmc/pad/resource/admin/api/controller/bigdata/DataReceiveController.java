package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.common.DataType;
import cmc.tohdfs.sdk.dto.SendDataDto;
import cmc.tohdfs.sdk.manager.SendDataManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.log.Logger;
import mtime.lark.util.log.LoggerManager;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName DataReceiveController
 * @Auth kevin.bao
 * @Description 数据接收
 * @Date 2022/2/22
 * 废弃
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping("bigdata")
public class DataReceiveController {
    private static final Logger logger = LoggerManager.getLogger(DataReceiveController.class);

//    @Resource
    private SendDataManager sendDataManager;

    /**
     * @return cmc.pad.resource.admin.api.controller.bigdata.ResultInfo
     * @Description 接收数据
     * @Date 2022/2/23
     * @Param [jsonData]
     **/
    @ResponseBody
    @RequestMapping(name = "sendData", method = RequestMethod.POST)
    public ResultInfo sendData(String jsonData) {
        logger.info("sendData-request:{}", jsonData);
        JSONObject jsonObject = JSON.parseObject(jsonData);
        ResultInfo resultInfo = this.dataParse(jsonObject);
        logger.info("sendData-response:{}", JSON.toJSONString(resultInfo));
        return resultInfo;
    }

    private ResultInfo dataParse(JSONObject jsonObject) {
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setCode(0);
        resultInfo.setMsg("推送成功");

        try {
            List<Integer> list = new ArrayList<>();
            list.add(this.dealContract(jsonObject));
            JSONObject businessCategory = jsonObject.getJSONObject("businessCategory");
            list.add(this.dealCardList(businessCategory));
            list.add(this.dealCardRechargeList(businessCategory));
            list.add(this.dealFixedPointLease(businessCategory));
            list.add(this.dealGoodsList(businessCategory));
            list.add(this.dealHallSeatsLease(businessCategory));
            list.add(this.dealMarketingPointLease(businessCategory));
            list.add(this.dealMediaPointLease(businessCategory));
            list.add(this.dealNamingHallLease(businessCategory));
            list.add(this.dealOtherPointLease(businessCategory));
            list.add(this.dealOthers(businessCategory));
            list.add(this.dealOuterResourceLease(businessCategory));
            list.add(this.dealPropagandaPointLease(businessCategory));
            list.add(this.dealRentAreaLease(businessCategory));
            list.add(this.dealVoucherList(businessCategory));
            list.add(this.dealWholeBookList(businessCategory));
            list.add(this.dealCollectionPlan(jsonObject));
            long count = list.stream().filter(item -> item != 0).count();
            if (count > 0) {
                resultInfo.setCode(1);
                resultInfo.setMsg("推送失败,推送内部错误");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultInfo.setCode(1);
            resultInfo.setMsg("推送失败,推送异常");
        }

        return resultInfo;
    }

    /**
     * 财管合同
     */
    private int dealContract(JSONObject jsonObject) throws Exception {

        FMContract fmContract = new FMContract();
        fmContract.setContractType(jsonObject.getString("contractType"));
        if (StringUtils.isEmpty(fmContract.getContractType())) {
            return 1;
        }
        int contractType = Integer.parseInt(fmContract.getContractType());
        if (contractType <= 3) {
            fmContract.setIsContract("否");
        } else {
            fmContract.setIsContract("有");
        }
        if (contractType == 1 || contractType == 5) {
            fmContract.setCategory("影城卡券");
        } else if (contractType == 3 || contractType == 4) {
            fmContract.setCategory("多种经营");
        } else if (contractType == 2 || contractType == 6) {
            fmContract.setCategory("其他收入");
        } else {
            return 1;
        }

        fmContract.setBillNo(jsonObject.getString("billNo"));
        fmContract.setOperatorId(jsonObject.getString("operatorId"));
        fmContract.setOperatorName(jsonObject.getString("operatorName"));
        fmContract.setOperatorDate(jsonObject.getString("operatorDate"));
        fmContract.setBillState(jsonObject.getString("billState"));
        fmContract.setHandleDept(jsonObject.getString("handleDept"));
        fmContract.setContractNo(jsonObject.getString("contractNo"));
        fmContract.setContractName(jsonObject.getString("contractName"));
        fmContract.setContractState(jsonObject.getString("contractState"));
        fmContract.setAreaCode(jsonObject.getString("areaCode"));
        fmContract.setAreaName(jsonObject.getString("areaName"));
        fmContract.setContractSubject(jsonObject.getString("contractSubject"));
        fmContract.setCustomerId(jsonObject.getString("customerId"));
        fmContract.setCustomerName(jsonObject.getString("customerName"));
        fmContract.setLinkName(jsonObject.getString("linkName"));
        fmContract.setLinkTel(jsonObject.getString("linkTel"));
        fmContract.setEmail(jsonObject.getString("email"));
        fmContract.setIsWithinGroup(jsonObject.getString("isWithinGroup"));
        fmContract.setTradePorperties(jsonObject.getString("tradePorperties"));
        fmContract.setContractStartDate(jsonObject.getString("contractStartDate"));
        fmContract.setContractEndDate(jsonObject.getString("contractEndDate"));
        fmContract.setContractSignDate(jsonObject.getString("contractSignDate"));
        fmContract.setIsMediaItem(jsonObject.getString("isMediaItem"));
        fmContract.setSettleStyle(jsonObject.getString("settleStyle"));
        fmContract.setTotalAmount(jsonObject.getString("totalAmount"));
        fmContract.setExpect(jsonObject.getString("expect"));
        fmContract.setAccruedAmount(jsonObject.getString("accruedAmount"));
        fmContract.setClaimedAmount(jsonObject.getString("claimedAmount"));
        fmContract.setCommissionBase(jsonObject.getString("commissionBase"));
        fmContract.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
        fmContract.setInvoicingAmount(jsonObject.getString("invoicingAmount"));
        fmContract.setBondAmount(jsonObject.getString("bondAmount"));
        fmContract.setClaimedBondAmount(jsonObject.getString("claimedBondAmount"));

        SendDataDto.SendDataRequest<FMContract> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT);
        request.setData(Collections.singletonList(fmContract));
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();
    }


    /**
     * 售卡
     */
    private int dealCardList(JSONObject businessCategory) throws Exception {

        List<FMCardList> list = new ArrayList<>();
        JSONArray cardList = businessCategory.getJSONArray("cardList");
        cardList.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMCardList fmCardList = new FMCardList();
            fmCardList.setBillNo(jsonObject.getString("billNo"));
            fmCardList.setCinemaName(jsonObject.getString("cinemaName"));
            fmCardList.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmCardList.setBusinessItem(jsonObject.getString("businessItem"));
            fmCardList.setCardTypeName(jsonObject.getString("cardTypeName"));
            fmCardList.setCardTypeCode(jsonObject.getString("cardTypeCode"));
            fmCardList.setCardTypeLevel(jsonObject.getString("cardTypeLevel"));
            fmCardList.setCardCategoryCode(jsonObject.getString("cardCategoryCode"));
            fmCardList.setCardForm(jsonObject.getString("cardForm"));
            fmCardList.setRechargeAmount(jsonObject.getString("rechargeAmount"));
            fmCardList.setCostAmount(jsonObject.getString("costAmount"));
            fmCardList.setCardCount(jsonObject.getString("cardCount"));
            fmCardList.setTotalAmount(jsonObject.getString("totalAmount"));
            list.add(fmCardList);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMCardList> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_CARD);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 储值卡续充
     */
    private int dealCardRechargeList(JSONObject businessCategory) throws Exception {

        List<FMCardRechargeList> list = new ArrayList<>();
        JSONArray cardRechargeList = businessCategory.getJSONArray("cardRechargeList");
        cardRechargeList.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMCardRechargeList fmCardRechargeList = new FMCardRechargeList();
            fmCardRechargeList.setBillNo(jsonObject.getString("billNo"));
            fmCardRechargeList.setCinemaName(jsonObject.getString("cinemaName"));
            fmCardRechargeList.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmCardRechargeList.setBusinessItem(jsonObject.getString("businessItem"));
            fmCardRechargeList.setRechargeAmount(jsonObject.getString("rechargeAmount"));
            fmCardRechargeList.setQuantity(jsonObject.getString("quantity"));
            fmCardRechargeList.setTotalAmount(jsonObject.getString("totalAmount"));
            fmCardRechargeList.setInvoicingAmount(jsonObject.getString("invoicingAmount"));
            fmCardRechargeList.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmCardRechargeList.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmCardRechargeList.setCommissionBase(jsonObject.getString("commissionBase"));
            list.add(fmCardRechargeList);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMCardRechargeList> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_CARDRECHARGE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 收款计划
     */
    private int dealCollectionPlan(JSONObject businessCategory) throws Exception {

        List<FMCollectionPlan> list = new ArrayList<>();
        JSONArray collectionPlan = businessCategory.getJSONArray("collectionPlan");
        collectionPlan.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMCollectionPlan fmCollectionPlan = new FMCollectionPlan();
            fmCollectionPlan.setBillNo(jsonObject.getString("billNo"));
            fmCollectionPlan.setCondition(jsonObject.getString("condition"));
            fmCollectionPlan.setType(jsonObject.getString("type"));
            fmCollectionPlan.setAmount(jsonObject.getString("amount"));
            fmCollectionPlan.setProportion(jsonObject.getString("proportion"));
            list.add(fmCollectionPlan);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMCollectionPlan> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_COLLECTIONPLAN);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 固定点位租赁
     */
    private int dealFixedPointLease(JSONObject businessCategory) throws Exception {

        List<FMFixedPointLease> list = new ArrayList<>();
        JSONArray fixedPointLease = businessCategory.getJSONArray("fixedPointLease");
        fixedPointLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMFixedPointLease fmFixedPointLease = new FMFixedPointLease();
            fmFixedPointLease.setBillNo(jsonObject.getString("billNo"));
            fmFixedPointLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmFixedPointLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmFixedPointLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmFixedPointLease.setBasePublishPriceMeterMonth(jsonObject.getString("basePublishPriceMeterMonth"));
            fmFixedPointLease.setDiscountFactorArea(jsonObject.getString("discountFactorArea"));
            fmFixedPointLease.setDiscountFactorDuration(jsonObject.getString("discountFactorDuration"));
            fmFixedPointLease.setPublishTotalPrice(jsonObject.getString("publishTotalPrice"));
            fmFixedPointLease.setLeaseArea(jsonObject.getString("leaseArea"));
            fmFixedPointLease.setSaleDurationMonth(jsonObject.getString("saleDurationMonth"));
            fmFixedPointLease.setStartDate(jsonObject.getString("startDate"));
            fmFixedPointLease.setEndDate(jsonObject.getString("endDate"));
            fmFixedPointLease.setCollectMode(jsonObject.getString("collectMode"));
            fmFixedPointLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmFixedPointLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmFixedPointLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmFixedPointLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmFixedPointLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmFixedPointLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmFixedPointLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmFixedPointLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmFixedPointLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmFixedPointLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmFixedPointLease.setResourceType(jsonObject.getString("resourceType"));
            fmFixedPointLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmFixedPointLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmFixedPointLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMFixedPointLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_FIXEDPOINTLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 卖品
     */
    private int dealGoodsList(JSONObject businessCategory) throws Exception {

        List<FMGoodsList> list = new ArrayList<>();
        JSONArray goodsList = businessCategory.getJSONArray("goodsList");
        goodsList.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMGoodsList fmGoodsList = new FMGoodsList();
            fmGoodsList.setBillNo(jsonObject.getString("billNo"));
            fmGoodsList.setCinemaName(jsonObject.getString("cinemaName"));
            fmGoodsList.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmGoodsList.setBusinessItem(jsonObject.getString("businessItem"));
            fmGoodsList.setGoodsName(jsonObject.getString("goodsName"));
            fmGoodsList.setGoodsItemName(jsonObject.getString("goodsItemName"));
            fmGoodsList.setSpecsModel(jsonObject.getString("specsModel"));
            fmGoodsList.setQuantity(jsonObject.getString("quantity"));
            fmGoodsList.setUnitPrice(jsonObject.getString("unitPrice"));
            fmGoodsList.setTotalPrice(jsonObject.getString("totalPrice"));
            fmGoodsList.setInvoicingAmount(jsonObject.getString("invoicingAmount"));
            fmGoodsList.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmGoodsList.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmGoodsList.setCommissionBase(jsonObject.getString("commissionBase"));
            list.add(fmGoodsList);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMGoodsList> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_GOODS);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 影厅座位租赁
     */
    private int dealHallSeatsLease(JSONObject businessCategory) throws Exception {

        List<FMHallSeatsLease> list = new ArrayList<>();
        JSONArray hallSeatsLease = businessCategory.getJSONArray("hallSeatsLease");
        hallSeatsLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMHallSeatsLease fmHallSeatsLease = new FMHallSeatsLease();
            fmHallSeatsLease.setBillNo(jsonObject.getString("billNo"));
            fmHallSeatsLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmHallSeatsLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmHallSeatsLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmHallSeatsLease.setHallName(jsonObject.getString("hallName"));
            fmHallSeatsLease.setHallId(jsonObject.getString("hallId"));
            fmHallSeatsLease.setBasePublishPricePcs(jsonObject.getString("basePublishPricePcs"));
            fmHallSeatsLease.setBaseDuration(jsonObject.getString("baseDuration"));
            fmHallSeatsLease.setRenewalPriceHourPcs(jsonObject.getString("renewalPriceHourPcs"));
            fmHallSeatsLease.setDiscountFactorDuration(jsonObject.getString("discountFactorDuration"));
            fmHallSeatsLease.setPublishTotalPrice(jsonObject.getString("publishTotalPrice"));
            fmHallSeatsLease.setSeatCount(jsonObject.getString("seatCount"));
            fmHallSeatsLease.setSaleSeatCount(jsonObject.getString("saleSeatCount"));
            fmHallSeatsLease.setSaleDurationHour(jsonObject.getString("saleDurationHour"));
            fmHallSeatsLease.setStartDate(jsonObject.getString("startDate"));
            fmHallSeatsLease.setEndDate(jsonObject.getString("endDate"));
            fmHallSeatsLease.setCollectMode(jsonObject.getString("collectMode"));
            fmHallSeatsLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmHallSeatsLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmHallSeatsLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmHallSeatsLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmHallSeatsLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmHallSeatsLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmHallSeatsLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmHallSeatsLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmHallSeatsLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmHallSeatsLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmHallSeatsLease.setResourceType(jsonObject.getString("resourceType"));
            fmHallSeatsLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            list.add(fmHallSeatsLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMHallSeatsLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_HALLSEATSLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 营销点位租赁
     */
    private int dealMarketingPointLease(JSONObject businessCategory) throws Exception {

        List<FMMarketingPointLease> list = new ArrayList<>();
        JSONArray marketingPointLease = businessCategory.getJSONArray("marketingPointLease");
        marketingPointLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMMarketingPointLease fmMarketingPointLease = new FMMarketingPointLease();
            fmMarketingPointLease.setBillNo(jsonObject.getString("billNo"));
            fmMarketingPointLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmMarketingPointLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmMarketingPointLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmMarketingPointLease.setBasePublishPriceMeterDay(jsonObject.getString("basePublishPriceMeterDay"));
            fmMarketingPointLease.setDiscountFactorArea(jsonObject.getString("discountFactorArea"));
            fmMarketingPointLease.setDiscountFactorDuration(jsonObject.getString("discountFactorDuration"));
            fmMarketingPointLease.setPublishTotalPrice(jsonObject.getString("publishTotalPrice"));
            fmMarketingPointLease.setLeaseCategory(jsonObject.getString("leaseCategory"));
            fmMarketingPointLease.setLeaseArea(jsonObject.getString("leaseArea"));
            fmMarketingPointLease.setSaleDurationDay(jsonObject.getString("saleDurationDay"));
            fmMarketingPointLease.setStartDate(jsonObject.getString("startDate"));
            fmMarketingPointLease.setEndDate(jsonObject.getString("endDate"));
            fmMarketingPointLease.setCollectMode(jsonObject.getString("collectMode"));
            fmMarketingPointLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmMarketingPointLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmMarketingPointLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmMarketingPointLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmMarketingPointLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmMarketingPointLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmMarketingPointLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmMarketingPointLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmMarketingPointLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmMarketingPointLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmMarketingPointLease.setResourceType(jsonObject.getString("resourceType"));
            fmMarketingPointLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmMarketingPointLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmMarketingPointLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMMarketingPointLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_MARKETINGPOINTLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 传媒点位租赁
     */
    private int dealMediaPointLease(JSONObject businessCategory) throws Exception {

        List<FMMediaPointLease> list = new ArrayList<>();
        JSONArray mediaPointLease = businessCategory.getJSONArray("mediaPointLease");
        mediaPointLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMMediaPointLease fmMediaPointLease = new FMMediaPointLease();
            fmMediaPointLease.setBillNo(jsonObject.getString("billNo"));
            fmMediaPointLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmMediaPointLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmMediaPointLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmMediaPointLease.setHallName(jsonObject.getString("hallName"));
            fmMediaPointLease.setHallId(jsonObject.getString("hallId"));
            fmMediaPointLease.setBasePublishPriceMonth(jsonObject.getString("basePublishPriceMonth"));
            fmMediaPointLease.setDiscountFactorDuration(jsonObject.getString("discountFactorDuration"));
            fmMediaPointLease.setPublishTotalPrice(jsonObject.getString("publishTotalPrice"));
            fmMediaPointLease.setSaleDurationMonth(jsonObject.getString("saleDurationMonth"));
            fmMediaPointLease.setStartDate(jsonObject.getString("startDate"));
            fmMediaPointLease.setEndDate(jsonObject.getString("endDate"));
            fmMediaPointLease.setCollectMode(jsonObject.getString("collectMode"));
            fmMediaPointLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmMediaPointLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmMediaPointLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmMediaPointLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmMediaPointLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmMediaPointLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmMediaPointLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmMediaPointLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmMediaPointLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmMediaPointLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmMediaPointLease.setResourceType(jsonObject.getString("resourceType"));
            fmMediaPointLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmMediaPointLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmMediaPointLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMMediaPointLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_MEDIAPOINTLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 冠名厅租赁
     */
    private int dealNamingHallLease(JSONObject businessCategory) throws Exception {

        List<FMNamingHallLease> list = new ArrayList<>();
        JSONArray namingHallLease = businessCategory.getJSONArray("namingHallLease");
        namingHallLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMNamingHallLease fmNamingHallLease = new FMNamingHallLease();
            fmNamingHallLease.setBillNo(jsonObject.getString("billNo"));
            fmNamingHallLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmNamingHallLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmNamingHallLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmNamingHallLease.setHallName(jsonObject.getString("hallName"));
            fmNamingHallLease.setHallId(jsonObject.getString("hallId"));
            fmNamingHallLease.setBasePublishPriceHallDay(jsonObject.getString("basePublishPriceHallDay"));
            fmNamingHallLease.setSaleDurationDay(jsonObject.getString("saleDurationDay"));
            fmNamingHallLease.setStartDate(jsonObject.getString("startDate"));
            fmNamingHallLease.setEndDate(jsonObject.getString("endDate"));
            fmNamingHallLease.setCollectMode(jsonObject.getString("collectMode"));
            fmNamingHallLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmNamingHallLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmNamingHallLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmNamingHallLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmNamingHallLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmNamingHallLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmNamingHallLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmNamingHallLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmNamingHallLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmNamingHallLease.setResourceType(jsonObject.getString("resourceType"));
            fmNamingHallLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            list.add(fmNamingHallLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMNamingHallLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_NAMEINGHALLLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 其他点位租赁
     */
    private int dealOtherPointLease(JSONObject businessCategory) throws Exception {

        List<FMOtherPointLease> list = new ArrayList<>();
        JSONArray otherPointLease = businessCategory.getJSONArray("otherPointLease");
        otherPointLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMOtherPointLease fmOtherPointLease = new FMOtherPointLease();
            fmOtherPointLease.setBillNo(jsonObject.getString("billNo"));
            fmOtherPointLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmOtherPointLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmOtherPointLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmOtherPointLease.setQuantity(jsonObject.getString("quantity"));
            fmOtherPointLease.setMachineName(jsonObject.getString("machineName"));
            fmOtherPointLease.setLeaseArea(jsonObject.getString("leaseArea"));
            fmOtherPointLease.setSaleDurationMonth(jsonObject.getString("saleDurationMonth"));
            fmOtherPointLease.setStartDate(jsonObject.getString("startDate"));
            fmOtherPointLease.setEndDate(jsonObject.getString("endDate"));
            fmOtherPointLease.setCollectMode(jsonObject.getString("collectMode"));
            fmOtherPointLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmOtherPointLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmOtherPointLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmOtherPointLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmOtherPointLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmOtherPointLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmOtherPointLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmOtherPointLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmOtherPointLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmOtherPointLease.setResourceType(jsonObject.getString("resourceType"));
            fmOtherPointLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmOtherPointLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmOtherPointLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMOtherPointLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_OTHERPOINTLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 其他
     */
    private int dealOthers(JSONObject businessCategory) throws Exception {

        List<FMOthers> list = new ArrayList<>();
        JSONArray others = businessCategory.getJSONArray("others");
        others.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMOthers fmOthers = new FMOthers();
            fmOthers.setBillNo(jsonObject.getString("billNo"));
            fmOthers.setCinemaName(jsonObject.getString("cinemaName"));
            fmOthers.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmOthers.setBusinessItem(jsonObject.getString("businessItem"));
            fmOthers.setName(jsonObject.getString("name"));
            fmOthers.setQuantity(jsonObject.getString("quantity"));
            fmOthers.setUnitPrice(jsonObject.getString("unitPrice"));
            fmOthers.setTotalPrice(jsonObject.getString("totalPrice"));
            fmOthers.setStartDate(jsonObject.getString("startDate"));
            list.add(fmOthers);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMOthers> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_OTHERS);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 外部资源租赁
     */
    private int dealOuterResourceLease(JSONObject businessCategory) throws Exception {

        List<FMOuterResourceLease> list = new ArrayList<>();
        JSONArray outerResourceLease = businessCategory.getJSONArray("outerResourceLease");
        outerResourceLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMOuterResourceLease fmOuterResourceLease = new FMOuterResourceLease();
            fmOuterResourceLease.setBillNo(jsonObject.getString("billNo"));
            fmOuterResourceLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmOuterResourceLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmOuterResourceLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmOuterResourceLease.setBasePublishPriceMeterMonth(jsonObject.getString("basePublishPriceMeterMonth"));
            fmOuterResourceLease.setLeaseArea(jsonObject.getString("leaseArea"));
            fmOuterResourceLease.setSaleDurationMonth(jsonObject.getString("saleDurationMonth"));
            fmOuterResourceLease.setStartDate(jsonObject.getString("startDate"));
            fmOuterResourceLease.setEndDate(jsonObject.getString("endDate"));
            fmOuterResourceLease.setCollectMode(jsonObject.getString("collectMode"));
            fmOuterResourceLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmOuterResourceLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmOuterResourceLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmOuterResourceLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmOuterResourceLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmOuterResourceLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmOuterResourceLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmOuterResourceLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmOuterResourceLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmOuterResourceLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmOuterResourceLease.setResourceType(jsonObject.getString("resourceType"));
            fmOuterResourceLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmOuterResourceLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmOuterResourceLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMOuterResourceLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_OUTERRESOURCELEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 宣传点位租赁
     */
    private int dealPropagandaPointLease(JSONObject businessCategory) throws Exception {

        List<FMPropagandaPointLease> list = new ArrayList<>();
        JSONArray propagandaPointLease = businessCategory.getJSONArray("propagandaPointLease");
        propagandaPointLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMPropagandaPointLease fmPropagandaPointLease = new FMPropagandaPointLease();
            fmPropagandaPointLease.setBillNo(jsonObject.getString("billNo"));
            fmPropagandaPointLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmPropagandaPointLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmPropagandaPointLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmPropagandaPointLease.setBaseArea(jsonObject.getString("baseArea"));
            fmPropagandaPointLease.setBasePrice(jsonObject.getString("basePrice"));
            fmPropagandaPointLease.setRenewalPriceMeterMonth(jsonObject.getString("renewalPriceMeterMonth"));
            fmPropagandaPointLease.setDiscountFactorArea(jsonObject.getString("discountFactorArea"));
            fmPropagandaPointLease.setDiscountFactorDuration(jsonObject.getString("discountFactorDuration"));
            fmPropagandaPointLease.setPublishTotalPrice(jsonObject.getString("publishTotalPrice"));
            fmPropagandaPointLease.setLeaseArea(jsonObject.getString("leaseArea"));
            fmPropagandaPointLease.setSaleDurationMonth(jsonObject.getString("saleDurationMonth"));
            fmPropagandaPointLease.setStartDate(jsonObject.getString("startDate"));
            fmPropagandaPointLease.setEndDate(jsonObject.getString("endDate"));
            fmPropagandaPointLease.setCollectMode(jsonObject.getString("collectMode"));
            fmPropagandaPointLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmPropagandaPointLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmPropagandaPointLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmPropagandaPointLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmPropagandaPointLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmPropagandaPointLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmPropagandaPointLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmPropagandaPointLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmPropagandaPointLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmPropagandaPointLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmPropagandaPointLease.setResourceType(jsonObject.getString("resourceType"));
            fmPropagandaPointLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmPropagandaPointLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmPropagandaPointLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMPropagandaPointLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_PROPAGANDAPOINTLEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 外租区域租赁
     */
    private int dealRentAreaLease(JSONObject businessCategory) throws Exception {

        List<FMRentAreaLease> list = new ArrayList<>();
        JSONArray rentAreaLease = businessCategory.getJSONArray("rentAreaLease");
        rentAreaLease.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMRentAreaLease fmRentAreaLease = new FMRentAreaLease();
            fmRentAreaLease.setBillNo(jsonObject.getString("billNo"));
            fmRentAreaLease.setCinemaName(jsonObject.getString("cinemaName"));
            fmRentAreaLease.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmRentAreaLease.setBusinessItem(jsonObject.getString("businessItem"));
            fmRentAreaLease.setBasePublishPriceMeterMonth(jsonObject.getString("basePublishPriceMeterMonth"));
            fmRentAreaLease.setDiscountFactorArea(jsonObject.getString("discountFactorArea"));
            fmRentAreaLease.setDiscountFactorDuration(jsonObject.getString("discountFactorDuration"));
            fmRentAreaLease.setPublishTotalPrice(jsonObject.getString("publishTotalPrice"));
            fmRentAreaLease.setLeaseArea(jsonObject.getString("leaseArea"));
            fmRentAreaLease.setSaleDurationMonth(jsonObject.getString("saleDurationMonth"));
            fmRentAreaLease.setStartDate(jsonObject.getString("startDate"));
            fmRentAreaLease.setEndDate(jsonObject.getString("endDate"));
            fmRentAreaLease.setCollectMode(jsonObject.getString("collectMode"));
            fmRentAreaLease.setCalculateBase(jsonObject.getString("calculateBase"));
            fmRentAreaLease.setShardProportion(jsonObject.getString("shardProportion"));
            fmRentAreaLease.setGuaranteedAmount(jsonObject.getString("guaranteedAmount"));
            fmRentAreaLease.setTradedPrice(jsonObject.getString("tradedPrice"));
            fmRentAreaLease.setDiscountProportion(jsonObject.getString("discountProportion"));
            fmRentAreaLease.setAccruedAmount(jsonObject.getString("accruedAmount"));
            fmRentAreaLease.setInvoicedAmount(jsonObject.getString("invoicedAmount"));
            fmRentAreaLease.setClaimedAmount(jsonObject.getString("claimedAmount"));
            fmRentAreaLease.setCommissionBase(jsonObject.getString("commissionBase"));
            fmRentAreaLease.setResourceTypeCode(jsonObject.getString("resourceTypeCode"));
            fmRentAreaLease.setResourceType(jsonObject.getString("resourceType"));
            fmRentAreaLease.setResourceBelong(jsonObject.getString("resourceBelong"));
            fmRentAreaLease.setResourceCode(jsonObject.getString("resourceCode"));
            list.add(fmRentAreaLease);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMRentAreaLease> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_RENTAREALEASE);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 售券
     */
    private int dealVoucherList(JSONObject businessCategory) throws Exception {

        List<FMVoucherList> list = new ArrayList<>();
        JSONArray voucherList = businessCategory.getJSONArray("voucherList");
        voucherList.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMVoucherList fmVoucherList = new FMVoucherList();
            fmVoucherList.setBillNo(jsonObject.getString("billNo"));
            fmVoucherList.setCinemaName(jsonObject.getString("cinemaName"));
            fmVoucherList.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmVoucherList.setBusinessItem(jsonObject.getString("businessItem"));
            fmVoucherList.setVoucherTypeName(jsonObject.getString("voucherTypeName"));
            fmVoucherList.setVoucherTypeCode(jsonObject.getString("voucherTypeCode"));
            fmVoucherList.setVoucherTypeLevel(jsonObject.getString("voucherTypeLevel"));
            fmVoucherList.setVoucherCategoryCode(jsonObject.getString("voucherCategoryCode"));
            fmVoucherList.setVoucherForm(jsonObject.getString("voucherForm"));
            fmVoucherList.setVoucherPurpose(jsonObject.getString("voucherPurpose"));
            fmVoucherList.setVoucherType(jsonObject.getString("voucherType"));
            fmVoucherList.setVoucherCost(jsonObject.getString("voucherCost"));
            fmVoucherList.setQuantity(jsonObject.getString("quantity"));
            fmVoucherList.setDiscountRate(jsonObject.getString("discountRate"));
            fmVoucherList.setTotalAmount(jsonObject.getString("totalAmount"));
            list.add(fmVoucherList);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMVoucherList> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_VOUCHER);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


    /**
     * 包场
     */
    private int dealWholeBookList(JSONObject businessCategory) throws Exception {

        List<FMWholeBookList> list = new ArrayList<>();
        JSONArray wholeBookList = businessCategory.getJSONArray("wholeBookList");
        wholeBookList.forEach(object -> {
            JSONObject jsonObject = (JSONObject) object;
            FMWholeBookList fmWholeBookList = new FMWholeBookList();
            fmWholeBookList.setBillNo(jsonObject.getString("billNo"));
            fmWholeBookList.setCinemaName(jsonObject.getString("cinemaName"));
            fmWholeBookList.setCinemaInnerCode(jsonObject.getString("cinemaInnerCode"));
            fmWholeBookList.setBusinessItem(jsonObject.getString("businessItem"));
            fmWholeBookList.setHallName(jsonObject.getString("hallName"));
            fmWholeBookList.setHallId(jsonObject.getString("hallId"));
            fmWholeBookList.setHallType(jsonObject.getString("hallType"));
            fmWholeBookList.setFilmName(jsonObject.getString("filmName"));
            fmWholeBookList.setShowTime(jsonObject.getString("showTime"));
            fmWholeBookList.setSessionsCode(jsonObject.getString("sessionsCode"));
            list.add(fmWholeBookList);
        });

        if (list.size() == 0) {
            return 0;
        }

        SendDataDto.SendDataRequest<FMWholeBookList> request = new SendDataDto.SendDataRequest<>();
        request.setDataType(DataType.FM_CONTRACT_WHOLEBOOK);
        request.setData(list);
        SendDataDto.SendDataResponse send = sendDataManager.send(request);
        return send.getCode();

    }


}
