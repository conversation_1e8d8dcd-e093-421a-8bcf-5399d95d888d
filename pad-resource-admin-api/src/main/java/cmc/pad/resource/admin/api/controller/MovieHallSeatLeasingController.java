package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.MovieHallSeatLeasingPriceModel;
import cmc.pad.resource.admin.service.dto.MovieHallSeatLeasingDto;
import cmc.pad.resource.admin.service.iface.MovieHallSeatLeasingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.admin.api.util.MoneyUtils.stringToBigDecimal;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 影厅租赁报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("movie-hall-seat-leasing/price")
public class MovieHallSeatLeasingController {

    private final MovieHallSeatLeasingService movieHallSeatLeasingService;

    @Autowired
    MovieHallSeatLeasingController(MovieHallSeatLeasingService movieHallSeatLeasingService) {
        this.movieHallSeatLeasingService = movieHallSeatLeasingService;
    }


    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated MovieHallSeatLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated MovieHallSeatLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(MovieHallSeatLeasingPriceModel.QueryParams params) {
        log.info(">>>查询影厅租赁刊例价, {}", params);

        // 构建RPC请求参数
        MovieHallSeatLeasingDto.QueryPricesRequest request = new MovieHallSeatLeasingDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setCityLevel(params.getCityLevel());
        request.setCinemaLevel(params.getCinemaLevel());
        request.setMovieHallType(params.getMovieHallType());

        // 调用RPC服务
        MovieHallSeatLeasingDto.QueryPricesResponse response = movieHallSeatLeasingService.queryPrices(request);
        List resultList;
        // 转换响应结果
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            resultList = response.getPriceInfos().stream().map(priceInfo -> {
                MovieHallSeatLeasingPriceModel.Info info = new MovieHallSeatLeasingPriceModel.Info();
                info.setCityLevel(priceInfo.getCityLevel());
                info.setCinemaLevel(priceInfo.getCinemaLevel());
                info.setMovieHallType(priceInfo.getMovieHallType());
                info.setBasePrice(stringToBigDecimal(priceInfo.getBasePrice()));
                info.setBaseDuration(priceInfo.getBaseDuration());
                info.setExtendedPrice(stringToBigDecimal(priceInfo.getExtendedPrice()));
                return info;
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        log.info(">>>响应:查询影厅租赁刊例价, request:{} result:{}", request, resultList);
        return resultList;
    }

}
