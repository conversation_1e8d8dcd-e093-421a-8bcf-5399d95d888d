package cmc.pad.resource.admin.api.model.price;

import cmc.pad.resource.admin.api.model.validation.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 影厅租赁报价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:06
 * @Version 1.0
 */
public class MovieHallSeatLeasingPriceModel {

    @Data
    public static class QueryParams {
        //城市级别编码
        @CityLevel
        private String cityLevel;
        //影院级别编码
        @CinemaLevel
        private String cinemaLevel;
        //影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。
        @CinemaCode
        private String cinemaCode;
        //影厅类型编码，见cmc维数据"影厅类型"
//        @MovieHallType
        private String movieHallType;
    }

    @Data
    public static class Info {
        //城市级别编码
        private String cityLevel;
        //影院级别编码
        private String cinemaLevel;
        private String movieHallType;
        //基础价，单位：元/月
        private BigDecimal basePrice;
        //基础时长，单位：小时
        private int baseDuration;
        //续价，单位：元/小时/个
        private BigDecimal extendedPrice;
    }
}
