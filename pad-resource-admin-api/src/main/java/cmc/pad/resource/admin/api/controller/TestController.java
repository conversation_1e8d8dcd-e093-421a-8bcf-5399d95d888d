package cmc.pad.resource.admin.api.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("test")
public class TestController {
    @RequestMapping(path = "testLock")
    public void cancelOccupationByPost() {
        lockTemplate("test_lock_key", () -> log.info(">>>测试锁"));
    }
}
