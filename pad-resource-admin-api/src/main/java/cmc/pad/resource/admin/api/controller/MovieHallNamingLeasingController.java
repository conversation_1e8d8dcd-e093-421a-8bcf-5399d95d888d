package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.MovieHallNamingLeasingPriceModel;
import cmc.pad.resource.admin.service.dto.MovieHallNamingLeasingDto;
import cmc.pad.resource.admin.service.iface.MovieHallNamingLeasingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.admin.api.util.MoneyUtils.stringToBigDecimal;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 冠名厅刊例报价控制器
 * 
 * 提供冠名厅租赁价格查询功能，支持按影院编码和影厅类型查询刊例价格。
 * 当指定影厅类型无数据时，会自动查询兜底刊例（未设置影厅类型的刊例）。
 *
 * <AUTHOR>
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("named-movie-hall-leasing/price")
public class MovieHallNamingLeasingController {

    private final MovieHallNamingLeasingService movieHallNamingLeasingService;

    @Autowired
    MovieHallNamingLeasingController(MovieHallNamingLeasingService movieHallNamingLeasingService) {
        this.movieHallNamingLeasingService = movieHallNamingLeasingService;
    }

    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated MovieHallNamingLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated MovieHallNamingLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(MovieHallNamingLeasingPriceModel.QueryParams params) {
        log.info(">>>查询冠名厅租赁刊例价, {}", params);

        // 构建RPC请求参数
        MovieHallNamingLeasingDto.QueryPricesRequest request = new MovieHallNamingLeasingDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setMovieHallType(params.getMovieHallType());

        // 调用RPC服务
        MovieHallNamingLeasingDto.QueryPricesResponse response = movieHallNamingLeasingService.queryPrices(request);
        List resultList;
        // 转换响应结果
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            resultList = response.getPriceInfos().stream().map(priceInfo -> {
                MovieHallNamingLeasingPriceModel.Info info = new MovieHallNamingLeasingPriceModel.Info();
                info.setCinemaCode(priceInfo.getCinemaCode());
                info.setMovieHallType(priceInfo.getMovieHallType());
                info.setBasePrice(stringToBigDecimal(priceInfo.getBasePrice()));
                return info;
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        log.info(">>>响应:查询冠名厅租赁刊例价, request:{} result:{}", request, resultList);
        return resultList;
    }

    /**
     * 测试接口
     * 
     * @return 测试字符串
     */
    @RequestMapping(value = "test", method = {POST})
    public String testPost() {
        return "test";
    }
}
