package cmc.pad.resource.admin.api.model.validation;

import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * created by lsh
 */
class BusinessTypeValidator implements ConstraintValidator<BusinessType, String> {

    @Override
    public void initialize(BusinessType constraintAnnotation) {
    }

    @Override
    public boolean isValid(String businessType, ConstraintValidatorContext constraintValidatorContext) {
        //为空不校验
        if (StringUtils.isBlank(businessType)) {
            return true;
        }
        if (!Strings.isNullOrEmpty(businessType)
                && !businessType.equalsIgnoreCase("YX")
                && !businessType.equalsIgnoreCase("WZ")
                && !businessType.equalsIgnoreCase("GD")
                && !businessType.equalsIgnoreCase("XC")
                && !businessType.equalsIgnoreCase("QT")
                && !businessType.equalsIgnoreCase("CMDX")
                && !businessType.equalsIgnoreCase("WBZY")
        ) {
            return false;
        }
        return true;
    }
}
