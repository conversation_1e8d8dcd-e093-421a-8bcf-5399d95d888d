
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 财管合同
 */

@Getter
@Setter
public class FMContract {

    
    //是否有合同
    @DSFiled(order = 1)
    private String isContract;
        
    //业务类别
    @DSFiled(order = 2)
    private String category;
        
    //合同分类 1无合同影城卡券 2无合同影城其他收入 3无合同多种经营收入 4有合同多种经营 5有合同卡券 6有合同其他收入
    @DSFiled(order = 3)
    private String contractType;
        
    //单据编号
    @DSFiled(order = 4)
    private String billNo;
        
    //填报人id
    @DSFiled(order = 5)
    private String operatorId;
        
    //填报人
    @DSFiled(order = 6)
    private String operatorName;
        
    //填报日期
    @DSFiled(order = 7)
    private String operatorDate;
        
    //单据状态
    @DSFiled(order = 8)
    private String billState;
        
    //经办部门
    @DSFiled(order = 9)
    private String handleDept;
        
    //申请单号/合同编号
    @DSFiled(order = 10)
    private String contractNo;
        
    //合同名称
    @DSFiled(order = 11)
    private String contractName;
        
    //申请单状态/合同状态
    @DSFiled(order = 12)
    private String contractState;
        
    //所属区域编码
    @DSFiled(order = 13)
    private String areaCode;
        
    //所属区域
    @DSFiled(order = 14)
    private String areaName;
        
    //合同主体
    @DSFiled(order = 15)
    private String contractSubject;
        
    //客户id
    @DSFiled(order = 16)
    private String customerId;
        
    //客户名称
    @DSFiled(order = 17)
    private String customerName;
        
    //联系人姓名
    @DSFiled(order = 18)
    private String linkName;
        
    //联系电话
    @DSFiled(order = 19)
    private String linkTel;
        
    //邮箱
    @DSFiled(order = 20)
    private String email;
        
    //是否集团内 1是2否
    @DSFiled(order = 21)
    private String isWithinGroup;
        
    //行业性质
    @DSFiled(order = 22)
    private String tradePorperties;
        
    //销售/合同起始日期
    @DSFiled(order = 23)
    private String contractStartDate;
        
    //销售/终止起始日期
    @DSFiled(order = 24)
    private String contractEndDate;
        
    //销售/合同签订日期
    @DSFiled(order = 25)
    private String contractSignDate;
        
    //是否传媒事项 1是2否
    @DSFiled(order = 26)
    private String isMediaItem;
        
    //结算方式
    @DSFiled(order = 27)
    private String settleStyle;
        
    //申请金额/总金额
    @DSFiled(order = 28)
    private String totalAmount;
        
    //预收款金额
    @DSFiled(order = 29)
    private String expect;
        
    //已计提金额
    @DSFiled(order = 30)
    private String accruedAmount;
        
    //已认领合同金额
    @DSFiled(order = 31)
    private String claimedAmount;
        
    //已提成基数
    @DSFiled(order = 32)
    private String commissionBase;
        
    //已开票金额
    @DSFiled(order = 33)
    private String invoicedAmount;
        
    //在途开票金额
    @DSFiled(order = 34)
    private String invoicingAmount;
        
    //保证金金额
    @DSFiled(order = 35)
    private String bondAmount;
        
    //已认领保证金
    @DSFiled(order = 36)
    private String claimedBondAmount;
        

}
    