package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.QueryResourceParams;
import cmc.pad.resource.admin.api.model.ResourceInfo;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cmc.pad.resource.application.AppError.BUSINESS_TYPE_NOT_EXIST;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 资源controller
 * 2022/2/23 废弃
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping("resource")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ResourceController {

    private final CinemaResourceRepository cinemaResourceRepository;

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated QueryResourceParams queryResourceParams) {
        return queryByGet(queryResourceParams);
    }

    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated QueryResourceParams queryResourceParams) {
        log.info(">>>查询影城资源,{}", queryResourceParams);
        String businessType = queryResourceParams.getBusinessType();
        Filter filter = Filter.create("cinema_code", queryResourceParams.getCinemaCode());
        Optional<CinemaResource> optional = cinemaResourceRepository.findOne(filter);
        List<ResourceInfo> resourceInfoList = new ArrayList<>();
        optional.ifPresent(cinemaResource -> {
            ResourceInfo yx = new ResourceInfo("YX", cinemaResource.getMarketingPointLeasableArea());
            ResourceInfo gd = new ResourceInfo("GD", cinemaResource.getFixedPointLeasableArea());
            ResourceInfo wz = new ResourceInfo("WZ", cinemaResource.getOuterAreaLeasableArea());
            ResourceInfo xc = new ResourceInfo("XC", cinemaResource.getAdvertisingPointLeasableQuantity().floatValue());
            if (Strings.isNullOrEmpty(businessType)) {
                resourceInfoList.add(yx);
                resourceInfoList.add(gd);
                resourceInfoList.add(wz);
                resourceInfoList.add(xc);
            } else if ("YX".equalsIgnoreCase(businessType)) {
                resourceInfoList.add(yx);
            } else if ("GD".equalsIgnoreCase(businessType)) {
                resourceInfoList.add(gd);
            } else if ("WZ".equalsIgnoreCase(businessType)) {
                resourceInfoList.add(wz);
            } else if ("XC".equalsIgnoreCase(businessType)) {
                resourceInfoList.add(xc);
            } else {
                throw BUSINESS_TYPE_NOT_EXIST.toException();
            }
        });
        return resourceInfoList;
    }
}
