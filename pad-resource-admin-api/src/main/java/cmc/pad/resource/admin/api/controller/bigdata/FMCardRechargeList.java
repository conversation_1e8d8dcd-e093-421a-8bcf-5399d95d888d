
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 *  储值卡续充
 */

@Getter
@Setter
public class FMCardRechargeList {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    //充值金额
    @DSFiled(order = 5)
    private String rechargeAmount;
        
    //数量
    @DSFiled(order = 6)
    private String quantity;
        
    //总金额
    @DSFiled(order = 7)
    private String totalAmount;
        
    //在途开票金额
    @DSFiled(order = 8)
    private String invoicingAmount;
        
    //已开票金额
    @DSFiled(order = 9)
    private String invoicedAmount;
        
    //已认领金额
    @DSFiled(order = 10)
    private String claimedAmount;
        
    //已提成基数
    @DSFiled(order = 11)
    private String commissionBase;
        

}
    