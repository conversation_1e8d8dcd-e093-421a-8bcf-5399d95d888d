package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.CancelOccupationParams;
import cmc.pad.resource.admin.api.model.validation.SaveOccupationParams;
import cmc.pad.resource.application.command.InventoryAppService;
import cmc.pad.resource.application.command.SaveOccupationCommand;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static cmc.pad.resource.application.AppError.OCCUPATION_DETAILS_NOT_NULL;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("inventory/occupation")
@RequiredArgsConstructor(onConstructor = @__({@Autowired}))
public class InventoryOccupationController {

    private final InventoryAppService inventoryAppService;


    @RequestMapping(path = "cancel", method = {POST})
    public Boolean cancelOccupationByPost(@RequestBody @Validated CancelOccupationParams cancelOccupationParams) {
        return cancelOccupationByGet(cancelOccupationParams);
    }

    @RequestMapping(path = "cancel", method = {GET})
    public Boolean cancelOccupationByGet(@ModelAttribute @Validated CancelOccupationParams cancelOccupationParams) {
        return (Boolean) lockTemplate(getLockKey(cancelOccupationParams.getContractNo()), () -> inventoryAppService.cancelOccupation(cancelOccupationParams.getContractNo()));
    }

    @RequestMapping(path = "save", method = {POST})
    public Boolean saveOccupation(@RequestBody @Validated SaveOccupationParams saveOccupationParams) {
        log.info(">>>创建库存占用, {}", saveOccupationParams);
        SaveOccupationCommand command = new SaveOccupationCommand();
        List<SaveOccupationParams.OccupationDetail> details = saveOccupationParams.getDetails();
        List<SaveOccupationCommand.OccupationDetail> detailList = new ArrayList<>();
        for (SaveOccupationParams.OccupationDetail detail : details) {
            if (Strings.isNullOrEmpty(detail.getCinemaCode()) || detail.getAmount() == null ||
                    Strings.isNullOrEmpty(detail.getStartDate()) || Strings.isNullOrEmpty(detail.getEndDate())) {
                throw OCCUPATION_DETAILS_NOT_NULL.toException();
            }
            SaveOccupationCommand.OccupationDetail occupationDetail = new SaveOccupationCommand.OccupationDetail();
            occupationDetail.setCinemaCode(detail.getCinemaCode());
            occupationDetail.setAmount(detail.getAmount());
            occupationDetail.setStartDate(detail.getStartDate());
            occupationDetail.setEndDate(detail.getEndDate());
            detailList.add(occupationDetail);
        }
        command.setDetails(detailList);
        command.setContractNo(saveOccupationParams.getContractNo());
        command.setBusinessType(saveOccupationParams.getBusinessType());
        return (Boolean) lockTemplate(getLockKey(saveOccupationParams.getContractNo()), () ->
                inventoryAppService.saveOccupation(command)
        );
    }

    private String getLockKey(String contractNo) {
        return "occupation_" + contractNo;
    }
}
