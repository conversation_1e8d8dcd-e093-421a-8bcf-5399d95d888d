package cmc.pad.resource.admin.api.controller.point;


import cmc.pad.resource.admin.api.model.point.PointLocationParamModel;
import cmc.pad.resource.admin.service.dto.PointLocationInventoryDto;
import cmc.pad.resource.admin.service.iface.PointLocationInventoryService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by fuwei on 2022/1/27.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("point-location/inventory")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationInventoryController {
    private final PointLocationInventoryService pointLocationInventoryService;

    @RequestMapping(value = "occupy")
    public boolean occupy(@RequestBody @Validated PointLocationParamModel.InventoryOccupationContractParam param) {
        log.info(">>>提交合同库存占用,param {}", JSON.toJSONString(param));

        // 转换API参数到RPC请求
        PointLocationInventoryDto.OccupyRequest request = convertToOccupyRequest(param);

        // 调用RPC服务
        PointLocationInventoryDto.OccupyResponse response = pointLocationInventoryService.occupy(request);

        return response.isSuccess();
    }

    @RequestMapping(value = "updateStatus")
    public boolean updateStatus(@RequestBody @Validated PointLocationParamModel.UpdateStatusParam param) {
        log.info(">>>更新库存状态,param:{}", JSON.toJSONString(param));

        // 转换API参数到RPC请求
        PointLocationInventoryDto.UpdateStatusRequest request = convertToUpdateStatusRequest(param);

        // 调用RPC服务
        try {
            PointLocationInventoryDto.UpdateStatusResponse response = pointLocationInventoryService.updateStatus(request);
            log.info(">>>更新库存状态完成,param:{}, response:{}", JSON.toJSONString(param), JSON.toJSONString(response));
            return response.isSuccess();
        } catch (Exception e) {
            log.error(">>>更新库存状态异常,param:{}", JSON.toJSONString(param), e);
            throw e;
        }
    }

    /**
     * 转换API参数到RPC占用请求
     */
    private PointLocationInventoryDto.OccupyRequest convertToOccupyRequest(
            PointLocationParamModel.InventoryOccupationContractParam param) {

        PointLocationInventoryDto.OccupyRequest request = new PointLocationInventoryDto.OccupyRequest();
        request.setContractType(param.getContractType());
        request.setContractNo(param.getContractNo());
        request.setBusinessTypeCode(param.getBusinessTypeCode());

        // 转换明细列表
        if (param.getDetails() != null) {
            List<PointLocationInventoryDto.OccupationDetail> details =
                    param.getDetails().stream()
                            .map(this::convertToOccupationDetail)
                            .collect(Collectors.toList());
            request.setDetails(details);
        }

        return request;
    }

    /**
     * 转换API明细参数到RPC明细
     */
    private PointLocationInventoryDto.OccupationDetail convertToOccupationDetail(
            PointLocationParamModel.InventoryOccupationContractParam.OccupationDetail apiDetail) {

        PointLocationInventoryDto.OccupationDetail detail = new PointLocationInventoryDto.OccupationDetail();
        detail.setCinemaInnerCode(apiDetail.getCinemaInnerCode());
        detail.setPointLocationId(apiDetail.getPointLocationId());
        detail.setAmount(apiDetail.getAmount());
        detail.setStartDate(apiDetail.getStartDate());
        detail.setEndDate(apiDetail.getEndDate());
        detail.setId(apiDetail.getId());
        detail.setStatus(apiDetail.getStatus());

        return detail;
    }

    /**
     * 转换API参数到RPC更新状态请求
     */
    private PointLocationInventoryDto.UpdateStatusRequest convertToUpdateStatusRequest(
            PointLocationParamModel.UpdateStatusParam param) {

        PointLocationInventoryDto.UpdateStatusRequest request = new PointLocationInventoryDto.UpdateStatusRequest();
        request.setContractNo(param.getContractNo());
        request.setStatus(param.getStatus());

        return request;
    }
}