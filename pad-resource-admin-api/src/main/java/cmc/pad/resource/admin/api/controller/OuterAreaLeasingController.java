package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.OuterAreaLeasingPriceModel;
import cmc.pad.resource.admin.service.dto.OuterAreaLeasingDto;
import cmc.pad.resource.admin.service.iface.OuterAreaLeasingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.admin.api.util.MoneyUtils.stringToBigDecimal;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 外租区域刊例价
 *
 * <AUTHOR>
 * @Date 2025/09/09
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("outer-area-leasing/price")
public class OuterAreaLeasingController {

    private final OuterAreaLeasingService outerAreaLeasingService;

    @Autowired
    OuterAreaLeasingController(OuterAreaLeasingService outerAreaLeasingService) {
        this.outerAreaLeasingService = outerAreaLeasingService;
    }

    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated OuterAreaLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated OuterAreaLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(OuterAreaLeasingPriceModel.QueryParams params) {
        log.info(">>>查询外租区域租赁刊例价, {}", params);

        // 构建RPC请求参数
        OuterAreaLeasingDto.QueryPricesRequest request = new OuterAreaLeasingDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setCityLevel(params.getCityLevel());
        request.setCinemaLevel(params.getCinemaLevel());

        // 调用RPC服务
        OuterAreaLeasingDto.QueryPricesResponse response = outerAreaLeasingService.queryPrices(request);
        List resultList;
        // 转换响应结果
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            resultList = response.getPriceInfos().stream().map(priceInfo -> {
                OuterAreaLeasingPriceModel.Info info = new OuterAreaLeasingPriceModel.Info();
                info.setCityLevel(priceInfo.getCityLevel());
                info.setCinemaLevel(priceInfo.getCinemaLevel());
                info.setBasePrice(stringToBigDecimal(priceInfo.getBasePrice()));
                return info;
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        log.info(">>>响应:查询外租区域租赁刊例价, request:{} result:{}", request, resultList);
        return resultList;
    }

}
