package cmc.pad.resource.admin.api.model.validation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2019/4/1 15:06
 * @Version 1.0
 */
class CinemaCodeValidator implements ConstraintValidator<CinemaCode, String> {
    private static final Pattern numberPattern = Pattern.compile("^\\d{1,6}$");

    @Override
    public void initialize(CinemaCode constraintAnnotation) {
    }

    @Override
    public boolean isValid(String cinemaCode, ConstraintValidatorContext constraintValidatorContext) {
        //为空不校验
        if (StringUtils.isBlank(cinemaCode)) {
            return true;
        }
        Matcher match = numberPattern.matcher(cinemaCode);
        return match.matches();
    }
}
