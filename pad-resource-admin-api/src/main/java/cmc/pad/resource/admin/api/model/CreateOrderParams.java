package cmc.pad.resource.admin.api.model;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class CreateOrderParams {

    private String cardNo;

    @Length(max = 50, min = 2)
    private String buyer;

    @NotNull
    private String operator;

    @Range(min = 1, max = 100)
    private int quantity;
}
