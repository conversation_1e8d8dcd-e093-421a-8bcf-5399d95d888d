package cmc.pad.resource.admin.api.controller.point;

import cmc.pad.resource.admin.api.model.PointLocationQueryParam;
import cmc.pad.resource.admin.service.dto.PointLocationQueryDto;
import cmc.pad.resource.admin.service.iface.PointLocationQueryService;
import com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 点位查询控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("point-location")
public class PointLocationQueryController {

    private final PointLocationQueryService pointLocationQueryService;

    @Autowired
    public PointLocationQueryController(PointLocationQueryService pointLocationQueryService) {
        this.pointLocationQueryService = pointLocationQueryService;
    }

    @ResponseBody
    @RequestMapping(value = "query")
    public PageResult<PointLocationQueryResult> query(@RequestBody @Validated PointLocationQueryParam param) throws MySQLSyntaxErrorException {
        log.info(">>>点位查询, param:{}", param);

        // 构建RPC请求参数
        PointLocationQueryDto.QueryPointLocationUsableAreaRequest request =
                new PointLocationQueryDto.QueryPointLocationUsableAreaRequest();
        request.setPageIndex(param.getPageIndex());
        request.setPageSize(param.getPageSize());
        request.setCode(param.getCode());
        request.setCinemaInnerCode(param.getCinemaInnerCode());
        request.setBusinessTypeCode(param.getBusinessTypeCode());
        request.setStart(param.getStart());
        request.setEnd(param.getEnd());

        // 调用RPC服务
        try {
            PointLocationQueryDto.QueryPointLocationUsableAreaResponse response =
                    pointLocationQueryService.queryPointLocationUsableArea(request);
            // 转换响应结果
            List<PointLocationQueryResult> resultList = Optional.ofNullable(response.getPageResult().getItems())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(this::convertToResult)
                    .collect(Collectors.toList());

            PageResult<PointLocationQueryResult> pageResult =
                    new PageResult<>(resultList, (int) response.getPageResult().getTotalCount());

            log.info(">>>点位查询完成, param:{}, totalCount:{}", param, pageResult.getTotalCount());
            return pageResult;

        } catch (Exception e) {
            log.error(">>>点位查询, param:{}", param, e);
            if (e.getMessage().contains("com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException")) {
                throw new com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException(e.getMessage());
            }
            throw e;
        }

    }

    /**
     * 转换DTO到API结果
     */
    private PointLocationQueryResult convertToResult(PointLocationQueryDto.PointLocationUsableAreaInfo info) {
        PointLocationQueryResult result = new PointLocationQueryResult();
        result.setId(info.getId());
        result.setCode(info.getCode());
        result.setUsableArea(info.getUsableArea());
        result.setBusinessTypeCode(info.getBusinessTypeCode());
        result.setCinemaInnerCode(info.getCinemaInnerCode());
        result.setResourceOwnershipCode(info.getResourceOwnershipCode());
        result.setLocationDesc(info.getLocationDesc());
        result.setPlanUse(info.getPlanUse());
        result.setLandingMode(info.getLandingMode());
        return result;
    }

    /**
     * 点位查询结果
     */
    public static class PointLocationQueryResult {
        private int id;
        private String code;
        private float usableArea;
        private String businessTypeCode;
        private String cinemaInnerCode;
        private String resourceOwnershipCode;
        private String locationDesc;
        private String planUse;
        private String landingMode;

        // Getters and Setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public float getUsableArea() {
            return usableArea;
        }

        public void setUsableArea(float usableArea) {
            this.usableArea = usableArea;
        }

        public String getBusinessTypeCode() {
            return businessTypeCode;
        }

        public void setBusinessTypeCode(String businessTypeCode) {
            this.businessTypeCode = businessTypeCode;
        }

        public String getCinemaInnerCode() {
            return cinemaInnerCode;
        }

        public void setCinemaInnerCode(String cinemaInnerCode) {
            this.cinemaInnerCode = cinemaInnerCode;
        }

        public String getResourceOwnershipCode() {
            return resourceOwnershipCode;
        }

        public void setResourceOwnershipCode(String resourceOwnershipCode) {
            this.resourceOwnershipCode = resourceOwnershipCode;
        }

        public String getLocationDesc() {
            return locationDesc;
        }

        public void setLocationDesc(String locationDesc) {
            this.locationDesc = locationDesc;
        }

        public String getPlanUse() {
            return planUse;
        }

        public void setPlanUse(String planUse) {
            this.planUse = planUse;
        }

        public String getLandingMode() {
            return landingMode;
        }

        public void setLandingMode(String landingMode) {
            this.landingMode = landingMode;
        }
    }
}