package cmc.pad.resource.admin.api.model.validation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2019/4/1 15:06
 * @Version 1.0
 */
class CityLevelValidator implements ConstraintValidator<CityLevel, String> {

    private static final Pattern numberPattern = Pattern.compile("^[l,L]\\d+$");

    @Override
    public void initialize(CityLevel constraintAnnotation) {

    }

    @Override
    public boolean isValid(String cityLevel, ConstraintValidatorContext constraintValidatorContext) {
        //为空不校验
        if (StringUtils.isBlank(cityLevel)) {
            return true;
        }
        Matcher match = numberPattern.matcher(cityLevel);
        return match.matches();
    }
}
