package cmc.pad.resource.admin.api.model;

import cmc.pad.resource.admin.api.model.validation.BusinessType;
import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UpdateOccupationParams {

    /**
     * 影院编码
     */
    @NotNull
    @CinemaCode
    private String cinemaCode;
    /**
     * 合同编号
     */
    @NotNull
    private String contractNo;
    /**
     * 广告业务类型
     */
    @NotNull
    @BusinessType
    private String businessType;
    /**
     * 占用的面积或者个数，取决于广告业务类型
     */
    @NotNull
    private Float amount;
    /**
     * 占位开始日期，格式：yyyy-MM-dd
     */
    @NotNull
    private String startDate;
    /**
     * 占位结束日期，格式：yyyy-MM-dd
     */
    @NotNull
    private String endDate;
}
