package cmc.pad.resource.admin.api.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/3/19 16:27
 * @Version 1.0
 */
public class MoneyUtilsBak {
    public static BigDecimal centConvertYuan(int money) {
        money = money < 0 ? 0 : money;
        BigDecimal yuan = new BigDecimal(money).divide(new BigDecimal(100));
        // 保留两位小数，精确到分
        return yuan.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    public static void main(String[] args) {
        System.out.println(centConvertYuan(1234567890));
    }

}