package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.CoefficientInfo;
import cmc.pad.resource.admin.api.model.QueryDiscountParams;
import cmc.pad.resource.admin.service.dto.DiscountDto;
import cmc.pad.resource.admin.service.iface.DiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 折扣controller
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("discount")
public class DiscountController {

    private final DiscountService discountService;

    @Autowired
    DiscountController(DiscountService discountService) {
        this.discountService = discountService;
    }

    @RequestMapping(value = "coefficient/match", method = {POST})
    public List queryByPost(@RequestBody @Validated QueryDiscountParams queryDiscountParams) {
        return queryByGet(queryDiscountParams);
    }

    @RequestMapping(value = "coefficient/match", method = {GET})
    public List queryByGet(@ModelAttribute @Validated QueryDiscountParams queryDiscountParams) {
        log.info(">>>查询折扣系数, {}", queryDiscountParams);

        // 构建RPC请求参数
        DiscountDto.MatchCoefficientRequest request = new DiscountDto.MatchCoefficientRequest();
        request.setBusinessType(queryDiscountParams.getBusinessType());
        request.setDiscountMethod(queryDiscountParams.getDiscountMethod());
        if (queryDiscountParams.getArea() != null) {
            request.setArea(queryDiscountParams.getArea());
        }
        if (queryDiscountParams.getDuration() != null) {
            request.setDuration(queryDiscountParams.getDuration());
        }

        // 调用RPC服务
        DiscountDto.MatchCoefficientResponse response = discountService.matchCoefficient(request);
        List resultList;
        
        // 转换响应结果
        if (response.getCoefficientInfos() != null && !response.getCoefficientInfos().isEmpty()) {
            resultList = response.getCoefficientInfos().stream().map(coefficientInfo -> {
                return new CoefficientInfo(coefficientInfo.getDiscountMethod(), coefficientInfo.getCoefficient());
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        
        log.info(">>>响应:查询折扣系数, request:{} result:{}", request, resultList);
        return resultList;
    }
}
