package cmc.pad.resource.admin.api.model.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * created by lsh
 */
@Constraint(validatedBy = BusinessTypeValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BusinessType {
    String message() default "不支持的业务类型";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
