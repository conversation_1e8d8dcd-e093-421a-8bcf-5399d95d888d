package cmc.pad.resource.admin.api.util;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2019/3/19 16:27
 * @Version 1.0
 */
public class MoneyUtils {
    public static float centConvertYuan(int money) {
        money = money < 0 ? 0 : money;
        return (float) money / 100;
    }

    /**
     * String类型转换为BigDecimal类型
     * @param value String类型的金额
     * @return BigDecimal类型的金额
     */
    public static BigDecimal stringToBigDecimal(String value) {
        if (value == null || value.isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    public static void main(String[] args) {
        System.out.println(centConvertYuan(100));
        System.out.println(stringToBigDecimal("100"));
        System.out.println(stringToBigDecimal("100.0"));
        System.out.println(stringToBigDecimal("100.00"));
        System.out.println(stringToBigDecimal("100.10"));
        System.out.println(stringToBigDecimal("100.11"));
        System.out.println(stringToBigDecimal("100.111"));
    }
}