package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.AdvertisingPointLeasingPriceModel;
import cmc.pad.resource.admin.service.dto.AdvertisingPointLeasingDto;
import cmc.pad.resource.admin.service.iface.AdvertisingPointLeasingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.admin.api.util.MoneyUtils.centConvertYuan;
import static cmc.pad.resource.admin.api.util.MoneyUtils.stringToBigDecimal;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 宣传点位报价
 *
 * <AUTHOR>
 * @Date 2019/3/29 17:10
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("advertising-point-leasing/price")
public class AdvertisingPointLeasingController {

    private final AdvertisingPointLeasingService advertisingPointLeasingService;

    @Autowired
    AdvertisingPointLeasingController(AdvertisingPointLeasingService advertisingPointLeasingService) {
        this.advertisingPointLeasingService = advertisingPointLeasingService;
    }

    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated AdvertisingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated AdvertisingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(AdvertisingPointLeasingPriceModel.QueryParams params) {
        log.info(">>>查询宣传点位租赁刊例价, {}", params);

        // 构建RPC请求参数
        AdvertisingPointLeasingDto.QueryPricesRequest request = new AdvertisingPointLeasingDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setCityLevel(params.getCityLevel());
        request.setCinemaLevel(params.getCinemaLevel());

        // 调用RPC服务
        AdvertisingPointLeasingDto.QueryPricesResponse response = advertisingPointLeasingService.queryPrices(request);
        List resultList;
        // 转换响应结果
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            resultList = response.getPriceInfos().stream().map(priceInfo -> {
                AdvertisingPointLeasingPriceModel.Info info = new AdvertisingPointLeasingPriceModel.Info();
                info.setCityLevel(priceInfo.getCityLevel());
                info.setCinemaLevel(priceInfo.getCinemaLevel());
                info.setBasePrice(stringToBigDecimal(priceInfo.getBasePrice()));
                info.setBaseArea(priceInfo.getBaseArea());
                info.setExtendedPrice(stringToBigDecimal(priceInfo.getExtendedPrice()));
                return info;
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        log.info(">>>响应:查询宣传点位租赁刊例价, request:{} result:{}", request, resultList);
        return resultList;
    }

}