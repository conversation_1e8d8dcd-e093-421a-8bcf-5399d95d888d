package cmc.pad.resource.admin.api.model.point;

import cmc.pad.resource.admin.api.model.validation.BusinessType;
import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by fuwei on 2022/1/12.
 */
@Data
public class PointLocationParamModel {
    @Data
    @NoArgsConstructor
    public static class InventoryOccupationContractParam {
        private int contractType;//1:新提交合同 2:变更已审核过的合同
        @NotNull(message = "不能为null")
        private String contractNo;
        @NotNull(message = "不能为null")
        @BusinessType
        private String businessTypeCode;
        @NotNull(message = "不能为null")
        @Valid
        private List<OccupationDetail> details;

        @Data
        @NoArgsConstructor
        public static class OccupationDetail {
            @NotNull(message = "不能为null")
            @CinemaCode
            private String cinemaInnerCode;
            @NotNull(message = "不能为null")
            private Integer pointLocationId;
            @NotNull(message = "不能为null")
            private Float amount;
            @NotNull(message = "不能为null")
            private LocalDate startDate;
            @NotNull(message = "不能为null")
            private LocalDate endDate;
            @NotNull(message = "不能为null")
            private String id;
            @NotNull(message = "不能为null")
            private Integer status; //1.占用（包含不变的和修改的） 2.新增 3.作废
        }
    }

    @Data
    public static class UpdateStatusParam {
        @NotNull(message = "不能为null")
        private String contractNo;
        @NotNull(message = "不能为null")
        private Integer status;//1:扣减 2:取消
    }
}
