package cmc.pad.resource.admin.api.controller;

import cmc.pad.resource.admin.api.model.price.MarketingPointLeasingPriceModel;
import cmc.pad.resource.admin.service.dto.MarketingPointLeasingDto;
import cmc.pad.resource.admin.service.iface.MarketingPointLeasingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.admin.api.util.MoneyUtils.stringToBigDecimal;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * 营销点位刊例价
 *
 * <AUTHOR>
 * @Date 2019/4/1 11:04
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("marketing-point-leasing/price")
public class MarketingPointLeasingController {

    private final MarketingPointLeasingService marketingPointLeasingService;

    @Autowired
    MarketingPointLeasingController(MarketingPointLeasingService marketingPointLeasingService) {
        this.marketingPointLeasingService = marketingPointLeasingService;
    }

    @RequestMapping(value = "query", method = {GET})
    public List queryByGet(@ModelAttribute @Validated MarketingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    @RequestMapping(value = "query", method = {POST})
    public List queryByPost(@RequestBody @Validated MarketingPointLeasingPriceModel.QueryParams params) {
        return getList(params);
    }

    private List getList(MarketingPointLeasingPriceModel.QueryParams params) {
        log.info(">>>查询营销点位租赁刊例价, {}", params);

        // 构建RPC请求参数
        MarketingPointLeasingDto.QueryPricesRequest request = new MarketingPointLeasingDto.QueryPricesRequest();
        request.setCinemaCode(params.getCinemaCode());
        request.setCityLevel(params.getCityLevel());
        request.setCinemaLevel(params.getCinemaLevel());
        request.setLeaseMethod(params.getLeaseMethod());

        // 调用RPC服务
        MarketingPointLeasingDto.QueryPricesResponse response = marketingPointLeasingService.queryPrices(request);
        List resultList;
        // 转换响应结果
        if (response.getPriceInfos() != null && !response.getPriceInfos().isEmpty()) {
            resultList = response.getPriceInfos().stream().map(priceInfo -> {
                MarketingPointLeasingPriceModel.Info info = new MarketingPointLeasingPriceModel.Info();
                info.setCityLevel(priceInfo.getCityLevel());
                info.setCinemaLevel(priceInfo.getCinemaLevel());
                info.setLeaseMethod(priceInfo.getLeaseMethod());
                info.setBasePrice(stringToBigDecimal(priceInfo.getBasePrice()));
                return info;
            }).collect(Collectors.toList());
        } else {
            resultList = Collections.EMPTY_LIST;
        }
        log.info(">>>响应:查询营销点位租赁刊例价, request:{} result:{}", request, resultList);
        return resultList;
    }

}
