package cmc.pad.resource.admin.api.model.validation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2019/4/1 15:06
 * @Version 1.0
 */
class CinemaLevelValidator implements ConstraintValidator<CinemaLevel, String> {

    private static final Pattern numberPattern = Pattern.compile("^[a-zA-Z]$");

    @Override
    public void initialize(CinemaLevel constraintAnnotation) {
    }

    @Override
    public boolean isValid(String cinemaLevel, ConstraintValidatorContext constraintValidatorContext) {
        //为空不校验
        if (StringUtils.isBlank(cinemaLevel)) {
            return true;
        }
        Matcher match = numberPattern.matcher(cinemaLevel);
        return match.matches();
    }
}
