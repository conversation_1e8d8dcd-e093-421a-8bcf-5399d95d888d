
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 其他
 */

@Getter
@Setter
public class FMOthers {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    //名称
    @DSFiled(order = 5)
    private String name;
        
    //数量
    @DSFiled(order = 6)
    private String quantity;
        
    //单价
    @DSFiled(order = 7)
    private String unitPrice;
        
    //总价
    @DSFiled(order = 8)
    private String totalPrice;
        
    //起始日期
    @DSFiled(order = 9)
    private String startDate;
        

}
    