
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 包场
 */

@Getter
@Setter
public class FMWholeBookList {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    // 影厅
    @DSFiled(order = 5)
    private String hallName;
        
    //影厅号
    @DSFiled(order = 6)
    private String hallId;
        
    //影厅类型
    @DSFiled(order = 7)
    private String hallType;
        
    //影片名称
    @DSFiled(order = 8)
    private String filmName;
        
    //影片时间
    @DSFiled(order = 9)
    private String showTime;
        
    //场次
    @DSFiled(order = 10)
    private String sessionsCode;
        

}
    