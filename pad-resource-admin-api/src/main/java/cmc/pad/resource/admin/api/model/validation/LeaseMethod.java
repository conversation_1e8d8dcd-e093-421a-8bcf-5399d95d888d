package cmc.pad.resource.admin.api.model.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2019/4/2 18:36
 * @Version 1.0
 */

@Constraint(validatedBy = LeaseMethodValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LeaseMethod {
    String message() default "城市级别错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
