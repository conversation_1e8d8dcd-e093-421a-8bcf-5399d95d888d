package cmc.pad.resource.admin.api.model;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Builder
@Data
public class OccupationInfo {

    /**
     * Occupation的ID
     */
    private Integer occupationId;
    /**
     * 影院编码
     */
    private String cinemaCode;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 广告业务类型
     */
    private String businessType;
    /**
     * 库存占用状态
     */
    private String status;
    /**
     * 占用的面积或者个数，取决于广告业务类型
     */
    private Float amount;
    /**
     * 开始日期
     */
    private String startDate;
    /**
     * 结束日期
     */
    private String endDate;
}
