
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 售券
 */

@Getter
@Setter
public class FMVoucherList {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    //券类型名称
    @DSFiled(order = 5)
    private String voucherTypeName;
        
    //券类型编码
    @DSFiled(order = 6)
    private String voucherTypeCode;
        
    //券类型级别
    @DSFiled(order = 7)
    private String voucherTypeLevel;
        
    //券类别编码
    @DSFiled(order = 8)
    private String voucherCategoryCode;
        
    //券形式
    @DSFiled(order = 9)
    private String voucherForm;
        
    //券用途
    @DSFiled(order = 10)
    private String voucherPurpose;
        
    //券类型 1影票券 2卖品券
    @DSFiled(order = 11)
    private String voucherType;
        
    //券价值
    @DSFiled(order = 12)
    private String voucherCost;
        
    //数量
    @DSFiled(order = 13)
    private String quantity;
        
    //折扣率%
    @DSFiled(order = 14)
    private String discountRate;
        
    //总金额
    @DSFiled(order = 15)
    private String totalAmount;
        

}
    