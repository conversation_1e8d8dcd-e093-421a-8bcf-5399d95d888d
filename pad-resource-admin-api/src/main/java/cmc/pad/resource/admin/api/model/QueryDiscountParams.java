package cmc.pad.resource.admin.api.model;

import cmc.pad.resource.admin.api.model.validation.BusinessType;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class QueryDiscountParams {

    /**
     * 广告业务类型
     */
    @NotNull(message = "不能为空")
    private String businessType;

    /**
     * 折扣方式
     */
    private String discountMethod;

    /**
     * 需要计算折扣的面积
     */
    private Float area;

    /**
     * 需要计算折扣的时长
     */
    private Float duration;
}
