
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 售卡
 */

@Getter
@Setter
public class FMCardList {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    //卡类型名称
    @DSFiled(order = 5)
    private String cardTypeName;
        
    //卡类型编码
    @DSFiled(order = 6)
    private String cardTypeCode;
        
    //卡类型级别
    @DSFiled(order = 7)
    private String cardTypeLevel;
        
    //卡类别编码
    @DSFiled(order = 8)
    private String cardCategoryCode;
        
    //卡形式
    @DSFiled(order = 9)
    private String cardForm;
        
    //单张充值金额
    @DSFiled(order = 10)
    private String rechargeAmount;
        
    //工本费
    @DSFiled(order = 11)
    private String costAmount;
        
    //发卡数量
    @DSFiled(order = 12)
    private String cardCount;
        
    //总金额
    @DSFiled(order = 13)
    private String totalAmount;
        

}
    