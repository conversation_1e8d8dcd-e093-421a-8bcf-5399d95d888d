
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 收款计划
 */

@Getter
@Setter
public class FMCollectionPlan {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //收款条件
    @DSFiled(order = 2)
    private String condition;
        
    //收款类型
    @DSFiled(order = 3)
    private String type;
        
    //收款金额
    @DSFiled(order = 4)
    private String amount;
        
    //收款比例
    @DSFiled(order = 5)
    private String proportion;
        

}
    