
package cmc.pad.resource.admin.api.controller.bigdata;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.Getter;
import lombok.Setter;

/**
 * 外部资源租赁
 */

@Getter
@Setter
public class FMOuterResourceLease {

    
    //单据编号
    @DSFiled(order = 1)
    private String billNo;
        
    //影城名称
    @DSFiled(order = 2)
    private String cinemaName;
        
    //影城内码
    @DSFiled(order = 3)
    private String cinemaInnerCode;
        
    //业务事项
    @DSFiled(order = 4)
    private String businessItem;
        
    //基础刊例价（元/平米/月）
    @DSFiled(order = 5)
    private String basePublishPriceMeterMonth;
        
    //租赁面积
    @DSFiled(order = 6)
    private String leaseArea;
        
    //销售时长（月）
    @DSFiled(order = 7)
    private String saleDurationMonth;
        
    //起始日期
    @DSFiled(order = 8)
    private String startDate;
        
    //终止日期
    @DSFiled(order = 9)
    private String endDate;
        
    //收取方式
    @DSFiled(order = 10)
    private String collectMode;
        
    //计算基数
    @DSFiled(order = 11)
    private String calculateBase;
        
    //分成比例
    @DSFiled(order = 12)
    private String shardProportion;
        
    //保底总金额,
    @DSFiled(order = 13)
    private String guaranteedAmount;
        
    //成交价格
    @DSFiled(order = 14)
    private String tradedPrice;
        
    //折扣比例
    @DSFiled(order = 15)
    private String discountProportion;
        
    //已计提金额
    @DSFiled(order = 16)
    private String accruedAmount;
        
    //已开票金额
    @DSFiled(order = 17)
    private String invoicedAmount;
        
    //已认领合同金额
    @DSFiled(order = 18)
    private String claimedAmount;
        
    //已提成基数
    @DSFiled(order = 19)
    private String commissionBase;
        
    //资源类型编码
    @DSFiled(order = 20)
    private String resourceTypeCode;
        
    //资源类型
    @DSFiled(order = 21)
    private String resourceType;
        
    //资源归属
    @DSFiled(order = 22)
    private String resourceBelong;
        
    //资源编码
    @DSFiled(order = 23)
    private String resourceCode;
        

}
    