package cmc.pad.resource.admin.api.model;

import cmc.pad.resource.admin.api.model.validation.BusinessType;
import cmc.pad.resource.admin.api.model.validation.CinemaCode;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class QueryResourceParams {

    /**
     * 影院编码(内码)
     */
    @NotNull
    @CinemaCode
    private String cinemaCode;

    /**
     * 广告业务类型(仅支持营销点位租赁、外租区域租赁、固定点位租赁、宣传点位租赁四种业务类型)
     */
    @BusinessType
    private String businessType;
}
