package cmc.pad.resource.admin.api.model.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2019/4/1 15:05
 * @Version 1.0
 */
@Constraint(validatedBy = CinemaLevelValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CinemaLevel {
    String message() default "影城级别错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
