package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.PointLocationQueryDto;
import cmc.pad.resource.admin.service.iface.PointLocationQueryService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 点位查询服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PointLocationQueryServiceImpl implements PointLocationQueryService {

    private final cmc.pad.resource.application.query.point.PointLocationQueryService corePointLocationQueryService;

    @Autowired
    public PointLocationQueryServiceImpl(cmc.pad.resource.application.query.point.PointLocationQueryService corePointLocationQueryService) {
        this.corePointLocationQueryService = corePointLocationQueryService;
    }

    @Override
    public PointLocationQueryDto.QueryPointLocationUsableAreaResponse queryPointLocationUsableArea(
            PointLocationQueryDto.QueryPointLocationUsableAreaRequest request) {

        log.info(">>>查询点位可用面积数据, request:{}", request);
        try {
            // 调用核心服务
            PageResult<PointLocationModel.QueryPointLocationUsableAreaData> coreResult =
                    corePointLocationQueryService.query(
                            request.getPageIndex(),
                            request.getPageSize(),
                            request.getCode(),
                            request.getCinemaInnerCode(),
                            request.getBusinessTypeCode(),
                            request.getStart(),
                            request.getEnd()
                    );
            // 转换结果
            List<PointLocationQueryDto.PointLocationUsableAreaInfo> resultList =
                    coreResult.getItems().stream()
                            .map(this::convertToDto)
                            .collect(Collectors.toList());

            PointLocationQueryDto.PageResult pageResult = new PointLocationQueryDto.PageResult();
            pageResult.setItems(resultList);
            pageResult.setTotalCount(coreResult.getTotalCount());

            PointLocationQueryDto.QueryPointLocationUsableAreaResponse response =
                    new PointLocationQueryDto.QueryPointLocationUsableAreaResponse();
            response.setPageResult(pageResult);

            log.info(">>>查询点位可用面积数据完成, request:{}, response:{}", request, response);

            return response;
        } catch (Exception e) {
            log.error(">>>查询点位可用面积数据异常, request:{}", request, e);
            throw new FaultException(e.getMessage());
        }
    }

    /**
     * 转换核心模型到DTO
     */
    private PointLocationQueryDto.PointLocationUsableAreaInfo convertToDto(
            PointLocationModel.QueryPointLocationUsableAreaData data) {

        PointLocationQueryDto.PointLocationUsableAreaInfo info =
                new PointLocationQueryDto.PointLocationUsableAreaInfo();

        info.setId(data.getId());
        info.setCode(data.getCode());
        info.setUsableArea(data.getUsableArea());
        info.setBusinessTypeCode(data.getBusinessTypeCode());
        info.setCinemaInnerCode(data.getCinemaInnerCode());
        info.setResourceOwnershipCode(data.getResourceOwnershipCode());
        info.setLocationDesc(data.getLocationDesc());
        info.setPlanUse(data.getPlanUse());
        info.setLandingMode(data.getLandingMode());

        return info;
    }
}