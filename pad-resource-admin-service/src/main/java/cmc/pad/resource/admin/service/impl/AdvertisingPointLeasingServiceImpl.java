package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.AdvertisingPointLeasingDto;
import cmc.pad.resource.admin.service.iface.AdvertisingPointLeasingService;
import cmc.pad.resource.application.query.AdvertisingPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.data.AdvertisingPointPriceQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.AdvertisingPointLeasingPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 宣传点位租赁价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/01
 * @Version 1.0
 */
@Slf4j
@Service
public class AdvertisingPointLeasingServiceImpl extends AbstractLeasingPriceService implements AdvertisingPointLeasingService {

    private final AdvertisingPointLeasingPriceQueryService queryService;

    @Autowired
    public AdvertisingPointLeasingServiceImpl(AdvertisingPointLeasingPriceQueryService queryService,
                                              CinemaLevelQueryService cinemaLevelQueryService,
                                              MysqlCinemaRepository cinemaRepository,
                                              DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @Override
    public AdvertisingPointLeasingDto.QueryPricesResponse queryPrices(AdvertisingPointLeasingDto.QueryPricesRequest request) {
        logQueryStart("宣传点位租赁", request);

        // 处理查询参数
        String[] levels = processQueryParams(request.getCinemaCode(), request.getCityLevel(), request.getCinemaLevel());
        String cityDistrictLevel = levels[0];
        String cinemaLevel = levels[1];

        // 构建查询条件
        AdvertisingPointPriceQuery query = new AdvertisingPointPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);

        // 执行查询
        PageResult<AdvertisingPointLeasingPrice> pageResult = queryService.effectivePage(query);
        List<AdvertisingPointLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();

        // 构建响应结果
        AdvertisingPointLeasingDto.QueryPricesResponse response = new AdvertisingPointLeasingDto.QueryPricesResponse();
        if (totalCount > 0) {
            List<AdvertisingPointLeasingDto.PriceInfo> priceInfos = items.stream().map(this::convertToPriceInfo).collect(Collectors.toList());
            response.setPriceInfos(priceInfos);
        }

        logQueryResult("宣传点位租赁", request, response);
        return response;
    }

    /**
     * 转换为价格信息DTO
     */
    private AdvertisingPointLeasingDto.PriceInfo convertToPriceInfo(AdvertisingPointLeasingPrice price) {
        AdvertisingPointLeasingDto.PriceInfo info = new AdvertisingPointLeasingDto.PriceInfo();
        info.setCityLevel(price.getCityLevel());
        info.setCinemaLevel(price.getCinemaLevel());
        info.setBasePrice(centConvertYuan(price.getMinTotalPrice()));
        info.setBaseArea(price.getMinArea());
        info.setExtendedPrice(centConvertYuan(price.getExpandedUnitPrice()));
        return info;
    }

}
