package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.MovieHallNamingLeasingDto;
import cmc.pad.resource.admin.service.iface.MovieHallNamingLeasingService;
import cmc.pad.resource.application.AppError;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.MovieHallNamingLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.MovieHallNamingQuery;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 冠名厅租赁价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/08
 * @Version 1.0
 */
@Slf4j
@Service
public class MovieHallNamingLeasingServiceImpl extends AbstractLeasingPriceService implements MovieHallNamingLeasingService {

    private final MovieHallNamingLeasingPriceQueryService namingLeasingPriceQueryService;

    @Autowired
    public MovieHallNamingLeasingServiceImpl(MovieHallNamingLeasingPriceQueryService namingLeasingPriceQueryService,
                                             CinemaLevelQueryService cinemaLevelQueryService,
                                             MysqlCinemaRepository cinemaRepository,
                                             DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.namingLeasingPriceQueryService = namingLeasingPriceQueryService;
    }

    @Override
    public MovieHallNamingLeasingDto.QueryPricesResponse queryPrices(MovieHallNamingLeasingDto.QueryPricesRequest request) {
        log.info(">>>查询冠名厅租赁刊例价, 参数:{}", request.toString());

        // 参数验证
        validateCinemaCode(request.getCinemaCode());

        // 构建查询条件
        MovieHallNamingQuery query = buildQuery(request);

        // 查询精确匹配的刊例
        PageResult<MovieHallNamingPrice> primaryResult = namingLeasingPriceQueryService.effectivePage(query);
        if (hasValidResult(primaryResult)) {
            return buildResponse(primaryResult.getItems());
        }

        // 查询兜底刊例（未设置影厅类型的刊例）
        PageResult<MovieHallNamingPrice> fallbackResult = namingLeasingPriceQueryService.effectivePageBack(query);
        if (hasValidResult(fallbackResult)) {
            return buildResponse(fallbackResult.getItems());
        }

        // 没有找到任何数据
        return buildEmptyResponse();
    }

    /**
     * 验证影院编码
     *
     * @param cinemaCode 影院编码
     */
    private void validateCinemaCode(String cinemaCode) {
        Optional<Cinema> one = cinemaRepository.findOne(f(Cinema.C_CODE, cinemaCode));
        if (!one.isPresent()) {
            throw AppError.CINEMA_NOT_EXIST.toException();
        }
    }

    /**
     * 构建查询条件对象
     *
     * @param request 请求参数
     * @return 查询条件对象
     */
    private MovieHallNamingQuery buildQuery(MovieHallNamingLeasingDto.QueryPricesRequest request) {
        MovieHallNamingQuery query = new MovieHallNamingQuery();
        query.setCinemaCode(request.getCinemaCode());
        query.setMovieHallType(request.getMovieHallType());
        return query;
    }

    /**
     * 检查查询结果是否有效（包含数据）
     *
     * @param pageResult 分页查询结果
     * @return 是否有有效数据
     */
    private boolean hasValidResult(PageResult<MovieHallNamingPrice> pageResult) {
        return pageResult != null && pageResult.getTotalCount() > 0;
    }

    /**
     * 构建响应结果
     *
     * @param priceList 价格列表
     * @return 响应结果
     */
    private MovieHallNamingLeasingDto.QueryPricesResponse buildResponse(List<MovieHallNamingPrice> priceList) {
        MovieHallNamingLeasingDto.QueryPricesResponse response = new MovieHallNamingLeasingDto.QueryPricesResponse();
        List<MovieHallNamingLeasingDto.PriceInfo> priceInfos = priceList.stream()
                .map(this::convertToPriceInfo)
                .collect(Collectors.toList());
        response.setPriceInfos(priceInfos);
        return response;
    }

    /**
     * 构建空响应结果
     *
     * @return 空响应结果
     */
    private MovieHallNamingLeasingDto.QueryPricesResponse buildEmptyResponse() {
        MovieHallNamingLeasingDto.QueryPricesResponse response = new MovieHallNamingLeasingDto.QueryPricesResponse();
        response.setPriceInfos(Collections.emptyList());
        return response;
    }

    /**
     * 将领域对象转换为DTO对象
     *
     * @param price 冠名厅价格领域对象
     * @return DTO对象
     */
    private MovieHallNamingLeasingDto.PriceInfo convertToPriceInfo(MovieHallNamingPrice price) {
        MovieHallNamingLeasingDto.PriceInfo info = new MovieHallNamingLeasingDto.PriceInfo();
        info.setCinemaCode(price.getCinemaCode());
        info.setMovieHallType(price.getMovieHallType());
        info.setBasePrice(centConvertYuan(price.getUnitPrice()));
        return info;
    }

}