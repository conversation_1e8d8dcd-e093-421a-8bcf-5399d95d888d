package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.DiscountDto;
import cmc.pad.resource.admin.service.iface.DiscountService;
import cmc.pad.resource.domain.discount.*;
import java.util.Arrays;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cmc.pad.resource.application.AppError.*;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 折扣服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/10
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountServiceImpl implements DiscountService {

    private final DiscountRepository discountRepository;
    private final DiscountRuleRepository discountRuleRepository;


    @Override
    public DiscountDto.MatchCoefficientResponse matchCoefficient(DiscountDto.MatchCoefficientRequest request) {
        log.info(">>>RPC服务:匹配折扣系数, request:{}", request);

        // 参数校验
        validateRequest(request);

        List<DiscountDto.CoefficientInfo> coefficientInfoList = new ArrayList<>();
        String businessType = request.getBusinessType();
        String discountMethod = request.getDiscountMethod();

        if (!Strings.isNullOrEmpty(discountMethod)) {
            // 指定了折扣方式，只匹配该方式的系数
            DiscountDto.CoefficientInfo coefficientInfo = matchSingleCoefficient(businessType, discountMethod, request);
            coefficientInfoList.add(coefficientInfo);
        } else {
            // 未指定折扣方式，匹配所有可能的折扣方式
            coefficientInfoList = matchAllCoefficients(businessType, request);
        }

        // 构建响应结果
        DiscountDto.MatchCoefficientResponse response = new DiscountDto.MatchCoefficientResponse();
        response.setCoefficientInfos(coefficientInfoList);

        log.info(">>>RPC服务响应:匹配折扣系数, request:{} response:{}", request, response);
        return response;
    }

    /**
     * 参数校验
     */
    private void validateRequest(DiscountDto.MatchCoefficientRequest request) {
        if (StringUtils.isBlank(request.getBusinessType())) {
            throw DISCOUNT_BUSINESS_TYPE_NOT_NULL.toException();
        }

        String businessType = request.getBusinessType();
        if (!Strings.isNullOrEmpty(businessType) && 
                Arrays.stream(BusinessType.values())
                      .noneMatch(bt -> bt.name().equalsIgnoreCase(businessType))) {
            throw DISCOUNT_METHOD_NOT_SUPPORT.toException();
        }

        // 如果指定了折扣方式，需要校验对应的参数
        String discountMethod = request.getDiscountMethod();
        if (!Strings.isNullOrEmpty(discountMethod)) {
            if (DiscountType.DURATION.name().equalsIgnoreCase(discountMethod)) {
                if (request.getDuration() == 0f) {
                    throw DISCOUNT_DURATION_NOT_NULL.toException();
                }
            } else if (DiscountType.AREA.name().equalsIgnoreCase(discountMethod)) {
                if (request.getArea() == 0f) {
                    throw DISCOUNT_AREA_NOT_NULL.toException();
                }
            } else {
                throw DISCOUNT_METHOD_NOT_SUPPORT.toException();
            }
        } else {
            // 未指定折扣方式时，时长和面积不能同时为空
            if (request.getArea() == 0f && request.getDuration() == 0f) {
                throw DISCOUNT_DURATION_AREA_NOT_NULL.toException();
            }
        }
    }

    /**
     * 匹配单个折扣系数
     */
    private DiscountDto.CoefficientInfo matchSingleCoefficient(String businessType, String discountMethod, 
                                                               DiscountDto.MatchCoefficientRequest request) {
        int discountType;
        float amount;
        
        if (DiscountType.DURATION.name().equalsIgnoreCase(discountMethod)) {
            discountType = 1;
            amount = request.getDuration();
        } else if (DiscountType.AREA.name().equalsIgnoreCase(discountMethod)) {
            discountType = 2;
            amount = request.getArea();
        } else {
            throw DISCOUNT_METHOD_NOT_SUPPORT.toException();
        }

        float coefficient = matchCoefficient(businessType, discountType, amount);
        
        DiscountDto.CoefficientInfo coefficientInfo = new DiscountDto.CoefficientInfo();
        coefficientInfo.setDiscountMethod(discountMethod);
        coefficientInfo.setCoefficient(coefficient == 0f ? 1f : coefficient);
        
        return coefficientInfo;
    }

    /**
     * 匹配所有可能的折扣系数
     */
    private List<DiscountDto.CoefficientInfo> matchAllCoefficients(String businessType, 
                                                                   DiscountDto.MatchCoefficientRequest request) {
        List<DiscountDto.CoefficientInfo> coefficientInfoList = new ArrayList<>();

        // 匹配时长折扣
        if (request.getDuration() != 0f) {
            float coefficient = matchCoefficient(businessType, 1, request.getDuration());
            DiscountDto.CoefficientInfo coefficientInfo = new DiscountDto.CoefficientInfo();
            coefficientInfo.setDiscountMethod(DiscountType.DURATION.name());
            coefficientInfo.setCoefficient(coefficient == 0f ? 1f : coefficient);
            coefficientInfoList.add(coefficientInfo);
        }

        // 匹配面积折扣
        if (request.getArea() != 0f) {
            float coefficient = matchCoefficient(businessType, 2, request.getArea());
            DiscountDto.CoefficientInfo coefficientInfo = new DiscountDto.CoefficientInfo();
            coefficientInfo.setDiscountMethod(DiscountType.AREA.name());
            coefficientInfo.setCoefficient(coefficient == 0f ? 1f : coefficient);
            coefficientInfoList.add(coefficientInfo);
        }

        if (coefficientInfoList.isEmpty()) {
            throw DISCOUNT_FACTOR_NOT_MATCHED.toException();
        }

        return coefficientInfoList;
    }

    /**
     * 匹配折扣系数的核心逻辑
     */
    private float matchCoefficient(String businessType, int discountType, float amount) {
        Filter filter = Filter.create("business_type", businessType);
        Optional<Discount> optional = discountRepository.findOne(filter.and(f("discount_type", discountType)));
        
        final float[] factor = {0f};
        optional.ifPresent(discount -> {
            List<DiscountRule> rules = discountRuleRepository.findMany(Filter.create("discount_id", discount.getId()));
            BigDecimal compared = new BigDecimal(Float.toString(amount));
            
            for (DiscountRule rule : rules) {
                if (rule.getComparisonSymbol() == 1) { // 区间比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) >= 0
                            && compared.compareTo(new BigDecimal(rule.getMax().toString())) < 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                } else if (rule.getComparisonSymbol() == 2) { // 等值==比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) == 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                } else if (rule.getComparisonSymbol() == 3) { // <=比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) <= 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                } else if (rule.getComparisonSymbol() == 4) { // >=比较
                    if (compared.compareTo(new BigDecimal(rule.getMin().toString())) >= 0) {
                        factor[0] = rule.getFactor();
                        break;
                    }
                }
            }
        });
        
        return factor[0];
    }
}