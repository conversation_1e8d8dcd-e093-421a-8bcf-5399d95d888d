package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.application.AppError;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 租赁价格服务抽象基类
 * 提炼通用功能，减少代码重复
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractLeasingPriceService {

    protected final CinemaLevelQueryService cinemaLevelQueryService;
    protected final MysqlCinemaRepository cinemaRepository;
    protected final DictionaryDomainService dictionaryDomainService;

    @Autowired
    public AbstractLeasingPriceService(CinemaLevelQueryService cinemaLevelQueryService,
                                     MysqlCinemaRepository cinemaRepository,
                                     DictionaryDomainService dictionaryDomainService) {
        this.cinemaLevelQueryService = cinemaLevelQueryService;
        this.cinemaRepository = cinemaRepository;
        this.dictionaryDomainService = dictionaryDomainService;
    }

    /**
     * 处理查询参数，获取城市级别和影城级别
     *
     * @param cinemaCode 影城编码
     * @param cityLevel 城市级别
     * @param cinemaLevel 影城级别
     * @return 处理后的级别信息 [cityLevel, cinemaLevel]
     */
    protected String[] processQueryParams(String cinemaCode, String cityLevel, String cinemaLevel) {
        String cityDistrictLevel = cityLevel;
        String processedCinemaLevel = cinemaLevel;

        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            processedCinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            validateCityDistrictLevel(cityDistrictLevel);
            validateCinemaLevel(processedCinemaLevel);
        }

        return new String[]{cityDistrictLevel, processedCinemaLevel};
    }

    protected String[] processQueryParamsV2(String cinemaCode, String cityLevel, String cinemaLevel) {
        String cityDistrictLevel = cityLevel;
        String processedCinemaLevel = cinemaLevel;

        if (StringUtils.isNotBlank(cinemaCode)) {
            CinemaLevelInfo cinemaLevelInfo = getCinemaLevel(cinemaCode);
            processedCinemaLevel = cinemaLevelInfo.getCinemaLevel();
            cityDistrictLevel = cinemaLevelInfo.getCityDistrictLevel();
        } else {
            if (StringUtils.isNotBlank(cityDistrictLevel)) {
                validateCityDistrictLevel(cityDistrictLevel);
            }
            if (StringUtils.isNotBlank(processedCinemaLevel)) {
                validateCinemaLevel(processedCinemaLevel);
            }
        }

        return new String[]{cityDistrictLevel, processedCinemaLevel};
    }

//    protected float centConvertYuan(Integer cent) {
//        if (cent == null) {
//            return 0f;
//        }
//        return cent / 100.0f;
//    }

    protected String centConvertYuan(Integer money) {
        money = money < 0 ? 0 : money;
        BigDecimal yuan = new BigDecimal(money).divide(new BigDecimal(100));
        
        // 先设置为2位小数
        yuan = yuan.setScale(2, BigDecimal.ROUND_HALF_UP);
        
        // 如果小数点后两位都是0（如20.00），只保留一位小数（20.0）
        if (yuan.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
            // 整数部分，保留一位小数 xx.0
            return yuan.setScale(1, BigDecimal.ROUND_HALF_UP).toString();
//        } else if (yuan.multiply(BigDecimal.TEN).remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
//            // 小数点后第二位是0，去掉末尾的0
//            return yuan.stripTrailingZeros();
        } else {
            // 小数点后两位都不是0，保持原样
            return yuan.toString();
        }
    }

    public static BigDecimal centConvertYuan(int money) {
        money = money < 0 ? 0 : money;
        BigDecimal yuan = new BigDecimal(money).divide(new BigDecimal(100));
        // 保留两位小数，精确到分
        return yuan.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取影城级别信息
     *
     * @param cinemaCode 影城编码
     * @return 影城级别信息
     */
    protected CinemaLevelInfo getCinemaLevel(String cinemaCode) {
        CinemaLevelInfo cinema = cinemaLevelQueryService.getCinema(cinemaCode);
        log.info(">>>影城内码:{}, 影城信息:{}", cinemaCode, cinema);
        if (Objects.isNull(cinema)) {
            throw AppError.CINEMA_LEVEL_NOT_CONFIG.toException();
        }
        String cityDistrictLevel = cinema.getCityDistrictLevel();
        if (StringUtils.isBlank(cityDistrictLevel)) {
            throw AppError.CITY_DISTRICT_NO_LEVEL.toException();
        }
        String cinemaLevel = cinema.getCinemaLevel();
        if (StringUtils.isBlank(cinemaLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_CONFIG.toException();
        }
        return cinema;
    }

    /**
     * 验证影城级别
     *
     * @param cinemaLevel 影城级别
     */
    protected void validateCinemaLevel(String cinemaLevel) {
        List<String> levelCodes = dictionaryDomainService.allCinemaLevelCodes();
        if (!levelCodes.contains(cinemaLevel)) {
            throw AppError.CINEMA_LEVEL_NOT_EXIST.toException();
        }
    }

    /**
     * 验证城市级别
     *
     * @param cityLevel 城市级别
     */
    protected void validateCityDistrictLevel(String cityLevel) {
        List<String> codes = dictionaryDomainService.allCityLevelCodes();
        if (!codes.contains(cityLevel)) {
            throw AppError.CITY_LEVEL_NOT_EXIST.toException();
        }
    }

    /**
     * 记录查询日志
     *
     * @param serviceName 服务名称
     * @param request 请求参数
     */
    protected void logQueryStart(String serviceName, Object request) {
        log.info(">>>查询{}刊例价, {}", serviceName, request);
    }

    /**
     * 记录查询结果日志
     *
     * @param serviceName 服务名称
     * @param request 请求参数
     * @param response 响应结果
     */
    protected void logQueryResult(String serviceName, Object request, Object response) {
        log.info(">>>查询{}刊例价, request:{} result:{}", serviceName, request, response);
    }
}
