package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.MovieHallSeatLeasingDto;
import cmc.pad.resource.admin.service.iface.MovieHallSeatLeasingService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.MovieHallSeatLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.MovieHallSeatPriceQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 影厅座位租赁价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/09
 * @Version 1.0
 */
@Slf4j
@Service
public class MovieHallSeatLeasingServiceImpl extends AbstractLeasingPriceService implements MovieHallSeatLeasingService {

    private final MovieHallSeatLeasingPriceQueryService queryService;

    @Autowired
    public MovieHallSeatLeasingServiceImpl(MovieHallSeatLeasingPriceQueryService queryService,
                                           CinemaLevelQueryService cinemaLevelQueryService,
                                           MysqlCinemaRepository cinemaRepository,
                                           DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @Override
    public MovieHallSeatLeasingDto.QueryPricesResponse queryPrices(MovieHallSeatLeasingDto.QueryPricesRequest request) {
        logQueryStart("影厅座位租赁", request);

        // 处理查询参数
        String[] levels = processQueryParams(request.getCinemaCode(), request.getCityLevel(), request.getCinemaLevel());
        String cityDistrictLevel = levels[0];
        String cinemaLevel = levels[1];
        String movieHallType = request.getMovieHallType();

        // 验证影厅类型
        if (StringUtils.isNotBlank(movieHallType)) {
            validateMovieHallType(movieHallType);
        }

        // 构建查询条件
        MovieHallSeatPriceQuery query = new MovieHallSeatPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);
        query.setMovieHallType(movieHallType);

        // 执行查询
        PageResult<MovieHallSeatLeasingPrice> pageResult = queryService.effectivePage(query);
        List<MovieHallSeatLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();

        // 构建响应结果
        MovieHallSeatLeasingDto.QueryPricesResponse response = new MovieHallSeatLeasingDto.QueryPricesResponse();
        if (totalCount > 0) {
            List<MovieHallSeatLeasingDto.PriceInfo> priceInfos = items.stream().map(this::convertToPriceInfo).collect(Collectors.toList());
            response.setPriceInfos(priceInfos);
        } else {
            // 查询兜底的刊例，返回不指定具体影厅的刊例
            PageResult<MovieHallSeatLeasingPrice> result = queryService.effectivePageBack(query);
            if (result.getTotalCount() > 0) {
                List<MovieHallSeatLeasingDto.PriceInfo> priceInfos = result.getItems().stream().map(this::convertToPriceInfo).collect(Collectors.toList());
                response.setPriceInfos(priceInfos);
            }
        }

        logQueryResult("影厅座位租赁", request, response);
        return response;
    }

    /**
     * 验证影厅类型
     *
     * @param movieHallType 影厅类型
     */
    private void validateMovieHallType(String movieHallType) {
        List<String> codes = dictionaryDomainService.allMovieHallTypeCodes();
        if (!codes.contains(movieHallType)) {
            throw cmc.pad.resource.application.AppError.MOVIE_HALL_TYPE_NOT_EXIST.toException();
        }
    }

    /**
     * 转换为价格信息DTO
     */
    private MovieHallSeatLeasingDto.PriceInfo convertToPriceInfo(MovieHallSeatLeasingPrice price) {
        MovieHallSeatLeasingDto.PriceInfo info = new MovieHallSeatLeasingDto.PriceInfo();
        info.setCityLevel(price.getCityLevel());
        info.setCinemaLevel(price.getCinemaLevel());
        info.setMovieHallType(price.getMovieHallType());
        info.setBasePrice(centConvertYuan(price.getMinTotalPrice()));
        info.setBaseDuration(price.getMinHours());
        info.setExtendedPrice(centConvertYuan(price.getExpandedUnitPrice()));
        return info;
    }

}