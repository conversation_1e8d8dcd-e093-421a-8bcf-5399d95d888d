package cmc.pad.resource.admin.service.util;

import com.google.common.base.Strings;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2019/4/3 14:55
 * @Version 1.0
 */
@Getter
public enum LeaseMethod {

    AREA("AREA", "按面积"),
    QUANTITY("QUANTITY", "按照数量");

    private String code;
    private String message;

    LeaseMethod(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static LeaseMethod valueByCode(String code) {
        if (!Strings.isNullOrEmpty(code)) {
            for (LeaseMethod leaseMethod : LeaseMethod.values()) {
                if (leaseMethod.code.equals(code.trim().toUpperCase())) {
                    return leaseMethod;
                }
            }
        }
        return null;
    }
}
