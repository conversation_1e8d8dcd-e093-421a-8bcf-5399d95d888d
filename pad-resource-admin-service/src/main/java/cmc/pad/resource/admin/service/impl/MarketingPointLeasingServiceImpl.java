package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.MarketingPointLeasingDto;
import cmc.pad.resource.admin.service.iface.MarketingPointLeasingService;
import cmc.pad.resource.admin.service.util.LeaseMethod;
import cmc.pad.resource.application.AppError;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.MarketingPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.MarketingPointQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.MarketingPointLeasingPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 营销点位租赁价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/05
 * @Version 1.0
 */
@Slf4j
@Service
public class MarketingPointLeasingServiceImpl extends AbstractLeasingPriceService implements MarketingPointLeasingService {

    private final MarketingPointLeasingPriceQueryService queryService;

    @Autowired
    public MarketingPointLeasingServiceImpl(MarketingPointLeasingPriceQueryService queryService,
                                            CinemaLevelQueryService cinemaLevelQueryService,
                                            MysqlCinemaRepository cinemaRepository,
                                            DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @Override
    public MarketingPointLeasingDto.QueryPricesResponse queryPrices(MarketingPointLeasingDto.QueryPricesRequest request) {
        logQueryStart("营销点位租赁", request);

        // 处理查询参数
        String[] levels = processQueryParams(request.getCinemaCode(), request.getCityLevel(), request.getCinemaLevel());
        String cityDistrictLevel = levels[0];
        String cinemaLevel = levels[1];
        String leaseMethod = request.getLeaseMethod();
        if (StringUtils.isBlank(request.getCinemaCode())) {
            // 验证租赁方式
            validateLeaseMethod(leaseMethod);
        }

        // 构建查询条件
        MarketingPointQuery query = new MarketingPointQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);

        // 执行查询
        PageResult<MarketingPointLeasingPrice> pageResult = queryService.effectivePage(query);
        List<MarketingPointLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();
        LeaseMethod leaseMethodEnum = LeaseMethod.valueByCode(leaseMethod);

        // 构建响应结果
        MarketingPointLeasingDto.QueryPricesResponse response = new MarketingPointLeasingDto.QueryPricesResponse();
        if (totalCount > 0) {
            List<MarketingPointLeasingDto.PriceInfo> priceInfos = new ArrayList<>();

            for (MarketingPointLeasingPrice item : items) {
                // 根据租赁方式返回对应的价格信息
                if (StringUtils.isBlank(leaseMethod)) {
                    // 如果没有指定租赁方式，返回两种方式的价格
                    addPriceInfo(priceInfos, item, "AREA", item.getUnitPriceByArea());
                    addPriceInfo(priceInfos, item, "QUANTITY", item.getUnitPriceByQuantity());
                } else if (LeaseMethod.AREA == leaseMethodEnum) {
                    addPriceInfo(priceInfos, item, "AREA", item.getUnitPriceByArea());
                } else if (LeaseMethod.QUANTITY == leaseMethodEnum) {
                    addPriceInfo(priceInfos, item, "QUANTITY", item.getUnitPriceByQuantity());
                }
            }

            response.setPriceInfos(priceInfos.stream().sorted(Comparator.comparing(MarketingPointLeasingDto.PriceInfo::getBasePrice).reversed()).collect(Collectors.toList()));
        }

        logQueryResult("营销点位租赁", request, response);
        return response;
    }

    /**
     * 添加价格信息
     *
     * @param priceInfos  价格信息列表
     * @param item        营销点位租赁价格
     * @param leaseMethod 租赁方式
     * @param unitPrice   单价（分）
     */
    private void addPriceInfo(List<MarketingPointLeasingDto.PriceInfo> priceInfos,
                              MarketingPointLeasingPrice item,
                              String leaseMethod,
                              Integer unitPrice) {
        MarketingPointLeasingDto.PriceInfo priceInfo = new MarketingPointLeasingDto.PriceInfo();
        priceInfo.setCityLevel(item.getCityLevel());
        priceInfo.setCinemaLevel(item.getCinemaLevel());
        priceInfo.setLeaseMethod(leaseMethod);
        priceInfo.setBasePrice(centConvertYuan(unitPrice));
        priceInfos.add(priceInfo);
    }

    /**
     * 验证租赁方式
     *
     * @param leaseMethod 租赁方式
     */
    private void validateLeaseMethod(String leaseMethod) {
        LeaseMethod method = LeaseMethod.valueByCode(leaseMethod);
        if (Objects.isNull(method)) {
            throw AppError.LEASING_METHOD_NOT_EXIST.toException();
        }
    }
}
