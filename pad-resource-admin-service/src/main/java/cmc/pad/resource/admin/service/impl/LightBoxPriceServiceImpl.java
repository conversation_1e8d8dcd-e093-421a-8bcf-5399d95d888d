package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.LightBoxPriceDto;
import cmc.pad.resource.admin.service.iface.LightBoxPriceService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.LightBoxQueryService;
import cmc.pad.resource.application.query.data.PriceQueryParam;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.LightBoxPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 灯箱价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/04
 * @Version 1.0
 */
@Slf4j
@Service
public class LightBoxPriceServiceImpl extends AbstractLeasingPriceService implements LightBoxPriceService {

    private final LightBoxQueryService queryService;

    @Autowired
    public LightBoxPriceServiceImpl(LightBoxQueryService queryService,
                                    CinemaLevelQueryService cinemaLevelQueryService,
                                    MysqlCinemaRepository cinemaRepository,
                                    DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @Override
    public LightBoxPriceDto.QueryPricesResponse queryPrices(LightBoxPriceDto.QueryPricesRequest request) {
        logQueryStart("灯箱价格", request);

        // 处理查询参数
        String[] levels = processQueryParamsV2(request.getCinemaCode(), request.getCityLevel(), request.getCinemaLevel());
        String cityDistrictLevel = levels[0];
        String cinemaLevel = levels[1];

        // 构建查询条件
        PriceQueryParam query = new PriceQueryParam();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);

        // 执行查询
        PageResult<LightBoxPrice> pageResult = queryService.effectivePage(query);
        List<LightBoxPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();

        // 构建响应结果
        LightBoxPriceDto.QueryPricesResponse response = new LightBoxPriceDto.QueryPricesResponse();
        if (totalCount > 0) {
            List<LightBoxPriceDto.PriceInfo> priceInfos = items.stream().map(this::convertToPriceInfo).collect(Collectors.toList());
            response.setPriceInfos(priceInfos);
        }

        logQueryResult("灯箱价格", request, response);
        return response;
    }

    /**
     * 转换为价格信息DTO
     */
    private LightBoxPriceDto.PriceInfo convertToPriceInfo(LightBoxPrice price) {
        LightBoxPriceDto.PriceInfo info = new LightBoxPriceDto.PriceInfo();
        info.setCityLevel(price.getCityLevel());
        info.setCinemaLevel(price.getCinemaLevel());
        info.setBasePrice(centConvertYuan(price.getMinTotalPrice()));
        info.setBaseArea(price.getMinArea());
        info.setExtendedPrice(centConvertYuan(price.getExpandedUnitPrice()));
        return info;
    }

}
