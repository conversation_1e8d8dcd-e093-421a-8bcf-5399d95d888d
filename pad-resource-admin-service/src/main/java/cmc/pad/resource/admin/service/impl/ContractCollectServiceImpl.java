package cmc.pad.resource.admin.service.impl;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.admin.service.dto.ContractCollectDto;
import cmc.pad.resource.admin.service.iface.ContractCollectService;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.PublishUtil;
import cmc.pad.resource.domain.contract.ContractMsg;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import static cmc.pad.resource.constant.TopicName.CONTRACT_SAVE_AND_TRANSFER_COLLECT_PLAN_TOPIC;

/**
 * Created by fuwei on 2024/3/21.
 */
@Slf4j
@Service
public class ContractCollectServiceImpl implements ContractCollectService {

    @Override
    public void receiveDataAndSendMsg(ContractCollectDto.ReceiveDataAndSendMsgRequest request) {
        log.info(">>>收集合同数据:{}", JSON.toJSONString(request.getContract()));
        List<ContractMsg> contracts = CopyUtil.listCopy(request.getContract(), ContractMsg.class);
        contracts.forEach(contractMsg -> PublishUtil.send(CONTRACT_SAVE_AND_TRANSFER_COLLECT_PLAN_TOPIC, contractMsg));
    }
}