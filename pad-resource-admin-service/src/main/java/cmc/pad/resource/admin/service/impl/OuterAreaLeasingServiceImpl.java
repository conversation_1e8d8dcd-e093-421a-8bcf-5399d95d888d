package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.OuterAreaLeasingDto;
import cmc.pad.resource.admin.service.iface.OuterAreaLeasingService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.OuterAreaLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.OuterAreaPriceQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 外租区域租赁价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/09
 * @Version 2.0
 */
@Slf4j
@Service
public class OuterAreaLeasingServiceImpl extends AbstractLeasingPriceService implements OuterAreaLeasingService {

    private final OuterAreaLeasingPriceQueryService queryService;

    @Autowired
    public OuterAreaLeasingServiceImpl(OuterAreaLeasingPriceQueryService queryService,
                                       CinemaLevelQueryService cinemaLevelQueryService,
                                       MysqlCinemaRepository cinemaRepository,
                                       DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @Override
    public OuterAreaLeasingDto.QueryPricesResponse queryPrices(OuterAreaLeasingDto.QueryPricesRequest request) {
        logQueryStart("外租区域租赁", request);

        // 处理查询参数
        String[] levels = processQueryParams(request.getCinemaCode(), request.getCityLevel(), request.getCinemaLevel());
        String cityDistrictLevel = levels[0];
        String cinemaLevel = levels[1];

        // 构建查询条件
        OuterAreaPriceQuery query = new OuterAreaPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);

        // 执行查询
        PageResult<OuterAreaLeasingPrice> pageResult = queryService.effectivePage(query);
        List<OuterAreaLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();

        // 构建响应结果
        OuterAreaLeasingDto.QueryPricesResponse response = new OuterAreaLeasingDto.QueryPricesResponse();
        if (totalCount > 0) {
            List<OuterAreaLeasingDto.PriceInfo> priceInfos = items.stream().map(this::convertToPriceInfo).collect(Collectors.toList());
            response.setPriceInfos(priceInfos);
        }

        logQueryResult("外租区域租赁", request, response);
        return response;
    }

    /**
     * 转换为价格信息DTO
     */
    private OuterAreaLeasingDto.PriceInfo convertToPriceInfo(OuterAreaLeasingPrice price) {
        OuterAreaLeasingDto.PriceInfo info = new OuterAreaLeasingDto.PriceInfo();
        info.setCityLevel(price.getCityLevel());
        info.setCinemaLevel(price.getCinemaLevel());
        // 处理Long类型的价格转换
        Integer unitPrice = price.getUnitPrice() != null ? price.getUnitPrice().intValue() : 0;
        info.setBasePrice(centConvertYuan(unitPrice));
        return info;
    }

}