package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.FixedPointLeasingDto;
import cmc.pad.resource.admin.service.iface.FixedPointLeasingService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.FixedPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.FixedPointPriceQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.price.FixedPointLeasingPrice;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 固定点位租赁价格服务实现
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
@Slf4j
@Service
public class FixedPointLeasingServiceImpl extends AbstractLeasingPriceService implements FixedPointLeasingService {

    private final FixedPointLeasingPriceQueryService queryService;

    @Autowired
    public FixedPointLeasingServiceImpl(FixedPointLeasingPriceQueryService queryService,
                                        CinemaLevelQueryService cinemaLevelQueryService,
                                        MysqlCinemaRepository cinemaRepository,
                                        DictionaryDomainService dictionaryDomainService) {
        super(cinemaLevelQueryService, cinemaRepository, dictionaryDomainService);
        this.queryService = queryService;
    }

    @Override
    public FixedPointLeasingDto.QueryPricesResponse queryPrices(FixedPointLeasingDto.QueryPricesRequest request) {
        logQueryStart("固定点位租赁", request);

        // 处理查询参数
        String[] levels = processQueryParams(request.getCinemaCode(), request.getCityLevel(), request.getCinemaLevel());
        String cityDistrictLevel = levels[0];
        String cinemaLevel = levels[1];

        // 构建查询条件
        FixedPointPriceQuery query = new FixedPointPriceQuery();
        query.setCityLevel(cityDistrictLevel);
        query.setCinemaLevel(cinemaLevel);

        // 执行查询
        PageResult<FixedPointLeasingPrice> pageResult = queryService.effectivePage(query);
        List<FixedPointLeasingPrice> items = pageResult.getItems();
        int totalCount = pageResult.getTotalCount();

        // 构建响应结果
        FixedPointLeasingDto.QueryPricesResponse response = new FixedPointLeasingDto.QueryPricesResponse();
        if (totalCount > 0) {
            List<FixedPointLeasingDto.PriceInfo> priceInfos = items.stream().map(this::convertToPriceInfo).collect(Collectors.toList());
            response.setPriceInfos(priceInfos);
        }

        logQueryResult("固定点位租赁", request, response);
        return response;
    }

    /**
     * 转换为价格信息DTO
     */
    private FixedPointLeasingDto.PriceInfo convertToPriceInfo(FixedPointLeasingPrice price) {
        FixedPointLeasingDto.PriceInfo info = new FixedPointLeasingDto.PriceInfo();
        info.setCityLevel(price.getCityLevel());
        info.setCinemaLevel(price.getCinemaLevel());
        info.setBasePrice(centConvertYuan(price.getUnitPrice()));
        return info;
    }

}
