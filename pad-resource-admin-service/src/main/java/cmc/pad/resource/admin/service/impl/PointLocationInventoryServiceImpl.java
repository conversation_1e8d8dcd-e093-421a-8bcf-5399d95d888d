package cmc.pad.resource.admin.service.impl;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.admin.service.dto.PointLocationInventoryDto;
import cmc.pad.resource.admin.service.iface.PointLocationInventoryService;
import cmc.pad.resource.application.command.point.inventory.occupy.PointInventoryOccupationService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 点位库存管理服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PointLocationInventoryServiceImpl implements PointLocationInventoryService {

    private final PointInventoryOccupationService pointInventoryOccupationService;

    @Autowired
    public PointLocationInventoryServiceImpl(PointInventoryOccupationService pointInventoryOccupationService) {
        this.pointInventoryOccupationService = pointInventoryOccupationService;
    }

    @Override
    public PointLocationInventoryDto.OccupyResponse occupy(PointLocationInventoryDto.OccupyRequest request) {
        log.info(">>>RPC服务-提交合同库存占用, request:{}", request);
        
        try {
            // 转换DTO到领域模型
            PointLocationModel.InventoryOccupationContractParam param = convertToOccupationParam(request);
            
            // 调用核心业务服务
            pointInventoryOccupationService.occupy(param);
            
            // 构造响应
            PointLocationInventoryDto.OccupyResponse response = new PointLocationInventoryDto.OccupyResponse();
            response.setSuccess(true);
            
            log.info(">>>RPC服务-提交合同库存占用完成, request:{}, response:{}", request, response);
            return response;
            
        } catch (Exception e) {
            log.error(">>>RPC服务-提交合同库存占用异常, request:{}", request, e);
            throw new FaultException(e.getMessage());
        }
    }

    @Override
    public PointLocationInventoryDto.UpdateStatusResponse updateStatus(PointLocationInventoryDto.UpdateStatusRequest request) {
        log.info(">>>RPC服务-更新库存状态, request:{}", request);
        
        try {
            // 转换DTO到领域模型
            PointLocationModel.UpdateStatusParam param = convertToUpdateStatusParam(request);
            
            // 调用核心业务服务
            pointInventoryOccupationService.updateStatus(param);
            
            // 构造响应
            PointLocationInventoryDto.UpdateStatusResponse response = new PointLocationInventoryDto.UpdateStatusResponse();
            response.setSuccess(true);
            
            log.info(">>>RPC服务-更新库存状态完成, request:{}, response:{}", request, response);
            return response;
            
        } catch (Exception e) {
            log.error(">>>RPC服务-更新库存状态异常, request:{}", request, e);
            throw new FaultException(e.getMessage());
        }
    }

    /**
     * 转换占用请求DTO到领域模型
     */
    private PointLocationModel.InventoryOccupationContractParam convertToOccupationParam(
            PointLocationInventoryDto.OccupyRequest request) {
        
        PointLocationModel.InventoryOccupationContractParam param = 
                new PointLocationModel.InventoryOccupationContractParam();
        
        param.setContractType(request.getContractType());
        param.setContractNo(request.getContractNo());
        param.setBusinessTypeCode(request.getBusinessTypeCode());
        
        // 转换明细列表
        if (request.getDetails() != null) {
            List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> details = 
                    request.getDetails().stream()
                            .map(this::convertToOccupationDetail)
                            .collect(Collectors.toList());
            param.setDetails(details);
        }
        
        return param;
    }

    /**
     * 转换占用明细DTO到领域模型
     */
    private PointLocationModel.InventoryOccupationContractParam.OccupationDetail convertToOccupationDetail(
            PointLocationInventoryDto.OccupationDetail dto) {
        
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail = 
                new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        
        detail.setCinemaInnerCode(dto.getCinemaInnerCode());
        detail.setPointLocationId(dto.getPointLocationId());
        detail.setAmount(dto.getAmount());
        detail.setStartDate(dto.getStartDate());
        detail.setEndDate(dto.getEndDate());
        detail.setId(dto.getId());
        detail.setStatus(dto.getStatus());
        
        return detail;
    }

    /**
     * 转换更新状态请求DTO到领域模型
     */
    private PointLocationModel.UpdateStatusParam convertToUpdateStatusParam(
            PointLocationInventoryDto.UpdateStatusRequest request) {
        
        PointLocationModel.UpdateStatusParam param = new PointLocationModel.UpdateStatusParam();
        param.setContractNo(request.getContractNo());
        param.setStatus(request.getStatus());
        
        return param;
    }
}
