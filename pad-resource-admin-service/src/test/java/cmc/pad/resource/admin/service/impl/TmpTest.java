package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.domain.discount.BusinessType;
import org.junit.Test;

import java.util.Arrays;

public class TmpTest {
    @Test
    public void test() {
        String[] businessTypes = {"YX", "WZ", "GD", "XC", "YT", "GMT", "QT", "CMDX", "WBZY", "TEST"};
        System.out.println(BusinessType.values()[1].name());
        for (String businessType : businessTypes) {
            System.out.println(
                    Arrays.stream(BusinessType.values())
                            .anyMatch(bt -> bt.name().equalsIgnoreCase(businessType))
            );
        }
    }
}
