package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.FixedPointLeasingDto;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * 固定点位租赁价格服务实现测试
 *
 * <AUTHOR>
 * @Date 2025/09/02
 * @Version 1.0
 */
public class FixedPointLeasingServiceImplTest extends AbstractCmcQasTest {
    FixedPointLeasingServiceImpl fixedPointLeasingService = ServiceLocator.current().getInstance(FixedPointLeasingServiceImpl.class);

    @Test
    public void queryPricesWithCinemaCode() {
        FixedPointLeasingDto.QueryPricesRequest request = new FixedPointLeasingDto.QueryPricesRequest();
        request.setCinemaCode("304");
        FixedPointLeasingDto.QueryPricesResponse response = fixedPointLeasingService.queryPrices(request);
        System.out.println("Response: " + response);
    }

    @Test
    public void queryPricesWithCityAndCinemaLevel() {
        FixedPointLeasingDto.QueryPricesRequest request = new FixedPointLeasingDto.QueryPricesRequest();
        request.setCityLevel("L1");
        request.setCinemaLevel("A");
        FixedPointLeasingDto.QueryPricesResponse response = fixedPointLeasingService.queryPrices(request);
        System.out.println("Response: " + response);
    }
}
