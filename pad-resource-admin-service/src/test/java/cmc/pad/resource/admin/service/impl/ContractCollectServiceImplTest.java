package cmc.pad.resource.admin.service.impl;

import cmc.pad.resource.admin.service.dto.ContractCollectDto;
import cmc.pad.resource.admin.service.dto.ContractCollectDto.Contract;
import cmc.pad.resource.admin.service.iface.ContractCollectService;
import com.google.common.collect.Lists;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Created by fuwei on 2024/3/21.
 */
public class ContractCollectServiceImplTest extends AbstractCmcQasTest {
    ContractCollectService service = ServiceLocator.current().getInstance(ContractCollectServiceImpl.class);

    @Test
    public void testCollectDataAndSendMsg() {
        ContractCollectDto.ReceiveDataAndSendMsgRequest request = new ContractCollectDto.ReceiveDataAndSendMsgRequest();
        request.setContract(Lists.newArrayList(generateContractTestData()));
        service.receiveDataAndSendMsg(request);
    }

    private static Contract generateContractTestData() {
        Contract contract = new Contract();
        // 使用随机数生成器初始化各个字段
        Random random = new Random();
        contract.setContractNo("CT374912");//""CT" + random.nextInt(1000000)
        contract.setContractType(random.nextInt(5)); // 假设可能的值在0-4之间
        contract.setAreaCode("01");
        contract.setAreaName("北京");
        contract.setCustomerName("Customer " + random.nextInt(1000));
        contract.setTotalAmount("1002");
        contract.setClaimedAmount("101");
        contract.setClaimedBondAmount("501");
        contract.setOperatorName("Operator");
        contract.setOperatorWanXin("WX001");
        LocalDate startDate = LocalDate.now().minusYears(random.nextInt(5));
        LocalDate endDate = startDate.plusMonths(random.nextInt(12) + 1);
        contract.setContractStartDate(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        contract.setContractEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        contract.setContractState(2); // 假设可能的值在0-6之间
        LocalDateTime operatorDate = LocalDateTime.of(startDate, LocalTime.of(random.nextInt(23), random.nextInt(59), random.nextInt(59)));
        contract.setOperatorDate(operatorDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        LocalDateTime updateTime = LocalDateTime.now();
        contract.setUpdateTime(updateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        contract.setContractCollectionPlanList(generateContractCollectionPlanTestData(contract.getContractNo()));
        return contract;
    }

    private static List<ContractCollectDto.ContractCollectionPlan> generateContractCollectionPlanTestData(String contractNo) {
        List<ContractCollectDto.ContractCollectionPlan> plans = new ArrayList<>();
        Random random = new Random();
        for (int i = 0; i < 5; i++) { // 假设每个合同有5个收款计划
            ContractCollectDto.ContractCollectionPlan plan = new ContractCollectDto.ContractCollectionPlan();
            plan.setContractNo(contractNo);
            plan.setCondition("Condition " + i);
            plan.setType(random.nextInt(2) + 1); // 假设可能的值在0-1之间
            plan.setPlanCollectionAmount("200");
            plan.setPlanCollectionDate(LocalDate.now().plusDays(random.nextInt(10)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            plans.add(plan);
        }
        return plans;
    }
}