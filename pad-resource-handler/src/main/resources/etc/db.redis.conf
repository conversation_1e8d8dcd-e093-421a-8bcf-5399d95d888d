<databases>
<database name = "RedisCache">
<servers>
<add host = "${cmc.redis.host.node1}" port="${cmc.redis.port.node1}" />
<add host = "${cmc.redis.host.node2}" port="${cmc.redis.port.node2}" />
<add host = "${cmc.redis.host.node3}" port="${cmc.redis.port.node3}" />
</servers>
<settings>
<add name = "Type" value="sentinel"/>
<add name = "MasterNames" value="${cmc.redis.master.name}"/>
<add name = "MinPoolSize" value="1"/>
<add name = "MaxPoolSize" value="1000"/>
<add name = "ConnectTimeout" value="5000"/>
<add name = "ReadTimeout" value="10000"/>
<add name = "WriteTimeout" value="10000"/>
<add name = "Password" value="${cmc.redis.password}"/>
</settings>
</database>
</databases>