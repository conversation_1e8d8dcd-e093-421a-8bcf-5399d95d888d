package cmc.pad.resource.model;

import cmc.portal.admin.service.facade.User;
import lombok.Getter;
import lombok.Setter;
import mx.common.excel.annotation.ExcelField;

public class InventoryModel {
    @Getter
    @Setter
    public static class QueryParam {
        private String regionCode;
        private String cinemaCode;
        private String startDate;
        private String endDate;
        private int pageIndex;
        private int pageSize;
        private User authUser;
    }

    @Getter
    @Setter
    public static class ExportView {
        @ExcelField(title = "区域", groups = 1, align = 2, sort = 1, width = 6000)
        private String regionName;

        @ExcelField(title = "影院内码", groups = 1, align = 2, sort = 2, width = 6000)
        private String cinemaCode;

        @ExcelField(title = "影院名称", groups = 1, align = 2, sort = 3, width = 6000)
        private String cinemaName;

        @ExcelField(title = "日期", groups = 1, align = 2, sort = 4, width = 6000)
        private String date;

        @ExcelField(title = "营销点位总量", groups = 1, align = 2, sort = 5, width = 6000)
        private String totalMarketingPointLeasableArea;

        @ExcelField(title = "营销点位剩余量", groups = 1, align = 2, sort = 6, width = 6000)
        private String soldMarketingPointLeasableArea;

        @ExcelField(title = "外租区域总量", groups = 1, align = 2, sort = 7, width = 6000)
        private String totalOuterAreaLeasableArea;

        @ExcelField(title = "外租区域剩余量", groups = 1, align = 2, sort = 8, width = 6000)
        private String soldOuterAreaLeasableArea;

        @ExcelField(title = "固定点位总量", groups = 1, align = 2, sort = 9, width = 6000)
        private String totalFixedPointLeasableArea;

        @ExcelField(title = "固定点位剩余量", groups = 1, align = 2, sort = 10, width = 6000)
        private String soldFixedPointLeasableArea;

        @ExcelField(title = "宣传点位总量", groups = 1, align = 2, sort = 11, width = 6000)
        private String totalAdvertisingPointLeasableQuantity;

        @ExcelField(title = "宣传点位剩余量", groups = 1, align = 2, sort = 12, width = 6000)
        private String soldAdvertisingPointLeasableQuantity;
    }

}
