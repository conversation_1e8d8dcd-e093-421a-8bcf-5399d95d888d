package cmc.pad.resource;


import mtime.lark.util.msg.MsgApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

/**
 * Task启动类
 */
@SpringBootApplication(exclude = MongoAutoConfiguration.class)
public class PadResourceHandlerBootstrap {

    /**
     * 启动入口
     *
     * @param args 启动参数
     */
    public static void main(String[] args) {
        MsgApplication app = new MsgApplication(PadResourceHandlerBootstrap.class, args);
        app.run();
    }
}