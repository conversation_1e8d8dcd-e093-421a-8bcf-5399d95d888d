package cmc.pad.resource.handler;

import cmc.pad.resource.application.command.common.FileExportAppService;
import cmc.pad.resource.application.command.point.PointLocationManageService;
import cmc.pad.resource.domain.common.FileExportRecord;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImportStatus;
import cmc.pad.resource.domain.resource.PointLocationListQueryParam;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.util.CreateExcelAndUploadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.msg.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;

import static cmc.pad.resource.constant.TopicName.EXPORT_FILE_TOPIC;

/**
 * Created by fuwei on 2022/1/20.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@MsgHandler(topic = EXPORT_FILE_TOPIC, channel = "biz", threads = 1)
public class ExportFileHandler extends AbstractHandler<Integer> {
    private final FileExportAppService fileExportAppService;
    private final CreateExcelAndUploadUtil createExcelAndUploadUtil;
    private final PointLocationManageService pointLocationManageService;

    @Override
    protected void process(Integer id, Message raw) {
        log.info(">>>上传excel文件任务开始, id:{}", id);
        FileExportRecord fileExportRecord = fileExportAppService.get(id);
        if (FileCategory.POINT_LOCATION == fileExportRecord.getCategory()) {
            PointLocationListQueryParam pointLocationListQueryParam = PointLocationListQueryParam.parseJson(fileExportRecord.getRemark());
            log.info(">>>开始导出点位 导出id:{} - 导出参数:{}", id, pointLocationListQueryParam.toString());
            createExcelAndUpdate(fileExportRecord, () ->
                    createExcelAndUploadUtil.createExcelAndUpload("点位资源信息.xlsx", pointLocationManageService.allMapExportExcelList(pointLocationListQueryParam), PointLocationModel.ListDataParam.class)
            );
        }
        log.info(">>>上传excel文件任务结束, id:{}", id);
    }

    private void createExcelAndUpdate(FileExportRecord fileExportRecord, Callable<String> callable) {
        if (FileImportStatus.UNDERWAY != fileExportRecord.getStatus()) {
            return;
        }
        String fileId = "";
        log.info(">>>开始上传excel文件, 类型: {}, id:{}", fileExportRecord.getCategory().displayName(), fileExportRecord.getId());
        try {
            fileId = callable.call();
            fileExportAppService.update(fileExportRecord.updateFileIdAndStatus(fileId, FileImportStatus.SUCCEEDED));
            log.info(">>>上传excel文件成功, id:{}", fileExportRecord.getId());
        } catch (Exception e) {
            log.error(">>>上传文件异常", e);
            fileExportAppService.update(fileExportRecord.updateFileIdAndStatus(fileId, FileImportStatus.FAILED));
        }
    }
}
