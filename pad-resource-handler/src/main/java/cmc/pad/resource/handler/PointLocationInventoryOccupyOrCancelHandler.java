package cmc.pad.resource.handler;

import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterationContractOccupyServiceComponent;
import cmc.pad.resource.application.command.point.inventory.occupy.init.contract.NewContractOccupyServiceComponent;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.OccupyOrCancelParam;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.msg.AbstractHandler;
import mtime.lark.util.msg.Message;
import mtime.lark.util.msg.MsgHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cmc.pad.resource.constant.TopicName.POINT_LOCATION_INVENTORY_OCCUPY_OR_CANCEL_TOPIC;

/**
 * Created by fuwei on 2022/3/1.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@MsgHandler(topic = POINT_LOCATION_INVENTORY_OCCUPY_OR_CANCEL_TOPIC, channel = "biz", threads = 2, options = "FastFinish=true")
public class PointLocationInventoryOccupyOrCancelHandler extends AbstractHandler<OccupyOrCancelParam> {
    private final NewContractOccupyServiceComponent newContractOccupyServiceComponent;
    private final AlterationContractOccupyServiceComponent alterationContractOccupyServiceComponent;

    @Override
    protected void process(OccupyOrCancelParam param, Message raw) {
        log.info(">>>开始处理点位库存, 参数:{}",
                JSON.toJSONString(param, true)
        );
        int contractType = param.getContractType();
        if (ContractType.NEW_CONTRACT.value() != contractType && ContractType.ALTER_CONTRACT.value() != contractType) {
            log.warn(">>>合同类型错误， contractType：{}", contractType);
            return;
        }
        if (ContractType.NEW_CONTRACT.value() == contractType) {
            if (ContractStatus.SUBMIT.value() == param.getContractStatus()) {
                newContractOccupyServiceComponent.occupyInventory(param.getContractNo());
            }
            if (ContractStatus.CANCEL.value() == param.getContractStatus()) {
                newContractOccupyServiceComponent.revertInventory(param.getContractNo());
            }
        }
        if (ContractType.ALTER_CONTRACT.value() == contractType) {
            if (ContractStatus.SUBMIT.value() == param.getContractStatus()) {
                alterationContractOccupyServiceComponent.submitOccupy(param.getContractNo());
            }
            if (ContractStatus.APPROVAL.value() == param.getContractStatus()) {
                alterationContractOccupyServiceComponent.approvalOccupy(param.getContractNo());
            }
            if (ContractStatus.CANCEL.value() == param.getContractStatus()) {
                alterationContractOccupyServiceComponent.rollbackOccupy(param.getContractNo());
            }
        }

    }
}