package cmc.pad.resource.handler;

import cmc.pad.resource.application.command.point.inventory.PointInventoryManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.msg.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cmc.pad.resource.constant.TopicName.POINT_LOCATION_GENERATE_INVENTORY_TOPIC;

/**
 * Created by fuwei on 2022/1/24.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@MsgHandler(topic = POINT_LOCATION_GENERATE_INVENTORY_TOPIC, channel = "biz", threads = 3)
public class PointLocationGenerateInventoryHandler extends AbstractHandler<Integer> {
    private final PointInventoryManageService pointInventoryManageService;

    @Override
    protected void process(Integer id, Message raw) {
        pointInventoryManageService.initInventory(id, 9);
    }
}
