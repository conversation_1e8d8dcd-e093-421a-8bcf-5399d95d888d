package cmc.pad.resource.handler;

import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.msg.AbstractHandler;
import mtime.lark.util.msg.Message;
import mtime.lark.util.msg.MsgHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@MsgHandler(topic = "PAD_RESOURCE_ADD_CINEMA_TOPIC", channel = "PAD_RESOURCE_ADD_CINEMA_TOPIC", threads = 2)
public class AddCinemaInventoryHandler extends AbstractHandler<String> {

    private final CinemaResourceRepository cinemaResourceRepository;
    private final InventoryRepository inventoryRepository;

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    protected void process(String body, Message message) {
        logger.info("消息处理直接完成");
        message.finished();
        if (isExpired(message)) {
            logger.info("Message is expired, message={}, time={}", body, message.getTime());
            return;
        }
        logger.info("新增的影院内码为{}", body);
        String cinemaCode = JSON.parseObject(body, String.class);
        logger.info("开始生成影院{}的资源和资源库存，时间{}", cinemaCode, LocalDateTime.now());
        initData(cinemaCode);
        logger.info("结束生成影院{}的资源和资源库存，时间{}", cinemaCode, LocalDateTime.now());
    }

    private void initData(String cinemaCode) {
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode(cinemaCode);
        cinemaResource.setAdvertisingPointLeasableQuantity(1000);
        cinemaResource.setFixedPointLeasableArea(1000f);
        cinemaResource.setMarketingPointLeasableArea(1000f);
        cinemaResource.setOuterAreaLeasableArea(1000f);
        LocalDate now = LocalDate.now();
        getDatabase().begin((Transaction tx) -> {
            if (cinemaResourceRepository.insertCinemaResource(tx, cinemaResource)) {
                //影院资源库存初始化
                LocalDate end = now.plusMonths(40);
                int between = (int) (end.toEpochDay() - now.toEpochDay());
                List<Inventory> inventoryList = new ArrayList<>();
                for (int i = 0; i <= between; i++) {
                    Inventory inventory = new Inventory();
                    inventory.setCinemaCode(cinemaCode);
                    inventory.setDate(now.plusDays(i));
                    inventory.setTotalAdvertisingPointLeasableQuantity(1000);
                    inventory.setTotalFixedPointLeasableArea(1000f);
                    inventory.setTotalOuterAreaLeasableArea(1000f);
                    inventory.setTotalMarketingPointLeasableArea(1000f);
                    inventory.setSoldAdvertisingPointLeasableQuantity(1000);
                    inventory.setSoldFixedPointLeasableArea(1000f);
                    inventory.setSoldOuterAreaLeasableArea(1000f);
                    inventory.setSoldMarketingPointLeasableArea(1000f);
                    inventoryList.add(inventory);
                }
                inventoryRepository.batchInsert(tx, inventoryList);
            }
        });
    }

    private boolean isExpired(Message message) {
        LocalDateTime messageTime = LocalDateTime.ofInstant(message.getTime().toInstant(), ZoneId.systemDefault());
        return messageTime.isBefore(LocalDateTime.now().minusMinutes(10));
    }

}
