package cmc.pad.resource.handler;

import cmc.pad.resource.application.query.InventoryQueryService;
import cmc.pad.resource.application.query.data.InventoryData;
import cmc.pad.resource.application.query.data.InventoryQueryParam;
import cmc.pad.resource.model.InventoryModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.cache.Cache;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.msg.AbstractHandler;
import mtime.lark.util.msg.Message;
import mtime.lark.util.msg.MsgHandler;
import mx.common.excel.ExportExcel;
import mx.common.excel.bean.XBeanExport;
import mx.common.file.service.dto.FileDto;
import mx.common.file.service.iface.FileService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@MsgHandler(topic = "PAD_RESOURCE_INVENTORY_EXPORT_TOPIC", channel = "PAD_RESOURCE_INVENTORY_EXPORT_TOPIC", threads = 2)
public class ExportInventoryHandler extends AbstractHandler<String> {

    private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private final static String KEY_INVENTORY = "pad_resource_inventory";
    private final static String PUBLIC = "public";
    private final static String UPLOAD_FILE_SUCCESS_LOG = "上传文件成功，文件的id为{}";
    private final static int DEFAULT_TYPE = 0;
    private final static String EXPORT_ERROR = "error";
    private final static int PAGE_SIZE = 3000;

    private final InventoryQueryService inventoryQueryService;
    private final FileService fileService;

    @Override
    protected void process(String body, Message message) {
        logger.info("消息处理直接完成");
        message.finished();
        if (isExpired(message)) {
            logger.info("Message is expired, message={}, time={}", body, message.getTime());
            return;
        }
        logger.info("阵地广告资源库存的导出条件为{}", body);
        Map data = JSON.parseObject(body, Map.class);
        String dataBody = (String) data.get("data");
        logger.info("开始导出阵地广告资源库存，时间{}", LocalDateTime.now());
        exportInventory(dataBody);
        logger.info("结束导出阵地广告资源库存，时间{}", LocalDateTime.now());
    }

    private boolean isExpired(Message message) {
        LocalDateTime messageTime = LocalDateTime.ofInstant(message.getTime().toInstant(), ZoneId.systemDefault());
        return messageTime.isBefore(LocalDateTime.now().minusMinutes(10));
    }

    /***
     * 导出阵地广告资源库存
     */
    private void exportInventory(String data) {
        XBeanExport xBeanExport;
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        logger.info("阵地广告资源库存导出的条件信息为{}", data);
        String redisKey = null;
        try {
            InventoryModel.QueryParam queryParam = JSON.parseObject(data, InventoryModel.QueryParam.class);
            redisKey = String.valueOf(queryParam.getAuthUser().getEhrUserId());
            redisKey = redisKey + KEY_INVENTORY;
            List<InventoryModel.ExportView> returnList = getInventoryDataSource(queryParam);
            xBeanExport = ExportExcel.BeanExport(InventoryModel.ExportView.class);
            xBeanExport.createBeanSheet("阵地广告资源库存列表", "阵地广告资源库存列表", InventoryModel.ExportView.class).addData(returnList);
            xBeanExport.write(stream);
            logger.info("导出阵地广告资源文件生成完毕，开始上传到ftp服务器");
            FileDto.UploadFileRequest uploadFileRequest = new FileDto.UploadFileRequest();
            uploadFileRequest.setFileName("阵地广告资源库存列表.xlsx");
            uploadFileRequest.setStorageType(PUBLIC);
            uploadFileRequest.setFileContent(stream.toByteArray());
            uploadFileRequest.setFileCategory(DEFAULT_TYPE);
            FileDto.UploadFileResponse response = fileService.uploadFile(uploadFileRequest);
            if (response != null) {
                logger.info(UPLOAD_FILE_SUCCESS_LOG, response.getFileId());
                logger.info("redis的key值为{}", redisKey);
                Cache.set(response.getFileId(), redisKey);

            } else {
                Cache.set(EXPORT_ERROR, redisKey);
            }
            stream.close();
        } catch (Exception e) {
            logger.info("导出阵地广告资源库存信息出错，异常信息为{}", e);
            Cache.set(EXPORT_ERROR, redisKey);
        }
    }


    /**
     * 获取阵地广告资源库存的数据源
     */
    private List<InventoryModel.ExportView> getInventoryDataSource(InventoryModel.QueryParam queryParam) throws Exception {
        List<InventoryModel.ExportView> resultList = new ArrayList<>();
        List<InventoryData> dataList = new ArrayList<>();
        InventoryQueryParam param = new InventoryQueryParam();
        BeanUtils.copyProperties(queryParam, param);
        long count = this.inventoryQueryService.countInventory(param);
        int totalPageNum = (int) Math.ceil((double) count / (double) PAGE_SIZE);
        log.info(">>>阵地广告资源库存总数:{}, 总页数:{}", count, totalPageNum);
        for (int i = 1; i <= totalPageNum; i++) {
            log.info(">>> 阵地广告资源库存总页数:{}, 执行当前页数:{}", totalPageNum, i);
            param.setPageIndex(((i - 1) * PAGE_SIZE) + 1);
            param.setPageSize(PAGE_SIZE);
            PageResult<InventoryData> page = this.inventoryQueryService.queryInventoryByPage(param);
            if (page == null || page.getTotalCount() == 0) {
                return resultList;
            }
            dataList.addAll(page.getItems());
        }
        return convertToExportViewResult(dataList);
    }


    /**
     * 转换成导出视图
     */
    private List<InventoryModel.ExportView> convertToExportViewResult(List<InventoryData> list) {
        List<InventoryModel.ExportView> exportViewList = new ArrayList<>();
        if (null == list || list.isEmpty()) {
            return exportViewList;
        }
        list.forEach(inventoryData -> {
            InventoryModel.ExportView exportView = new InventoryModel.ExportView();
            exportView.setRegionName(inventoryData.getRegionName());
            exportView.setCinemaCode(inventoryData.getCinemaCode());
            exportView.setCinemaName(inventoryData.getCinemaName());
            exportView.setDate(sdf.format(inventoryData.getDate()));
            exportView.setTotalAdvertisingPointLeasableQuantity(inventoryData.getTotalAdvertisingPointLeasableQuantity().toString());
            exportView.setSoldAdvertisingPointLeasableQuantity(inventoryData.getSoldAdvertisingPointLeasableQuantity().toString());
            exportView.setTotalMarketingPointLeasableArea(inventoryData.getTotalMarketingPointLeasableArea().toString());
            exportView.setSoldMarketingPointLeasableArea(inventoryData.getSoldMarketingPointLeasableArea().toString());
            exportView.setTotalFixedPointLeasableArea(inventoryData.getTotalFixedPointLeasableArea().toString());
            exportView.setSoldFixedPointLeasableArea(inventoryData.getSoldFixedPointLeasableArea().toString());
            exportView.setTotalOuterAreaLeasableArea(inventoryData.getTotalOuterAreaLeasableArea().toString());
            exportView.setSoldOuterAreaLeasableArea(inventoryData.getSoldOuterAreaLeasableArea().toString());
            exportViewList.add(exportView);
        });
        return exportViewList;
    }


}
