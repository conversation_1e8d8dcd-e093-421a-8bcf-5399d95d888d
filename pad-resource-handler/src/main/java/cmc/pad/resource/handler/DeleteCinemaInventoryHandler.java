package cmc.pad.resource.handler;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.msg.AbstractHandler;
import mtime.lark.util.msg.Message;
import mtime.lark.util.msg.MsgHandler;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@MsgHandler(topic = "PAD_RESOURCE_DELETE_CINEMA_TOPIC", channel = "PAD_RESOURCE_DELETE_CINEMA_TOPIC", threads = 2)
public class DeleteCinemaInventoryHandler extends AbstractHandler<String> {

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    protected void process(String body, Message message) {
        logger.info("消息处理直接完成");
        message.finished();
        if (isExpired(message)) {
            logger.info("Message is expired, message={}, time={}", body, message.getTime());
            return;
        }
        logger.info("删除的影院内码为{}", body);
        String cinemaCode = JSON.parseObject(body, String.class);
        logger.info("开始删除影院{}下的资源和资源库存，时间{}", cinemaCode, LocalDateTime.now());
        deleteData(cinemaCode);
        logger.info("结束删除影院{}下的资源和资源库存，时间{}", cinemaCode, LocalDateTime.now());
    }

    private void deleteData(String cinemaCode) {
        getDatabase().begin((Transaction tx) -> {
            if (tx.delete("cinema_resource").where(Filter.create("cinema_code", cinemaCode)).result().getAffectedRows() == 1) {
                tx.delete("inventory").where(Filter.create("cinema_code", cinemaCode)).result();
            }
        });
    }

    private boolean isExpired(Message message) {
        LocalDateTime messageTime = LocalDateTime.ofInstant(message.getTime().toInstant(), ZoneId.systemDefault());
        return messageTime.isBefore(LocalDateTime.now().minusMinutes(10));
    }


}
