package cmc.pad.resource.handler;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.application.command.contract.DeContractCollectionPlanService;
import cmc.pad.resource.domain.contract.*;
import cmc.pad.resource.util.RedisLockSupport;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.msg.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;

import static cmc.pad.resource.constant.TopicName.CONTRACT_SAVE_AND_TRANSFER_COLLECT_PLAN_TOPIC;

/**
 * Created by fuwei on 2024/3/21.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@MsgHandler(topic = CONTRACT_SAVE_AND_TRANSFER_COLLECT_PLAN_TOPIC, channel = "biz", threads = 2)
public class ContractSaveAndTransferCollectPlanHandler extends AbstractHandler<ContractMsg> {
    private final ContractRepository contractRepository;
    private final DeContractCollectionPlanService service;

    @Override
    protected void process(ContractMsg contractMsg, Message raw) {
        log.info(">>>收集保存合同数据并转换催缴合同收款计划, {}", JSON.toJSONString(contractMsg));
        log.info(">>>{}合同开始保存数据库", contractMsg.getContractNo());
        RedisLockSupport.lockTemplate("pad-resource-de-contract-" + contractMsg.getContractNo(), () -> {
            contractRepository.insertOrUpdate(transferContract(contractMsg));
            service.queryContractCollectPlanAndTransferSave(true, contractMsg.getContractNo(), null, null);
        });
    }

    private Contract transferContract(ContractMsg contractMsg) {
        Contract contract = CopyUtil.copy(contractMsg, Contract.class);
        contract.setContractStartDate(toDate(contractMsg.getContractStartDate(), "yyyyMMdd"));
        contract.setContractEndDate(toDate(contractMsg.getContractEndDate(), "yyyyMMdd"));
        contract.setTotalAmount(new BigDecimal(getAmountAttachDefaultValue(contractMsg.getTotalAmount())));
        contract.setClaimedAmount(new BigDecimal(getAmountAttachDefaultValue(contractMsg.getClaimedAmount())));
        contract.setClaimedBondAmount(new BigDecimal(getAmountAttachDefaultValue(contractMsg.getClaimedBondAmount())));
        contract.setContractCollectionPlanList(
                Optional.ofNullable(contractMsg.getContractCollectionPlanList()).orElse(Collections.emptyList()).stream().map(planMsg -> {
                    ContractCollectionPlan plan = CopyUtil.copy(planMsg, ContractCollectionPlan.class);
                    plan.setPlanCollectionAmount(new BigDecimal(getAmountAttachDefaultValue(planMsg.getPlanCollectionAmount())));
                    return plan;
                }).collect(Collectors.toList())
        );
        log.info(">>>转换的合同实体:{}", JSON.toJSONString(contract));
        return contract;
    }

    private String getAmountAttachDefaultValue(String amount) {
        return StringUtils.isBlank(amount) ? "0" : amount;
    }

    private static LocalDate toDate(String date, String format) {
        if (StringUtils.isBlank(date)) return null;
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(format));
    }
}
