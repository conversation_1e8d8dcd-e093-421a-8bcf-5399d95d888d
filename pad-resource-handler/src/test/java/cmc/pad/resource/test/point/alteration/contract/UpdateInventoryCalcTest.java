package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.OccupationBuilder;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.junit.Assert.fail;

/**
 * Created by fuyuanpu on 2022/6/25.
 */
public class UpdateInventoryCalcTest {
    private OccupationBuilder builder = new OccupationBuilder();
    private RealOccupyData.DateOccupyArea oldData =
            RealOccupyData.DateOccupyArea.of(date("2022-06-01"), date("2022-06-30"), 10F);

    @Test
    public void testNewDateRange() {
        AlterContractUpdateDetailRemark remark1 = testNewData("2022-05-01", "2022-05-30", 20F);
        Assert.assertEquals("更新新时间段", remark1.EXPAND_NEW_DATE_RANGE_AND_AREA, remark1.getDesc());
        RealOccupyData.DateOccupyArea dateOccupyAreaNew = remark1.getSubmitExecData().get(0);
        assertDateEqual(dateOccupyAreaNew.getStartDate(), date("2022-05-01"));
        assertDateEqual(dateOccupyAreaNew.getEndDate(), date("2022-05-30"));
        Assert.assertEquals(dateOccupyAreaNew.getOccupyArea().floatValue(), 20F, 0.01);

        RealOccupyData.DateOccupyArea dateOccupyAreaOld = remark1.getSubmitExecData().get(1);
        assertDateEqual(dateOccupyAreaOld.getStartDate(), date("2022-06-01"));
        assertDateEqual(dateOccupyAreaOld.getEndDate(), date("2022-06-30"));
        Assert.assertEquals(dateOccupyAreaOld.getOccupyArea().floatValue(), 10F, 0.01);


        RealOccupyData.DateOccupyArea checkData = remark1.getExpandCheckData().get(0);
        assertDateEqual(checkData.getStartDate(), date("2022-05-01"));
        assertDateEqual(checkData.getEndDate(), date("2022-05-30"));
        Assert.assertEquals(checkData.getOccupyArea().floatValue(), 20F, 0.01);

        AlterContractUpdateDetailRemark remark2 = testNewData("2022-07-01", "2022-07-30", 20F);
        Assert.assertEquals("更新新时间段", remark2.EXPAND_NEW_DATE_RANGE_AND_AREA, remark2.getDesc());
    }

    private void assertDateEqual(LocalDate expected, LocalDate actual) {
        if (!expected.isEqual(actual))
            fail("日期不相同");
    }

    @Test
    public void testCrossDateRange() {
        AlterContractUpdateDetailRemark remark1 = testNewData("2022-05-01", "2022-06-01", 10F);
        Assert.assertEquals("更新交叉时间段", "left cross date range area is equal", remark1.getDesc());
        RealOccupyData.DateOccupyArea expand = remark1.getSubmitExecData().get(0);
        assertDateEqual(expand.getStartDate(), date("2022-05-01"));
        assertDateEqual(expand.getEndDate(), date("2022-05-31"));
        Assert.assertEquals(expand.getOccupyArea().floatValue(), 10F, 0.01);

        RealOccupyData.DateOccupyArea shrink = remark1.getSubmitExecData().get(1);
        assertDateEqual(shrink.getStartDate(), date("2022-06-02"));
        assertDateEqual(shrink.getEndDate(), date("2022-06-30"));
        Assert.assertEquals(shrink.getOccupyArea().floatValue(), 10F, 0.01);

        RealOccupyData.DateOccupyArea inter = remark1.getSubmitExecData().get(2);
        assertDateEqual(inter.getStartDate(), date("2022-06-01"));
        assertDateEqual(inter.getEndDate(), date("2022-06-01"));
        Assert.assertEquals(inter.getOccupyArea().floatValue(), 10F, 0.01);


        AlterContractUpdateDetailRemark remark2 = testNewData("2022-05-01", "2022-06-06", 10F);
        Assert.assertEquals("更新交叉时间段", "left cross date range area is equal", remark2.getDesc());
        expand = remark2.getSubmitExecData().get(0);
        assertDateEqual(expand.getStartDate(), date("2022-05-01"));
        assertDateEqual(expand.getEndDate(), date("2022-05-31"));
        Assert.assertEquals(expand.getOccupyArea().floatValue(), 10F, 0.01);

        shrink = remark2.getSubmitExecData().get(1);
        assertDateEqual(shrink.getStartDate(), date("2022-06-07"));
        assertDateEqual(shrink.getEndDate(), date("2022-06-30"));
        Assert.assertEquals(shrink.getOccupyArea().floatValue(), 10F, 0.01);

        inter = remark2.getSubmitExecData().get(2);
        assertDateEqual(inter.getStartDate(), date("2022-06-01"));
        assertDateEqual(inter.getEndDate(), date("2022-06-06"));
        Assert.assertEquals(inter.getOccupyArea().floatValue(), 10F, 0.01);


        AlterContractUpdateDetailRemark remark3 = testNewData("2022-06-30", "2022-07-30", 10F);
        Assert.assertEquals("更新交叉时间段", "right cross date range area is equal", remark3.getDesc());
        expand = remark3.getSubmitExecData().get(0);
        assertDateEqual(expand.getStartDate(), date("2022-07-01"));
        assertDateEqual(expand.getEndDate(), date("2022-07-30"));
        Assert.assertEquals(expand.getOccupyArea().floatValue(), 10F, 0.01);

        shrink = remark3.getSubmitExecData().get(1);
        assertDateEqual(shrink.getStartDate(), date("2022-06-01"));
        assertDateEqual(shrink.getEndDate(), date("2022-06-29"));
        Assert.assertEquals(shrink.getOccupyArea().floatValue(), 10F, 0.01);

        inter = remark3.getSubmitExecData().get(2);
        assertDateEqual(inter.getStartDate(), date("2022-06-30"));
        assertDateEqual(inter.getEndDate(), date("2022-06-30"));
        Assert.assertEquals(inter.getOccupyArea().floatValue(), 10F, 0.01);



        AlterContractUpdateDetailRemark remark4 = testNewData("2022-06-20", "2022-07-23", 10F);
        Assert.assertEquals("更新交叉时间段", "right cross date range area is equal", remark4.getDesc());

        AlterContractUpdateDetailRemark remark5 = testNewData("2022-05-01", "2022-06-01", 20.1F);
        Assert.assertEquals("更新交叉时间段", "left cross date range area is expand", remark5.getDesc());
        AlterContractUpdateDetailRemark remark6 = testNewData("2022-05-01", "2022-06-06", 20.1F);
        Assert.assertEquals("更新交叉时间段", "left cross date range area is expand", remark6.getDesc());
        AlterContractUpdateDetailRemark remark7 = testNewData("2022-06-30", "2022-07-30", 20.1F);
        Assert.assertEquals("更新交叉时间段", "right cross date range area is expand", remark7.getDesc());
        AlterContractUpdateDetailRemark remark8 = testNewData("2022-06-20", "2022-07-23", 20.1F);
        Assert.assertEquals("更新交叉时间段", "right cross date range area is expand", remark8.getDesc());

        AlterContractUpdateDetailRemark remark9 = testNewData("2022-05-01", "2022-06-01", 9.11F);
        Assert.assertEquals("更新交叉时间段", "left cross date range area is shrink", remark9.getDesc());
        AlterContractUpdateDetailRemark remark10 = testNewData("2022-05-01", "2022-06-06", 9.11F);
        Assert.assertEquals("更新交叉时间段", "left cross date range area is shrink", remark10.getDesc());
        AlterContractUpdateDetailRemark remark11 = testNewData("2022-06-30", "2022-07-30", 9.11F);
        Assert.assertEquals("更新交叉时间段", "right cross date range area is shrink", remark11.getDesc());
        AlterContractUpdateDetailRemark remark12 = testNewData("2022-06-20", "2022-07-23", 9.11F);
        Assert.assertEquals("更新交叉时间段", "right cross date range area is shrink", remark12.getDesc());
    }

    private AlterContractUpdateDetailRemark testNewData(String startDate, String endDate, Float area) {
        AlterContractUpdateDetailRemark remark = AlterContractUpdateDetailRemark.parseJson(
                builder.remarkAlertContractDetailUpdateData(
                        RealOccupyData.DateOccupyArea.of(date(startDate), date(endDate), area),
                        oldData
                )
        );
        System.out.println(remark.toJson());
        return remark;
    }

    public LocalDate date(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
