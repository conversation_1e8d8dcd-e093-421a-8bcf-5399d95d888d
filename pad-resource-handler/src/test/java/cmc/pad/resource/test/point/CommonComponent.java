package cmc.pad.resource.test.point;

import cmc.pad.resource.application.command.point.inventory.occupy.PointInventoryOccupationService;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.test.Blocker;
import cmc.pad.resource.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Assert;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_NO;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationLog.PLO_LOG_CONTRACT_NO;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/5/24.
 */
@Slf4j
public class CommonComponent {
    DateUtil.DateRange TEST_DATE_RANAGE = new DateUtil.DateRange(LocalDate.of(2022, 5, 1), LocalDate.of(2022, 8, 1));
    PointInventoryOccupationService service = ServiceLocator.current().getInstance(PointInventoryOccupationService.class);
    PointLocationOccupationContractRepository contractRep = ServiceLocator.current().getInstance(PointLocationOccupationContractRepository.class);
    PointLocationOccupationContractDetailRepository detailRep = ServiceLocator.current().getInstance(PointLocationOccupationContractDetailRepository.class);
    PointLocationOccupationLogRepository logRep = ServiceLocator.current().getInstance(PointLocationOccupationLogRepository.class);
    PointLocationInventoryRepository inventoryRep = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);

    public void initNewContract(PointLocationModel.InventoryOccupationContractParam contractParam) {
        log.info("初始化测试数据");
        clear(contractParam);
        submit(contractParam);
        waitContractProcess(contractParam.getContractNo());
        approve(contractParam.getContractNo());
    }

    public void clear(PointLocationModel.InventoryOccupationContractParam contractParam) {
        String contractNo = contractParam.getContractNo();
        Map<Integer, List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail>> pidMapDetails = contractParam.getDetails().stream().collect(Collectors.groupingBy(PointLocationModel.InventoryOccupationContractParam.OccupationDetail::getPointLocationId));
        pidMapDetails.forEach((pid, details) -> {
            revertDetailPointLocation(pid);
        });
        contractRep.delete(contractNo);
        detailRep.delete(f(PLO_C_D_CONTRACT_NO, contractNo));
        logRep.delete(f(PLO_LOG_CONTRACT_NO, contractNo));
    }

    public void revertDetailPointLocation(int pointLocationId) {
        inventoryRep.revertNotSellArea(pointLocationId, TEST_DATE_RANAGE.getStart(), TEST_DATE_RANAGE.getEnd());
    }

    public PointLocationModel.InventoryOccupationContractParam submit(PointLocationModel.InventoryOccupationContractParam contractParam) {
        log.info(">>>测试提交");
        service.occupy(contractParam);
        return contractParam;
    }

    public void approve(String contractNo) {
        log.info(">>>测试审批");
        PointLocationModel.UpdateStatusParam statusParam = new PointLocationModel.UpdateStatusParam();
        statusParam.setContractNo(contractNo);
        statusParam.setStatus(1);
        service.updateStatus(statusParam);
    }

    public void cancel(String contractNo) {
        log.info(">>>测试取消");
        PointLocationModel.UpdateStatusParam statusParam = new PointLocationModel.UpdateStatusParam();
        statusParam.setContractNo(contractNo);
        statusParam.setStatus(2);
        service.updateStatus(statusParam);
    }

    public void waitContractProcess(String contractNo) {
        Blocker.waiting(10, () ->
                contractRep.query(contractNo).getProcessStatus() == ProcessStatus.SUCESS
        );
    }

    public PointLocationModel.InventoryOccupationContractParam.OccupationDetail addTestDetail() {
        Integer pid = 64;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail addDetail = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        addDetail.setCinemaInnerCode("111");
        addDetail.setPointLocationId(pid);
        addDetail.setAmount(100f);
        addDetail.setStartDate(LocalDate.of(2022, 5, 29));
        addDetail.setEndDate(LocalDate.of(2022, 5, 30));
        addDetail.setId(pid + "_" + 15);
        addDetail.setStatus(AlterStatus.ADD.value());
        return addDetail;
    }

    public void assertAlertContractLastVersionInventory(String contractNo) {
        waitContractProcess(contractNo);
        logRep.queryRecentApprovalContractLog(contractNo)
                .stream()
                .filter(log -> log.getAlterStatus() == AlterStatus.DESTROY)
                .forEach(detail -> {
                    List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate());
                    inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", detail.getAmount().floatValue(), (inventory.getSellArea() - inventory.getNotSellArea()), 0.00));
                });
    }
}
