package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.resource.PointLocationModel;
import org.junit.Before;
import org.junit.Test;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark.EXPAND_ALL;

/**
 * Created by fuyuanpu on 2022/6/26.
 */
public class ExpandAllUpdateTest extends AbstractTest {
    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-1011";

    @Before
    public void initNewContract() {
        super.initNewContract(TEST_POINT_LOCATION_CONTRACT_CODE);
    }

    private PointLocationModel.InventoryOccupationContractParam buildExpandAllContract() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildTestContract(ContractType.ALTER_CONTRACT);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = contractParam.getDetails().get(0);
        detail1.setStartDate(detail1.getStartDate().minusDays(1));
        detail1.setAmount(100F);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = contractParam.getDetails().get(1);
        detail2.setEndDate(detail2.getEndDate().plusDays(1));

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = contractParam.getDetails().get(2);
        detail3.setAmount(detail3.getAmount() + 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = contractParam.getDetails().get(3);
        detail4.setStartDate(detail4.getStartDate().minusDays(1));
        detail4.setEndDate(detail4.getEndDate().plusDays(1));
        detail4.setAmount(detail4.getAmount() + 10);

        return contractParam;
    }

    @Test
    public void test1Update_EXPAND_ALL() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildExpandAllContract();
        String contractNo = contractParam.getContractNo();

        commonComp.submit(buildExpandAllContract());
        commonComp.submit(buildExpandAllContract());
        assertAlertContractSubmitRealInventory(contractNo, EXPAND_ALL);

        commonComp.cancel(contractNo);
        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(buildExpandAllContract());
        commonComp.submit(buildExpandAllContract());
        assertAlertContractSubmitRealInventory(contractNo, EXPAND_ALL);

        commonComp.approve(contractNo);
        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }
}
