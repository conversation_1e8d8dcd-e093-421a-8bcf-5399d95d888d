package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.resource.PointLocationModel;
import org.junit.Before;
import org.junit.Test;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark.EXPAND_DATE_RANGE_SHRINK_AREA;

/**
 * Created by fuyuanpu on 2022/6/26.
 */
public class ExpandDateRangeShrinkAreaTest extends AbstractTest {
    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-1011";

    @Before
    public void initNewContract() {
        super.initNewContract(TEST_POINT_LOCATION_CONTRACT_CODE);
    }

    private PointLocationModel.InventoryOccupationContractParam buildEXPAND_DATE_RANGE_SHRINK_AREAContract() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildTestContract(ContractType.ALTER_CONTRACT);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = contractParam.getDetails().get(0);
        detail1.setStartDate(detail1.getStartDate().minusDays(1));
        detail1.setAmount(detail1.getAmount() - 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = contractParam.getDetails().get(1);
        detail2.setEndDate(detail2.getEndDate().plusDays(1));
        detail2.setAmount(detail2.getAmount() - 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = contractParam.getDetails().get(2);
        detail3.setEndDate(detail3.getEndDate().plusDays(1));
        detail3.setAmount(detail3.getAmount() - 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = contractParam.getDetails().get(3);
        detail4.setStartDate(detail4.getStartDate().minusDays(1));
        detail4.setEndDate(detail4.getEndDate().plusDays(1));
        detail4.setAmount(detail4.getAmount() - 10);

        return contractParam;
    }

    @Test
    public void test6Update_EXPAND_DATE_RANGE_SHRINK_AREA() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildEXPAND_DATE_RANGE_SHRINK_AREAContract();
        String contractNo = contractParam.getContractNo();

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractSubmitRealInventory(contractNo, EXPAND_DATE_RANGE_SHRINK_AREA);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        contractParam = buildEXPAND_DATE_RANGE_SHRINK_AREAContract();
        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractSubmitRealInventory(contractNo, EXPAND_DATE_RANGE_SHRINK_AREA);

        commonComp.approve(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }
}
