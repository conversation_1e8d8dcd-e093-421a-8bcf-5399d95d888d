package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark.SHRINK_ALL;

/**
 * Created by fuyuanpu on 2022/6/26.
 */
public class ShrinkAllUpdateTest extends AbstractTest {
    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-103";

    @Before
    public void initNewContract() {
        super.initNewContract(TEST_POINT_LOCATION_CONTRACT_CODE);
    }

    public PointLocationModel.InventoryOccupationContractParam buildTestContract(ContractType contractType) {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractType(contractType.value());
        param.setContractNo(TEST_POINT_LOCATION_CONTRACT_CODE);
        param.setBusinessTypeCode("GD");
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(11);
        int pid = 56;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("111");
        detail1.setPointLocationId(pid);
        detail1.setAmount(80f);
        detail1.setStartDate(LocalDate.of(2022, 6, 1));
        detail1.setEndDate(LocalDate.of(2022, 6, 15));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail1);

        pid = 64;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("111");
        detail2.setPointLocationId(pid);
        detail2.setAmount(80f);
        detail2.setStartDate(LocalDate.of(2022, 6, 1));
        detail2.setEndDate(LocalDate.of(2022, 6, 15));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        pid = 72;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("111");
        detail3.setPointLocationId(pid);
        detail3.setAmount(20f);
        detail3.setStartDate(LocalDate.of(2022, 6, 1));
        detail3.setEndDate(LocalDate.of(2022, 6, 6));
        detail3.setId(pid + "_" + (i.getAndIncrement()));
        detail3.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail3);

        pid = 72;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail4.setCinemaInnerCode("111");
        detail4.setPointLocationId(pid);
        detail4.setAmount(80f);
        detail4.setStartDate(LocalDate.of(2022, 6, 10));
        detail4.setEndDate(LocalDate.of(2022, 6, 20));
        detail4.setId(pid + "_" + (i.getAndIncrement()));
        detail4.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail4);

        param.setDetails(list);
        return param;
    }

    private PointLocationModel.InventoryOccupationContractParam buildShrinkAllContract() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildTestContract(ContractType.ALTER_CONTRACT);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = contractParam.getDetails().get(0);
        detail1.setStartDate(detail1.getStartDate().plusDays(1));

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = contractParam.getDetails().get(1);
        detail2.setEndDate(detail2.getEndDate().minusDays(1));

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = contractParam.getDetails().get(2);
        detail3.setAmount(detail3.getAmount() - 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = contractParam.getDetails().get(3);
        detail4.setStartDate(detail4.getStartDate().plusDays(1));
        detail4.setEndDate(detail4.getEndDate().minusDays(1));
        detail4.setAmount(detail4.getAmount() - 10);

        return contractParam;
    }

    @Test
    public void test5Update_SHRINK_ALL() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildShrinkAllContract();
        String contractNo = contractParam.getContractNo();

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractSubmitRealInventory(contractNo, SHRINK_ALL);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        contractParam = buildShrinkAllContract();
        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractSubmitRealInventory(contractNo, SHRINK_ALL);

        commonComp.approve(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }
}
