package cmc.pad.resource.test.point.init.contract;

import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.test.AbstractCmcQasTest;
import cmc.pad.resource.test.point.CommonComponent;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by fuyuanpu on 2022/5/26.
 */
@Slf4j
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class InitContractTest extends AbstractCmcQasTest {
    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-101";
    CommonComponent commonComp = new CommonComponent();
    PointLocationInventoryRepository inventoryRep = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);
    PointLocationModel.InventoryOccupationContractParam contract;

    public PointLocationModel.InventoryOccupationContractParam buildTestContract(String contractNo, ContractType contractType) {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractType(contractType.value());
        param.setContractNo(contractNo);
        param.setBusinessTypeCode("GD");
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(11);
        int pid = 1001;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("111");
        detail1.setPointLocationId(pid);
        detail1.setAmount(80f);
        detail1.setStartDate(LocalDate.of(2022, 5, 23));
        detail1.setEndDate(LocalDate.of(2022, 5, 30));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail1);

        pid = 1002;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("111");
        detail2.setPointLocationId(pid);
        detail2.setAmount(80f);
        detail2.setStartDate(LocalDate.of(2022, 5, 23));
        detail2.setEndDate(LocalDate.of(2022, 5, 25));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        pid = 1002;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("111");
        detail3.setPointLocationId(pid);
        detail3.setAmount(50f);
        detail3.setStartDate(LocalDate.of(2022, 5, 26));
        detail3.setEndDate(LocalDate.of(2022, 5, 30));
        detail3.setId(pid + "_" + (i.getAndIncrement()));
        detail3.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail3);

        param.setDetails(list);
        return param;
    }

    @Before
    public void initNewContract() {
        contract = buildTestContract(TEST_POINT_LOCATION_CONTRACT_CODE, ContractType.NEW_CONTRACT);
    }

    @Test
    public void test1Submit() {
        commonComp.clear(contract);
        commonComp.submit(contract);
        commonComp.submit(contract);
        commonComp.waitContractProcess(contract.getContractNo());
        assertNewContractOccupyInventory(contract);
    }

    @Test
    public void test2Cancel() {
        commonComp.cancel(contract.getContractNo());
        commonComp.cancel(contract.getContractNo());
        commonComp.waitContractProcess(contract.getContractNo());
        assertNewContractCancelInventory(contract);
    }

    @Test
    public void test3Submit() {
        test1Submit();
    }

    @Test
    public void test4Approval() {
        commonComp.approve(contract.getContractNo());
        commonComp.approve(contract.getContractNo());
        commonComp.waitContractProcess(contract.getContractNo());
        assertNewContractOccupyInventory(contract);
    }

    private void assertNewContractCancelInventory(PointLocationModel.InventoryOccupationContractParam contractParam) {
        contractParam.getDetails()
                .stream()
                .forEach(detail -> {
                    List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate());
                    inventoryList.forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", inventory.getSellArea(), inventory.getNotSellArea(), 0.00));
                });
    }

    private void assertNewContractOccupyInventory(PointLocationModel.InventoryOccupationContractParam contractParam) {
        contractParam.getDetails()
                .stream()
                .forEach(detail -> {
                    List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate());
                    inventoryList.forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", detail.getAmount().floatValue(), (inventory.getSellArea() - inventory.getNotSellArea()), 0.00));
                });
    }
}
