package cmc.pad.resource.test;

import cmc.pad.resource.application.command.InventoryAppService;
import cmc.pad.resource.application.command.SaveOccupationCommand;
import cmc.pad.resource.application.command.point.inventory.occupy.PointInventoryOccupationService;
import cmc.pad.resource.application.command.point.inventory.occupy.init.contract.NewContractSubmitBiz;
import cmc.pad.resource.application.query.point.PointLocationQueryService;
import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractRepository;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created by fuwei on 2022/2/7.
 */
public class PointInventoryOccupationServiceTest extends AbstractCmcQasTest {
    public static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-101";
    InventoryAppService inventoryAppService = ServiceLocator.current().getInstance(InventoryAppService.class);
    PointInventoryOccupationService service = ServiceLocator.current().getInstance(PointInventoryOccupationService.class);
    PointLocationQueryService pointLocationQueryService = ServiceLocator.current().getInstance(PointLocationQueryService.class);
    PointLocationOccupationContractRepository contractBaseInfoRepository = ServiceLocator.current().getInstance(PointLocationOccupationContractRepository.class);
    PointLocationInventoryRepository pointLocationInventoryRepository = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);
    PointLocationInfoRepository pointLocationInfoRepository = ServiceLocator.current().getInstance(PointLocationInfoRepository.class);
    NewContractSubmitBiz occupyBiz = ServiceLocator.current().getInstance(NewContractSubmitBiz.class);

    @Test
    public void tt() {
        char c = 'A';
//        if (i % 1000 == 0)
//            c = (char) (c + i);

        IntStream.rangeClosed(9000, 9000 + 4379).forEach(i -> {
            PointLocationInfo pointLocationInfo = new PointLocationInfo();
            pointLocationInfo.setCode("AB" + i);
            pointLocationInfo.setId(i);
            pointLocationInfo.setRegionCode("111");
            pointLocationInfo.setCinemaInnerCode("111");
            pointLocationInfo.setBusinessTypeCode("GD");
            pointLocationInfo.setResourceOwnershipCode("1");
            pointLocationInfo.setLocationDesc("");
            pointLocationInfo.setPlanUse("");
            pointLocationInfo.setLandingMode("");
            pointLocationInfo.setSellArea(100f);
            pointLocationInfo.setSellAreaAdjustDate(null);
            pointLocationInfo.setCreateTime(LocalDateTime.now());
            pointLocationInfo.setUpdateTime(LocalDateTime.now());
            pointLocationInfo.setInventoryStatus(ProcessStatus.SUCESS);
            pointLocationInfoRepository.batchInsert(Lists.newArrayList(pointLocationInfo));
            pointLocationInventoryRepository.insert(pointLocationInfo, LocalDate.now(), LocalDate.now().plusYears(6));
        });
//        PointLocationInfo pointLocationInfo = new PointLocationInfo();
//        pointLocationInfo.setCode("099");
//        pointLocationInfo.setId(99);
//        pointLocationInfo.setRegionCode("111");
//        pointLocationInfo.setCinemaInnerCode("111");
//        pointLocationInfo.setBusinessTypeCode("GD");
//        pointLocationInfo.setResourceOwnershipCode("1");
//        pointLocationInfo.setLocationDesc("");
//        pointLocationInfo.setPlanUse("");
//        pointLocationInfo.setLandingMode("");
//        pointLocationInfo.setSellArea(100f);
//        pointLocationInfo.setSellAreaAdjustDate(null);
//        pointLocationInfo.setCreateTime(LocalDateTime.now());
//        pointLocationInfo.setUpdateTime(LocalDateTime.now());
//        pointLocationInfo.setInventoryStatus(PointLocationInfo.InventoryStatus.DONE);
//        pointLocationInventoryRepository.insert(pointLocationInfo, LocalDate.now(), LocalDate.now().plusYears(6));
    }

    @Test
    public void cancel() {
        PointLocationModel.UpdateStatusParam param = new PointLocationModel.UpdateStatusParam();
        param.setContractNo(TEST_POINT_LOCATION_CONTRACT_CODE);
        param.setStatus(2);
        long start = System.currentTimeMillis();
        service.updateStatus(param);
        System.out.println(System.currentTimeMillis() - start);
    }

    @Test
    public void active() {
        PointLocationModel.UpdateStatusParam param = new PointLocationModel.UpdateStatusParam();
        param.setContractNo(TEST_POINT_LOCATION_CONTRACT_CODE);
        param.setStatus(1);
        long start = System.currentTimeMillis();
        service.updateStatus(param);
        System.out.println(System.currentTimeMillis() - start);
    }

    @Test
    public void submitNewContract() {
//        printInventory();
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractNo(TEST_POINT_LOCATION_CONTRACT_CODE);
        param.setBusinessTypeCode("GD");
        AtomicInteger i = new AtomicInteger(1);
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> occupyDetails =
                Lists.newArrayList(2992, 3088).stream().map(pid -> {
                    PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
                    detail.setCinemaInnerCode("111");
                    detail.setPointLocationId(pid);
                    detail.setAmount(10f);
                    detail.setStartDate(LocalDate.of(2022, 5, 17));
                    detail.setEndDate(LocalDate.of(2022, 5, 20));
                    detail.setId(pid + "_" + (i.getAndIncrement()));
                    return detail;
                }).collect(Collectors.toList());

//        for (int i = 1; i < 100; i++) {
//            PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
//            detail.setCinemaInnerCode("986");
//            detail.setPointLocationId(i);
//            detail.setAmount(20f);
//            detail.setStartDate(LocalDate.now());
//            detail.setEndDate(LocalDate.now().plusYears(5));
//            list.add(detail);
//        }
        param.setDetails(occupyDetails);
        param.setContractType(1);
        long start = System.currentTimeMillis();
        service.occupy(param);
        System.out.println(">>>" + (System.currentTimeMillis() - start));
    }

    @Test
    public void submitAlterContract() {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractNo(TEST_POINT_LOCATION_CONTRACT_CODE);
        param.setBusinessTypeCode("GD");

        ArrayList<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(1);
        int pid = 2992;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("111");
        detail1.setPointLocationId(pid);
        detail1.setAmount(10f);
        detail1.setStartDate(LocalDate.of(2022, 5, 17));
        detail1.setEndDate(LocalDate.of(2022, 5, 20));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.DESTROY.value());
        list.add(detail1);

        pid = 3088;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("111");
        detail2.setPointLocationId(pid);
        detail2.setAmount(10f);
        detail2.setStartDate(LocalDate.of(2022, 5, 17));
        detail2.setEndDate(LocalDate.of(2022, 5, 20));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        pid = 265;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("111");
        detail3.setPointLocationId(pid);
        detail3.setAmount(20f);
        detail3.setStartDate(LocalDate.of(2022, 5, 21));
        detail3.setEndDate(LocalDate.of(2022, 5, 22));
        detail3.setId(pid + "_" + (i.getAndIncrement()));
        detail3.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail3);

        param.setDetails(list);
        param.setContractType(2);
        long start = System.currentTimeMillis();
        service.occupy(param);
        System.out.println(">>>" + (System.currentTimeMillis() - start));
    }

    @Test
    public void printInventory() {
        System.out.printf(" >>> " + JSON.toJSONString(
                pointLocationQueryService
                        .query(0, 0,
                                null, "", "",
                                LocalDate.of(2024, 1, 1),
                                LocalDate.of(2024, 12, 31)
                        )
                , true));
    }

    @Test
    public void saveOccupation() {//测试老库存占用接口
        SaveOccupationCommand command = new SaveOccupationCommand();
        command.setContractNo("test-old-contract-001");
        command.setBusinessType("YX");

        SaveOccupationCommand.OccupationDetail detail1 = new SaveOccupationCommand.OccupationDetail();
        detail1.setCinemaCode("001");
        detail1.setAmount(998f);
        detail1.setStartDate("2022-02-08");
        detail1.setEndDate("2022-02-08");

        SaveOccupationCommand.OccupationDetail detail2 = new SaveOccupationCommand.OccupationDetail();
        detail2.setCinemaCode("001");
        detail2.setAmount(998f);
        detail2.setStartDate("2022-02-08");
        detail2.setEndDate("2022-02-08");

        command.setDetails(Lists.newArrayList(detail1, detail2));

        inventoryAppService.saveOccupation(command);
    }

    @Test
    public void query() {
        System.out.printf(" >>> " + JSON.toJSONString(
                pointLocationQueryService.query(1, 10, null, "", "", LocalDate.now(), LocalDate.now())
                , true));
    }
}