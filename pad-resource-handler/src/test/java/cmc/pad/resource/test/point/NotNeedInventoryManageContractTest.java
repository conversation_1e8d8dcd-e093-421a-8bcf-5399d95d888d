package cmc.pad.resource.test.point;

import cmc.pad.resource.application.command.point.inventory.occupy.PointInventoryOccupationService;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.test.AbstractCmcQasTest;
import com.google.common.collect.Lists;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/6/30.
 */
public class NotNeedInventoryManageContractTest extends AbstractCmcQasTest {
    PointInventoryOccupationService service = ServiceLocator.current().getInstance(PointInventoryOccupationService.class);
    PointLocationOccupationContractRepository contractRepository = ServiceLocator.current().getInstance(PointLocationOccupationContractRepository.class);
    NotNeedInventoryManagePointLocationContractRepository notNeedInventoryManagePointLocationContractRepository = ServiceLocator.current().getInstance(NotNeedInventoryManagePointLocationContractRepository.class);
    String contractNo = "test_not_need_inventory_001";

    @Before
    public void initNewContract() {
        notNeedInventoryManagePointLocationContractRepository.delete(f("contract_no", contractNo));
    }

    @Test
    public void test() {

        service.occupy(buildTestContract(contractNo, ContractType.NEW_CONTRACT));
        assertContract(contractNo, 1);

        update(contractNo, 1);
        assertContract(contractNo, 2);

        update(contractNo, 2);
        assertContract(contractNo, 3);

        service.occupy(buildTestContract(contractNo, ContractType.ALTER_CONTRACT));
        assertContract(contractNo, 4);

        update(contractNo, 1);
        assertContract(contractNo, 5);

        update(contractNo, 2);
        assertContract(contractNo, 6);
    }

    private void assertContract(String contractNo, int expectSize) {
        PointLocationOccupationContract contract = contractRepository.query(contractNo);
        Assert.assertNull(contract);
        long size = notNeedInventoryManagePointLocationContractRepository.count(f("contract_no", contractNo));
        Assert.assertEquals(expectSize, size);
    }

    private void update(String contractNo, int status) {
        PointLocationModel.UpdateStatusParam param = new PointLocationModel.UpdateStatusParam();
        param.setContractNo(contractNo);
        param.setStatus(status);
        service.updateStatus(param);
    }

    public PointLocationModel.InventoryOccupationContractParam buildTestContract(String contractNo, ContractType contractType) {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractType(contractType.value());
        param.setContractNo(contractNo);
        param.setBusinessTypeCode("YT");
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(11);
        int pid = 20988;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("849");
        detail1.setPointLocationId(pid);
        detail1.setAmount(80f);
        detail1.setStartDate(LocalDate.of(2022, 5, 23));
        detail1.setEndDate(LocalDate.of(2022, 5, 30));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail1);

        pid = 20989;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("849");
        detail2.setPointLocationId(pid);
        detail2.setAmount(80f);
        detail2.setStartDate(LocalDate.of(2022, 5, 23));
        detail2.setEndDate(LocalDate.of(2022, 5, 25));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        param.setDetails(list);
        return param;
    }
}
