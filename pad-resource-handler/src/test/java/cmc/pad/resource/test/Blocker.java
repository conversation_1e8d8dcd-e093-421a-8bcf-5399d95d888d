package cmc.pad.resource.test;

/**
 * Created by fuwei on 2018/2/2.
 */

import org.junit.Assert;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;

/**
 * Created by fuwei on 2017/12/26.
 */
public class Blocker {
    public static void waiting(int seconds, BlockUnit blockerUnit) {
        try {
            // 指定超时时间3s, 如果在这时间段内,条件依然不满足,将抛出ConditionTimeoutException
            await().atMost(seconds, SECONDS).until(() -> blockerUnit.callBack());
        } catch (Exception e) {
            Assert.fail("测试代码运行异常：" + e.getMessage() + "，代码位置：" + e.getStackTrace()[0].toString());
        }
    }

    public interface BlockUnit {
        boolean callBack();
    }
}
