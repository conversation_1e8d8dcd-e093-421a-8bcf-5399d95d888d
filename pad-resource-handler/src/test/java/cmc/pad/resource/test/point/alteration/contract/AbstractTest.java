package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.test.AbstractCmcQasTest;
import cmc.pad.resource.test.point.CommonComponent;
import com.google.common.collect.Lists;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Assert;
import org.junit.Before;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by fuyuanpu on 2022/6/26.
 */
public abstract class AbstractTest extends AbstractCmcQasTest {
    CommonComponent commonComp = new CommonComponent();
    PointLocationInventoryRepository inventoryRep = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);
    PointLocationOccupationContractRepository contractRep = ServiceLocator.current().getInstance(PointLocationOccupationContractRepository.class);
    PointLocationModel.InventoryOccupationContractParam contractParam;
    public String contractNo;

    public void initNewContract(String contractNo) {
        this.contractNo = contractNo;
        contractParam = buildTestContract(ContractType.NEW_CONTRACT);
        commonComp.initNewContract(contractParam);
        contractParam.setContractType(ContractType.ALTER_CONTRACT.value());
    }

    public PointLocationModel.InventoryOccupationContractParam buildTestContract(ContractType contractType) {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractType(contractType.value());
        param.setContractNo(this.contractNo);
        param.setBusinessTypeCode("GD");
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(11);
        int pid = 56;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("111");
        detail1.setPointLocationId(pid);
        detail1.setAmount(80f);
        detail1.setStartDate(LocalDate.of(2022, 6, 1));
        detail1.setEndDate(LocalDate.of(2022, 6, 15));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail1);

        pid = 64;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("111");
        detail2.setPointLocationId(pid);
        detail2.setAmount(80f);
        detail2.setStartDate(LocalDate.of(2022, 6, 1));
        detail2.setEndDate(LocalDate.of(2022, 6, 15));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        pid = 72;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("111");
        detail3.setPointLocationId(pid);
        detail3.setAmount(20f);
        detail3.setStartDate(LocalDate.of(2022, 6, 1));
        detail3.setEndDate(LocalDate.of(2022, 6, 6));
        detail3.setId(pid + "_" + (i.getAndIncrement()));
        detail3.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail3);

        pid = 72;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail4.setCinemaInnerCode("111");
        detail4.setPointLocationId(pid);
        detail4.setAmount(80f);
        detail4.setStartDate(LocalDate.of(2022, 6, 10));
        detail4.setEndDate(LocalDate.of(2022, 6, 20));
        detail4.setId(pid + "_" + (i.getAndIncrement()));
        detail4.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail4);

        param.setDetails(list);
        return param;
    }

    public void assertAlertContractSubmitRealInventory(String contractNo, String remarkDesc) {
        commonComp.waitContractProcess(contractNo);
        PointLocationOccupationContract contract = contractRep.query(contractNo, true);
        contract.getOccupationDetailList()
                .stream()
                .filter(detail -> AlterStatus.UPDATE == detail.getAlterStatus())
                .forEach(detail -> {
                    AlterContractUpdateDetailRemark alterContractUpdateDetailRemark = AlterContractUpdateDetailRemark.parseJson(detail.getAlterRemark());
                    Assert.assertEquals(detail.getPointLocationId().toString(), remarkDesc, alterContractUpdateDetailRemark.getDesc());
                    alterContractUpdateDetailRemark.getSubmitExecData().forEach(execData -> {
                        List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), execData.getStartDate(), execData.getEndDate());
                        inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "占用时间:" + execData.getStartDate() + "-" + execData.getEndDate() + "库存", execData.getOccupyArea().floatValue(), (inventory.getSellArea() - inventory.getNotSellArea()), 0.00));
                    });

                });
    }

    public void assertAlterContractDetailInventory(String contractNo) {
        commonComp.waitContractProcess(contractNo);
        PointLocationOccupationContract contract = contractRep.query(contractNo, true);
        contract.getOccupationDetailList()
                .stream()
                .filter(detail -> AlterStatus.UPDATE == detail.getAlterStatus())
                .forEach(detail -> {
                    List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate());
                    inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "占用时间:" + detail.getStartDate() + "-" + detail.getEndDate() + "库存", detail.getAmount().floatValue(), (inventory.getSellArea() - inventory.getNotSellArea()), 0.00));
                });
    }
}
