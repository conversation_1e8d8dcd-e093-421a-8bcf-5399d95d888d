package cmc.pad.resource.test.point.alteration.contract;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.resource.PointLocationModel;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;

/**
 * Created by fuyuanpu on 2022/6/26.
 */
public class CrossDateRangeUpdateTest extends AbstractTest {

    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-103";

    @Before
    public void initNewContract() {
        super.initNewContract(TEST_POINT_LOCATION_CONTRACT_CODE);
    }

    private PointLocationModel.InventoryOccupationContractParam buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildTestContract(ContractType.ALTER_CONTRACT);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = contractParam.getDetails().get(0);
        LocalDate startDate = detail1.getStartDate();
        detail1.setEndDate(startDate);
        detail1.setStartDate(startDate.minusDays(10));

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = contractParam.getDetails().get(1);
        detail2.setStartDate(detail2.getStartDate().minusDays(10));
        detail2.setEndDate(detail2.getEndDate().minusDays(2));
        return contractParam;
    }

    @Test
    public void test9Update_LEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL();
        String contractNo = contractParam.getContractNo();

        commonComp.submit(buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        commonComp.submit(buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        assertAlertContractSubmitRealInventory(contractNo, "left cross date range area is equal");

        commonComp.cancel(contractNo);
        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        commonComp.submit(buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        assertAlertContractSubmitRealInventory(contractNo, "left cross date range area is equal");

        commonComp.approve(contractNo);
        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }

    @Test
    public void test9Update_LEFT_CROSS_DATE_RANGE_AND_AREA_EXPAND() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL();
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d1 = contractParam.getDetails().get(0);
        d1.setAmount(d1.getAmount() + 10);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d2 = contractParam.getDetails().get(1);
        d2.setAmount(d2.getAmount() + 10);
        String contractNo = contractParam.getContractNo();
        PointLocationModel.InventoryOccupationContractParam copyParam = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);

        commonComp.submit(contractParam);
        assertAlertContractSubmitRealInventory(contractNo, "left cross date range area is expand");

        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(copyParam);
        assertAlertContractSubmitRealInventory(contractNo, "left cross date range area is expand");

        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }

    @Test
    public void test9Update_LEFT_CROSS_DATE_RANGE_AND_AREA_SHRINK() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildLEFT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL();
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d1 = contractParam.getDetails().get(0);
        d1.setAmount(d1.getAmount() - 10);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d2 = contractParam.getDetails().get(1);
        d2.setAmount(d2.getAmount() - 20);
        PointLocationModel.InventoryOccupationContractParam copyParam = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);

        String contractNo = contractParam.getContractNo();
        commonComp.submit(contractParam);
        assertAlertContractSubmitRealInventory(contractNo, "left cross date range area is shrink");

        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(copyParam);
        assertAlertContractSubmitRealInventory(contractNo, "left cross date range area is shrink");

        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }

    private PointLocationModel.InventoryOccupationContractParam buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildTestContract(ContractType.ALTER_CONTRACT);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = contractParam.getDetails().get(0);
        detail1.setStartDate(detail1.getEndDate());
        detail1.setEndDate(detail1.getEndDate().plusDays(10));

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = contractParam.getDetails().get(1);
        detail2.setStartDate(detail2.getStartDate().plusDays(2));
        detail2.setEndDate(detail2.getEndDate().plusDays(2));
        return contractParam;
    }

    @Test
    public void test9Update_RIGHT_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL();
        String contractNo = contractParam.getContractNo();

        commonComp.submit(buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        commonComp.submit(buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        assertAlertContractSubmitRealInventory(contractNo, "right cross date range area is equal");

        commonComp.cancel(contractNo);
        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        commonComp.submit(buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL());
        assertAlertContractSubmitRealInventory(contractNo, "right cross date range area is equal");

        commonComp.approve(contractNo);
        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }

    @Test
    public void test9Update_RIGHT_CROSS_DATE_RANGE_AND_AREA_IS_EXPAND() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL();
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d1 = contractParam.getDetails().get(0);
        d1.setAmount(d1.getAmount() + 10);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d2 = contractParam.getDetails().get(1);
        d2.setAmount(d2.getAmount() + 20);

        String contractNo = contractParam.getContractNo();
        PointLocationModel.InventoryOccupationContractParam copy1 = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);
        PointLocationModel.InventoryOccupationContractParam copy2 = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);
        PointLocationModel.InventoryOccupationContractParam copy3 = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);
        commonComp.submit(contractParam);
        commonComp.submit(copy1);
        assertAlertContractSubmitRealInventory(contractNo, "right cross date range area is expand");

        commonComp.cancel(contractNo);
        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(copy2);
        commonComp.submit(copy3);
        assertAlertContractSubmitRealInventory(contractNo, "right cross date range area is expand");

        commonComp.approve(contractNo);
        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }

    @Test
    public void test9Update_RIGHT_CROSS_DATE_RANGE_AND_AREA_IS_SHRINK() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildRight_CROSS_DATE_RANGE_AND_AREA_IS_EQUAL();
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d1 = contractParam.getDetails().get(0);
        d1.setAmount(d1.getAmount() - 10);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail d2 = contractParam.getDetails().get(1);
        d2.setAmount(d2.getAmount() - 20);
        PointLocationModel.InventoryOccupationContractParam copyParam1 = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);
        PointLocationModel.InventoryOccupationContractParam copyParam2 = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);
        PointLocationModel.InventoryOccupationContractParam copyParam3 = CopyUtil.copy(contractParam, PointLocationModel.InventoryOccupationContractParam.class);

        String contractNo = contractParam.getContractNo();

        commonComp.submit(contractParam);
        commonComp.submit(copyParam1);
        assertAlertContractSubmitRealInventory(contractNo, "right cross date range area is shrink");

        commonComp.cancel(contractNo);
        commonComp.cancel(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        commonComp.submit(copyParam2);
        commonComp.submit(copyParam3);
        assertAlertContractSubmitRealInventory(contractNo, "right cross date range area is shrink");

        commonComp.approve(contractNo);
        commonComp.approve(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }
}
