package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.test.AbstractCmcQasTest;
import cmc.pad.resource.test.point.CommonComponent;
import cmc.pad.resource.util.DateMapSumAreaUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runners.MethodSorters;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cmc.pad.resource.util.DateUtil.getDates;

/**
 * Created by fuyuanpu on 2022/5/23.
 */
@Slf4j
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class OccpuyAndAddAndDestroyTest extends AbstractCmcQasTest {
    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-102";

    CommonComponent commonComp = new CommonComponent();
    PointLocationInventoryRepository inventoryRep = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);
    PointLocationModel.InventoryOccupationContractParam contractParam;

    public PointLocationModel.InventoryOccupationContractParam buildTestContract(ContractType contractType) {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractType(contractType.value());
        param.setContractNo(TEST_POINT_LOCATION_CONTRACT_CODE);
        param.setBusinessTypeCode("GD");
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(11);
        int pid = 16;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("111");
        detail1.setPointLocationId(pid);
        detail1.setAmount(80f);
        detail1.setStartDate(LocalDate.of(2022, 5, 23));
        detail1.setEndDate(LocalDate.of(2022, 5, 30));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail1);

        pid = 24;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("111");
        detail2.setPointLocationId(pid);
        detail2.setAmount(80f);
        detail2.setStartDate(LocalDate.of(2022, 5, 23));
        detail2.setEndDate(LocalDate.of(2022, 5, 25));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        pid = 40;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("111");
        detail3.setPointLocationId(pid);
        detail3.setAmount(20f);
        detail3.setStartDate(LocalDate.of(2022, 6, 1));
        detail3.setEndDate(LocalDate.of(2022, 6, 6));
        detail3.setId(pid + "_" + (i.getAndIncrement()));
        detail3.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail3);

        pid = 40;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail4.setCinemaInnerCode("111");
        detail4.setPointLocationId(pid);
        detail4.setAmount(80f);
        detail4.setStartDate(LocalDate.of(2022, 6, 7));
        detail4.setEndDate(LocalDate.of(2022, 6, 20));
        detail4.setId(pid + "_" + (i.getAndIncrement()));
        detail4.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail4);

        param.setDetails(list);
        return param;
    }

    @Before
    public void initNewContract() {
        contractParam = buildTestContract(ContractType.NEW_CONTRACT);
        commonComp.initNewContract(contractParam);
        contractParam.setContractType(ContractType.ALTER_CONTRACT.value());
    }

    @Test
    public void test1Occupy() {
        commonComp.submit(contractParam);
        String contractNo = contractParam.getContractNo();
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);

        commonComp.approve(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
    }

    @Test
    public void test2Destroy() {
        contractParam.getDetails().stream()
                .filter(detail -> detail.getPointLocationId() == 16)
                .forEach(detail -> detail.setStatus(3));
        String contractNo = contractParam.getContractNo();

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractDestroyInventory(contractParam, false);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractDestroyInventory(contractParam, false);


        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractDestroyInventory(contractParam, false);


        commonComp.approve(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractDestroyInventory(contractParam, true);

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractDestroyInventory(contractParam, true);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractDestroyInventory(contractParam, true);
    }


    @Test
    public void test3Add() {
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail addTestDetail = commonComp.addTestDetail();
        commonComp.revertDetailPointLocation(addTestDetail.getPointLocationId());
        contractParam.getDetails().add(addTestDetail);
        String contractNo = contractParam.getContractNo();

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractAddInventory(contractParam, false);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractAddInventory(contractParam, true);

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractAddInventory(contractParam, false);

        commonComp.approve(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractOccupyInventory(contractParam);
        assertAlertContractAddInventory(contractParam, false);
    }

    private void assertAlertContractOccupyInventory(PointLocationModel.InventoryOccupationContractParam contractParam) {
        Map<Integer, List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail>> pidMapDetails = contractParam.getDetails()
                .stream()
                .filter(detail -> AlterStatus.OCCUPY == AlterStatus.valueOf(detail.getStatus()))
                .collect(Collectors.groupingBy(PointLocationModel.InventoryOccupationContractParam.OccupationDetail::getPointLocationId));

        pidMapDetails.forEach((pid, details) -> {
            DateMapSumAreaUtil dateMapSumAreaUtil = new DateMapSumAreaUtil();
            details.forEach(detail ->
                    getDates(detail.getStartDate(), detail.getEndDate())
                            .forEach(date -> dateMapSumAreaUtil.add(date, detail.getAmount()))
            );
            dateMapSumAreaUtil.getDateMapSumAreaResult().forEach((date, area) -> {
                PointLocationInventory inventory = inventoryRep.query(pid, date, date).get(0);
                Assert.assertEquals(String.format("pid:%s库存 时间:%s", pid, date), area, (inventory.getSellArea() - inventory.getNotSellArea()), 0.00);
            });
        });
    }


    private void assertAlertContractDestroyInventory(PointLocationModel.InventoryOccupationContractParam contractParam, boolean real) {
        contractParam.getDetails()
                .stream()
                .filter(detail -> AlterStatus.DESTROY == AlterStatus.valueOf(detail.getStatus()))
                .forEach(detail -> {
                    List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate());
                    if (real)
                        inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", inventory.getSellArea(), inventory.getNotSellArea(), 0.00));
                    else
                        inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", detail.getAmount().floatValue(), (inventory.getSellArea() - inventory.getNotSellArea()), 0.00));
                });
    }

    private void assertAlertContractAddInventory(PointLocationModel.InventoryOccupationContractParam contractParam, boolean isRollback) {
        contractParam.getDetails()
                .stream()
                .filter(detail -> AlterStatus.ADD == AlterStatus.valueOf(detail.getStatus()))
                .forEach(detail -> {
                    List<PointLocationInventory> inventoryList = inventoryRep.query(detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate());
                    if (isRollback)
                        inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", inventory.getSellArea(), inventory.getNotSellArea(), 0.00));
                    else
                        inventoryList.stream().forEach(inventory -> Assert.assertEquals("pid:" + detail.getPointLocationId() + "库存", detail.getAmount().floatValue(), (inventory.getSellArea() - inventory.getNotSellArea()), 0.00));
                });
    }


}
