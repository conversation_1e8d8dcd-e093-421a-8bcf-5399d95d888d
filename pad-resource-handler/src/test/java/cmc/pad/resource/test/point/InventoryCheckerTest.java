package cmc.pad.resource.test.point;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.InventoryChecker;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import cmc.pad.resource.test.AbstractCmcQasTest;
import com.google.common.collect.Lists;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.time.LocalDate;

/**
 * Created by fuyuanpu on 2022/6/27.
 */
public class InventoryCheckerTest extends AbstractCmcQasTest {
    InventoryChecker inventoryChecker = ServiceLocator.current().getInstance(InventoryChecker.class);

    @Test
    public void test() {
        inventoryChecker.checkInventoryUsableArea(
                Lists.newArrayList(
                        new RealOccupyData(8,
                                RealOccupyData.DateOccupyArea.of(
                                        LocalDate.of(2022,1,1),
                                        LocalDate.of(2026,12,1),
                                        1F
                                        )
                        ))
        );
    }
}
