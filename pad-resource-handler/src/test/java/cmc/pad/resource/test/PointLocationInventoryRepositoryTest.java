package cmc.pad.resource.test;

import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.time.LocalDate;

/**
 * Created by fuwei on 2022/1/21.
 */
public class PointLocationInventoryRepositoryTest extends AbstractCmcQasTest {
    PointLocationInventoryRepository resp = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);
    PointLocationInfoRepository pLocationResp = ServiceLocator.current().getInstance(PointLocationInfoRepository.class);

    @Test
    public void tt() {
//        boolean b = resp.queryHaveOccupy(95);
//        System.out.println(b);
        PointLocationInventory pointLocationInventory = resp.queryMinRemainderArea(40, null);
        System.out.println(pointLocationInventory.getSellArea() - pointLocationInventory.getNotSellArea());
    }
}
