package cmc.pad.resource.test.point;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.OccupyDetailParamChecker;
import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.test.AbstractCmcQasTest;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.ioc.ServiceLocator;
import mtime.lark.util.lang.FaultException;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by fuyuanpu on 2022/6/30.
 */
@Slf4j
public class OccupyDetailParamCheckerTest extends AbstractCmcQasTest {
    OccupyDetailParamChecker checker = ServiceLocator.current().getInstance(OccupyDetailParamChecker.class);
    @Rule
    public ExpectedException exception = ExpectedException.none();

    @Test
    public void checkAllPointLocationBusinessTypeSame() {
        exception.expect(FaultException.class);
        exception.expectMessage("点位id:[20974, 21013]业务类型和入参定义不一致");
        checker.checkAllPointLocationBusinessTypeSame(buildTestContract("123", ContractType.NEW_CONTRACT));
    }

    @Test(expected = FaultException.class)
    public void checkPointLocationDateIntersection() {//测交
        checkException(() -> checker.checkDateRange(buildTestContract("123", ContractType.NEW_CONTRACT).getDetails()));
    }

    @Test(expected = FaultException.class)
    public void checkValidDateRange() {
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> details = buildTestContract("123", ContractType.NEW_CONTRACT).getDetails();
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail = details.get(0);
        detail.setStartDate(detail.getEndDate().plusDays(1));
        checkException(() -> checker.checkDateRange(details));
    }

    private void checkException(Runnable runnable) {
        try {
            runnable.run();
        } catch (FaultException e) {
            log.error(e.getMessage());
            throw e;
        }
    }

    @Test
    public void test() {
        checker.checkDateRange(buildTest().getDetails());
    }

    public PointLocationModel.InventoryOccupationContractParam buildTest() {
        String json = "{\"contract_type\":\"1\",\"contract_no\":\"YXDZJY202207160007\",\"business_type_code\":\"WZ\",\"details\":[{\"point_location_id\":\"9708\",\"start_date\":\"2022-10-01\",\"end_date\":\"2023-09-30\",\"id\":\"72A2BF51EAD7E058BE4E3A597BF32AB4\",\"cinema_inner_code\":\"336\",\"status\":\"1\",\"amount\":\"130\"},{\"point_location_id\":\"9708\",\"start_date\":\"2023-10-01\",\"end_date\":\"2024-09-30\",\"id\":\"9C305A020ED8C7EFE12DBF9F46B32319\",\"cinema_inner_code\":\"336\",\"status\":\"1\",\"amount\":\"130\"},{\"point_location_id\":\"9708\",\"start_date\":\"2024-10-01\",\"end_date\":\"2025-09-30\",\"id\":\"0786AA16E908F8C9EEEBF71B11D4A5D4\",\"cinema_inner_code\":\"336\",\"status\":\"1\",\"amount\":\"130\"},{\"point_location_id\":\"9708\",\"start_date\":\"2025-10-01\",\"end_date\":\"2026-09-30\",\"id\":\"4055B1EBC356EF0D61FBE708D53A84DB\",\"cinema_inner_code\":\"336\",\"status\":\"1\",\"amount\":\"130\"},{\"point_location_id\":\"9708\",\"start_date\":\"2026-10-01\",\"end_date\":\"2027-09-30\",\"id\":\"B5C8F88564E364AD84FF9927388C3AC1\",\"cinema_inner_code\":\"336\",\"status\":\"1\",\"amount\":\"130\"},{\"point_location_id\":\"9710\",\"start_date\":\"2022-10-01\",\"end_date\":\"2023-09-30\",\"id\":\"2B441E4064F66E5E279A07F8E0E98939\",\"cinema_inner_code\":\"808\",\"status\":\"1\",\"amount\":\"150\"},{\"point_location_id\":\"9710\",\"start_date\":\"2023-10-01\",\"end_date\":\"2024-09-30\",\"id\":\"8F9730E3971706C0193E22F0024858BF\",\"cinema_inner_code\":\"808\",\"status\":\"1\",\"amount\":\"150\"},{\"point_location_id\":\"9710\",\"start_date\":\"2024-10-01\",\"end_date\":\"2025-09-30\",\"id\":\"03E0A2A2FEADBD04DCF54A872C8DE1B6\",\"cinema_inner_code\":\"808\",\"status\":\"1\",\"amount\":\"150\"},{\"point_location_id\":\"9710\",\"start_date\":\"2025-10-01\",\"end_date\":\"2026-09-30\",\"id\":\"5FDDD92F8DEF7B840EBAA35592D132B7\",\"cinema_inner_code\":\"808\",\"status\":\"1\",\"amount\":\"150\"},{\"point_location_id\":\"9710\",\"start_date\":\"2026-10-01\",\"end_date\":\"2027-09-30\",\"id\":\"7FE4D636FE2B1BECE6435135C11CD5AC\",\"cinema_inner_code\":\"808\",\"status\":\"1\",\"amount\":\"150\"}]}";
        return JSON.parseObject(json, PointLocationModel.InventoryOccupationContractParam.class);
    }

    public PointLocationModel.InventoryOccupationContractParam buildTestContract(String contractNo, ContractType contractType) {
        PointLocationModel.InventoryOccupationContractParam param = new PointLocationModel.InventoryOccupationContractParam();
        param.setContractType(contractType.value());
        param.setContractNo(contractNo);
        param.setBusinessTypeCode("GD");
        List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> list = Lists.newArrayList();
        AtomicInteger i = new AtomicInteger(11);
        int pid = 20974;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail1.setCinemaInnerCode("111");
        detail1.setPointLocationId(pid);
        detail1.setAmount(80f);
        detail1.setStartDate(LocalDate.of(2022, 5, 23));
        detail1.setEndDate(LocalDate.of(2022, 5, 30));
        detail1.setId(pid + "_" + (i.getAndIncrement()));
        detail1.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail1);

        pid = 1002;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail2.setCinemaInnerCode("111");
        detail2.setPointLocationId(pid);
        detail2.setAmount(80f);
        detail2.setStartDate(LocalDate.of(2022, 5, 23));
        detail2.setEndDate(LocalDate.of(2022, 5, 25));
        detail2.setId(pid + "_" + (i.getAndIncrement()));
        detail2.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail2);

        pid = 1002;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail3.setCinemaInnerCode("111");
        detail3.setPointLocationId(pid);
        detail3.setAmount(50f);
        detail3.setStartDate(LocalDate.of(2022, 6, 23));
        detail3.setEndDate(LocalDate.of(2022, 6, 30));
        detail3.setId(pid + "_" + (i.getAndIncrement()));
        detail3.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail3);

        pid = 21013;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail4 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail4.setCinemaInnerCode("111");
        detail4.setPointLocationId(pid);
        detail4.setAmount(80f);
        detail4.setStartDate(LocalDate.of(2022, 5, 23));
        detail4.setEndDate(LocalDate.of(2022, 5, 30));
        detail4.setId(pid + "_" + (i.getAndIncrement()));
        detail4.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail4);

        pid = 21013;
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail5 = new PointLocationModel.InventoryOccupationContractParam.OccupationDetail();
        detail5.setCinemaInnerCode("111");
        detail5.setPointLocationId(pid);
        detail5.setAmount(80f);
        detail5.setStartDate(LocalDate.of(2022, 6, 23));
        detail5.setEndDate(LocalDate.of(2022, 6, 30));
        detail5.setId(pid + "_" + (i.getAndIncrement()));
        detail5.setStatus(AlterStatus.OCCUPY.value());
        list.add(detail5);

        param.setDetails(list);
        return param;
    }
}
