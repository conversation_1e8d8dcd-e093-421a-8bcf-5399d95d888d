package cmc.pad.resource.test;

import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCityDistrictRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlPointLocationInventoryRepository;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.time.LocalDate;

/**
 * Created by fuwei on 2020/10/16.
 */
public class MysqlCityDistrictRepositoryTest extends AbstractCmcQasTest {
    private MysqlCityDistrictRepository repository = new MysqlCityDistrictRepository();
    private PointLocationInventoryRepository plIRepository = new MysqlPointLocationInventoryRepository();
    private DictionaryDomainService dictionaryDomainService = ServiceLocator.current().getInstance(DictionaryDomainService.class);

    @Test
    public void test() {
        plIRepository
                .updateSellArea(9, 19.99f, LocalDate.of(2025, 5, 24));
    }
}
