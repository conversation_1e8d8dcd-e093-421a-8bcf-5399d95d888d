package cmc.pad.resource.test.point.alteration.contract;

import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.resource.PointLocationModel;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDate;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark.EXPAND_NEW_DATE_RANGE_AND_AREA;

/**
 * Created by fuyuanpu on 2022/6/26.
 */
public class ExpandNewDateRangeAndAreaTest extends AbstractTest {
    private static final String TEST_POINT_LOCATION_CONTRACT_CODE = "test-point-location-1011";

    @Before
    public void initNewContract() {
        super.initNewContract(TEST_POINT_LOCATION_CONTRACT_CODE);
    }

    private PointLocationModel.InventoryOccupationContractParam buildEXPAND_NEW_DATE_RANGE_AND_AREAContract() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildTestContract(ContractType.ALTER_CONTRACT);
        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail1 = contractParam.getDetails().get(0);
        LocalDate startDate = detail1.getStartDate();
        detail1.setStartDate(startDate.minusDays(10));
        detail1.setEndDate(startDate.minusDays(5));
        detail1.setAmount(detail1.getAmount() + 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail2 = contractParam.getDetails().get(1);
        LocalDate endDate = detail2.getEndDate();
        detail2.setStartDate(endDate.plusDays(10));
        detail2.setEndDate(endDate.plusDays(20));
        detail2.setAmount(detail2.getAmount() + 10);

        PointLocationModel.InventoryOccupationContractParam.OccupationDetail detail3 = contractParam.getDetails().get(2);
        LocalDate startDate1 = detail3.getStartDate();
        detail3.setStartDate(startDate1.minusDays(10));
        detail3.setEndDate(startDate1.minusDays(5));

        return contractParam;
    }

    @Test
    public void test8Update_EXPAND_NEW_DATE_RANGE_AND_AREA() {
        PointLocationModel.InventoryOccupationContractParam contractParam = buildEXPAND_NEW_DATE_RANGE_AND_AREAContract();
        String contractNo = contractParam.getContractNo();

        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractSubmitRealInventory(contractNo, EXPAND_NEW_DATE_RANGE_AND_AREA);

        commonComp.cancel(contractNo);
        commonComp.waitContractProcess(contractNo);
        commonComp.assertAlertContractLastVersionInventory(contractNo);

        contractParam = buildEXPAND_NEW_DATE_RANGE_AND_AREAContract();
        commonComp.submit(contractParam);
        commonComp.waitContractProcess(contractNo);
        assertAlertContractSubmitRealInventory(contractNo, EXPAND_NEW_DATE_RANGE_AND_AREA);

        commonComp.approve(contractNo);
        commonComp.waitContractProcess(contractNo);
        assertAlterContractDetailInventory(contractNo);
    }
}
