package cmc.pad.resource.handler;

import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.OccupyOrCancelParam;
import cmc.pad.resource.test.AbstractCmcQasTest;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import static cmc.pad.resource.test.PointInventoryOccupationServiceTest.TEST_POINT_LOCATION_CONTRACT_CODE;

/**
 * Created by fuyuanpu on 2022/5/18.
 */
public class PointLocationInventoryOccupyOrCancelHandlerTest extends AbstractCmcQasTest {
    PointLocationInventoryOccupyOrCancelHandler handler = ServiceLocator.current().getInstance(PointLocationInventoryOccupyOrCancelHandler.class);

    @Test
    public void process() {
        handler.process(
                new OccupyOrCancelParam(ContractType.ALTER_CONTRACT.value(), TEST_POINT_LOCATION_CONTRACT_CODE, ContractStatus.APPROVAL.value())
                , null);
    }
}