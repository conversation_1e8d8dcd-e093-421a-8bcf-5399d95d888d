package cmc.pad.resource.handler;

import cmc.pad.resource.application.command.point.inventory.PointInventoryManageService;
import cmc.pad.resource.test.AbstractCmcQasTest;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2022/3/31.
 */
public class PointLocationGenerateInventoryHandlerTest extends AbstractCmcQasTest {
    PointInventoryManageService pointInventoryManageService = ServiceLocator.current().getInstance(PointInventoryManageService.class);

    @Test
    public void testProcess() {
        pointInventoryManageService.initInventory(1, 9);
    }
}