<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <id>bin</id>
    <formats>
        <format>tar.gz</format>
        <format>dir</format>
    </formats>
    <files>
        <file>
            <source>build/build.json</source>
            <outputDirectory></outputDirectory>
            <filtered>true</filtered>
        </file>
        <file>
            <source>build/pom.properties</source>
            <outputDirectory></outputDirectory>
            <filtered>true</filtered>
        </file>
    </files>
    <fileSets>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory></outputDirectory>
            <includes>
                <include>**/file/*.*</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/resources</directory>
            <outputDirectory></outputDirectory>
            <excludes>
                <exclude>**/file/*.*</exclude>
            </excludes>
            <filtered>true</filtered>
        </fileSet>
    </fileSets>
    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <scope>runtime</scope>
            <outputDirectory>lib</outputDirectory>
            <outputFileNameMapping>${artifact.artifactId}-${artifact.baseVersion}.${artifact.extension}
            </outputFileNameMapping>
        </dependencySet>
    </dependencySets>
</assembly>