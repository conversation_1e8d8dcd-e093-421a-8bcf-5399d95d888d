# PAD Resource Management System

## Project Overview
This is a multi-module Maven project for PAD (Pad) resource management system built with Java 8 and Spring Boot. The system uses the Lark framework (mtime.lark) and follows a modular architecture pattern.

## Development Commands

### Build and Package
```bash
# Build entire project
mvn clean compile

# Package all modules
mvn clean package

# Install to local repository
mvn clean install

# Skip tests during build (tests are disabled by default in root pom)
mvn clean package -DskipTests=true
```

### Testing
```bash
# Run all tests (note: tests are skipped by default in root pom.xml)
mvn test

# Run specific test class
mvn test -Dtest=DiscountControllerBasicTest

# Run discount controller acceptance tests
./run-discount-test.sh

# Run tests for specific module
cd pad-resource-admin-api && mvn test
```

### Running Services
```bash
# Start admin API service (runs on port 8125)
cd pad-resource-admin-api
java -jar target/pad-resource-admin-api-1.0-SNAPSHOT.jar

# Check service health
curl http://localhost:8125/health
```

## Module Architecture

The project follows a layered architecture with clear separation of concerns:

### Core Modules
- **pad-resource-core**: Domain logic, entities, and business rules using DDD patterns
  - `domain/`: Domain entities and business logic
  - `application/`: Application services and use cases  
  - `infrastructures/`: Data access and external integrations
  - `adapter/`: Adapters for external systems

- **pad-resource-admin-service**: Business service implementations
- **pad-resource-admin-service-contract**: Service contracts and interfaces
- **pad-resource-handler**: Event handlers and message processing
- **pad-resource-task**: Background task processing

### API Layers
- **pad-resource-admin-api**: REST API layer (Spring Boot application)
  - Main class: `cmc.pad.resource.admin.api.AdminApiBootstrap`
  - Controllers in `controller/` package
  - Port: 8125

- **pad-resource-admin-web**: Web frontend controllers

## Key Technologies
- **Framework**: Lark framework (mtime.lark) v1.1.48-SNAPSHOT
- **Java**: 1.8
- **Spring Boot**: With Jetty server
- **Database**: Uses lark-db for data access
- **Testing**: JUnit with PowerMock, MariaDB4j for embedded testing
- **Build**: Maven with custom assembly plugin

## Package Structure
All code follows the `cmc.pad` package namespace with component scanning enabled for the entire `cmc.pad` package tree.

## Development Notes
- Tests are disabled by default in the root pom.xml (`<skipTests>true</skipTests>`)
- The project uses a custom RestApplication from the Lark framework instead of standard Spring Boot
- BigDecimal refactoring is documented in `价格字段BigDecimal类型重构.md`
- Discount controller testing procedures are in `DiscountController-测试说明.md`

## Dependencies
The project integrates with several internal services:
- File service (mx.common.file-service-contract)
- Mail service (mx.common.mail-service-contract) 
- Location services (cmc.mdm.location-*)
- Data dictionary (cmc.basic.data-dict-contract)
- HDFS integration (cmc.tohdfs.cmc-tohdfs-sdk)