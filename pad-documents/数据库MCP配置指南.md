# 数据库MCP配置指南

## 推荐方案

基于搜索结果，我为您推荐以下几个免费的数据库MCP服务器：

### 方案一：DBHub - Universal Database MCP Server（强烈推荐）

**项目地址**：https://github.com/bytebase/dbhub

**特点**：
- 支持多种数据库：MySQL、PostgreSQL、MariaDB、SQL Server、SQLite、Oracle
- 由Bytebase团队开发，专业可靠
- 免费开源
- 支持Docker部署

#### 安装配置

**方法1：使用Docker（推荐）**
```bash
# MySQL示例
docker run --rm -p 3001:3001 -e DSN="mysql://mxuser:mxuser123456@**************:3306/cmc_pad_resource" bytebase/dbhub
```

**方法2：本地安装**
```bash
# 安装
npm install -g @bytebase/dbhub

# 运行
dbhub --database-url="mysql://mxuser:mxuser123456@**************:3306/cmc_pad_resource"
```

#### MCP配置文件
在Claude Desktop的配置文件中添加：

**macOS路径**：`~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "dbhub": {
      "command": "docker",
      "args": [
        "run", "--rm", "-i",
        "-e", "DATABASE_URL=mysql://mxuser:mxuser123456@**************:3306/cmc_pad_resource",
        "bytebase/dbhub"
      ]
    }
  }
}
```

或者如果本地安装：
```json
{
  "mcpServers": {
    "dbhub": {
      "command": "dbhub",
      "args": [
        "--database-url=mysql://mxuser:mxuser123456@**************:3306/cmc_pad_resource"
      ]
    }
  }
}
```

### 方案二：SQLite MCP Server（学习用）

如果想先学习MCP配置，可以从SQLite开始：

```json
{
  "mcpServers": {
    "sqlite": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sqlite", "/path/to/test.db"]
    }
  }
}
```

### 方案三：自定义MySQL MCP Server

如果上述方案不满足需求，可以考虑自己开发：

**基础框架**：
```javascript
// package.json
{
  "name": "mysql-mcp-server",
  "version": "1.0.0",
  "dependencies": {
    "@modelcontextprotocol/sdk": "^0.1.0",
    "mysql2": "^3.0.0"
  }
}

// server.js
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const mysql = require('mysql2/promise');

const server = new Server({
  name: "mysql-mcp-server",
  version: "1.0.0"
});

// 数据库连接配置
const dbConfig = {
  host: '**************',
  port: 3306,
  user: 'mxuser',
  password: 'mxuser123456',
  database: 'cmc_pad_resource'
};
```

## 配置步骤

### 1. 选择方案
推荐使用**DBHub**，因为它专门为MCP设计，支持MySQL。

### 2. 安装Docker（如果使用Docker方案）
```bash
# macOS
brew install docker

# 或者下载Docker Desktop
```

### 3. 配置Claude Desktop
编辑配置文件：`~/Library/Application Support/Claude/claude_desktop_config.json`

### 4. 重启Claude Desktop
配置完成后重启Claude Desktop使配置生效。

### 5. 测试连接
重启后，您可以在Claude中直接询问数据库相关问题，MCP会自动连接数据库。

## 使用示例

配置完成后，您可以这样使用：

```
请查询cmc_pad_resource数据库中的所有表
```

```
请查看用户表的结构
```

```
请查询最近10条广告数据
```

## 注意事项

### 安全考虑
1. **网络安全**：确保数据库服务器允许从您的IP访问
2. **权限控制**：建议为MCP创建专门的只读数据库用户
3. **密码安全**：避免在配置文件中明文存储密码，考虑使用环境变量

### 性能考虑
1. **连接池**：DBHub通常会自动管理连接池
2. **查询限制**：避免执行大量数据的查询
3. **索引优化**：确保常用查询有适当的索引

### 故障排除
1. **连接失败**：检查网络连通性和数据库配置
2. **权限错误**：确认数据库用户权限
3. **端口冲突**：确保MCP服务器端口未被占用

## 建议的配置流程

1. **先用Docker测试DBHub**
2. **验证数据库连接**
3. **配置Claude Desktop**
4. **测试基本查询功能**
5. **为测试用例准备数据查询**

配置完成后，您就可以通过MCP直接查询数据库，为API验收测试准备测试数据了。

需要我帮您进一步配置或者开始查看项目的API结构吗？
