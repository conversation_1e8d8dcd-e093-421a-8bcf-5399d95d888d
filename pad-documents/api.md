- [1 基本信息](#1-%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AF)
  - [1.1 接口请求说明](#11-%E6%8E%A5%E5%8F%A3%E8%AF%B7%E6%B1%82%E8%AF%B4%E6%98%8E)
  - [1.2 公共响应参数](#12-%E5%85%AC%E5%85%B1%E5%93%8D%E5%BA%94%E5%8F%82%E6%95%B0)
  - [1.3 枚举常量](#13-%E6%9E%9A%E4%B8%BE%E5%B8%B8%E9%87%8F)
    - [1.3.1 城市级别(CityLevel)](#131-%E5%9F%8E%E5%B8%82%E7%BA%A7%E5%88%ABcitylevel)
    - [1.3.2 广告业务类型(BusinessType)](#132-%E5%B9%BF%E5%91%8A%E4%B8%9A%E5%8A%A1%E7%B1%BB%E5%9E%8Bbusinesstype)
    - [1.3.3 库存占位(Occupation)状态](#133-%E5%BA%93%E5%AD%98%E5%8D%A0%E4%BD%8Doccupation%E7%8A%B6%E6%80%81)
    - [1.3.4 影院级别(CinemaLevel)](#134-%E5%BD%B1%E9%99%A2%E7%BA%A7%E5%88%ABcinemalevel)
    - [1.3.5 折扣方式(DiscountMethod)](#135-%E6%8A%98%E6%89%A3%E6%96%B9%E5%BC%8Fdiscountmethod)
    - [1.3.6 租赁方式(LeaseMethod)](#136-%E7%A7%9F%E8%B5%81%E6%96%B9%E5%BC%8Fleasemethod)
  - [1.4 错误码](#14-%E9%94%99%E8%AF%AF%E7%A0%81)
- [2 刊例价](#2-%E5%88%8A%E4%BE%8B%E4%BB%B7)
  - [2.1 查询冠名厅租赁刊例价](#21-%E6%9F%A5%E8%AF%A2%E5%86%A0%E5%90%8D%E5%8E%85%E7%A7%9F%E8%B5%81%E5%88%8A%E4%BE%8B%E4%BB%B7)
  - [2.2 查询固定点位租赁刊例价](#22-%E6%9F%A5%E8%AF%A2%E5%9B%BA%E5%AE%9A%E7%82%B9%E4%BD%8D%E7%A7%9F%E8%B5%81%E5%88%8A%E4%BE%8B%E4%BB%B7)
  - [2.3 查询外租区域租赁刊例价](#23-%E6%9F%A5%E8%AF%A2%E5%A4%96%E7%A7%9F%E5%8C%BA%E5%9F%9F%E7%A7%9F%E8%B5%81%E5%88%8A%E4%BE%8B%E4%BB%B7)
  - [2.4 查询营销点位租赁刊例价](#24-%E6%9F%A5%E8%AF%A2%E8%90%A5%E9%94%80%E7%82%B9%E4%BD%8D%E7%A7%9F%E8%B5%81%E5%88%8A%E4%BE%8B%E4%BB%B7)
  - [2.5 查询影厅座位租赁刊例价](#25-%E6%9F%A5%E8%AF%A2%E5%BD%B1%E5%8E%85%E5%BA%A7%E4%BD%8D%E7%A7%9F%E8%B5%81%E5%88%8A%E4%BE%8B%E4%BB%B7)
  - [2.6 查询宣传点位租赁刊例价](#26-%E6%9F%A5%E8%AF%A2%E5%AE%A3%E4%BC%A0%E7%82%B9%E4%BD%8D%E7%A7%9F%E8%B5%81%E5%88%8A%E4%BE%8B%E4%BB%B7)
- [3. 折扣](#3-%E6%8A%98%E6%89%A3)
  - [3.1 查询折扣系数](#31-%E6%9F%A5%E8%AF%A2%E6%8A%98%E6%89%A3%E7%B3%BB%E6%95%B0)
- [4. 资源](#4-%E8%B5%84%E6%BA%90)
  - [4.1 查询影城资源](#41-%E6%9F%A5%E8%AF%A2%E5%BD%B1%E5%9F%8E%E8%B5%84%E6%BA%90)
- [5. 库存](#5-%E5%BA%93%E5%AD%98)
  - [5.1 创建库存占用](#51-%E5%88%9B%E5%BB%BA%E5%BA%93%E5%AD%98%E5%8D%A0%E7%94%A8)
  - [5.2 取消库存占用](#52-%E5%8F%96%E6%B6%88%E5%BA%93%E5%AD%98%E5%8D%A0%E7%94%A8)
  - [5.3 更新库存占用](#53-%E6%9B%B4%E6%96%B0%E5%BA%93%E5%AD%98%E5%8D%A0%E7%94%A8)



# 1 基本信息

## 1.1 接口请求说明

接口参数和数据使用utf-8编码。

当使用http的GET方式请求时，通过url的参数来传递接口参数.

当使用POST方式请求时，需要将接口参数构造成Json字符串作为http请求的body传入。


## 1.2 公共响应参数

 名称   | 类型    | 是否必须| 描述 
--- | --- | --- | --- 
 status | Integer | 必须| 接口状态码（0表示请求成功；>0表示请求出错，其值为错误码）
 msg    | String  | 特殊必须| 接口的出错信息，当status>0时，必须有值
 data   | Object  | 特殊必须| 接口调用的结果，当status=0时，必须有值

## 1.3 枚举常量

### 1.3.1 城市级别(CityLevel)

　编码　|　名称　
---|---
L1|一线
L2|二线
L3|三线
L4|四线

### 1.3.2 广告业务类型(BusinessType)

　编码　|　名称　
---|---
YT|影厅座位租赁
YX|营销点位租赁
WZ|外租区域租赁
GD|固定点位租赁
XC|宣传点位租赁
GMT|冠名厅租赁

### 1.3.3 库存占位(Occupation)状态

　编码　|　说明　
---|---
ACTIVE|占用中
CANCELLED|已取消

### 1.3.4 影院级别(CinemaLevel)

　编码　|　名称　
---|---
S|S
A|A
B|B
C|C
D|D

### 1.3.5 折扣方式(DiscountMethod)

　编码　|　说明　
---|---
AREA|按面积计算折扣
DURATION|按时间计算折扣

### 1.3.6 租赁方式(LeaseMethod)

　编码　|　说明　
---|---
AREA|按面积租赁
QUANTITY|按数量租赁


## 1.4 错误码

错误码|错误信息
---|---
1001|缺少xx参数
1002|参数xx格式错误
1003|xx服务超时
1004|xx服务不可用
2004|影院不存在
2005|影城未评级
2006|影城级别不存在
2007|影城没有对应的影城级别
3001|城市未评级
4006|租赁方式编码不存在
5001|不存在的广告业务类型
5006|影厅类型编码不存在
6001|没有匹配的折扣系数
6002|不支持的折扣类别
7001|库存占用不存在
7002|库存占用已创建
7003|营销点位面积库存不足
7004|外租区域面积库存不足
7005|固定点位面积库存不足
7006|宣传点位个数库存不足

# 2 刊例价

## 2.1 查询冠名厅租赁刊例价

地址：/named-movie-hall-leasing/price/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
cinema_code | String | 必选  | 影院编码(内码)
movie_hall_type|String|可选|影厅类型编码，见cmc维数据"影厅类型"

响应参数：

| 名称              | 类型                             | 是否必须  | 描述                |
| ----------------- | -------------------------------- | --------  | ------------------- |
| data              | GMTPriceInfo[] | 特殊必选 |      | 查询到的刊例价      |
| └ cinema_code     | String                           | 必选      | 影院编码(内码)      |
| └ movie_hall_type | String                           | 可选      | 影厅类型编码        |
| └ base_price      | float                              | 必须     | 基础价，单位：元/年|


请求示例：

```
curl "...?cinema_code=4"
```

响应示例：

```
{
  "status": 0,
  "data": [
      {
        "cinema_code": "333",
        "movie_hall_type": "I",
        "base_price": 400
      }
  ]
}
```


## 2.2 查询固定点位租赁刊例价

地址：/fixed-point-leasing/price/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
city_level | String | 可选 | 城市级别编码
cinema_level | String | 可选 | 影院级别编码
cinema_code | String | 可选  | 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。

响应参数：

名称 | 类型 | 是否必须 | 描述
---|---|---|---
data|GDPriceInfo[]|特殊必选|查询到的刊例价
　└ city_level | String | 必须  | 城市级别编码
　└ cinema_level | String | 必须  | 影院级别编码
　└ base_price| float | 必须|基础价，单位：元/平米/月

请求示例：

```
curl "...?city_level=L3&cinema_level=A"
```

响应示例：

```
{
  "status": 0,
  "data": [
      {
        "city_level": "L3",
        "cinema_level": "A",
        "base_price": 4000
      }
  ]
}
```


## 2.3 查询外租区域租赁刊例价

地址：/outer-area-leasing/price/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
city_level | String | 可选  | 城市级别编码
cinema_level | String | 可选  | 影院级别编码
cinema_code | String | 可选 | 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。

响应参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
data|WZPriceInfo[]|特殊必选|查询到的刊例价
　└ city_level | String | 必须  | 城市级别编码
　└ cinema_level | String | 必须  | 影院级别编码
　└ base_price| float | 必须|基础价，单位：元/平米/月

请求示例：

```
curl "...?city_level=L1&cinema_level=S"
```

响应示例：

```
{
  "status": 0,
  "data": [
      {
        "city_level": "L1",
        "cinema_level": "S",
        "base_price": 4000
      }
  ]
}
```


## 2.4 查询营销点位租赁刊例价

地址：/marketing-point-leasing/price/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
city_level | String | 可选 | 城市级别编码
cinema_level | String | 可选 | 影院级别编码
cinema_code | String | 可选  | 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。
lease_method | String | 可选  | 租赁方式

响应参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
data|YXPriceInfo[]|特殊必选|查询到的刊例价
　└ city_level | String | 必须 |城市级别编码
　└ cinema_level | String | 必须 | 影院级别编码
　└ lease_method | String | 必须 |  租赁方式
　└ base_price| float | 必须|基础价<br>按面积租赁时单位：元/平米/天<br>按数量租赁时单位：元/个/天

请求示例：

```
curl "...?city_level=L2&cinema_level=B"
```

响应示例：
```
{
  "status": 0,
  "data": [
      {
        "city_level": "L2",
        "cinema_level": "B",
        "lease_method": "AREA",
        "base_price": 800
      }
  ]
}
```

## 2.5 查询影厅座位租赁刊例价

地址：/movie-hall-seat-leasing/price/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
city_level | String | 可选  | 城市级别编码
cinema_level | String | 可选  | 影院级别编码
cinema_code | String | 可选 | 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。
movie_hall_type|String|可选|影厅类型编码，见cmc维数据"影厅类型"

响应参数：

名称 | 类型 | 是否必须 | 描述
---|---|---|---
data|YTPriceInfo[]|特殊必选|查询到的刊例价
　└ city_level | String | 必须  | 城市级别编码
　└ cinema_level | String | 必须  | 影院级别编码
　└ movie_hall_type|String|可选影厅类型编码
　└ base_price| float | 必须|基础价，单位：元/月
　└ base_duration| int | 必须6|基础时长，单位：小时
　└ extended_price| float | 必须|续价，单位：元/小时/个

请求示例：

```
curl "...?city_level=L2&cinema_level=B"
```

响应示例：

```
{
  "status": 0,
  "data": [
      {
        "city_level": "L2",
        "cinema_level": "B",
        "movie_hall_type": "M",
        "base_price": 4000,
        "base_duration": 6,
        "extended_price": 666
      }
  ]
}
```

## 2.6 查询宣传点位租赁刊例价

地址：/advertising-point-leasing/price/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
city_level | String | 可选 | 城市级别编码
cinema_level | String | 可选  | 影院级别编码
cinema_code | String | 可选  | 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效。
响应参数：

名称 | 类型 | 是否必须  | 描述
---|---|---|---
data|XCPriceInfo[]|特殊必选|查询到的刊例价
　└ city_level | String | 必须  | 城市级别编码
　└ cinema_level | String | 必须  | 影院级别编码
　└ base_price| float | 必须|基础价，单位：元/月
　└ base_area| int | 必须|基础面积，单位：平米
　└ extended_price| float | 必须|续价，单位：元/平米/月


请求示例：

```
curl "...?city_level=L3&cinema_level=A"
```

响应示例：


```
{
  "status": 0,
  "data": [
      {
        "city_level": "L3",
        "cinema_level": "A",
        "base_price": 4000,
        "base_area": 6,
        "extended_price": 600
      }
  ]
}
```

# 3. 折扣

## 3.1 查询折扣系数

地址：/discount/coefficient/match

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须 |  描述
---|---|---|---
business_type | String | 必须 | 广告业务类型
discount_method | String | 必须 |折扣方式
amount| Integer| 必须 |需要计算折扣的面积/时长


响应参数：

名称 | 类型 | 是否必须 |描述
---|---|---|---
data|float|特殊必须|查询到的折扣系数

请求示例：

```
curl "...?business_type=YT&discount_method=AREA&amount=4"
```

响应示例：

```
{
  "status": 0,
  "data": 0.6
}
```

# 4. 资源

## 4.1 查询影城资源

地址：/resource/query

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须 |描述
---|---|---|---
cinema_code | String | 必选 | 影院编码(内码)
business_type|String|可选|广告业务类型(仅支持营销点位租赁、外租区域租赁、固定点位租赁、宣传点位租赁四种业务类型)

响应参数：

名称 | 类型 | 是否必须 | 描述
---|---|---|---
data|ResourceInfo[]|特殊必选|查询到的刊例价
　└ business_type|String|必须|广告业务类型
　└ amount | Integer | 必选 | 资源体量，宣传点位租赁是为个数，其余业务类型为平面数

请求示例：

```
curl "...?cinema_code=4"
```

响应示例：

```
{
  "status": 0,
  "data": [
      {
        "business_type": "XY",
        "amount": 4000
      }
  ]
}
```

# 5. 库存

## 5.1 创建库存占用

地址：/inventory/occupation/create

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须 |  描述
---|---|---|---
cinema_code| String | 必须 | 影院编码
contract_no|String|必须|合同编号
business_type|String|必须|广告业务类型
amount| Integer| 必须 | 占用的面积或者个数，取决于广告业务类型
start_date| String| 必须 | 占位开始日期，格式：yyyy-MM-dd
end_date| String| 必须 |占位接收日期，格式：yyyy-MM-dd


响应参数：

名称 | 类型 | 是否必须 |描述
---|---|---|---
data|OccupationInfo|特殊必选|占位信息
　└ occupation_id| Integer | 必须 |Occupation的ID
　└ cinema_code| String | 必须 |影院编码
　└ contract_no|String|必须|合同编号
　└ business_type|String|必须|广告业务类型
　└ status|String|必须|库存占位状态
　└ amount| Integer| 必须 | 占用的面积或者个数，取决于广告业务类型
　└ start_date| String| 必须 |占位开始日期，格式：yyyy-MM-dd
　└ end_date| String| 必须 |占位接收日期，格式：yyyy-MM-dd

请求示例：

```
curl "...?cinema_code=3&contract_no=4&business_type=XT&amount=100&start_date=2019-05-06&end_date=2019-05-06"
```

响应示例：

```
{
    "status": 0,
    "data": {
        "occupation_id"=12,
        "cinema_code"="123",
        "contract_no"="123213123",
        "business_type"="XY",
         "status"="ACTIVE"
        "amount"=12,
        "start_date"="2019-05-06",
        "end_date"="2019-05-06"
    }
}
```


## 5.2 取消库存占用

地址：/inventory/occupation/cancel

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须 | 描述
---|---|---|---
contract_no|String|必须|合同编号

响应参数：

名称 | 类型 | 是否必须 |描述
---|---|---|---
data|OccupationInfo|特殊必选|占位信息
　└ occupation_id| String | 必须 |Occupation的ID
　└ cinema_code| String | 必须 |影院编码
　└ contract_no|String|必须|合同编号
　└ business_type|String|必须|广告业务类型
　└ status|String|必须|库存占位状态
　└ amount| Integer| 必须 | 占用的面积或者个数，取决于广告业务类型
　└ start_date| String| 必须 | 占位开始日期，格式：yyyy-MM-dd
　└ end_date| String| 必须 | 占位接收日期，格式：yyyy-MM-dd

请求示例：

```
curl "...?cinema_code=3&contract_no=4&business_type=XT&amount=100&start_date=2019-05-06&end_date=2019-05-06"
```

响应示例：

```
{
    "status": 0,
    "data": {
        "occupation_id"=12,
        "cinema_code"="123",
        "contract_no"="123213123",
        "business_type"="XY",
        "status"="CANCELLED"
        "amount"=12,
        "start_date"="2019-05-06",
        "end_date"="2019-05-06"
    }
}

```


## 5.3 更新库存占用

地址：/inventory/occupation/update

方法：GET,POST

请求参数：

名称 | 类型 | 是否必须 | 描述
---|---|---|---
cinema_code| String | 必须 |影院编码
contract_no|String|必须|合同编号
business_type|String|必须|广告业务类型
amount| Integer| 必须 |  占用的面积或者个数，取决于广告业务类型
start_date| String| 必须 | 占位开始日期，格式：yyyy-MM-dd
end_date| String| 必须 | 占位接收日期，格式：yyyy-MM-dd


响应参数：

名称 | 类型 | 是否必须 |描述
---|---|---|---
data|OccupationInfo|特殊必选|占位信息
　└ occupation_id| String | 必须 |  Occupation的ID
　└ cinema_code| String | 必须 |影院编码
　└ contract_no|String|必须|合同编号
　└ business_type|String|必须|广告业务类型
　└ status|String|必须|库存占位状态
　└ amount| Integer| 必须 |占用的面积或者个数，取决于广告业务类型
　└ start_date| String| 必须 |占位开始日期，格式：yyyy-MM-dd
　└ end_date| String| 必须 | 占位接收日期，格式：yyyy-MM-dd

请求示例：

```
curl "...?cinema_code=3&contract_no=4&business_type=XT&amount=100&start_date=2019-05-06&end_date=2019-05-06"
```

响应示例：

```
{
    "status": 0,
    "data": {
        "occupation_id"=12,
        "cinema_code"="123",
        "contract_no"="123213123",
        "business_type"="XY",
        "status"="ACTIVE"
        "amount"=12,
        "start_date"="2019-05-06",
        "end_date"="2019-05-06"
    }
}

```


