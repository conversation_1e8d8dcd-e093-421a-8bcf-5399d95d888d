package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.*;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaLevel;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/3/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final CinemaQueryService cinemaQueryService;
    private final CinemaLevelQueryService cinemaLevelQueryService;
    private final CinemaResourceQueryService cinemaResourceQueryService;

    public void pushCinema() {
        log.info(">>> 推送影城信息");
        LocalDateTime timeStamp = LocalDateTime.now();
        cinemaQueryService.eachAll(list -> {
            List<String> pushDataList = list.stream().map(item -> cinemaMsg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String cinemaMsg(Cinema item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_CINEMA).append(splitStr);
        mqData.append(item.getCode()).append(splitStr);
        mqData.append(item.getName()).append(splitStr);
        mqData.append(item.getRegionCode()).append(splitStr);
        mqData.append(item.getRegionName()).append(splitStr);
        mqData.append(item.getCityCode()).append(splitStr);
        mqData.append(item.getCityDistrictCode()).append(splitStr);
        mqData.append(item.getSyncTime()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }

    public void pushCinemaLevel() {
        log.info(">>> 推送影城级别信息");
        LocalDateTime timeStamp = LocalDateTime.now();
        cinemaLevelQueryService.eachAll(item -> pushBigDataService.push(cinemaLevelMsg(item, timeStamp)));
    }

    private String cinemaLevelMsg(CinemaLevel item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_CINEMA_LEVEL).append(splitStr);
        mqData.append(item.getInnerCode()).append(splitStr);
        mqData.append(item.getLevel()).append(splitStr);
        mqData.append(item.getImportId()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }

    public void pushCinemaResource() {
        log.info(">>> 推送影城资源信息");
        LocalDateTime timeStamp = LocalDateTime.now();
        cinemaResourceQueryService.eachAll(item -> pushBigDataService.push(cinemaResourceMsg(item, timeStamp)));
    }

    private String cinemaResourceMsg(CinemaResource item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_CINEMA_RESOURCE).append(splitStr);
        mqData.append(item.getCinemaCode()).append(splitStr);
        mqData.append(item.getAdvertisingPointLeasableQuantity()).append(splitStr);
        mqData.append(item.getMarketingPointLeasableArea()).append(splitStr);
        mqData.append(item.getOuterAreaLeasableArea()).append(splitStr);
        mqData.append(item.getFixedPointLeasableArea()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
