package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.DiscountQueryService;
import cmc.pad.resource.domain.discount.Discount;
import cmc.pad.resource.domain.discount.DiscountRule;
import cmc.pad.resource.util.DataType;
import cmc.pad.resource.util.PushKafkaStringBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/3/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountQueryServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final DiscountQueryService discountQueryService;

    public void push() {
        log.info(">>> 推送折扣系数");
        LocalDateTime timeStamp = LocalDateTime.now();
        discountQueryService.eachAll(list -> {
            List<String> pushDataList = list.stream().map(item -> msg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String msg(Discount item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_DISCOUNT).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getBusinessType()).append(splitStr);
        mqData.append(item.getDiscountType().value()).append(splitStr);
        mqData.append(item.getCreateTime()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(item.getCreator()).append(splitStr);
        mqData.append(item.getUpdator()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }

    public void pushRule() {
        log.info(">>> 推送折扣系数规则");
        LocalDateTime timeStamp = LocalDateTime.now();
        discountQueryService.eachAllRule(list -> {
            List<String> pushDataList = list.stream().map(item -> ruleMsg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    public String ruleMsg(DiscountRule item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_DISCOUNT_RULE).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getDiscountId()).append(splitStr);
        mqData.append(item.getComparisonSymbol()).append(splitStr);
        mqData.append(item.getMin()).append(splitStr);
        mqData.append(item.getMax()).append(splitStr);
        mqData.append(item.getFactor()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
