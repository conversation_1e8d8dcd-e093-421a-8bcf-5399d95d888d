package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.CityInfoQueryService;
import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.domain.city.CityDistrict;
import cmc.pad.resource.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/4/1.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CityServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final CityInfoQueryService cityInfoQueryService;

    public void pushCity() {
        log.info(">>> 推送城市信息");
        LocalDateTime timeStamp = LocalDateTime.now();
        cityInfoQueryService.eachAllCity(list -> {
            List<String> pushDataList = list.stream().map(item -> cityMsg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String cityMsg(City item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_CITY).append(splitStr);
        mqData.append(item.getCode()).append(splitStr);
        mqData.append(item.getName()).append(splitStr);
        mqData.append(item.getRegionCode()).append(splitStr);
        mqData.append(item.getRegionName()).append(splitStr);
        mqData.append(item.getSuperiorCode()).append(splitStr);
        mqData.append(item.getSuperiorName()).append(splitStr);
        mqData.append(item.getCityLevel()).append(splitStr);
        mqData.append(item.getSyncTime()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }

    public void pushCityDistrict() {
        log.info(">>> 推送城市地区信息");
        LocalDateTime timeStamp = LocalDateTime.now();
        cityInfoQueryService.eachAllCityDistrict(list -> {
            List<String> pushDataList = list.stream().map(item -> cityDistrictMsg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String cityDistrictMsg(CityDistrict item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_CITY_DISTRICT).append(splitStr);
        mqData.append(item.getCode()).append(splitStr);
        mqData.append(item.getName()).append(splitStr);
        mqData.append(item.getCityCode()).append(splitStr);
        mqData.append(item.getLevel()).append(splitStr);
        mqData.append(item.getSyncTime()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
