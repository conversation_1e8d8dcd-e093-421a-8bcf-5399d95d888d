package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.OuterAreaLeasingPriceQueryService;
import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/3/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OuterAreaLeasingPriceQueryServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final OuterAreaLeasingPriceQueryService outerAreaLeasingPriceQueryService;

    public void push() {
        log.info(">>> 2推送外租区域租赁刊例价");
        LocalDateTime timeStamp = LocalDateTime.now();
        outerAreaLeasingPriceQueryService.eachAll(list -> {
            List<String> pushDataList = list.stream().map(item -> msg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String msg(OuterAreaLeasingPrice item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_OUTER_AREA_LEASING_PRICE).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getCityLevel()).append(splitStr);
        mqData.append(item.getCinemaLevel()).append(splitStr);
        mqData.append(item.getUnitPrice()).append(splitStr);
        mqData.append(item.getImportId()).append(splitStr);
        mqData.append(item.getUpdater()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(item.getEffectiveDate()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
