package cmc.pad.resource.proxy;

import cmc.tohdfs.front.service.dto.TohdfsFrontDto;
import cmc.tohdfs.front.service.iface.TohdfsFrontService;
import cmc.tohdfs.sdk.dto.SendDataDto;
import cmc.tohdfs.sdk.manager.SendDataManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by mchao on 2017/9/5.
 * Kafka发现消息工具
 */
@Slf4j
@Service
public class PushBigDataService {
    @Autowired
    private TohdfsFrontService tohdfsFrontService;
    @Autowired
    private SendDataManager sendDataManager;

    /**
     * 发送Kafka消息
     *
     * @param mqData 消息体
     */
    public void push(String mqData) {
        push(Lists.newArrayList(mqData));
    }

    public void push(List<String> mqDataList) {
        if (CollectionUtils.isEmpty(mqDataList))
            return;
        TohdfsFrontDto.ReceiveDateRequest request = new TohdfsFrontDto.ReceiveDateRequest();
        request.setData(mqDataList);
        TohdfsFrontDto.ReceiveDateResponse response = tohdfsFrontService.receiveDate(request);
        log.info(">>> push data {}\nresponse code:{}, msg:{}", mqDataList, response.getCode(), response.getMsg());
    }

    public void send(SendDataDto.SendDataRequest request) {
        try {
            request.getData().stream().forEach(data -> log.info(">>>{}", data.toString()));
            sendDataManager.send(request);
            log.info(">>>增量推送大数据成功");
        } catch (Exception e) {
            log.error(">>>增量推送大数据失败", e);
            throw new FaultException(">>>增量推送大数据失败");
        }
    }

    public void sendAll(SendDataDto.SendDataRequest request) {
        try {
            request.getData().stream().forEach(data -> log.info(">>>{}", data.toString()));
            sendDataManager.sendAll(request);
            log.info(">>>全量推送大数据成功");
        } catch (Exception e) {
            log.error(">>>全量推送大数据失败", e);
            throw new FaultException(">>>全量推送大数据失败");
        }
    }
}
