package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.MovieHallSeatLeasingPriceQueryService;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/3/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MovieHallSeatLeasingPriceQueryServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final MovieHallSeatLeasingPriceQueryService movieHallSeatLeasingPriceQueryService;

    public void push() {
        log.info(">>> 3推送影厅租赁刊例价");
        LocalDateTime timeStamp = LocalDateTime.now();
        movieHallSeatLeasingPriceQueryService.eachAll(list -> {
            List<String> pushDataList = list.stream().map(item -> msg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String msg(MovieHallSeatLeasingPrice item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_MOVIE_HALL_SEAT_LEASING_PRICE).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getCityLevel()).append(splitStr);
        mqData.append(item.getCinemaLevel()).append(splitStr);
        mqData.append(item.getMovieHallType()).append(splitStr);
        mqData.append(item.getMinHours()).append(splitStr);
        mqData.append(item.getMinTotalPrice()).append(splitStr);
        mqData.append(item.getExpandedUnitPrice()).append(splitStr);
        mqData.append(item.getImportId()).append(splitStr);
        mqData.append(item.getUpdater()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(item.getEffectiveDate()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
