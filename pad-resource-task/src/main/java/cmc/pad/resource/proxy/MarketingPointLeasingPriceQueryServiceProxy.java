package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.MarketingPointLeasingPriceQueryService;
import cmc.pad.resource.domain.price.MarketingPointLeasingPrice;
import cmc.pad.resource.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/3/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MarketingPointLeasingPriceQueryServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final MarketingPointLeasingPriceQueryService marketingPointLeasingPriceQueryService;

    public void push() {
        log.info(">>> 4推送营销点位租赁刊例价");
        LocalDateTime timeStamp = LocalDateTime.now();
        marketingPointLeasingPriceQueryService.eachAll(list -> {
            List<String> pushDataList = list.stream().map(item -> msg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String msg(MarketingPointLeasingPrice item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_MARKETING_POINT_LEASING_PRICE).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getCityLevel()).append(splitStr);
        mqData.append(item.getCinemaLevel()).append(splitStr);
        mqData.append(item.getUnitPriceByArea()).append(splitStr);
        mqData.append(item.getUnitPriceByQuantity()).append(splitStr);
        mqData.append(item.getImportId()).append(splitStr);
        mqData.append(item.getUpdater()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(item.getEffectiveDate()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
