package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.ResourceBudgetHallQueryService;
import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.util.DataType;
import cmc.pad.resource.util.PushKafkaStringBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * <AUTHOR>
 * @create 2022/1/28 9:21
 */
@Slf4j
@Component
public class ResourceBudgetHallQueryServiceProxy {
    @Autowired
    private ResourceBudgetHallQueryService resourceBudgetHallQueryService;

    @Autowired
    private PushBigDataService pushBigDataService;

    public void push(LocalDateTime startTime, LocalDateTime endTime) {
        log.info(">>> 推送影厅资源预算 start=" + startTime + ",end=" + endTime);
        LocalDateTime timeStamp = LocalDateTime.now();

        resourceBudgetHallQueryService.eachAll(list -> {
            List<String> pushDataList = list.stream().map(item -> msg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        }, startTime, endTime);

        log.info(">>> 推送影厅资源预算完成 start=" + startTime + ",end=" + endTime);
    }

    private String msg(ResourceBudgetHall item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_RESOURCE_BUDGET_HALL).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getYear()).append(splitStr);
        mqData.append(item.getResourceType()).append(splitStr);
        mqData.append(item.getRegionCode()).append(splitStr);
        mqData.append(item.getRegionName()).append(splitStr);
        mqData.append(item.getCinemaInnerCode()).append(splitStr);
        mqData.append(item.getCinemaName()).append(splitStr);
        mqData.append(item.getBudgetYear()).append(splitStr);
        mqData.append(item.getBudgetMonth1()).append(splitStr);
        mqData.append(item.getBudgetMonth2()).append(splitStr);
        mqData.append(item.getBudgetMonth3()).append(splitStr);
        mqData.append(item.getBudgetMonth4()).append(splitStr);
        mqData.append(item.getBudgetMonth5()).append(splitStr);
        mqData.append(item.getBudgetMonth6()).append(splitStr);
        mqData.append(item.getBudgetMonth7()).append(splitStr);
        mqData.append(item.getBudgetMonth8()).append(splitStr);
        mqData.append(item.getBudgetMonth9()).append(splitStr);
        mqData.append(item.getBudgetMonth10()).append(splitStr);
        mqData.append(item.getBudgetMonth11()).append(splitStr);
        mqData.append(item.getBudgetMonth12()).append(splitStr);
        mqData.append(item.getImportId()).append(splitStr);
        mqData.append(item.getUserId()).append(splitStr);
        mqData.append(item.getUserName()).append(splitStr);
        mqData.append(item.getCreateTime()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
