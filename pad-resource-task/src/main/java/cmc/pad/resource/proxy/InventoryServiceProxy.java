package cmc.pad.resource.proxy;

import cmc.pad.resource.application.query.InventoryQueryService;
import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuwei on 2021/4/1.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryServiceProxy {
    private final PushBigDataService pushBigDataService;
    private final InventoryQueryService inventoryQueryService;

    public void pushAllInventory(LocalDateTime startTime) {
        log.info(">>> 推送库存信息, startTime:{}", startTime);
        LocalDateTime timeStamp = LocalDateTime.now();
        inventoryQueryService.eachAllInventory(startTime, list -> {
            List<String> pushDataList = list.stream().map(item -> inventoryMsg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String inventoryMsg(Inventory item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_INVENTORY).append(splitStr);
        mqData.append(item.getCinemaCode()).append(splitStr);
        mqData.append(item.getDate()).append(splitStr);
        mqData.append(item.getTotalAdvertisingPointLeasableQuantity()).append(splitStr);
        mqData.append(item.getTotalOuterAreaLeasableArea()).append(splitStr);
        mqData.append(item.getTotalFixedPointLeasableArea()).append(splitStr);
        mqData.append(item.getTotalMarketingPointLeasableArea()).append(splitStr);
        mqData.append(item.getSoldAdvertisingPointLeasableQuantity()).append(splitStr);
        mqData.append(item.getSoldOuterAreaLeasableArea()).append(splitStr);
        mqData.append(item.getSoldFixedPointLeasableArea()).append(splitStr);
        mqData.append(item.getSoldMarketingPointLeasableArea()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }

    public void pushAllOccupation(LocalDateTime startTime) {
        log.info(">>> 推送库存占用信息, startTime:{}", startTime);
        LocalDateTime timeStamp = LocalDateTime.now();
        inventoryQueryService.eachAllOccupation(startTime, list -> {
            List<String> pushDataList = list.stream().map(item -> occupationMsg(item, timeStamp)).collect(Collectors.toList());
            pushBigDataService.push(pushDataList);
        });
    }

    private String occupationMsg(Occupation item, LocalDateTime timeStamp) {
        PushKafkaStringBuilder mqData = new PushKafkaStringBuilder();
        mqData.append(DataType.PAD_OCCUPATION).append(splitStr);
        mqData.append(item.getId()).append(splitStr);
        mqData.append(item.getCinemaCode()).append(splitStr);
        mqData.append(item.getContractNo()).append(splitStr);
        mqData.append(item.getBusinessType()).append(splitStr);
        mqData.append(item.getAmount()).append(splitStr);
        mqData.append(item.getStartDate()).append(splitStr);
        mqData.append(item.getEndDate()).append(splitStr);
        mqData.append(item.getStatus().value()).append(splitStr);
        mqData.append(item.getUpdateTime()).append(splitStr);
        mqData.append(timeStamp);//批次号
        return mqData.toString();
    }
}
