package cmc.pad.resource.util;

import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/**
 * Created by fuwei on 2021/3/26.
 */
@Component
public class ThreadPoolExecutorUtil {
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(4, 4,
            20L, TimeUnit.MINUTES, new LinkedBlockingQueue<>());

    public ThreadPoolExecutorUtil() {
        threadPoolExecutor.allowCoreThreadTimeOut(true);
    }
}
