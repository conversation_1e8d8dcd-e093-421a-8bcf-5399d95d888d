package cmc.pad.resource.util;

import mtime.lark.util.lang.EnumValueSupport;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cmc.tohdfs.contracts.CommonContracts.nullStr;
import static cmc.tohdfs.contracts.CommonContracts.splitStr;

/**
 * Created by fuyuanpu on 2020/4/10.
 */
public class PushKafkaStringBuilder {
    private StringBuilder sb = new StringBuilder();

    public PushKafkaStringBuilder append(String str) {
        if (str != null && (str.equals(splitStr) || nullStr.equals(str))) {
            sb.append(str);
            return this;
        }
        sb.append(dataStrNullToKafkaNull(str));
        return this;
    }

    private String dataStrNullToKafkaNull(String dataStr) {
        return StringUtils.isEmpty(dataStr) ? nullStr : dataStr;
    }

    public PushKafkaStringBuilder append(Object obj) {
        if (obj == null) {
            sb.append(nullStr);
            return this;
        }
        sb.append(obj);
        return this;
    }

    public PushKafkaStringBuilder append(LocalDate date) {
        if (date == null) {
            sb.append(nullStr);
            return this;
        }
        sb.append(DateUtil.localDate2Str(date));
        return this;
    }

    public PushKafkaStringBuilder append(LocalDateTime time) {
        if (time == null) {
            sb.append(nullStr);
            return this;
        }
        sb.append(DateUtil.localDateTime2Str(time));
        return this;
    }

    public PushKafkaStringBuilder append(int i) {
        sb.append(i);
        return this;
    }

    public PushKafkaStringBuilder append(EnumValueSupport val) {
        if (val == null) {
            sb.append(nullStr);
            return this;
        }
        sb.append(val.value());
        return this;
    }

    public PushKafkaStringBuilder append(char i) {
        sb.append(i);
        return this;
    }

    public PushKafkaStringBuilder append(long l) {
        sb.append(l);
        return this;
    }

    public PushKafkaStringBuilder append(StringBuffer sb) {
        this.sb.append(sb);
        return this;
    }

    public PushKafkaStringBuilder append(CharSequence s) {
        this.sb.append(s);
        return this;
    }

    public PushKafkaStringBuilder append(CharSequence s, int start, int end) {
        sb.append(s, start, end);
        return this;
    }

    public PushKafkaStringBuilder append(char[] str) {
        sb.append(str);
        return this;
    }

    public PushKafkaStringBuilder append(char[] str, int offset, int len) {
        sb.append(str, offset, len);
        return this;
    }

    public PushKafkaStringBuilder append(boolean b) {
        sb.append(b);
        return this;
    }

    public PushKafkaStringBuilder append(float f) {
        sb.append(f);
        return this;
    }

    public PushKafkaStringBuilder append(double d) {
        sb.append(d);
        return this;
    }

    public String toString() {
        return sb.toString();
    }
}
