package cmc.pad.resource;

import mtime.lark.net.rpc.config.ServerOptions;
import mtime.lark.task.TaskApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * Task启动类
 */
@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@ComponentScan(basePackages = {"cmc.tohdfs.sdk", "cmc.pad.resource"})
public class TaskBootstrap {

    /**
     * 启动入口
     *
     * @param args 启动参数
     */
    public static void main(String[] args) {
        ServerOptions options = new ServerOptions();
        options.setType("simple");
        options.setName("cmc.pad.resource.task");
        options.setAddress(":9402");
        options.setDescription("阵地广告相关Task");
        TaskApplication app = new TaskApplication(TaskBootstrap.class, args, options);
        app.run();
//        ApplicationContext context=app.run();
//        List<Arg> argList = new ArrayList<>();
//        Arg arg = new Arg();
//        arg.Name = "startDate";
//        arg.Value = "2018-11-04";
//        argList.add(arg);
//        Arg arg2 = new Arg();
//        arg2.Name = "endDate";
//        arg2.Value = "2018-11-06";
//        argList.add(arg2);
//        Arg arg3 = new Arg();
//        arg3.Name = "days";
//        arg3.Value = "3";
//        argList.add(arg3);
//        ExecuteParam param = new ExecuteParam();
//        param.setArgs(argList);
//        TaskContext taskContext = new TaskContext(param);
//        Executor executor =  (Executor) ServiceLocator.current().getInstance("syncCinemaTask");
//        executor.execute(taskContext);
    }
}