package cmc.pad.resource.task;

import cmc.pad.resource.application.DateUtil;
import cmc.pad.resource.application.command.point.inventory.PointInventoryManageService;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.BasicFilter;
import mtime.lark.task.*;
import mtime.lark.util.config.SettingMap;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static cmc.pad.resource.domain.resource.PointLocationInfo.PLI_BUSINESS_TYPE_CODE;
import static mtime.lark.db.jsd.FilterType.NIN;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * 生成点位库存task
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationAddInventoryTask implements Executor {
    private final PointInventoryManageService pointInventoryManageService;
    private final PointLocationInfoRepository pointLocationInfoRepository;

    @Override
    public void execute(TaskContext ctx) {
        SettingMap args = ctx.getArgs();
        String ids = args.getString("id");
        String startDate = args.getString("startDate", null);
        String endDate = args.getString("endDate", null);
        int defaultAddMonthNum = args.getInt32("defaultAddMonthNum", 1);
        boolean isBatchInsert = args.getBool("isBatchInsert", true);
        String notNeedInventoryBusinessTypes = args.getString("notNeedInventoryBusinessTypes", "YT,GMT,QT,CMDX,WBZY");//影厅 冠名厅 其他 传媒灯箱 外部资源
        String[] notNeedInventoryBusinessTypeArray = new String[0];
        if (!Strings.isNullOrEmpty(notNeedInventoryBusinessTypes))
            notNeedInventoryBusinessTypeArray = notNeedInventoryBusinessTypes.split(",");
        log.info(">>>点位库存追加任务, ids:{}, startDate:{}, endDate:{}, defaultAddMonthNum:{}", ids, startDate, endDate, defaultAddMonthNum);
        log.info(">>>isBatchInsert:{}", isBatchInsert);
        log.info(">>>notNeedInventoryBusinessTypes:{}", notNeedInventoryBusinessTypes);
        if (!Strings.isNullOrEmpty(ids)) {
            Lists.newArrayList(ids.split(",")).parallelStream().forEach(id ->
                    addInventoryAndLockPointLocation(Integer.valueOf(id), startDate, endDate, defaultAddMonthNum, isBatchInsert)
            );
        } else {
            BasicFilter f = f();
            if (notNeedInventoryBusinessTypeArray.length != 0)
                f.add(PLI_BUSINESS_TYPE_CODE, NIN, notNeedInventoryBusinessTypeArray);
            int page = 1;
            while (true) {
                PageResult<PointLocationInfo> pageResult = pointLocationInfoRepository
                        .findPage(f, 1000, page++);
                List<PointLocationInfo> items = pageResult.getItems();
                if (items.isEmpty())
                    break;
                items.parallelStream().forEach(item -> addInventoryAndLockPointLocation(item.getId(), startDate, endDate, defaultAddMonthNum, isBatchInsert));
            }
        }
        log.info(">>>完成点位库存追加任务, ids:{}, startDate:{}, endDate:{}, defaultAddMonthNum:{}", ids, startDate, endDate, defaultAddMonthNum);
    }

    private void addInventoryAndLockPointLocation(int pointLocationId, String start, String end, int defaultAddMonthNum, boolean isBatchInsert) {
        try {
            pointInventoryManageService.addInventoryAndLockPointLocation(pointLocationId, DateUtil.parseDate(start), DateUtil.parseDate(end), defaultAddMonthNum, isBatchInsert);
        } catch (Exception e) {
            log.warn(">>>>点位id{}追加库存失败!!!", pointLocationId, e);
        }
    }
}