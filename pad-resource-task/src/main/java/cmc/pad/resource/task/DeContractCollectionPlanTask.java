package cmc.pad.resource.task;

import cmc.pad.resource.application.command.contract.DeContractCollectionPlanService;
import cmc.pad.resource.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import mtime.lark.util.config.SettingMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Created by fuwei on 2024/3/15.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DeContractCollectionPlanTask implements Executor {
    private final DeContractCollectionPlanService deContractCollectionPlanService;

    @Override
    public void execute(TaskContext ctx) {
        SettingMap args = ctx.getArgs();
        String init = args.getString("init");
        String contractNo = args.getString("contractNo");
        LocalDateTime start = DateUtil.dateTimeStr2LocalDateTime(args.getString("start"));
        LocalDateTime end = DateUtil.dateTimeStr2LocalDateTime(args.getString("end"));
        if ("true".equals(init) || "force".equals(init))
            deContractCollectionPlanService.queryContractCollectPlanAndTransferSave("force".equals(init), contractNo, start, end);
        else
            deContractCollectionPlanService.queryPlanAndFlagOverdue();
    }
}
