package cmc.pad.resource.task;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.util.DateMapSumAreaUtil;
import cmc.pad.resource.util.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.task.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealOccupyData;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealSubmitOccupyData;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.*;
import static cmc.pad.resource.util.DateUtil.getDates;
import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/5/20.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CheckPointLocationInventoryTask implements Executor {
    private final PointLocationOccupationLogRepository occupationLogRepo;
    private final PointLocationOccupationContractDetailRepository detailRep;
    private final PointLocationInventoryRepository pointLocationInventoryRep;

    private Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void execute(TaskContext ctx) {
        String pointLocationIds = ctx.getArgs().getString("pointLocationIds", null);
        String contractNo = ctx.getArgs().getString("contractNo", "");
        boolean onlyPrintErrorData = ctx.getArgs().getBool("onlyPrintErrorData", true);
        String startCheckDate = ctx.getArgs().getString("startCheckDate");

        List<String> pointLocationIdList = null;
        if (!contractNo.isEmpty()) {
            log.info("修复合同{}，包含的所有点位", contractNo);
            Set<Integer> pointLocationIdSet = detailRep.findMany(f(PLO_C_D_CONTRACT_NO, contractNo)).stream().map(detail -> detail.getPointLocationId()).collect(Collectors.toSet());
            pointLocationIdList = pointLocationIdSet.stream().map(String::valueOf).collect(Collectors.toList());
        } else if (pointLocationIds != null) {
            pointLocationIdList = Lists.newArrayList(pointLocationIds.split(","));
        }
        if (CollectionUtils.isEmpty(pointLocationIdList)) {
            LocalDateTime updateTime =
                    Strings.isNullOrEmpty(startCheckDate) ? DateUtil.date2StartLocalDateTime(LocalDate.now().minusDays(1)) : DateUtil.date2StartLocalDateTime(DateUtil.dateStr2LocalDate(startCheckDate));
            log.info(">>>开始扫描点位明细 开始时间:{}", updateTime);
            Set<Integer> pIdSet = detailRep.findMany(f(PLO_C_D_UPDATE_TIME, GTE, updateTime)).stream().map(detail -> detail.getPointLocationId()).collect(Collectors.toSet());
            pointLocationIdList = pIdSet.stream().map(String::valueOf).collect(Collectors.toList());
        }
        eachPointLocationListCheck(pointLocationIdList, onlyPrintErrorData);
    }

    private void eachPointLocationListCheck(List<String> pointLocationIdList, boolean onlyPrintErrorData) {
        pointLocationIdList.stream().forEach(pointLocationIdStr -> check(Integer.valueOf(pointLocationIdStr), onlyPrintErrorData));
    }

    private void check(Integer pointLocationId, boolean onlyPrintErrorData) {
        DateMapSumAreaUtil dateMapSumAreaUtil = new DateMapSumAreaUtil();
        List<PointLocationOccupationContractDetail> singlePointLocationDetailList = detailRep.findMany(f(PLO_C_D_POINT_LOCATION_ID, pointLocationId));
        singlePointLocationDetailList.forEach(detail -> {
            log.info(">>>检查点位详情:{}", JSON.toJSONString(detail));
            ContractStatus contractStatus = detail.getContractStatus();
            String contractNo = detail.getContractNo();
            if (ContractType.NEW_CONTRACT == detail.getContractType()) {
                if (contractStatus == ContractStatus.SUBMIT || contractStatus == ContractStatus.APPROVAL) {
                    log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                    sumPointLocationDateArea(new RealOccupyData(detail.getPointLocationId(), RealOccupyData.DateOccupyArea.of(detail.getStartDate(), detail.getEndDate(), detail.getAmount())), dateMapSumAreaUtil);
                }
            }
            if (ContractType.ALTER_CONTRACT == detail.getContractType()) {
                if (ContractStatus.SUBMIT == contractStatus) {
                    log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                    sumPointLocationDateArea(toRealSubmitOccupyData(detail), dateMapSumAreaUtil);
                }
                if (ContractStatus.APPROVAL == contractStatus) {
                    log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                    sumPointLocationDateArea(toRealOccupyData(detail), dateMapSumAreaUtil);
                }
                if (ContractStatus.CANCEL == contractStatus) {
                    log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                    List<PointLocationOccupationLog> lastVersionApprovalOccupyLogList = occupationLogRepo.queryRecentApprovalContractLog(contractNo);
                    List<PointLocationOccupationContractDetail> lastVersionApprovalOccupyList = CopyUtil.listCopy(lastVersionApprovalOccupyLogList, PointLocationOccupationContractDetail.class);
                    PointLocationOccupationContractDetail lastVersionOccupationContractDetail = lastVersionApprovalOccupyList.stream().filter(lastVersionDetail -> lastVersionDetail.getDetailId().equals(detail.getDetailId())).findFirst().get();
                    sumPointLocationDateArea(toRealOccupyData(lastVersionOccupationContractDetail), dateMapSumAreaUtil);
                }
            }
        });
        dateMapSumAreaUtil.getDateMapSumAreaResult().forEach((date, occupyArea) -> {
            PointLocationInventory inventory = pointLocationInventoryRep.query(pointLocationId, date);
            CheckResult checkResult = new CheckResult(pointLocationId, date, occupyArea, (inventory.getSellArea() - inventory.getNotSellArea()));
            if (onlyPrintErrorData) {
                if (!checkResult.result)
                    log.info(">>>{}", checkResult.toString());
            } else
                log.info(">>>{}", checkResult.toString());
        });
    }

    private void sumPointLocationDateArea(RealOccupyData realOccupyData, DateMapSumAreaUtil dateMapSumAreaUtil) {
        RealOccupyData.DateOccupyArea dateOccupyArea = realOccupyData.getDateOccupyArea();
        getDates(dateOccupyArea.getStartDate(), dateOccupyArea.getEndDate()).forEach(date -> dateMapSumAreaUtil.add(date, dateOccupyArea.getOccupyArea()));
    }


    class CheckResult {
        boolean result;
        String resultStr;
        LocalDate date;
        int pointLocationId;
        float realOccupyArea;
        float inventoryOccupyArea;

        public CheckResult(int pointLocationId, LocalDate date, float realOccupyArea, float inventoryOccupyArea) {
            this.pointLocationId = pointLocationId;
            this.date = date;
            this.realOccupyArea = realOccupyArea;
            this.inventoryOccupyArea = inventoryOccupyArea;
            this.result = (realOccupyArea == inventoryOccupyArea);
            this.resultStr = this.result ? "正常" : "错误";
        }

        @Override
        public String toString() {
            return String.format("检查结果:[%s], 点位id:%s 日期:%s 实际占用面积:%s 数据库占用面积:%s",
                    this.resultStr, this.pointLocationId, this.date, this.realOccupyArea, this.inventoryOccupyArea
            );
        }
    }
}