package cmc.pad.resource.task;

import cmc.pad.resource.application.command.CityAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.Executor;
import mtime.lark.task.Task;
import mtime.lark.task.TaskContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * fuwei on 2018/6/29.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SyncCityTask implements Executor {

    private final CityAppService cityAppService;

    @Override
    public void execute(TaskContext ctx) {
        //地区编码,地区级别;地区编码,地区级别;....
        //例：1000,L1;1002,L2;....
        //分号分地区，逗号映射地区和级别
        String initCityDistrictLevel = ctx.getArgs().getString("initCityDistrictLevel");
        cityAppService.synchronizeCities(initCityDistrictLevel);
    }
}
