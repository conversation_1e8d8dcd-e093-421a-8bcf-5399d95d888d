package cmc.pad.resource.task;

import cmc.pad.resource.proxy.CityServiceProxy;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by fuwei on 2021/3/25.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CityInfoPushBiTask implements Executor {
    private final CityServiceProxy cityServiceProxy;

    @Override
    public void execute(TaskContext ctx) {
        log.info(">>> 开始推送城市信息");
        String taskIndex = ctx.getArgs().getString("taskIndex");
        if (StringUtils.isEmpty(taskIndex))
            taskIndex = "1,2";
        Lists.newArrayList(taskIndex.split(",")).parallelStream().forEach(index -> push(Integer.parseInt(index)));
        log.info(">>> 结束推送城市信息");
    }

    private void push(int index) {
        if (index == 1)
            cityServiceProxy.pushCity();
        if (index == 2)
            cityServiceProxy.pushCityDistrict();
    }
}
