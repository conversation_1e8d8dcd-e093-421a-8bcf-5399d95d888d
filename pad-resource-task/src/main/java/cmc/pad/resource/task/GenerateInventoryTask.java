package cmc.pad.resource.task;

import cmc.pad.resource.application.command.InventoryAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.Executor;
import mtime.lark.task.Task;
import mtime.lark.task.TaskContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 生成库存task
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class GenerateInventoryTask implements Executor {
    private final static DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final InventoryAppService inventoryAppService;

    @Override
    public void execute(TaskContext ctx) {
        //灵活参数：影院内码 库存开始日期,库存截止日期
        String cinemaCode = ctx.getArgs().getString("cinemaCode");
        String startDate = ctx.getArgs().getString("startDate");
        String endDate = ctx.getArgs().getString("endDate");
        LocalDateTime start = LocalDateTime.now().plusDays(1);
        //默认是task执行后一天所有影院所有业务类型的库存
        if (StringUtils.isEmpty(startDate)) {
            //如果开始日期不指定，默认为task执行的后一天
            startDate = fmt.format(start);
        }
        if (StringUtils.isEmpty(endDate)) {
            //如果结束日期不指定，默认为+40月
            LocalDateTime end = start.plusMonths(40);
            endDate = fmt.format(end);
        }
        inventoryAppService.GenerateInventory(cinemaCode, startDate, endDate);
    }
}
