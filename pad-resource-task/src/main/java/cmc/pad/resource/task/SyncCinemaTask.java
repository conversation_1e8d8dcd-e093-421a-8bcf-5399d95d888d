package cmc.pad.resource.task;

import cmc.pad.resource.application.command.CinemaAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.Executor;
import mtime.lark.task.Task;
import mtime.lark.task.TaskContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *  同步影城信息至阵地广告
 *  lsh on 2019/3/11
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SyncCinemaTask implements Executor {

    private final CinemaAppService cinemaAppService;

    @Override
    public void execute(TaskContext ctx) {
        cinemaAppService.synchronizeCinemas();
    }
}
