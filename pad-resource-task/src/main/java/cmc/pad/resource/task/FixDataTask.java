package cmc.pad.resource.task;

import cmc.mdm.cinema.admin.contract.dto.CinemaAdminDto;
import cmc.mdm.cinema.admin.contract.iface.CinemaAdminService;
import cmc.pad.resource.application.query.point.PointLocationQueryService;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import cmc.pad.resource.infrastructures.service.region.RegionServiceAdapter;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by fuyuanpu on 2022/7/7.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FixDataTask implements Executor {
    private final CinemaAdminService cinemaAdminService;
    private final RegionServiceAdapter regionServiceAdapter;
    private final PointLocationQueryService pointLocationQueryService;
    private final PointLocationInfoRepository pointLocationInfoRepository;

    @Override
    public void execute(TaskContext ctx) {
        Map<String, String> cache = Maps.newHashMap();
        List<String> allCinemaInnerCodes = pointLocationInfoRepository.queryAllCinemaInnerCode();
        allCinemaInnerCodes.forEach(cinemaInnerCode -> {
            CinemaAdminDto.Cinema cinema = getCinema(cinemaInnerCode);
            if (cinema != null) {
                log.info(">>>更新影城区域编码:{} - {}", cinema.getInnerCode(), cinema.getRegionId());
                pointLocationInfoRepository.updateRegionCode(cinema.getRegionId(), cinemaInnerCode);
            }
        });
        List<String> regionCodes = pointLocationInfoRepository.queryAllRegionCode();
        regionCodes.forEach(regionCode -> {
            String largeWard = getLargeWard(cache, regionCode);
            if (!Strings.isNullOrEmpty(largeWard)) {
                log.info(">>>更新regionCode:{} largeWardCode:{}", regionCode, largeWard);
                pointLocationInfoRepository.updateLargeWardCode(largeWard, regionCode);

            }
        });
    }

    private CinemaAdminDto.Cinema getCinema(String innerCode) {
        CinemaAdminDto.GetCinemaRequest req = new CinemaAdminDto.GetCinemaRequest();
        req.setInnerCode(innerCode);
        CinemaAdminDto.GetCinemaResponse response = cinemaAdminService.getCinema(req);
        CinemaAdminDto.Cinema cinema = response.getCinema();
        if (cinema == null) return null;
        log.info(">>>cinema innerCode:{}, regionCode:{}", cinema.getInnerCode(), cinema.getRegionId());
        return cinema;
    }

    private String getLargeWard(Map<String, String> cache, String regionCode) {
        if (cache.containsKey(regionCode)) {
            return cache.get(regionCode);
        }
        String largeWardCode = regionServiceAdapter.getLargeWardCode(regionCode);
        cache.put(regionCode, largeWardCode);
        return largeWardCode;
    }
}
