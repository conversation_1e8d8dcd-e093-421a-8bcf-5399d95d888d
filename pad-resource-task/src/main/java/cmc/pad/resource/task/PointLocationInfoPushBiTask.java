package cmc.pad.resource.task;

import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import cmc.pad.resource.proxy.PushBigDataService;
import cmc.tohdfs.sdk.common.DataType;
import cmc.tohdfs.sdk.dto.SendDataDto;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.Executor;
import mtime.lark.task.Task;
import mtime.lark.task.TaskContext;
import mtime.lark.util.config.SettingMap;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

import static cmc.pad.resource.domain.resource.PointLocationInfo.PLI_UPDATE_TIME;
import static cmc.pad.resource.util.DateUtil.dateStr2EndLocalDateTime;
import static cmc.pad.resource.util.DateUtil.dateStr2StartLocalDateTime;
import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.FilterType.LTE;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/2/5.
 */
@Slf4j
@Task
@Component
public class PointLocationInfoPushBiTask implements Executor {
    @Autowired
    private PushBigDataService pushBigDataService;
    @Autowired
    private PointLocationInfoRepository pointLocationInfoRepository;

    @Override
    public void execute(TaskContext ctx) {
        SettingMap args = ctx.getArgs();
        boolean isAll = ctx.getArgs().getBool("isAll", false);
        LocalDateTime startDate = dateStr2StartLocalDateTime(args.getString("startDate"));
        LocalDateTime endDate = dateStr2EndLocalDateTime(args.getString("endDate"));
        LocalDateTime now = LocalDateTime.now();
        if (startDate == null)
            startDate = now.minusHours(2);
        if (endDate == null)
            endDate = now;
        log.info(">>>开始推送点位信息到bi, start:{} - end:{}", startDate, endDate);
        int page = 1;
        while (true) {
            PageResult<PointLocationInfo> pageResult = pointLocationInfoRepository.findPage(
                    f(PLI_UPDATE_TIME, GTE, startDate).add(PLI_UPDATE_TIME, LTE, endDate),
                    500,
                    page++);
            if (pageResult.isEmpty()) {
                log.info(">>>查询不到点位基础信息数据,停止推送");
                return;
            }
            SendDataDto.SendDataRequest<PointLocationInfo> request = new SendDataDto.SendDataRequest<>();
            request.setDataType(DataType.ODS_POINT_LOCATION_INFO);
            request.setSendTime(now);
            request.setData(pageResult.getItems());
            if (isAll) {
                pushBigDataService.sendAll(request);
            } else {
                pushBigDataService.send(request);
            }
            log.info(">>>完成推送点位信息到bi, start:{} - end:{}, data size:{}", startDate, endDate, pageResult.getItems().size());
        }
    }
}
