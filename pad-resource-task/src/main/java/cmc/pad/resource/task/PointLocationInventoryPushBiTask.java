package cmc.pad.resource.task;

import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.proxy.PushBigDataService;
import cmc.tohdfs.sdk.common.DataType;
import cmc.tohdfs.sdk.dto.SendDataDto;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.TableQuery;
import mtime.lark.db.jsd.result.ExecuteResult;
import mtime.lark.task.*;
import mtime.lark.util.config.SettingMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cmc.pad.resource.domain.inventory.point.PointLocationInventory.PL_INVENTORY_POINT_LOCATION_ID;
import static cmc.pad.resource.domain.inventory.point.PointLocationInventory.PL_INVENTORY_UPDATE_TIME;
import static cmc.pad.resource.util.DateUtil.dateStr2EndLocalDateTime;
import static cmc.pad.resource.util.DateUtil.dateStr2StartLocalDateTime;
import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.FilterType.LTE;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/2/5.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationInventoryPushBiTask implements Executor {
    private final PushBigDataService pushBigDataService;
    private final PointLocationInventoryRepository pointLocationInventoryRepo;

    @Override
    public void execute(TaskContext ctx) {
        boolean isAll = ctx.getArgs().getBool("isAll", false);
        SettingMap args = ctx.getArgs();
        LocalDateTime startDate = dateStr2StartLocalDateTime(args.getString("startDate"));
        LocalDateTime endDate = dateStr2EndLocalDateTime(args.getString("endDate"));
        LocalDateTime now = LocalDateTime.now();
        if (startDate == null)
            startDate = now.minusHours(2);
        if (endDate == null)
            endDate = now;
        log.info(">>>开始推送点位库存到bi, start:{} - end:{}", startDate, endDate);
        LocalDateTime finalStartDate = startDate;
        LocalDateTime finalEndDate = endDate;

        pointLocationInventoryRepo
                .tableNodes()
                .parallelStream()
                .forEach(shardTable -> {
                    List<Integer> pidList = Lists.newArrayList();
                    try (ExecuteResult result = shardTable.getDb().execute(distinctSql(shardTable.getTableName(), finalStartDate, finalEndDate)).result()) {
                        result.each(reader -> pidList.add(reader.getInt(1)));
                    }
                    pidList.stream().forEach(pid ->
                            pushBigData(isAll, pid, shardTable.getTableQuery(), finalStartDate, finalEndDate, now)
                    );
                });

    }

    private void pushBigData(boolean isAll, int pid, TableQuery tableQuery, LocalDateTime finalStartDate, LocalDateTime finalEndDate, LocalDateTime now) {
        int pageNum = 1;
        while (true) {
            List<PointLocationInventory> list = tableQuery
                    .select(PointLocationInventory.class)
                    .where(f(PL_INVENTORY_POINT_LOCATION_ID, pid)
                            .add(PL_INVENTORY_UPDATE_TIME, GTE, finalStartDate)
                            .add(PL_INVENTORY_UPDATE_TIME, LTE, finalEndDate))
                    .page(pageNum++, 500)
                    .result()
                    .all(PointLocationInventory.class);
            if (list.isEmpty()) {
                log.info(">>>查询不到{}点位库存数据,停止推送", pid);
                break;
            }
            SendDataDto.SendDataRequest<PointLocationInventory> request = new SendDataDto.SendDataRequest<>();
            request.setDataType(DataType.ODS_POINT_LOCATION_INVENTORY);
            request.setSendTime(now);
            request.setData(list);
            if (isAll) {
                pushBigDataService.sendAll(request);
            } else {
                pushBigDataService.send(request);
            }
            log.info(">>>完成推送{}点位库存到bi, start:{} - end:{}, data size：{}", pid, finalStartDate, finalEndDate, list.size());
        }
    }

    private String distinctSql(String tableName, LocalDateTime start, LocalDateTime end) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startStr = start.format(formatter);
        String endStr = end.format(formatter);
        String sql =
                "select" +
                        " distinct(point_location_id)" +
                        " from" +
                        " " + tableName +
                        " where" +
                        " update_time >= '" + startStr + "' and update_time <= '" + endStr + "'";
        log.info(">>> distinct sql:{}", sql);
        return sql;
    }

}