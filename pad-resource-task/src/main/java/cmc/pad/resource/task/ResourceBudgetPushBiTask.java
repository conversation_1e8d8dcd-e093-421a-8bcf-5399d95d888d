package cmc.pad.resource.task;

import cmc.pad.resource.proxy.ResourceBudgetHallQueryServiceProxy;
import cmc.pad.resource.proxy.ResourceBudgetPointLocationQueryServiceProxy;
import cmc.pad.resource.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.Executor;
import mtime.lark.task.Task;
import mtime.lark.task.TaskContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2022/1/28 9:17
 */
@Slf4j
@Task
@Component
public class ResourceBudgetPushBiTask implements Executor {

    @Autowired
    private ResourceBudgetHallQueryServiceProxy resourceBudgetHallQueryServiceProxy;
    @Autowired
    private ResourceBudgetPointLocationQueryServiceProxy resourceBudgetPointLocationQueryServiceProxy;

    @Override
    public void execute(TaskContext ctx) {
        log.info(">>> 开始推送资源预算");
        String taskIndex = ctx.getArgs().getString("taskIndex");
        if (StringUtils.isEmpty(taskIndex)) {
            taskIndex = "1,2";
        }
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMinutes(65);
        String startDate = ctx.getArgs().getString("startDate");
        String endDate = ctx.getArgs().getString("endDate");
        if (!org.springframework.util.StringUtils.isEmpty(startDate)) {
            startTime = DateUtil.dateStr2StartLocalDateTime(startDate);
        }
        if (!org.springframework.util.StringUtils.isEmpty(endDate)) {
            endTime = DateUtil.dateStr2EndLocalDateTime(endDate);
        }
        LocalDateTime finalStartTime = startTime;
        LocalDateTime finalEndTime = endTime;
        Lists.newArrayList(taskIndex.split(",")).parallelStream().forEach(idx -> pushData(Integer.parseInt(idx), finalStartTime, finalEndTime));
        log.info(">>> 结束推送资源预算");
    }

    private void pushData(int type,LocalDateTime startTime, LocalDateTime endTime) {
        if (type == 1)
            resourceBudgetHallQueryServiceProxy.push(startTime,endTime);
        if (type == 2)
            resourceBudgetPointLocationQueryServiceProxy.push(startTime,endTime);

    }
}
