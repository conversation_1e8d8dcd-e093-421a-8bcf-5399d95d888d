package cmc.pad.resource.task;

import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetailRepository;
import cmc.pad.resource.proxy.PushBigDataService;
import cmc.tohdfs.sdk.common.DataType;
import cmc.tohdfs.sdk.dto.SendDataDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import mtime.lark.util.config.SettingMap;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_UPDATE_TIME;
import static cmc.pad.resource.util.DateUtil.dateStr2EndLocalDateTime;
import static cmc.pad.resource.util.DateUtil.dateStr2StartLocalDateTime;
import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.FilterType.LTE;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/2/5.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationOccupyContractDetailPushBiTask implements Executor {
    private final PointLocationOccupationContractDetailRepository detailRep;
    private final PushBigDataService pushBigDataService;

    @Override
    public void execute(TaskContext ctx) {
        boolean isAll = ctx.getArgs().getBool("isAll", false);
        SettingMap args = ctx.getArgs();
        LocalDateTime startDate = dateStr2StartLocalDateTime(args.getString("startDate"));
        LocalDateTime endDate = dateStr2EndLocalDateTime(args.getString("endDate"));
        LocalDateTime now = LocalDateTime.now();
        if (startDate == null)
            startDate = now.minusHours(2);
        if (endDate == null)
            endDate = now;
        log.info(">>>开始推送点位合同占用明细到bi, start:{} - end:{}", startDate, endDate);
        int page = 1;
        while (true) {
            PageResult<PointLocationOccupationContractDetail> pageResult = detailRep.findPage(
                    f(PLO_C_D_UPDATE_TIME, GTE, startDate).add(PLO_C_D_UPDATE_TIME, LTE, endDate)
                    , 500, page++
            );
            if (pageResult.isEmpty()) {
                log.info(">>>查询不到点位合同明细数据,停止推送");
                break;
            }
            log.info(">>>开始推送点位合同占用明细到bi, start:{} - end:{}", startDate, endDate);
            SendDataDto.SendDataRequest<PointLocationOccupationContractDetail> request = new SendDataDto.SendDataRequest<>();
            request.setDataType(DataType.ODS_POINT_LOCATION_OCCUPATION_CONTRACT_DETAIL);
            request.setSendTime(now);
            request.setData(pageResult.getItems());
            if (isAll) {
                pushBigDataService.sendAll(request);
            } else {
                pushBigDataService.send(request);
            }
            log.info(">>>完成推送点位合同占用明细到bi, start:{} - end:{}, data size:{}",
                    startDate, endDate, pageResult.getItems().size());
        }

    }
}
