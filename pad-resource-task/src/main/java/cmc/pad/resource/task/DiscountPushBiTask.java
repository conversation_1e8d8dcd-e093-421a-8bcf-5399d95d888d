package cmc.pad.resource.task;

import cmc.pad.resource.proxy.DiscountQueryServiceProxy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by fuwei on 2021/3/25.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountPushBiTask implements Executor {
    private final DiscountQueryServiceProxy discountQueryServiceProxy;

    @Override
    public void execute(TaskContext ctx) {
        log.info(">>> 开始推送折扣系数");
        discountQueryServiceProxy.push();
        discountQueryServiceProxy.pushRule();
        log.info(">>> 结束推送折扣系数");
    }
}
