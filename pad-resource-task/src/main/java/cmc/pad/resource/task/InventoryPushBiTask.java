package cmc.pad.resource.task;

import cmc.pad.resource.proxy.InventoryServiceProxy;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cmc.pad.resource.util.DateUtil.dateStr2StartLocalDateTime;

/**
 * Created by fuwei on 2021/3/25.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryPushBiTask implements Executor {
    private final InventoryServiceProxy inventoryServiceProxy;

    @Override
    public void execute(TaskContext ctx) {
        log.info(">>> 开始推送库存信息");
        String taskIndex = ctx.getArgs().getString("taskIndex");
        if (StringUtils.isEmpty(taskIndex))
            taskIndex = "1,2";
        boolean isPushAll = ctx.getArgs().getBool("isPushAll", false);
        String startDateTimeStr = ctx.getArgs().getString("startDateTime");
        LocalDateTime startDateTime = dateStr2StartLocalDateTime(startDateTimeStr);
        if (startDateTime == null)
            startDateTime = LocalDate.now().plusDays(-1).atTime(0, 0, 0);
        LocalDateTime finalStartDateTime = startDateTime;
        Lists.newArrayList(taskIndex.split(",")).parallelStream().forEach(index -> push(Integer.parseInt(index), finalStartDateTime, isPushAll));
        log.info(">>> 结束推送库存信息");
    }

    private void push(int index, LocalDateTime startDateTime, boolean isPushAll) {
        if (index == 1) {
            if (isPushAll)
                inventoryServiceProxy.pushAllInventory(null);
            else
                inventoryServiceProxy.pushAllInventory(startDateTime);
        }
        if (index == 2) {
            if (isPushAll)
                inventoryServiceProxy.pushAllOccupation(null);
            else
                inventoryServiceProxy.pushAllOccupation(startDateTime);
        }
    }
}