package cmc.pad.resource.task;

import cmc.pad.resource.proxy.*;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by fuwei on 2021/3/25.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LeasingPricePushBiTask implements Executor {
    private final OuterAreaLeasingPriceQueryServiceProxy outerAreaLeasingPriceQueryService;
    private final FixedPointLeasingPriceQueryServiceProxy fixedPointLeasingPriceQueryService;
    private final MovieHallSeatLeasingPriceQueryServiceProxy movieHallSeatLeasingPriceQueryService;
    private final MarketingPointLeasingPriceQueryServiceProxy marketingPointLeasingPriceQueryService;
    private final MovieHallNamingLeasingPriceQueryServiceProxy movieHallNamingLeasingPriceQueryService;
    private final AdvertisingPointLeasingPriceQueryServiceProxy advertisingPointLeasingPriceQueryService;

    @Override
    public void execute(TaskContext ctx) {
        log.info(">>> 开始推送租赁刊例价");
        String taskIndex = ctx.getArgs().getString("taskIndex");
        if (StringUtils.isEmpty(taskIndex))
            taskIndex = "1,2,3,4,5,6";
        Lists.newArrayList(taskIndex.split(",")).parallelStream().forEach(idx -> pushData(Integer.parseInt(idx)));
        log.info(">>> 结束推送租赁刊例价");
    }

    private void pushData(int type) {
        if (type == 1)
            fixedPointLeasingPriceQueryService.push();
        if (type == 2)
            outerAreaLeasingPriceQueryService.push();
        if (type == 3)
            movieHallSeatLeasingPriceQueryService.push();
        if (type == 4)
            marketingPointLeasingPriceQueryService.push();
        if (type == 5)
            movieHallNamingLeasingPriceQueryService.push();
        if (type == 6)
            advertisingPointLeasingPriceQueryService.push();
    }
}
