package cmc.pad.resource.task;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.OccupyInventoryBiz;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper;
import cmc.pad.resource.domain.inventory.point.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import mtime.lark.task.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealOccupyData;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealSubmitOccupyData;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_NO;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_POINT_LOCATION_ID;
import static cmc.pad.resource.util.DateUtil.DateRange;
import static cmc.pad.resource.util.DateUtil.queryMaxAndMin;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/5/20.
 */
@Slf4j
@Task
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FixPointLocationInventoryTask implements Executor {
    private final OccupyInventoryBiz occupyInventoryBiz;
    private final UpdateInventoryHelper updateInventoryHelper;
    private final PointLocationOccupationLogRepository occupationLogRepo;
    private final PointLocationOccupationContractDetailRepository detailRep;
    private final PointLocationInventoryRepository pointLocationInventoryRep;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;

    private Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void execute(TaskContext ctx) {
        String pointLocationIds = ctx.getArgs().getString("pointLocationIds", "");
        String contractNo = ctx.getArgs().getString("contractNo", "");
        List<String> pointLocationIdList = null;
        if (!contractNo.isEmpty()) {
            log.info("修复合同{}，包含的所有点位", contractNo);
            Set<Integer> pointLocationIdSet = detailRep.findMany(f(PLO_C_D_CONTRACT_NO, contractNo)).stream().map(detail -> detail.getPointLocationId()).collect(Collectors.toSet());
            pointLocationIdList = pointLocationIdSet.stream().map(String::valueOf).collect(Collectors.toList());
        } else {
            pointLocationIdList = Lists.newArrayList(pointLocationIds.split(","));
        }
        eachPointLocationListFix(pointLocationIdList);
    }

    private void eachPointLocationListFix(List<String> pointLocationIdList) {
        pointLocationIdList.stream().forEach(pointLocationIdStr -> {
            fixPointLocation(Integer.valueOf(pointLocationIdStr));
        });
    }

    private void fixPointLocation(Integer pointLocationId) {
        List<PointLocationOccupationContractDetail> singlePointLocationDetailList = detailRep.findMany(f(PLO_C_D_POINT_LOCATION_ID, pointLocationId));
        log.info(">>>pid:{} detail size:{}", pointLocationId, singlePointLocationDetailList.size());
        Set<LocalDate> dateSet = Sets.newHashSet();
        singlePointLocationDetailList.forEach(detail -> {
            log.info(">>>点位id:{} startDate：{} endDate: {}", detail.getId(), detail.getStartDate(), detail.getEndDate());
            dateSet.add(detail.getStartDate());
            dateSet.add(detail.getEndDate());
            if (detail.getContractType() == ContractType.ALTER_CONTRACT && detail.getContractStatus() == ContractStatus.CANCEL) {
                List<PointLocationOccupationLog> lastVersionPointLocationIdLst = occupationLogRepo.queryRecentApprovalContractLog(detail.getContractNo()).stream().filter(log -> (log.getPointLocationId().equals(pointLocationId))).collect(Collectors.toList());
                lastVersionPointLocationIdLst.stream().forEach(log -> {
                    dateSet.add(log.getStartDate());
                    dateSet.add(log.getEndDate());
                });
            }
        });
        DateRange dateRange = queryMaxAndMin(dateSet);
        LocalDate minDate = dateRange.getStart();
        LocalDate maxDate = dateRange.getEnd();
        log.info(">>>pid:{} minDate：{} maxDate: {}", pointLocationId, minDate, maxDate);
        lockTemplate(String.valueOf(pointLocationId), () -> {
            pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(pointLocationId);
            pointLocationLockOperateHelper.batchLockPointLocation(pointLocationId);
            log.info(">>>pid:{}上锁.", pointLocationId);
        });
        revertInventory(pointLocationId, minDate, maxDate);
        singlePointLocationDetailList.forEach(detail -> {
            log.info(">>>点位详情:{}", JSON.toJSONString(detail));
            getDatabase().begin((Transaction tx) -> {
                ContractStatus contractStatus = detail.getContractStatus();
                String contractNo = detail.getContractNo();
                if (ContractType.NEW_CONTRACT == detail.getContractType()) {
                    if (contractStatus == ContractStatus.SUBMIT || contractStatus == ContractStatus.APPROVAL) {
                        log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                        updateInventoryHelper.updateInventory(UpdateInventoryHelper.InventoryOperate.OCCUPY, tx, detail);
                    }
                }
                if (ContractType.ALTER_CONTRACT == detail.getContractType()) {
                    if (ContractStatus.SUBMIT == contractStatus) {
                        log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                        occupyInventoryBiz.occupyInventory(tx, contractNo, Lists.newArrayList(toRealSubmitOccupyData(detail)));
                    }
                    if (ContractStatus.APPROVAL == contractStatus) {
                        log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                        occupyInventoryBiz.occupyInventory(tx, contractNo, Lists.newArrayList(toRealOccupyData(detail)));
                    }
                    if (ContractStatus.CANCEL == contractStatus) {
                        log.info(">>>点位id：{} {} {}合同:{}", detail.getPointLocationId(), contractStatus, detail.getContractType(), contractNo);
                        List<PointLocationOccupationLog> lastVersionApprovalOccupyLogList = occupationLogRepo.queryRecentApprovalContractLog(contractNo);
                        List<PointLocationOccupationContractDetail> lastVersionApprovalOccupyList = CopyUtil.listCopy(lastVersionApprovalOccupyLogList, PointLocationOccupationContractDetail.class);
                        occupyInventoryBiz.occupyInventory(tx, contractNo,
                                toRealOccupyData(
                                        lastVersionApprovalOccupyList.stream().filter(lastVersionDetail -> lastVersionDetail.getDetailId().equals(detail.getDetailId())).collect(Collectors.toList())
                                )
                        );
                    }
                }
            });
        });

        pointLocationLockOperateHelper.batchUnLockPointLocation(pointLocationId);
        log.info(">>>pid:{}解锁.", pointLocationId);
    }

    private void revertInventory(int pointLocationId, LocalDate minDate, LocalDate maxDate) {
        getDatabase().begin((Transaction tx) -> {
            pointLocationInventoryRep.revertNotSellArea(tx, pointLocationId, minDate, maxDate);
            log.info(">>>恢复库存, pointLocationId:{}, minDate:{} - maxDate:{}", pointLocationId, minDate, maxDate);
        });
    }
}
