package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.util.List;

/**
 * Created by fuwei on 2022/6/8.
 */
public class CheckPointLocationInventoryTaskTest extends AbstractCmcQasTest {
    CheckPointLocationInventoryTask task = ServiceLocator.current().getInstance(CheckPointLocationInventoryTask.class);

    @Test
    public void testExecute() {
        List argLst = Lists.newArrayList();
        Arg arg1 = new Arg();
        arg1.Name = "pointLocationIds";
        arg1.Value = "64";
//        argLst.add(arg1);

        Arg arg2 = new Arg();
        arg2.Name = "onlyPrintErrorData";
        arg2.Value = "false";
//        argLst.add(arg2);

        ExecuteParam param = new ExecuteParam();
        param.setArgs(argLst);
        task.execute(new TaskContext(param));
    }
}