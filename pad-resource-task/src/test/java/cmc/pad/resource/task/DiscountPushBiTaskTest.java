package cmc.pad.resource.task;

import mtime.lark.task.TaskContext;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2021/3/31.
 */
public class DiscountPushBiTaskTest extends AbstractCmcQasTest {
    DiscountPushBiTask discountPushBiTask = ServiceLocator.current().getInstance(DiscountPushBiTask.class);

    @Test
    public void testExecute() {
        discountPushBiTask.execute(new TaskContext(new ExecuteParam()));
    }
}