package cmc.pad.resource.task;

import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by fuwei on 2020/10/14.
 */
public class TmpTest {

    @Test
    public void tt() {
        List<PointLocationOccupationContractDetail> list = Lists.newArrayList();
        for (int i = 1; i < 100; i++) {
            PointLocationOccupationContractDetail detail = new PointLocationOccupationContractDetail();
            detail.setId(i);
            detail.setCinemaCode("804");
            detail.setContractNo("contract_" + i);
            detail.setBusinessType("GD");
            detail.setPointLocationId(1);
            detail.setAmount(1F);
            detail.setStartDate(LocalDate.now());
            detail.setEndDate(LocalDate.now());
            detail.setContractStatus(ContractStatus.APPROVAL);
            detail.setUpdateTime(LocalDateTime.now());
            detail.setVersion(0);
            list.add(detail);
        }
        list.stream()
                .map(detail -> {
                    LocalDate startDate = detail.getStartDate();
//                    new PointLocationOccupyFixModel(String.format("%s_%s", detail.getPointLocationId(), detail.get));
                    return null;
                })
                .collect(Collectors.toList());

    }

    @Test
    public void test1() {
        List<Long> list = Lists.newArrayList();
        for (long i = 8092498300142601L; i <= 8092498300143600L; i++) {
            list.add(i);
        }

        Lists.partition(list, 10000).forEach(pList -> {
            System.out.println(pList.stream().map(Object::toString).collect(Collectors.joining(",")));
            System.out.println(">>>>>>>>>>>>>>>>>>>>");
        });
    }

    @Test
    public void t() {
//        LocalDate now = LocalDate.now().minusMonths(6);
//        System.out.println(now + " " + now.plusMonths(7 * 12));

        LocalDate start = LocalDate.now().minusMonths(6);
        LocalDate end = LocalDate.of(start.plusYears(7).getYear(), 12, 31);
        System.out.println(start + " " + end);
    }
}
