package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.util.List;

/**
 * Created by fuwei on 2024/3/15.
 */
public class DeContractCollectionPlanTaskTest extends AbstractCmcQasTest {
    DeContractCollectionPlanTask task = ServiceLocator.current().getInstance(DeContractCollectionPlanTask.class);

    @Test
    public void testExecute() {
        List argLst = Lists.newArrayList();
        Arg arg1 = new Arg();
        arg1.Name = "contractNo";
        arg1.Value = "";
//        argLst.add(arg1);

        Arg arg2 = new Arg();
        arg2.Name = "start";
        arg2.Value = "";
//        argLst.add(arg2);

        Arg arg3 = new Arg();
        arg3.Name = "init";
        arg3.Value = "force";
        argLst.add(arg3);

        ExecuteParam param = new ExecuteParam();
        param.setArgs(argLst);
        task.execute(new TaskContext(param));
    }
}