package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2021/4/1.
 */
public class InventoryPushBiTaskTest extends AbstractCmcQasTest {
    InventoryPushBiTask inventoryPushBiTask = ServiceLocator.current().getInstance(InventoryPushBiTask.class);

    @Test
    public void testExecute() {
        Arg arg1 = new Arg();
        arg1.Name = "taskIndex";
        arg1.Value = "1";

        Arg arg2 = new Arg();
        arg2.Name = "isPushAll";
        arg2.Value = "true";

        Arg arg3 = new Arg();
        arg3.Name = "startDateTime";
        arg3.Value = "2021-04-01";

        ExecuteParam param = new ExecuteParam();
        param.setArgs(Lists.newArrayList(arg1));
        inventoryPushBiTask.execute(new TaskContext(param));
    }
}