package cmc.pad.resource.task;

import cmc.location.admin.service.dto.DistrictInfoDto;
import cmc.location.admin.service.iface.DistrictInfoService;
import cmc.mdm.cinema.admin.contract.dto.CinemaAdminDto;
import cmc.mdm.cinema.admin.contract.iface.CinemaAdminService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2020/10/14.
 */
@Slf4j
public class ComponentTest extends AbstractCmcQasTest {
    public DistrictInfoService districtInfoService = ServiceLocator.current().getInstance(DistrictInfoService.class);
    public CinemaAdminService cinemaAdminService = ServiceLocator.current().getInstance(CinemaAdminService.class);

    @Test
    public void test() {
        DistrictInfoDto.FindDistrictInfosRequest request = new DistrictInfoDto.FindDistrictInfosRequest();
        request.setCity(37);
        DistrictInfoDto.FindDistrictInfosResponse response = districtInfoService.findDistrictInfos(request);
        response.getItems().forEach(item -> {
            log.info(">>>{}", item);
        });
    }

    @Test
    public void test1() {
        CinemaAdminDto.QueryCinemaInfoListResponse response = cinemaAdminService.queryCinemaInfoList(new CinemaAdminDto.QueryCinemaInfoListRequest());
        response.getQueryCinemaList().forEach(cinema -> {
            System.out.println(cinema.getShortName() + " " + cinema.getCity() + " " + cinema.getArea());
        });
    }
}
