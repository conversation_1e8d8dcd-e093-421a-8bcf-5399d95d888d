package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2020/10/14.
 */
public class SyncCityTaskTest extends AbstractCmcQasTest {
    SyncCityTask syncCityTask = ServiceLocator.current().getInstance(SyncCityTask.class);

    @Test
    public void test() {
        Arg level = new Arg();
        level.Name = "initCityDistrictLevel";
        level.Value = "1698,L3;1700,L3;1812,L4;1799,L4;1797,L4;1751,L4;1705,L4;1765,L4;1760,L4;3034,L2;3044,L2;3038,L2;3041,L2;3039,L2;3030,L2;3042,L2;3032,L2;3027,L2;446,L4;506,L4;528,L4;522,L4;461,L2;466,L2;463,L2;455,L3;500,L4;482,L4;548,L4;539,L3;3173,L2;3170,L2;3172,L2;3159,L2;3165,L2;3160,L2;3171,L2;3164,L2;3169,L2;3161,L2;3163,L4;3168,L4;3157,L4;3154,L4;50068,L4;3138,L4;3103,L4;3239,L3;3115,L4;3229,L4;3228,L4;3132,L4;3123,L4;3233,L4;3113,L4;3174,L4;3194,L4;3049,L4;3048,L4;434,L3;436,L3;438,L3;440,L3;431,L3;432,L3;439,L3;441,L3;509,L3;489,L3;488,L3;3747,L2;3746,L2;3774,L2;3771,L2;3761,L2;3763,L2;3772,L2;3773,L2;3745,L2;3822,L2;3821,L2;3668,L3;3835,L3;3832,L3;960,L2;958,L2;968,L4;915,L4;901,L3;947,L3;3858,L2;3861,L2;3859,L2;3857,L2;3856,L2;3693,L1;3685,L1;3690,L1;3686,L1;3684,L1;3687,L1;3692,L1;3680,L1;3869,L3;3863,L3;3865,L3;3814,L4;3788,L4;3792,L3;3789,L3;3714,L4;3850,L3;3849,L3;3726,L2;3736,L2;3716,L2;2807,L2;2817,L2;2818,L2;2731,L4;2795,L4;3347,L3;3349,L3;3385,L2;3384,L2;3390,L2;3387,L2;3379,L4;3380,L4;3369,L4;3322,L4;3319,L4;3273,L4;3356,L3;3263,L4;2356,L3;2543,L3;2542,L3;2541,L3;2549,L4;1429,L3;1428,L3;1430,L3;1433,L3;1407,L3;1402,L3;1370,L3;1369,L3;1389,L3;1391,L3;1451,L3;1400,L3;1397,L3;1401,L3;1395,L3;1775,L4;1712,L2;1714,L2;1713,L2;1715,L2;1709,L4;1711,L4;1719,L4;1766,L4;1742,L3;1755,L4;1727,L3;1804,L4;1828,L3;1833,L3;1830,L3;1843,L4;1872,L4;1818,L3;1820,L3;1819,L3;1855,L4;1839,L4;1924,L4;2864,L3;2863,L3;2861,L4;2867,L3;2881,L3;2883,L3;2882,L3;2884,L3;2823,L4;2824,L4;2821,L4;2919,L3;2913,L3;2920,L4;2856,L3;2852,L3;4452,L4;4481,L4;4453,L4;4347,L2;4346,L2;4443,L3;4486,L4;4463,L4;4492,L3;4415,L3;4421,L3;4426,L4;4400,L4;4395,L4;4379,L3;4372,L3;2946,L2;2598,L2;2608,L2;2610,L2;2609,L2;2605,L2;2616,L4;2595,L4;2720,L4;2585,L4;4300,L4;4276,L2;4274,L2;4320,L4;984,L3;982,L3;1078,L4;1139,L3;1040,L3;1056,L2;1061,L2;1057,L2;1055,L2;1030,L4;1091,L3;1089,L4;1119,L4;1110,L4;2888,L2;2889,L2;2890,L2;2892,L4;2893,L4;2932,L2;2942,L2;2944,L2;2941,L2;2931,L2;2939,L4;2830,L3;2829,L4;2827,L3;2846,L3;2837,L3;3520,L3;3521,L3;3465,L4;3462,L4;3493,L3;3503,L3;3492,L3;3490,L3;3473,L2;3470,L2;3474,L2;3469,L2;3472,L2;3452,L4;1414,L2;1419,L2;1416,L4;1408,L4;1410,L4;1440,L2;1441,L2;1384,L2;1383,L2;1379,L4;1378,L4;1374,L4;4351,L4;4355,L4;4390,L3;4387,L3;4388,L3;4392,L3;4383,L3;4402,L4;4410,L4;4408,L4;4367,L3;4366,L3;4358,L3;884,L3;943,L3;942,L3;912,L2;911,L2;908,L4;906,L4;907,L4;904,L4;903,L4;902,L4;977,L2;971,L2;975,L2;936,L3;927,L3;4520,L1;4503,L1;4519,L1;4517,L1;4509,L1;4521,L1;4518,L1;4515,L1;4516,L1;4506,L1;4508,L1;3699,L1;3698,L1;3703,L1;3695,L1;4011,L3;4036,L3;4018,L3;4035,L3;4089,L3;4095,L4;3925,L2;3924,L2;3958,L3;3957,L3;4007,L4;4006,L4;883,L4;835,L4;858,L4;782,L4;865,L2;753,L4;833,L4;3657,L2;3646,L2;3654,L2;3647,L2;3653,L2;3661,L2;3652,L2;1473,L2;1480,L2;2908,L2;2900,L2;2897,L2;2904,L2;2905,L2;2896,L4;2895,L4;2898,L4;2894,L4;2930,L2;2921,L2;2923,L4;2924,L4;626,L4;627,L4;629,L4;603,L4;582,L4;585,L4;644,L3;645,L3;616,L4;657,L4;597,L2;592,L2;593,L2;587,L2;588,L2;594,L2;589,L2;567,L3;625,L4;555,L3;3525,L4;3601,L4;3568,L4;3641,L4;3553,L2;3555,L2;3546,L2;3554,L2;3547,L2;3626,L3;3625,L3;3576,L4;3614,L4;1363,L4;1342,L4;1356,L3;1358,L3;1357,L3;1677,L3;1680,L3;1673,L4;1689,L4;1690,L4;1632,L2;1638,L2;1634,L2;1635,L2;1639,L2;1629,L4;4170,L4;4223,L4;4152,L3;4148,L3;4098,L4;4111,L4;4138,L4;4139,L4;4198,L4;4194,L4;4185,L3;4132,L2;4131,L2;4133,L2;4130,L2;4135,L4;4127,L4;4117,L3;1213,L4;1201,L4;1225,L4;1188,L4;1172,L3;1170,L3;1286,L3;1223,L4;1302,L4;1320,L4;1321,L4;1247,L4;1284,L4;1160,L2;1165,L2;1155,L2;1158,L2;1159,L2;1308,L4;1316,L4;3883,L3;3876,L3;3879,L3;3880,L3;3878,L3;3875,L3;3877,L3;3884,L3;3871,L3;3870,L3;3908,L3;3909,L3;3886,L3;3899,L3;3872,L3;3907,L3;";
        ExecuteParam param = new ExecuteParam();
        param.setArgs(Lists.newArrayList(level));
        TaskContext taskContext = new TaskContext(param);
        syncCityTask.execute(taskContext);
    }
}
