package cmc.pad.resource.task;

import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2021/4/1.
 */
public class CityInfoPushBiTaskTest extends AbstractCmcQasTest {
    CityInfoPushBiTask cityInfoPushBiTask = ServiceLocator.current().getInstance(CityInfoPushBiTask.class);

    @Test
    public void testExecute() {
        Arg arg1 = new Arg();
        arg1.Name = "taskIndex";
        arg1.Value = "2";
        ExecuteParam param = new ExecuteParam();
//        param.setArgs(Lists.newArrayList(arg1));
        cityInfoPushBiTask.execute(new TaskContext(param));
    }
}