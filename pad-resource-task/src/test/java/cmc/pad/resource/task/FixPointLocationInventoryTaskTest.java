package cmc.pad.resource.task;

import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.util.DateMapSumAreaUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_POINT_LOCATION_ID;
import static cmc.pad.resource.util.DateUtil.getDates;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/5/20.
 */
@Slf4j
public class FixPointLocationInventoryTaskTest extends AbstractCmcQasTest {
    FixPointLocationInventoryTask task = ServiceLocator.current().getInstance(FixPointLocationInventoryTask.class);
    PointLocationInventoryRepository inventoryRep = ServiceLocator.current().getInstance(PointLocationInventoryRepository.class);
    PointLocationOccupationContractRepository contractRep = ServiceLocator.current().getInstance(PointLocationOccupationContractRepository.class);
    PointLocationOccupationContractDetailRepository detailRep = ServiceLocator.current().getInstance(PointLocationOccupationContractDetailRepository.class);
    PointLocationOccupationLogRepository logRep = ServiceLocator.current().getInstance(PointLocationOccupationLogRepository.class);

    @Test
    public void execute() {
        List argLst = Lists.newArrayList();
        Arg arg1 = new Arg();
        arg1.Name = "pointLocationIds";
        arg1.Value = "64";
        argLst.add(arg1);

        Arg arg2 = new Arg();
        arg2.Name = "onlyCheck";
        arg2.Value = "false";
        argLst.add(arg2);

        ExecuteParam param = new ExecuteParam();
        param.setArgs(argLst);
        task.execute(new TaskContext(param));

        List<String> pointLocationIdList = Lists.newArrayList(arg1.Value.split(","));
        eachPointLocationIdList(pointLocationIdList);
    }

    private void eachPointLocationIdList(List<String> pointLocationIdList) {
        pointLocationIdList.stream().forEach(pointLocationIdStr -> {
            DateMapSumAreaUtil dateMapSumAreaUtil = new DateMapSumAreaUtil();
            Integer pointLocationId = Integer.valueOf(pointLocationIdStr);
            List<PointLocationOccupationContractDetail> details = detailRep.findMany(f(PLO_C_D_POINT_LOCATION_ID, pointLocationId));
            details.forEach(detail -> {
                log.info(">>>点位id:{} startDate：{} endDate: {} area:{}", detail.getPointLocationId(), detail.getStartDate(), detail.getEndDate(), detail.getAmount());
                if (detail.getContractType() == ContractType.ALTER_CONTRACT && detail.getContractStatus() == ContractStatus.CANCEL) {
                    List<PointLocationOccupationLog> lastVersionPointLocationIdLst = logRep.queryRecentApprovalContractLog(detail.getContractNo())
                            .stream()
                            .filter(log -> (log.getPointLocationId().equals(pointLocationId)))
                            .filter(log -> log.getAlterStatus() != AlterStatus.DESTROY)
                            .collect(Collectors.toList());
                    lastVersionPointLocationIdLst.stream().forEach(log -> {
                        calcDetailDateArea(dateMapSumAreaUtil, log.getStartDate(), log.getEndDate(), log.getAmount());
                    });
                } else {
                    calcDetailDateArea(dateMapSumAreaUtil, detail.getStartDate(), detail.getEndDate(), detail.getAmount());
                }
            });
            dateMapSumAreaUtil.getDateMapSumAreaResult().forEach((date, area) -> {
                log.info(">>>pid:{} date:{} area:{}", pointLocationId, date, area);
                PointLocationInventory plInventory = inventoryRep.query(pointLocationId, date);
                Assert.assertEquals(pointLocationId + " - " + date.toString(), area.floatValue(), plInventory.getSellArea().floatValue() - plInventory.getNotSellArea().floatValue(), 0.00);
            });
        });
    }

    private void calcDetailDateArea(DateMapSumAreaUtil dateMapSumAreaUtil, LocalDate start, LocalDate end, Float amount) {
        getDates(start, end).stream().forEach(date -> {
            dateMapSumAreaUtil.add(date, amount);
        });
    }
}