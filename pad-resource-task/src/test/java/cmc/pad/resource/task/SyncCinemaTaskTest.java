package cmc.pad.resource.task;

import cmc.mdm.cinema.admin.contract.dto.CinemaAdminDto;
import cmc.mdm.cinema.admin.contract.iface.CinemaAdminService;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import mtime.lark.util.msg.Publisher;
import org.junit.Test;

import static cmc.pad.resource.constant.TopicName.POINT_LOCATION_GENERATE_INVENTORY_TOPIC;

/**
 * Created by fuwei on 2020/10/14.
 */
public class SyncCinemaTaskTest extends AbstractCmcQasTest {
    SyncCinemaTask syncCinemaTask = ServiceLocator.current().getInstance(SyncCinemaTask.class);
    CinemaAdminService cinemaAdminService = ServiceLocator.current().getInstance(CinemaAdminService.class);

    @Test
    public void test1() {
        ExecuteParam param = new ExecuteParam();
        TaskContext taskContext = new TaskContext(param);
        syncCinemaTask.execute(taskContext);
    }

    @Test
    public void test2() {
        cinemaAdminService.queryCinemaInfoList(new CinemaAdminDto.QueryCinemaInfoListRequest())
                .getQueryCinemaList().forEach(cinema -> {
                    if (cinema.getInnerCode().equals("304"))
                        System.out.println(cinema.getArea());
        });

    }

    @Test
    public void sendMsg() {
//        Publisher.get().publish(POINT_LOCATION_GENERATE_INVENTORY_TOPIC, 20988);

        for (String s : "20991,20992,20993,20994,20995,20996,20998,20999,21000".split(",")) {
            Publisher.get().publish(POINT_LOCATION_GENERATE_INVENTORY_TOPIC, s);
        }
    }
}
