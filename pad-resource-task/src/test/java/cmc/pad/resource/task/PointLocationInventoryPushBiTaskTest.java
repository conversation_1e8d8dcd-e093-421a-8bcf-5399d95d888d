package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.util.List;

/**
 * Created by fuwei on 2022/2/5.
 */
public class PointLocationInventoryPushBiTaskTest extends AbstractCmcQasTest {
    PointLocationInventoryPushBiTask task = ServiceLocator.current().getInstance(PointLocationInventoryPushBiTask.class);

    @Test
    public void testExecute() {
        List<Arg> argList = Lists.newArrayList();

        Arg startDate = new Arg();
        startDate.Name = "startDate";
        startDate.Value = "2022-07-27";
//        argList.add(startDate);

        Arg endDate = new Arg();
        endDate.Name = "endDate";
        endDate.Value = "2022-07-28";
//        argList.add(endDate);

        Arg isAll = new Arg();
        isAll.Name = "isAll";
        isAll.Value = "true";
//        argList.add(isAll);

        ExecuteParam param = new ExecuteParam();
        param.setArgs(argList);
        task.execute(new TaskContext(param));
    }
}