package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2021/4/1.
 */
public class ResourceBudgetPushBiTaskTest extends AbstractCmcQasTest {
    ResourceBudgetPushBiTask resourceBudgetPushBiTask = ServiceLocator.current().getInstance(ResourceBudgetPushBiTask.class);

    @Test
    public void testExecute() {
        Arg arg1 = new Arg();
        arg1.Name = "startDate";
        arg1.Value = "2022-01-18";
        ExecuteParam param = new ExecuteParam();
        param.setArgs(Lists.newArrayList(arg1));
        resourceBudgetPushBiTask.execute(new TaskContext(param));
    }
}