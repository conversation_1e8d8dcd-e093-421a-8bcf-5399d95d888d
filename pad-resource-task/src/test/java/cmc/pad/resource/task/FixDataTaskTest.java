package cmc.pad.resource.task;

import mtime.lark.task.TaskContext;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuyuanpu on 2022/7/7.
 */
public class FixDataTaskTest extends AbstractCmcQasTest {
    FixDataTask fixDataTask = ServiceLocator.current().getInstance(FixDataTask.class);

    @Test
    public void execute() {
        fixDataTask.execute(new TaskContext(new ExecuteParam()));
    }
}