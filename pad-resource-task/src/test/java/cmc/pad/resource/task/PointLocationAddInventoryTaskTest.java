package cmc.pad.resource.task;

import com.google.common.collect.Lists;
import mtime.lark.task.TaskContext;
import mtime.lark.task.data.Arg;
import mtime.lark.task.data.ExecuteParam;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.util.List;

/**
 * Created by fuwei on 2022/1/26.
 */
public class PointLocationAddInventoryTaskTest extends AbstractCmcQasTest {
    PointLocationAddInventoryTask task = ServiceLocator.current().getInstance(PointLocationAddInventoryTask.class);

    @Test
    public void testExecute() {
        List<Arg> argList = Lists.newArrayList();
        Arg isBatchInsert = new Arg();
        isBatchInsert.Name = "isBatchInsert";
        isBatchInsert.Value = "false";
//        argList.add(isBatchInsert);

        Arg idArg = new Arg();
        idArg.Name = "id";
        idArg.Value = "20960";
//        argList.add(idArg);

        Arg startDate = new Arg();
        startDate.Name = "startDate";
        startDate.Value = "2028-05-01";
//        argList.add(startDate);

        Arg endDate = new Arg();
        endDate.Name = "endDate";
        endDate.Value = "2029-01-01";
        argList.add(endDate);


//        Arg endDate = new Arg();
//        endDate.Name = "endDate";
//        endDate.Value = "2028-05-04";
//        argList.add(endDate);

        ExecuteParam param = new ExecuteParam();
        param.setArgs(argList);
        task.execute(new TaskContext(param));
    }
}