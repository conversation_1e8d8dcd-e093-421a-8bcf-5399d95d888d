<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>parent</artifactId>
        <groupId>cmc.parent</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <groupId>cmc.pad</groupId>
    <artifactId>pad-resource</artifactId>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>pad-resource-admin-service</module>
        <module>pad-resource-core</module>
        <module>pad-resource-admin-service-contract</module>
        <module>pad-resource-task</module>
        <module>pad-resource-handler</module>
        <module>pad-resource-admin-web</module>
        <module>pad-resource-admin-api</module>
    </modules>
    <packaging>pom</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <version.lark>1.1.48-SNAPSHOT</version.lark>
        <version.watcher>1.9.9</version.watcher>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>mtime.lark</groupId>
                <artifactId>lark-dependencies</artifactId>
                <version>${version.lark}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>mtime.lark</groupId>
                <artifactId>lark-bom</artifactId>
                <version>${version.lark}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>mx.common</groupId>
                <artifactId>file-service-contract</artifactId>
                <version>${version.file}</version>
            </dependency>
            <dependency>
                <groupId>mx.common</groupId>
                <artifactId>mail-service-contract</artifactId>
                <version>${version.mail}</version>
            </dependency>
            <dependency>
                <groupId>mx.common</groupId>
                <artifactId>mx-util-excel</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cmc.mdm</groupId>
                <artifactId>location-front-service-contract</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cmc.mdm</groupId>
                <artifactId>location-admin-service-contract</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cmc.mdm</groupId>
                <artifactId>mdm-cinema-admin-contract</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cmc.basic</groupId>
                <artifactId>data-dict-contract</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cmc.mdm</groupId>
                <artifactId>location-front-service-contract</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <resources>
            <resource>
                <directory>build</directory>
                <includes>
                    <include>build.json</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.19.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.6.0</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>2.2.0</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>revision</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <!--上传配置 必须-->
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>${releases-url}</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>${snapshots-url}</url>
        </snapshotRepository>
    </distributionManagement>
    <profiles>
        <profile>
            <id>mtime</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <releases-url>http://repo.mx.com/content/repositories/mx</releases-url>
                <snapshots-url>http://repo.mx.com/content/repositories/mx-snapshots</snapshots-url>
            </properties>
        </profile>
    </profiles>
</project>

