package cmc.pad.resource.domain.contract;

import cmc.pad.resource.common.repository.jsd.JsdRepository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by fuwei on 2022/1/12.
 */
public abstract class ContractRepository extends JsdRepository<Contract, Long> {
    public abstract List<Contract> pageList(String contractNo, LocalDateTime start, LocalDateTime end, int pageNo, int pageSize);

    public abstract void insertOrUpdate(Contract contract);
}