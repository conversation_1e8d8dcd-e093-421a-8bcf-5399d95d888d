package cmc.pad.resource.domain.inventory.point;

import cmc.tohdfs.sdk.annotation.DSFiled;
import com.google.common.base.Objects;
import lombok.*;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Transient;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 点位合同明细
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@ToString
public class PointLocationOccupationContractDetail {
    @DSFiled(order = 1)
    private Integer id;
    @DSFiled(order = 2)
    private String detailId;//明细唯一标识
    @DSFiled(order = 3)
    private String cinemaCode;
    @DSFiled(order = 4)
    private String contractNo;
    @DSFiled(order = 5)
    private String businessType;
    @DSFiled(order = 6)
    private Integer pointLocationId;
    @DSFiled(order = 7)
    private Float amount;
    @DSFiled(order = 8)
    private LocalDate startDate;
    @DSFiled(order = 9)
    private LocalDate endDate;
    @DSFiled(order = 10)
    private ContractStatus contractStatus;
    @DSFiled(order = 11)
    private ContractType contractType;
    @DSFiled(order = 12)
    private LocalDateTime updateTime;
    @DSFiled(order = 13)
    private Integer version;
    @DSFiled(order = 14)
    private AlterStatus alterStatus;//点位明细变更状态
    @DSFiled(order = 15)
    private String alterRemark;//变更备注

    @Transient
    private LocalDateTime createTime;//用于传给日志表

    public static final String TABLE = "point_location_occupation_contract_detail";
    public static final String PLO_C_D_CONTRACT_STATUS = "contract_status";
    public static final String PLO_C_D_ALTER_STATUS = "alter_status";
    public static final String PLO_C_D_END_DATE = "end_date";
    public static final String PLO_C_D_CONTRACT_NO = "contract_no";
    public static final String PLO_C_D_POINT_LOCATION_ID = "point_location_id";
    public static final String PLO_C_D_VERSION = "version";
    public static final String PLO_C_D_UPDATE_TIME = "update_time";

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PointLocationOccupationContractDetail)) return false;
        PointLocationOccupationContractDetail detail = (PointLocationOccupationContractDetail) o;
        return Objects.equal(getCinemaCode(), detail.getCinemaCode()) &&
                Objects.equal(getContractNo(), detail.getContractNo()) &&
                Objects.equal(getBusinessType(), detail.getBusinessType()) &&
                Objects.equal(getPointLocationId(), detail.getPointLocationId()) &&
                Objects.equal(getAmount(), detail.getAmount()) &&
                Objects.equal(getStartDate(), detail.getStartDate()) &&
                Objects.equal(getEndDate(), detail.getEndDate());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getCinemaCode(), getContractNo(), getBusinessType(), getPointLocationId(), getAmount(), getStartDate(), getEndDate());
    }
}