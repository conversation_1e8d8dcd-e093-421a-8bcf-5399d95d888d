package cmc.pad.resource.domain.inventory.point;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import java.time.LocalDateTime;

/**
 * Created by fuyuanpu on 2022/6/30.
 */
@Getter
@Setter
@ToString
@JsdTable(nameStyle = NameStyle.LOWER)
public class NotNeedInventoryManagePointLocationContract {
    @Id
    private Integer id;
    private String contractNo;
    private ContractStatus status;
    private ContractType contractType;
    private String businessType;
    private String details;
    private LocalDateTime createTime;
}