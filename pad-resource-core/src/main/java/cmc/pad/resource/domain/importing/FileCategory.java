package cmc.pad.resource.domain.importing;

import mtime.lark.util.lang.EnumDisplayNameSupport;
import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * <AUTHOR>
 */
public enum FileCategory implements EnumValueSupport, EnumDisplayNameSupport {
    ADVERTISING_POINT_LEASING_PRICE(1, "宣传点位刊例价文件"),
    FIXED_POINT_LEASING_PRICE(2, "固定点位刊例价文件"),
    OUTER_AREALEASING_PRICE(3, "外租区域刊例价"),
    MOVIE_HALL_SEAT_LEASING_PRICE(4, "影厅租赁刊例价"),
    NAMED_MOVIE_HALL_LEASING_PRICE(5, "影厅冠名刊例价"),
    MARKETING_POINT_LEASING_PRICE(6, "营销点位刊例价"),
    CINEMA_LEVEL(7, "影城级别"),
    BUDGET_HALL(8, "影厅资源预算"),
    BUDGET_POSITION(9, "点位资源预算"),
    POINT_LOCATION(10, "点位资源"),
    LIGHT_BOX_PRICE(11, "灯箱刊例");

    private int value;
    private String displayName;

    private FileCategory(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    FileCategory() {

    }

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }

    public static FileCategory valueOf(int value) {
        return Enums.valueOf(FileCategory.class, value);
    }
}
