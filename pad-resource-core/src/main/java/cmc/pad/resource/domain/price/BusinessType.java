package cmc.pad.resource.domain.price;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
public enum BusinessType {
    MOVIE_HALL_SEAT_LEASING("YT", "影厅座位租赁", 1),
    ADVERTISING_POINT_LEASING("YX", "营销点位租赁", 2),
    OUTER_AREA_LEASING("WZ", "外租区域租赁", 3),
    FIXED_POINT_LEASING("GD", "固定点位租赁", 4),
    MARKETING_POINT_LEASING("XC", "宣传点位租赁", 5),
    NAMED_MOVIE_HALL_LEASING("GMT", "冠名厅租赁", 6),;

    private final String nameLowerCase;
    private final String code;
    @Setter(AccessLevel.PACKAGE)
    private String nameChs;
    @Setter(AccessLevel.PACKAGE)
    private int order;

    BusinessType(String code, String nameChs, int order) {
        this.order = order;
        this.code = code;
        this.nameChs = nameChs;
        this.nameLowerCase = this.name().toLowerCase();
    }

}
