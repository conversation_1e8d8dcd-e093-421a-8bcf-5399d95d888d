package cmc.pad.resource.domain.inventory;

import lombok.*;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@ToString
public class Occupation {
    private Integer id;
    private String cinemaCode;
    private String contractNo;
    private String businessType;
    private Float amount;
    private LocalDate startDate;
    private LocalDate endDate;
    private OccupationStatus status;
    private LocalDateTime updateTime;
}
