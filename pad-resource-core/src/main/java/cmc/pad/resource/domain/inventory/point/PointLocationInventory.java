package cmc.pad.resource.domain.inventory.point;

import cmc.tohdfs.sdk.annotation.DSFiled;
import lombok.*;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Created by fuwei on 2022/1/10.
 */
@Getter
@Setter
@ToString
@JsdTable(nameStyle = NameStyle.LOWER)
public class PointLocationInventory implements Cloneable {
    @Id
    private Long id;
    @DSFiled(order = 1)
    private Integer pointLocationId;
    @DSFiled(order = 2)
    private LocalDate date;
    @DSFiled(order = 3)
    private Float sellArea;
    @DSFiled(order = 4)
    private Float notSellArea;
    @DSFiled(order = 5)
    private LocalDateTime updateTime;

    @Override
    public Object clone() {
        PointLocationInventory pli = null;
        try {
            pli = (PointLocationInventory) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return pli;
    }

    public static final String PL_INVENTORY_DATE = "date";
    public static final String PL_INVENTORY_SELL_AREA = "sell_area";
    public static final String PL_INVENTORY_UPDATE_TIME = "update_time";
    public static final String PL_INVENTORY_NOT_SELL_AREA = "not_sell_area";
    public static final String PL_INVENTORY_POINT_LOCATION_ID = "point_location_id";
    public static final String TABLE_NAME = "point_location_inventory";
}