package cmc.pad.resource.domain.common;

import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImportStatus;
import lombok.*;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * Created by fuwei on 2022/1/20.
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@Table(name = "file_export_record")
@NoArgsConstructor
public class FileExportRecord {
    @Id
    Integer id;
    String fileId;
    FileImportStatus status;
    FileCategory category;
    LocalDateTime createTime;
    LocalDateTime updateTime;
    private String remark;

    public FileExportRecord(FileCategory category, String remark) {
        LocalDateTime now = LocalDateTime.now();
        this.status = FileImportStatus.UNDERWAY;
        this.category = category;
        this.createTime = now;
        this.updateTime = now;
        this.remark = remark;
    }

    public FileExportRecord(FileCategory category) {
        LocalDateTime now = LocalDateTime.now();
        this.status = FileImportStatus.UNDERWAY;
        this.category = category;
        this.createTime = now;
        this.updateTime = now;
    }

    public FileExportRecord updateFileIdAndStatus(String fileId, FileImportStatus status) {
        this.fileId = fileId;
        this.status = status;
        this.updateTime = LocalDateTime.now();
        return this;
    }
}
