package cmc.pad.resource.domain.budget;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.util.data.PageResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
abstract public class ResourceBudgetPointLocationRepository extends JsdRepository<ResourceBudgetPointLocation, Long> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    public abstract int batchInsert(List<ResourceBudgetPointLocation> list);



    public abstract int update(ResourceBudgetPointLocation hall);

    public abstract List<ResourceBudgetPointLocation> getList(int pageSize, int page, LocalDateTime startTime, LocalDateTime endTime);

    public abstract PageResult<ResourceBudgetPointLocationView> getPageList(String year, String regionCode, String cinemaInnerCode, String resourceType,String resourceCode, int pageSize, int pageIndex);
}
