package cmc.pad.resource.domain.contract;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 多种经营合同收款计划实体类
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class ContractCollectionPlan {
    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 合同/申请单号
     */
    private String contractNo;

    /**
     * 收款计划条件
     */
    private String condition;

    /**
     * 收款类型
     * 1:保证金
     * 2:合同款
     */
    private Integer type;

    /**
     * 计划收款日期
     */
    private LocalDate planCollectionDate;

    /**
     * 计划收款金额（元）
     */
    private BigDecimal planCollectionAmount;
    
}