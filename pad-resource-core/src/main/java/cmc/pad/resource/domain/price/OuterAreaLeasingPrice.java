package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 外租区域报价
 *
 * <AUTHOR>
 * @Date 2019/3/20 17:33
 * @Version 1.0
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class OuterAreaLeasingPrice extends Price {
    private String cityLevel;
    private String cinemaLevel;
    private Integer unitPrice;
    public static final String T_OUTER_AREA_LEASING_PRICE = "outer_area_leasing_price";
    public static final String C_CITY_LEVEL = "city_level";
    public static final String C_CINEMA_LEVEL = "cinema_level";
}
