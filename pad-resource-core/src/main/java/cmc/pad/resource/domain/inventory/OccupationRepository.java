package cmc.pad.resource.domain.inventory;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.db.jsd.Transaction;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class OccupationRepository extends JsdRepository<Occupation, Integer> {

    public abstract int insertOccupation(Transaction tx, Occupation occupation);

    public abstract int cancelOccupation(Transaction tx, String contractNo);

    public abstract int updateOccupation(Transaction tx, Occupation occupation);

    public abstract int updateOccupationStatus(Transaction tx, String contractNo, OccupationStatus status);

    public abstract int updateOccupationStatus(String contractNo, OccupationStatus status);

    public abstract int batchInsert(Transaction tx, List<Occupation> list);

    public abstract int delete(Transaction tx, String contractNo);

    public abstract Occupation queryRecentlyOccupation(String contractNo);
}
