package cmc.pad.resource.domain.budget;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2022/1/12 15:16
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class ResourceBudgetPointLocationChangeRecord {
    @Id
    private Long id;
    private String year;
    private String regionCode;
    private String regionName;
    private String cinemaInnerCode;
    private String cinemaName;

    /**
     * 资源编码
     */
    private String resourceCode;

    private BigDecimal budgetYear;
    private BigDecimal budgetMonth1;
    private BigDecimal budgetMonth2;
    private BigDecimal budgetMonth3;
    private BigDecimal budgetMonth4;
    private BigDecimal budgetMonth5;
    private BigDecimal budgetMonth6;
    private BigDecimal budgetMonth7;
    private BigDecimal budgetMonth8;
    private BigDecimal budgetMonth9;
    private BigDecimal budgetMonth10;
    private BigDecimal budgetMonth11;
    private BigDecimal budgetMonth12;
    private LocalDateTime createTime;
    private Integer userId;
    private String userName;
    private Integer importId;
}
