package cmc.pad.resource.domain.inventory.point;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDate;
import java.time.LocalDateTime;


@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@ToString
public class PointLocationOccupationLog {
    private Long id;
    private String cinemaCode;
    private String contractNo;
    private String businessType;
    private Integer pointLocationId;
    private Float amount;
    private LocalDate startDate;
    private LocalDate endDate;
    private ContractStatus contractStatus;
    private LocalDateTime createTime;
    private Integer version;

    private String detailId;//唯一标识
    private ContractType contractType;
    private AlterStatus alterStatus;//点位明细变更状态
    private String alterRemark;//变更备注

    public static final String TABLE = "point_location_occupation_log";
    public static final String PLO_LOG_CONTRACT_STATUS = "contract_status";
    public static final String PLO_LOG_ALTER_STATUS = "alter_status";
    public static final String PLO_LOG_VERSION = "version";
    public static final String PLO_LOG_END_DATE = "end_date";
    public static final String PLO_LOG_CONTRACT_NO = "contract_no";
    public static final String PLO_LOG_CREATE_TIME = "create_time";
    public static final String PLO_LOG_POINT_LOCATION_ID = "point_location_id";
}