package cmc.pad.resource.domain.city;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;

@Setter
@Getter
@ToString
public class CityDistrictListModel {
    @Column(name = "code")
    private String code;
    @Column(name = "dName")
    private String districtName;
    @Column(name = "cName")
    private String cityName;
    @Column(name = "region_name")
    private String regionName;
    @Column(name = "superior_name")
    private String superiorName;
    @Column(name = "level")
    private String level;
}
