package cmc.pad.resource.domain.resource;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import com.alibaba.fastjson.JSON;
import lombok.*;
import mtime.lark.util.lang.FaultException;
import mx.common.excel.annotation.ExcelField;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by fuwei on 2022/1/12.
 */
@Data
public class PointLocationModel {
    @Data
    @NoArgsConstructor
    public static class InventoryOccupationContractParam {
        private int contractType;//1:新提交合同 2:变更已审核过的合同
        private String contractNo;
        private String businessTypeCode;
        private List<OccupationDetail> details;

        @Data
        public static class OccupationDetail {
            private String cinemaInnerCode;
            private Integer pointLocationId;
            private Float amount;
            private LocalDate startDate;
            private LocalDate endDate;
            private String id;
            private Integer status; //1.占用（包含不变的和修改的） 2.新增 3.作废
        }
    }

    @NoArgsConstructor
    public static class UpdateStatusParam {
        @Getter
        @Setter
        private String contractNo;
        @Setter
        private int status;//1:扣减 2:取消

        public ContractStatus getStatus() {
            if (status == 1)
                return ContractStatus.APPROVAL;
            if (status == 2)
                return ContractStatus.CANCEL;
            throw new FaultException("status参数错误");
        }
    }

    @Data
    @NoArgsConstructor
    public static class ContractNoParam {
        private String contractNo;
    }

    @Data
    @NoArgsConstructor
    public static class QueryPointLocationUsableAreaData {
        int id;
        String code;
        float usableArea;
        String businessTypeCode;
        String cinemaInnerCode;
        private String resourceOwnershipCode;
        private String locationDesc;
        private String planUse;
        private String landingMode;


        public QueryPointLocationUsableAreaData(
                int id, String code, String businessTypeCode, String cinemaInnerCode,
                String resourceOwnershipCode, String locationDesc, String planUse, String landingMode
        ) {
            this.id = id;
            this.code = code;
            this.usableArea = 0;
            this.cinemaInnerCode = cinemaInnerCode;
            this.businessTypeCode = businessTypeCode;
            this.resourceOwnershipCode = resourceOwnershipCode;
            this.locationDesc = locationDesc;
            this.planUse = planUse;
            this.landingMode = landingMode;
        }
    }

    @Data
    public static class SaveParam extends PointLocation {
        private String userName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ListQueryParam {
        private int pageIndex;
        private int pageSize;
        private String largeWardSelect;
        private String code;
        private String region;
        private String cinema;
        private String businessTypeCode;
        private String resourceOwnershipCode;
        private UserRankInfo userRankInfo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserRankInfo {
        int rank;
        String largeWardCode;
        String areaCode;
        String cinemaInnerCode;
    }

    @Data
    @NoArgsConstructor
    public static class ListDataParam extends PointLocation {
        private String regionName;
        private String businessTypeName;
        private String resourceOwnershipName;
        private String isSplittableText; // 是否可拆分
        private String waterSupplyText; // 上下水
        private String powerSupplyText; // 强电
        private String fireFacilitiesText; // 消防设施

        public ListDataParam(int id, String code, String cityCode, String cinemaInnerCode,
                             String businessTypeCode, String resourceOwnershipCode, String locationDesc, String planUse,
                             String landingMode, Float sellArea, String sellAreaUpdateTime,
                             String regionName, String cinemaName, String businessTypeName, String resourceOwnershipName
                , String availablePeriod, String floor, BigDecimal floorHeight
                , String isSplittableText, String waterSupplyText, String powerSupplyText, String fireFacilitiesText
                , String decoration
        ) {
            super(id, code, cityCode, cinemaInnerCode, cinemaName, businessTypeCode, resourceOwnershipCode, locationDesc, planUse, landingMode, sellArea, sellAreaUpdateTime
                    , availablePeriod, floor, floorHeight, null, null, null, null, decoration);
            this.regionName = regionName;
            this.businessTypeName = businessTypeName;
            this.resourceOwnershipName = resourceOwnershipName;
            this.isSplittableText = isSplittableText; // 是否可拆分
            this.waterSupplyText = waterSupplyText; // 上下水
            this.powerSupplyText = powerSupplyText; // 强电
            this.fireFacilitiesText = fireFacilitiesText; // 消防设施
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchQueryParam {
        private String cinemaInnerCode;
        private String code;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PointLocation {
        private int id;
        private String code;
        private String regionCode;
        private String cinemaInnerCode;
        private String cinemaName;
        private String businessTypeCode;
        private String resourceOwnershipCode;
        private String locationDesc;
        private String planUse;
        private String landingMode;
        private Float sellArea;
        private String sellAreaAdjustDate;
        private String availablePeriod; // 可用时段
        private String floor; // 楼层
        private BigDecimal floorHeight; // 层高
        private Integer isSplittable; // 是否可拆分
        private Integer waterSupply; // 上下水
        private Integer powerSupply; // 强电
        private Integer fireFacilities; // 消防设施
        private String decoration; // 装修
    }

    public static void main(String[] args) {
        SaveParam saveParam = new SaveParam();
//        saveParam.setFloorHeight("123.33");
        saveParam.setIsSplittable(null);
        saveParam.setFireFacilities(PointLocationInfo.YesOrNo.YES.value());
        PointLocationInfo copy = CopyUtil.copy(saveParam, PointLocationInfo.class);
//        copy.setFloorHeight(new BigDecimal(saveParam.getFloorHeight()));
        System.out.printf(" >>> " + JSON.toJSONString(copy, true));
    }

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExportExcel {
        @ExcelField(title = "区域内码", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String regionCode;
        @ExcelField(title = "区域", groups = 0, align = 2, sort = 2, width = 5000, fieldType = String.class)
        private String regionName;
        @ExcelField(title = "影城内码", groups = 0, align = 2, sort = 3, width = 5000, fieldType = String.class)
        private String cinemaInnerCode;
        @ExcelField(title = "影城名称", groups = 0, align = 2, sort = 4, width = 5000, fieldType = String.class)
        private String cinemaName;
        @ExcelField(title = "资源类型编码", groups = 0, align = 2, sort = 5, width = 5000, fieldType = String.class)
        private String businessTypeCode;
        @ExcelField(title = "资源类型", groups = 0, align = 2, sort = 6, width = 5000, fieldType = String.class)
        private String businessTypeName;
        @ExcelField(title = "资源归属编码", groups = 0, align = 2, sort = 7, width = 5000, fieldType = String.class)
        private String resourceOwnershipCode;
        @ExcelField(title = "资源归属", groups = 0, align = 2, sort = 8, width = 5000, fieldType = String.class)
        private String resourceOwnershipName;
        @ExcelField(title = "资源编码", groups = 0, align = 2, sort = 9, width = 5000, fieldType = String.class)
        private String code;
        @ExcelField(title = "可用时段", groups = 0, align = 2, sort = 10, width = 5000, fieldType = String.class)
        private String availablePeriod;
        @ExcelField(title = "楼层", groups = 0, align = 2, sort = 11, width = 5000, fieldType = String.class)
        private String floor;
        @ExcelField(title = "位置描述", groups = 0, align = 2, sort = 12, width = 5000, fieldType = String.class)
        private String locationDesc;
        @ExcelField(title = "规划用途", groups = 0, align = 2, sort = 13, width = 5000, fieldType = String.class)
        private String planUse;
        @ExcelField(title = "落地方式", groups = 0, align = 2, sort = 14, width = 5000, fieldType = String.class)
        private String landingMode;
        @ExcelField(title = "可售面积", groups = 0, align = 2, sort = 15, width = 5000, fieldType = String.class)
        private String sellArea;
        @ExcelField(title = "层高", groups = 0, align = 2, sort = 16, width = 5000, fieldType = String.class)
        private String floorHeight;
        @ExcelField(title = "是否可拆分", groups = 0, align = 2, sort = 17, width = 5000, fieldType = String.class)
        private String isSplittable;
        @ExcelField(title = "上下水", groups = 0, align = 2, sort = 18, width = 5000, fieldType = String.class)
        private String waterSupply;
        @ExcelField(title = "强电", groups = 0, align = 2, sort = 19, width = 5000, fieldType = String.class)
        private String powerSupply;
        @ExcelField(title = "消防设施", groups = 0, align = 2, sort = 20, width = 5000, fieldType = String.class)
        private String fireFacilities;
        @ExcelField(title = "装修", groups = 0, align = 2, sort = 21, width = 5000, fieldType = String.class)
        private String decoration;
    }

    @Setter
    @Getter
    public static class ImportExcel {
        @ExcelField(title = "影城内码", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaInnerCode;
        @ExcelField(title = "影城名称", groups = 0, align = 2, sort = 2, width = 5000, fieldType = String.class)
        private String cinemaName;
        @ExcelField(title = "资源类型", groups = 0, align = 2, sort = 3, width = 5000, fieldType = String.class)
        private String resourceType;
        @ExcelField(title = "资源归属", groups = 0, align = 2, sort = 4, width = 5000, fieldType = String.class)
        private String resourceOwnership;
        @ExcelField(title = "资源编码", groups = 0, align = 2, sort = 5, width = 5000, fieldType = String.class)
        private String resourceCode;
        @ExcelField(title = "可用时段", groups = 0, align = 2, sort = 6, width = 5000, fieldType = String.class)
        private String availablePeriod;
        @ExcelField(title = "楼层", groups = 0, align = 2, sort = 7, width = 5000, fieldType = String.class)
        private String floor;
        @ExcelField(title = "位置描述", groups = 0, align = 2, sort = 8, width = 5000, fieldType = String.class)
        private String locationDesc;
        @ExcelField(title = "规划用途", groups = 0, align = 2, sort = 9, width = 5000, fieldType = String.class)
        private String planUse;
        @ExcelField(title = "落地方式", groups = 0, align = 2, sort = 10, width = 5000, fieldType = String.class)
        private String landingMode;
        @ExcelField(title = "可售面积", groups = 0, align = 2, sort = 11, width = 5000, fieldType = String.class)
        private String sellArea;
        @ExcelField(title = "层高", groups = 0, align = 2, sort = 12, width = 5000, fieldType = String.class)
        private String floorHeight;
        @ExcelField(title = "是否可拆分", groups = 0, align = 2, sort = 13, width = 5000, fieldType = String.class)
        private String isSplittable;
        @ExcelField(title = "上下水", groups = 0, align = 2, sort = 14, width = 5000, fieldType = String.class)
        private String waterSupply;
        @ExcelField(title = "强电", groups = 0, align = 2, sort = 15, width = 5000, fieldType = String.class)
        private String powerSupply;
        @ExcelField(title = "消防设施", groups = 0, align = 2, sort = 16, width = 5000, fieldType = String.class)
        private String fireFacilities;
        @ExcelField(title = "装修", groups = 0, align = 2, sort = 17, width = 5000, fieldType = String.class)
        private String decoration;
        @ExcelField(title = "错误信息", groups = 0, align = 2, sort = 18, width = 5000, fieldType = String.class)
        private String errorMsg;
    }
}
