package cmc.pad.resource.domain.budget;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.util.data.PageResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
abstract public class ResourceBudgetHallRepository extends JsdRepository<ResourceBudgetHall, Long> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    public abstract int batchInsert(List<ResourceBudgetHall> list);



    public abstract int update(ResourceBudgetHall hall);


    public abstract List<ResourceBudgetHall> getList(int pageSize, int page, LocalDateTime startTime, LocalDateTime endTime);

    public abstract PageResult<ResourceBudgetHall> getPageList(String year, String regionCode, String cinemaInnerCode, String resourceType, int pageSize, int pageIndex);
}
