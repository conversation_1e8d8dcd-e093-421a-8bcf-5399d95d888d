package cmc.pad.resource.domain.inventory.point;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.infrastructures.model.IdMapTable;
import cmc.pad.resource.infrastructures.model.ShardTable;
import mtime.lark.db.jsd.Transaction;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class PointLocationInventoryRepository extends JsdRepository<PointLocationInventory, Integer> {
    public abstract void insert(PointLocationInfo pointLocationInfo, LocalDate start, LocalDate end);

    public abstract void insert(PointLocationInfo pointLocationInfo, LocalDate start, LocalDate end, boolean isBatchInsert);

    public abstract List<PointLocationInventory> query(int pointLocationId, LocalDate start, LocalDate end);

    public abstract PointLocationInventory query(int pointLocationId, LocalDate date);

    public abstract boolean queryHaveOccupy(int pointLocationId);

    public abstract PointLocationInventory queryMinRemainderArea(int pointLocationId, LocalDate adjustDate);

    public abstract PointLocationInventory queryMaxDateInventory(int pointLocationId);

    public abstract PointLocationInventory queryMinDateInventory(int pointLocationId);

    public abstract PointLocationInventory queryUsableAreaInventory(int pointLocationId, LocalDate start, LocalDate end);

    public abstract void updateSellArea(int pointLocationId, float newSellArea, LocalDate adjustDate);

    public abstract void updateNotSellArea(Transaction tx, int pointLocationId, float occupySellArea, LocalDate start, LocalDate end);

    public abstract void revertNotSellArea(Transaction tx, int pointLocationId, LocalDate start, LocalDate end);

    public abstract void revertNotSellArea(int pointLocationId, LocalDate start, LocalDate end);

    public abstract Map<String, List<IdMapTable>> reduceIdHashTable(List<Integer> pointLocationIds);

    public abstract List<ShardTable> tableNodes();
}
