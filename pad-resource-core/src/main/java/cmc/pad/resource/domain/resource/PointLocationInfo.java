package cmc.pad.resource.domain.resource;

import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import cmc.tohdfs.sdk.annotation.DSFiled;
import com.google.common.base.Strings;
import lombok.*;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;
import mtime.lark.util.lang.*;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static cmc.pad.resource.domain.inventory.point.ProcessStatus.CREATE;

/**
 * Created by fuwei on 2022/1/12.
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@JsdTable(nameStyle = NameStyle.LOWER)
public class PointLocationInfo {
    @Id
    @DSFiled(order = 1)
    private Integer id;
    @DSFiled(order = 2)
    private String code;
    @DSFiled(order = 3)
    private String regionCode;
    @DSFiled(order = 4)
    private String cinemaInnerCode;
    @DSFiled(order = 5)
    private String businessTypeCode;
    @DSFiled(order = 6)
    private String resourceOwnershipCode;
    @DSFiled(order = 7)
    private String locationDesc;
    @DSFiled(order = 8)
    private String planUse;
    @DSFiled(order = 9)
    private String landingMode;
    @DSFiled(order = 10)
    private Float sellArea;
    @DSFiled(order = 11)
    private LocalDate sellAreaAdjustDate;
    @DSFiled(order = 12)
    private LocalDateTime createTime;
    @DSFiled(order = 13)
    private LocalDateTime updateTime;
    @DSFiled(order = 14)
    private String largeWardCode;
    @DSFiled(order = 15)
    private String availablePeriod; // 可用时段
    @DSFiled(order = 16)
    private String floor; // 楼层
    @DSFiled(order = 17)
    private BigDecimal floorHeight; // 层高
    @DSFiled(order = 18)
    private Integer isSplittable; // 是否可拆分
    @DSFiled(order = 19)
    private Integer waterSupply; // 上下水
    @DSFiled(order = 20)
    private Integer powerSupply; // 强电
    @DSFiled(order = 21)
    private Integer fireFacilities; // 消防设施
    @DSFiled(order = 22)
    private String decoration; // 装修

    private ProcessStatus inventoryStatus;

    public PointLocationInfo(String code, String largeWardCode, String regionCode, String cinemaInnerCode,
                             String businessTypeCode, String resourceOwnershipCode, String locationDesc,
                             String planUse, String landingMode, Float sellArea,
                             String availablePeriod, String floor, String floorHeight,
                             String isSplittable, String waterSupply, String powerSupply, String fireFacilities,
                             String decoration
    ) {
        this.setCode(code);
        this.largeWardCode = largeWardCode;
        this.regionCode = regionCode;
        this.cinemaInnerCode = cinemaInnerCode;
        this.businessTypeCode = businessTypeCode;
        this.resourceOwnershipCode = resourceOwnershipCode;
        this.locationDesc = locationDesc;
        this.planUse = planUse;
        this.landingMode = landingMode;
        this.sellArea = sellArea;
        LocalDateTime now = LocalDateTime.now();
        this.updateTime = now;
        this.createTime = now;
        inventoryStatus = CREATE;
        this.availablePeriod = availablePeriod;
        this.floor = floor;
        if (StringUtils.isNotEmpty(floorHeight))
            this.floorHeight = new BigDecimal(floorHeight);
        this.isSplittable = convertYesOrNo(isSplittable.trim());
        this.waterSupply = convertYesOrNo(waterSupply.trim());
        this.powerSupply = convertYesOrNo(powerSupply.trim());
        this.fireFacilities = convertYesOrNo(fireFacilities.trim());
        this.decoration = decoration;
    }

    public void setCode(String code) {
        this.code = code.toUpperCase();
    }

    private Integer convertYesOrNo(String text) {
        text = text.trim();
        if (Strings.isNullOrEmpty(text))
            return null;
        if ("是".equals(text))
            return YesOrNo.YES.value;
        if ("否".equals(text))
            return YesOrNo.NO.value;
        throw new FaultException("文本转枚举异常");
    }

    private String outYesOrNoText(Integer yesOrNo) {
        if (yesOrNo == null)
            return "";
        return YesOrNo.valueOf(yesOrNo).displayName();
    }

    public String getIsSplittableText() {
        return outYesOrNoText(isSplittable);
    }

    public String getWaterSupplyText() {
        return outYesOrNoText(waterSupply);
    }

    public String getPowerSupplyText() {
        return outYesOrNoText(powerSupply);
    }

    public String getFireFacilitiesText() {
        return outYesOrNoText(fireFacilities);
    }

    public String getFloorHeightText() {
        return this.floorHeight != null ? this.floorHeight.toString() : "";
    }

    public static final String PLI_ID = "id";
    public static final String PLI_CODE = "code";
    public static final String PLI_REGION_CODE = "region_code";
    public static final String PLI_LARGE_WARD_CODE = "large_ward_code";
    public static final String PLI_TABLE = "point_location_info";
    public static final String PLI_INVENTORY_STATUS = "inventory_status";
    public static final String PLI_CINEMA_INNER_CODE = "cinema_inner_code";
    public static final String PLI_BUSINESS_TYPE_CODE = "business_type_code";
    public static final String PLI_RESOURCE_OWNERSHIP_CODE = "resource_ownership_code";
    public static final String PLI_UPDATE_TIME = "update_time";

    public enum YesOrNo implements EnumValueSupport, EnumDisplayNameSupport {
        YES(1, "是"),
        NO(0, "否");
        private int value;
        private String displayName;

        YesOrNo(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        @Override
        public int value() {
            return this.value;
        }

        @Override
        public String displayName() {
            return this.displayName;
        }

        public static YesOrNo valueOf(int value) {
            return Enums.valueOf(YesOrNo.class, value);
        }
    }
}
