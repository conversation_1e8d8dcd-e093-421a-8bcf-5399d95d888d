package cmc.pad.resource.domain.discount;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class DiscountRule {
    private Integer id;
    private Integer comparisonSymbol;
    private Float min;
    private Float max;
    private Float factor;
    private Integer discountId;
}
