package cmc.pad.resource.domain.inventory;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class Inventory {
    private String cinemaCode;
    private LocalDate date;
    private Integer totalAdvertisingPointLeasableQuantity;
    private Float totalOuterAreaLeasableArea;
    private Float totalFixedPointLeasableArea;
    private Float totalMarketingPointLeasableArea;
    private Integer soldAdvertisingPointLeasableQuantity;
    private Float soldOuterAreaLeasableArea;
    private Float soldFixedPointLeasableArea;
    private Float soldMarketingPointLeasableArea;
    private LocalDateTime updateTime;
}
