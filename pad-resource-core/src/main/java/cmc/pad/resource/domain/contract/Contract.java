package cmc.pad.resource.domain.contract;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 多种经营合同基本信息实体类
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class Contract {
    /**
     * 合同ID
     */
    @Id
    private Long id;

    /**
     * 合同/申请单号
     */
    private String contractNo;

    /**
     * 单据类型（目前只保存：3-无合同多种经营收入，4-有合同多种经营）
     */
    private Integer contractType;

    /**
     * 所属区域编码
     */
    private String areaCode;

    /**
     * 所属区域名称
     */
    private String areaName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同金额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 累计已认领金额（元）
     */
    private BigDecimal claimedAmount;

    /**
     * 累计已认领保证金（元）
     */
    private BigDecimal claimedBondAmount;

    /**
     * 填报人
     */
    private String operatorName;

    /**
     * 填报人万信号
     */
    private String operatorWanXin;

    /**
     * 合同起始日期
     */
    private LocalDate contractStartDate;

    /**
     * 合同终止日期
     */
    private LocalDate contractEndDate;

    /**
     * 合同/申请单状态（0-未生效，1-正常，2-变更中，3-作废中，4-已作废，5-关闭中，6-已关闭）
     */
    private Integer contractState;

    /**
     * 合同/申请日期
     */
    private LocalDateTime operatorDate;

    /**
     * 最后修改日期
     */
    private LocalDateTime updateTime;

    /**
     * 合同收款计划
     */
    @Transient
    private List<ContractCollectionPlan> contractCollectionPlanList;
}