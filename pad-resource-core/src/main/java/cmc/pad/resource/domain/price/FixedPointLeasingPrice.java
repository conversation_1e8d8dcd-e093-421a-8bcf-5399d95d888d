package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 固定点位报价
 *
 * <AUTHOR>
 * @Date 2019/3/19 15:22
 * @Version 1.0
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class FixedPointLeasingPrice extends Price {
    private String cityLevel;
    private String cinemaLevel;
    private Integer unitPrice;

    public static final String T_FIXED_POINT_LEASING_PRICE = "fixed_point_leasing_price";
    public static final String C_CITY_LEVEL = "city_level";
    public static final String C_CINEMA_LEVEL = "cinema_level";
}
