package cmc.pad.resource.domain.importing;

import mtime.lark.util.lang.EnumDisplayNameSupport;
import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * <AUTHOR>
 */
public enum FileImportStatus implements EnumValueSupport, EnumDisplayNameSupport {
    UNDERWAY(1, "上传中"),
    SUCCEEDED(2, "上传成功"),
    FAILED(3, "上传失败"),
    OBSOLETE(4, "已作废");


    private int value;
    private String displayName;

    private FileImportStatus(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }

    public static FileImportStatus valueOf(int value) {
        return Enums.valueOf(FileImportStatus.class, value);
    }
}
