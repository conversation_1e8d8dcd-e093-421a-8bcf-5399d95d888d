package cmc.pad.resource.domain.inventory.point;

import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * Created by fuyuanpu on 2022/5/20.
 */
public enum ContractStatus implements EnumValueSupport {
    SUBMIT(1),
    APPROVAL(2),
    CANCEL(3);

    private final int value;

    ContractStatus(int value) {
        this.value = value;
    }

    @Override
    public int value() {
        return value;
    }

    public static ContractType valueOf(int value) {
        return Enums.valueOf(ContractType.class, value);
    }
}