package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 营销点位报价
 *
 * <AUTHOR>
 * @Date 2019/3/20 16:54
 * @Version 1.0
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class MarketingPointLeasingPrice extends Price {
    private String cityLevel;
    private String cinemaLevel;
    private Integer unitPriceByArea;
    private Integer unitPriceByQuantity;

    public static final String T_MARKETING_POINT_LEASING_PRICE = "marketing_point_leasing_price";
    public static final String C_CITY_LEVEL = "city_level";
    public static final String C_CINEMA_LEVEL = "cinema_level";
}
