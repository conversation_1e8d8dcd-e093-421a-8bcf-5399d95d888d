package cmc.pad.resource.domain.budget;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.util.data.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 */
abstract public class ResourceBudgetHallChangeRecordRepository extends JsdRepository<ResourceBudgetHallChangeRecord, Long> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    public abstract int batchInsert(List<ResourceBudgetHallChangeRecord> list);

    public abstract List<ResourceBudgetHallChangeRecord> getList(String year, String regionCode, String cinemaInnerCode, String resourceType);
}
