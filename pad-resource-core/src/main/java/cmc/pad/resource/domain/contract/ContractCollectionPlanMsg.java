package cmc.pad.resource.domain.contract;

import lombok.Getter;
import lombok.Setter;

/**
 * 多种经营合同收款计划实体类
 */
@Getter
@Setter
public class ContractCollectionPlanMsg {

    /**
     * 合同/申请单号
     */
    private String contractNo;

    /**
     * 收款计划条件
     */
    private String condition;

    /**
     * 收款类型
     * 1:保证金
     * 2:合同款
     */
    private Integer type;

    /**
     * 计划收款日期yyyy-MM-dd
     */
    private String planCollectionDate;

    /**
     * 计划收款金额（元）
     */
    private String planCollectionAmount;
    
}