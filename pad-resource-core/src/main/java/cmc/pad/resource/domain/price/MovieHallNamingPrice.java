package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 影厅冠名报价
 * 按年计价
 *
 * <AUTHOR>
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class MovieHallNamingPrice extends Price {
    private String cinemaCode;
    private String movieHallType;
    private Integer unitPrice;
    public static final String T_MOVIE_HALL_NAMING_PRICE = "movie_hall_naming_price";
    public static final String C_CINEMA_CODE = "cinema_code";
    public static final String C_MOVIE_HALL_TYPE = "movie_hall_type";

}
