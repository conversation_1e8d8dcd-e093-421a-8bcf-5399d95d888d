package cmc.pad.resource.domain.city;

import cmc.pad.resource.application.query.data.ResultList;
import cmc.pad.resource.common.repository.jsd.JsdRepository;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class CityDistrictRepository extends JsdRepository<CityDistrict, String> {

    public abstract void batchInsert(List<CityDistrict> list);

    public abstract void updateLevelByCode(String code, String level);

    public abstract ResultList<CityDistrictListModel> find(String regionCode, String superiorCode,
                                                           String cityCode, String districtCode,
                                                           String districtName, String districtLevel,
                                                           int pageIndex, int pageSize);
}
