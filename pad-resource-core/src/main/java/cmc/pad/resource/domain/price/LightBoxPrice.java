package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 灯箱报价
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class LightBoxPrice extends Price {
    private String cityLevel;
    private String cinemaLevel;
    private Integer minArea;
    private Integer minTotalPrice;
    private Integer expandedUnitPrice;

    public static final String T_LIGHT_BOX_PRICE = "light_box_price";
    public static final String C_CITY_LEVEL = "city_level";
    public static final String C_CINEMA_LEVEL = "cinema_level";
}
