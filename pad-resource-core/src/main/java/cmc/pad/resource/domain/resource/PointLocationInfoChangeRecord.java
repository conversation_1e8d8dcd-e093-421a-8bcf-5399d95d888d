package cmc.pad.resource.domain.resource;

import lombok.*;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Created by fuwei on 2022/1/12.
 */
@Getter
@Setter
@NoArgsConstructor
@JsdTable(nameStyle = NameStyle.LOWER)
public class PointLocationInfoChangeRecord {
    @Id
    private Integer id;
    private Integer pointLocationId;
    private String code;
    private String largeWardCode;
    private String regionCode;
    private String cinemaInnerCode;
    private String businessTypeCode;
    private String resourceOwnershipCode;
    private String locationDesc;
    private String planUse;
    private String landingMode;
    private Float sellArea;
    private LocalDate sellAreaAdjustDate;
    private String availablePeriod; // 可用时段
    private String floor; // 楼层
    private BigDecimal floorHeight; // 层高
    private PointLocationInfo.YesOrNo isSplittable; // 是否可拆分
    private PointLocationInfo.YesOrNo waterSupply; // 上下水
    private PointLocationInfo.YesOrNo powerSupply; // 强电
    private PointLocationInfo.YesOrNo fireFacilities; // 消防设施
    private String decoration; // 装修
    private LocalDateTime createTime;
    private String updater;

    public PointLocationInfoChangeRecord(Integer pointLocationId, String code, String regionCode,
                                         String cinemaInnerCode, String businessTypeCode, String resourceOwnershipCode,
                                         String locationDesc, String planUse, String landingMode,
                                         Float sellArea, String updater,
                                         String availablePeriod, String floor, BigDecimal floorHeight,
                                         PointLocationInfo.YesOrNo isSplittable, PointLocationInfo.YesOrNo waterSupply,
                                         PointLocationInfo.YesOrNo powerSupply, PointLocationInfo.YesOrNo fireFacilities,
                                         String decoration
    ) {
        this.pointLocationId = pointLocationId;
        this.code = code;
        this.regionCode = regionCode;
        this.cinemaInnerCode = cinemaInnerCode;
        this.businessTypeCode = businessTypeCode;
        this.resourceOwnershipCode = resourceOwnershipCode;
        this.locationDesc = locationDesc;
        this.planUse = planUse;
        this.landingMode = landingMode;
        this.sellArea = sellArea;
        this.createTime = LocalDateTime.now();
        this.updater = updater;
        this.availablePeriod = availablePeriod;
        this.floor = floor;
        this.floorHeight = floorHeight;
        this.isSplittable = isSplittable;
        this.waterSupply = waterSupply;
        this.powerSupply = powerSupply;
        this.fireFacilities = fireFacilities;
        this.decoration = decoration;
    }
}
