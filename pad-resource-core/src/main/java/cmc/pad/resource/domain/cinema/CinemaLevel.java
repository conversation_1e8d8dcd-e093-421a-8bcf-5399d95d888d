package cmc.pad.resource.domain.cinema;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class CinemaLevel {
    //影城内码
    private String innerCode;
    //影城级别
    private String level;
    //批次号
    private Integer importId;

    public static final String T_CINEMA_LEVEL = "cinema_level";
    public static final String C_INNER_CODE = "inner_code";
    public static final String C_IMPORT_ID = "import_id";
    public static final String C_LEVEL = "level";
}
