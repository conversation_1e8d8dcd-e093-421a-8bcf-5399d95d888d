package cmc.pad.resource.domain.city;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
@AllArgsConstructor
@NoArgsConstructor
public class CityDistrict {
    private String code;
    private String name;
    private String cityCode;
    private String level;
    private LocalDateTime syncTime;

    public static final String T_CITY_DISTRICT = "city_district";
    public static final String C_NAME = "name";
    public static final String C_CODE = "code";
    public static final String C_CITY_CODE = "city_code";
    public static final String C_CITY_DISTRICT_LEVEL = "level";
}
