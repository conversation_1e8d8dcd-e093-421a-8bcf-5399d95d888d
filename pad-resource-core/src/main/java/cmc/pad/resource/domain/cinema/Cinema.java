package cmc.pad.resource.domain.cinema;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class Cinema {
    private String code;
    private String name;
    private String regionCode;
    private String regionName;
    private String cityCode;
    private String cityDistrictCode;
    private LocalDateTime syncTime;

    public static final String T_CINEMA = "cinema";
    public static final String C_NAME = "name";
    public static final String C_CODE = "code";
    public static final String C_CITY_CODE = "city_code";
    public static final String C_CITY_DISTRICT_CODE = "city_district_code";
    public static final String C_REGION_NAME = "region_name";
}
