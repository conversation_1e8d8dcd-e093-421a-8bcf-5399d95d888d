package cmc.pad.resource.domain.resource;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class ResourceChangeRecord {
    private String id;
    private String cinemaCode;
    private Float marketingPointLeasableArea;
    private Float outerAreaLeasableArea;
    private Float fixedPointLeasableArea;
    private Integer advertisingPointLeasableQuantity;
    private String updator;
    private LocalDateTime updateTime;
}
