package cmc.pad.resource.domain.city;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class City {
    private String code;
    private String name;
    private String regionName;
    private String regionCode;
    private String superiorName;
    private String superiorCode;
    private String cityLevel;
    private LocalDateTime syncTime;

    public static final String T_CITY = "city";
    public static final String C_NAME = "name";
    public static final String C_CODE = "code";
    public static final String C_SUPERIOR_CODE = "superior_code";
    public static final String C_SUPERIOR_NAME = "superior_name";
    public static final String C_REGION_CODE = "region_code";
    public static final String C_REGION_NAME = "region_name";
    public static final String C_CITY_LEVEL = "city_level";
}
