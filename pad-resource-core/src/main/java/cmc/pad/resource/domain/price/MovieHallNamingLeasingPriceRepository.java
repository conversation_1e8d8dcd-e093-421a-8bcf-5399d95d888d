package cmc.pad.resource.domain.price;

import cmc.pad.resource.common.repository.jsd.JsdRepository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:17
 * @Version 1.0
 */
abstract public class MovieHallNamingLeasingPriceRepository extends JsdRepository<MovieHallNamingPrice, Integer> {
    public abstract void batchInsert(List<MovieHallNamingPrice> list);

    public abstract LocalDate latestEffectiveDate();
}
