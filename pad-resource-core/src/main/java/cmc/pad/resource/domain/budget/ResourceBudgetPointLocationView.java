package cmc.pad.resource.domain.budget;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2022/1/12 15:16
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class ResourceBudgetPointLocationView extends ResourceBudgetPointLocation{
    private String resourceType;
    /**
     * 资源归属
     */
    private String resourceAffiliation;

    /**
     * 位置描述
     */
    private String locationDescription;
    /**
     * 规划用途
     */
    private String usePlan;
    /**
     * 落地方式
     */
    private String landMode;
    /**
     * 可售面积
     */
    private Float areaSize;
}
