package cmc.pad.resource.domain.inventory.point;

import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * Created by fuwei on 2022/3/26.
 */
public enum AlterStatus implements EnumValueSupport {

    OCCUPY(1, "占用"),
    ADD(2, "追加"),
    UPDATE(3, "变更"),
    DESTROY(4, "作废");

    private final int value;
    private final String name;

    AlterStatus(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public int value() {
        return value;
    }

    public static AlterStatus valueOf(int value) {
        return Enums.valueOf(AlterStatus.class, value);
    }
}
