package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 影厅租赁报价
 *
 * <AUTHOR>
 * @Date 2019/3/20 11:24
 * @Version 1.0
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class MovieHallSeatLeasingPrice extends Price {
    private String cityLevel;
    private String cinemaLevel;
    private String movieHallType;
    private Integer minHours;
    private Integer minTotalPrice;
    private Integer expandedUnitPrice;

    public static final String T_MOVIE_HALL_SEAT_LEASING_PRICE = "movie_hall_seat_leasing_price";
    public static final String C_CITY_LEVEL = "city_level";
    public static final String C_CINEMA_LEVEL = "cinema_level";
    public static final String C_MOVIE_HALL_TYPE = "movie_hall_type";
}
