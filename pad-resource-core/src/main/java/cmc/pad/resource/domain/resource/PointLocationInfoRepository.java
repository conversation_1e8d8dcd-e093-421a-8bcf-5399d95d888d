package cmc.pad.resource.domain.resource;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import mtime.lark.db.jsd.BasicFilter;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Transaction;

import java.util.List;

import static cmc.pad.resource.domain.resource.PointLocationInfo.PLI_CINEMA_INNER_CODE;
import static cmc.pad.resource.domain.resource.PointLocationInfo.PLI_CODE;

/**
 * Created by fuwei on 2022/1/12.
 */
public abstract class PointLocationInfoRepository extends JsdRepository<PointLocationInfo, Integer> {
    public BasicFilter getFilter(String code, String cinemaInnerCode) {
        return Filter.create(PLI_CODE, code).add(PLI_CINEMA_INNER_CODE, cinemaInnerCode);
    }

    public abstract void batchInsert(List<PointLocationInfo> list);

    public abstract int saveReturnId(PointLocationInfo pointLocationInfo);

    public abstract List<PointLocationInfo> batchQuery(List<PointLocationModel.BatchQueryParam> paramList);

    public abstract void updateInventoryStatus(int id, ProcessStatus processStatus);

    public abstract void batchUpdateInventoryProcessStatus(Transaction tx, List<Integer> pointLocationIds, ProcessStatus processStatus);

    public abstract void batchUpdateInventoryProcessStatus(List<Integer> pointLocationIds, ProcessStatus processStatus);

    public abstract void updateLargeWard(int pointLocationId, String largeWardCode);

    public abstract List<String> queryAllCinemaInnerCode();

    public abstract List<String> queryAllRegionCode();

    public abstract void updateRegionCode(String regionCode, String cinemaInnerCode);

    public abstract void updateLargeWardCode(String largeWardCode, String regionCode);
}