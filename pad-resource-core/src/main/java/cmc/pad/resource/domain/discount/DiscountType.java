package cmc.pad.resource.domain.discount;

import mtime.lark.util.lang.EnumDisplayNameSupport;
import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * <AUTHOR>
 */
public enum DiscountType implements EnumValueSupport, EnumDisplayNameSupport {
    DURATION(1, "时长折扣"),
    AREA(2, "面积折扣");
    private int value;
    private String displayName;

    DiscountType(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }

    public static DiscountType valueOf(int value) {
        return Enums.valueOf(DiscountType.class, value);
    }
}
