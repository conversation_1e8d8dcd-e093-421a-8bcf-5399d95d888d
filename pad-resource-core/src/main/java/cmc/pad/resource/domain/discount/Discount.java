package cmc.pad.resource.domain.discount;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class Discount {
    private Integer id;
    private String businessType;
    private DiscountType discountType;
    private String creator;
    private LocalDateTime createTime;
    private String updator;
    private LocalDateTime updateTime;
}
