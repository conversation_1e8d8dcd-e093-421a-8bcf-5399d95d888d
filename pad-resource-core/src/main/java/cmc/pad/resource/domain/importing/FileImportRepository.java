package cmc.pad.resource.domain.importing;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.util.data.PageResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
abstract public class FileImportRepository extends JsdRepository<FileImport, Integer> {
    /**
     * 保存文件
     *
     * @param file 文件信息
     * @return 记录ID
     */
    abstract public int saveGenerateId(FileImport file);

    /**
     * 更新文件状态,结束时间
     *
     * @param id     记录ID
     * @param status 状态
     * @return
     */
    abstract public boolean updateStatus(int id, int status, LocalDateTime endTime);

    /**
     * 更新文件状态
     *
     * @param id     记录ID
     * @param status 状态
     * @return
     */
    abstract public boolean updateStatus(int id, int status);

    abstract public PageResult<FileImport> findPage(int category, int pageIdx, int pageSize);

    public abstract List<FileImport> list(FileCategory category);
}
