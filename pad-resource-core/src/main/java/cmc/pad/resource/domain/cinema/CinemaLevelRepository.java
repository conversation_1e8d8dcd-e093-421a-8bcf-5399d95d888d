package cmc.pad.resource.domain.cinema;

import cmc.pad.resource.common.repository.jsd.JsdRepository;

import java.util.List;

/**
 * <AUTHOR>
 */
abstract public class CinemaLevelRepository extends JsdRepository<CinemaLevel, String> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    public abstract int batchInsert(List<CinemaLevel> list);

    /**
     * 获取导入ID列表
     *
     * @return
     */
    public abstract List<Integer> getAllOldImportId(int newImportId);

    /**
     * 删除所有记录
     *
     * @return
     */
    public abstract void deleteByImportId(int importId);

}
