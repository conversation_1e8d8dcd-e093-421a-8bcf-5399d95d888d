package cmc.pad.resource.domain.resource;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.db.jsd.Transaction;

/**
 * <AUTHOR>
 */
public abstract class CinemaResourceRepository extends JsdRepository<CinemaResource, String> {

    public abstract int updateCinemaResourceByCode(Transaction tx, String cinemaCode, Float marketingPointLeasableArea, Float outerAreaLeasableArea, Float fixedPointLeasableArea, Integer advertisingPointLeasableQuantity);

    public abstract boolean insertCinemaResource(Transaction tx, CinemaResource cinemaResource);
}
