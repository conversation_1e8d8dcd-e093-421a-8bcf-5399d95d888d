package cmc.pad.resource.domain.importing;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@Table(name = "file_import_record")
public class FileImport {
    //记录ID
    @Id
    private Integer id;
    //文件ID
    private String fileId;
    //文件名
    private String fileName;
    //文件类型
    private FileCategory fileCategory;
    //版本
    private String version;
    //生效日期
    private LocalDate effectiveDate;
    //上传状态
    private FileImportStatus status;
    //上传时间
    private LocalDateTime beginTime;
    //上传完成时间
    private LocalDateTime endTime;
    //上传人ID
    private Integer importer;
    //备注信息
    private String remark;
    private String largeWardCode;
    private String regionCode;
    private String cinemaInnerCode;

    public void importStartd() {
        this.beginTime = LocalDateTime.now();
        this.endTime = this.beginTime;
        this.status = FileImportStatus.UNDERWAY;
    }

    public void importSucceeded() {
        this.status = FileImportStatus.SUCCEEDED;
        this.endTime = LocalDateTime.now();
    }

    public void importFailed() {
        this.status = FileImportStatus.FAILED;
        this.endTime = LocalDateTime.now();
    }

    public void importObsolete() {
        this.status = FileImportStatus.OBSOLETE;
    }

    public static final String T_FILE_IMPORT_RECORD = "file_import_record";
    public static final String C_ID = "id";
    public static final String C_STATUS = "status";
    public static final String C_BEGIN_TIME = "begin_time";
    public static final String C_END_TIME = "end_time";
    public static final String C_EFFECTIVE_DATE = "effective_date";
    public static final String C_FILE_CATEGORY = "file_category";
    public static final String C_LARGE_WARD_CODE = "large_ward_code";
    public static final String C_REGION_CODE = "region_code";
    public static final String C_CINEMA_INNER_CODE = "cinema_inner_code";
}
