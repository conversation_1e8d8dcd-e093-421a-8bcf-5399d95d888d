package cmc.pad.resource.domain.contract;

import lombok.Data;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;
import mtime.lark.util.lang.*;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@JsdTable(nameStyle = NameStyle.LOWER)
public class DeContractCollectionPlan {
    /**
     * 主键ID
     */
    @Id
    private Long id;

    /**
     * 合同/申请单号
     */
    private String contractNo;

    /**
     * 单据类型 （目前只保存：3-无合同多种经营收入，4-有合同多种经营）
     */
    private Integer contractType;

    /**
     * 大区代码
     */
    private String largeWardCode;

    /**
     * 区域代码
     */
    private String areaCode;

    /**
     * 所属区域
     */
    private String areaName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同金额(元)
     */
    private BigDecimal totalAmount;

    /**
     * 收款计划
     */
    private String condition;

    /**
     * 收款类型
     */
    private Integer type;

    /**
     * 计划收款日期
     */
    private LocalDate planCollectionDate;

    /**
     * 计划收款金额(元)
     */
    private BigDecimal planCollectionAmount;

    /**
     * 累计应收金额(元)
     */
    private BigDecimal accumulatedReceivableAmount;

    /**
     * 累计已认领金额(元)
     */
    private BigDecimal claimedAmount;

    /**
     * 累计欠缴金额(元)
     */
    private BigDecimal accumulatedUnpaidAmount;

    /**
     * 逾期状态 1:正常 2:逾期
     */
    private Integer overdueState;

    /**
     * 填报人
     */
    private String operatorName;

    /**
     * 填报人万信
     */
    private String operatorWanXin;

    /**
     * 合同起始日期
     */
    private LocalDate contractStartDate;

    /**
     * 合同终止日期
     */
    private LocalDate contractEndDate;

    /**
     * 合同/申请单状态（0-未生效，1-正常，2-变更中，3-作废中，4-已作废，5-关闭中，6-已关闭）
     */
    private Integer contractState;

    /**
     * 合同/申请日期
     */
    private LocalDateTime operatorDate;

    /**
     * 最后修改日期
     */
    private LocalDateTime updateTime;

    /**
     * 收款类型
     */
    public enum CollectType implements EnumValueSupport, EnumDisplayNameSupport {
        BOND_AMOUNT(1, "保证金"),
        AMOUNT(2, "合同款");
        private int value;
        private String displayName;

        CollectType(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        @Override
        public int value() {
            return this.value;
        }

        @Override
        public String displayName() {
            return this.displayName;
        }

        public static CollectType valueOf(int value) {
            return Enums.valueOf(CollectType.class, value);
        }
    }

    public enum OverdueState implements EnumValueSupport, EnumDisplayNameSupport {
        NORMAL(1, "正常"),
        OVERDUE(2, "已逾期");

        private int value;
        private String displayName;

        OverdueState(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        @Override
        public int value() {
            return this.value;
        }

        @Override
        public String displayName() {
            return this.displayName;
        }

        public static OverdueState valueOf(int value) {
            return Enums.valueOf(OverdueState.class, value);
        }
    }

    public enum ContractState implements EnumValueSupport, EnumDisplayNameSupport {
        NOT_EFFECTIVE(0, "未生效"),
        NORMAL(1, "正常"),
        CHANGING(2, "变更中"),
        BEING_CANCELLED(3, "作废中"),
        CANCELLED(4, "已作废"),
        CLOSING(5, "关闭中"),
        CLOSED(6, "已关闭");
        private int value;
        private String displayName;

        private ContractState(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        @Override
        public int value() {
            return this.value;
        }

        @Override
        public String displayName() {
            return this.displayName;
        }

        public static ContractState valueOf(int value) {
            return Enums.valueOf(ContractState.class, value);
        }
    }

    public enum ContractType implements EnumValueSupport, EnumDisplayNameSupport {
        APPLY_ORDER(3, "多种经营收入申请单"),
        CONTRACT(4, "多种经营收入合同");
        private int value;
        private String displayName;

        private ContractType(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        @Override
        public int value() {
            return this.value;
        }

        @Override
        public String displayName() {
            return this.displayName;
        }

        public static ContractType valueOf(int value) {
            return Enums.valueOf(ContractType.class, value);
        }
    }

    public static final String TABLE = "de_contract_collection_plan";
}