package cmc.pad.resource.domain.inventory.point;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by fuyuanpu on 2022/2/26.
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@NoArgsConstructor
public class PointLocationOccupationContract {
    @Id
    private String contractNo;
    private ContractStatus status;
    private ProcessStatus processStatus;
    private LocalDateTime updateTime;
    private Integer version;
    private ContractType contractType;//是否是提交的变更合同
    @Transient
    private List<PointLocationOccupationContractDetail> occupationDetailList;

    public PointLocationOccupationContract(String contractNo, ContractStatus contractStatus, List<PointLocationOccupationContractDetail> occupationDetailList) {
        this.contractNo = contractNo;
        this.status = contractStatus;
        this.processStatus = ProcessStatus.CREATE;
        this.updateTime = LocalDateTime.now();
        this.contractType = ContractType.NEW_CONTRACT;
        this.version = 1;
        occupationDetailList.forEach(occupation -> occupation.setVersion(this.version));
        this.occupationDetailList = occupationDetailList;
    }

    public static final String TABLE = "point_location_occupation_contract";
    public static final String PLO_C_CONTRACT_NO = "contract_no";
}