package cmc.pad.resource.domain.inventory;

import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * <AUTHOR>
 */
public enum OccupationStatus implements EnumValueSupport {

    ACTIVE(1),
    CANCEL(2);

    private final int value;

    OccupationStatus(int value) {
        this.value = value;
    }

    @Override
    public int value() {
        return value;
    }

    public static OccupationStatus valueOf(int value) {
        return Enums.valueOf(OccupationStatus.class, value);
    }
}
