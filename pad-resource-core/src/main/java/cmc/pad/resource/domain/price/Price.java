package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Id;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:22
 * @Version 1.0
 */
@Setter
@Getter
public abstract class Price {
    //逻辑ID
    @Id
    private Integer id;
    //导入编号
    private Integer importId;
    //更新人ID
    private Integer updater;
    //更新日期
    private LocalDateTime updateTime;
    //生效日期
    private LocalDate effectiveDate;

    public static final String C_ID = "id";
    public static final String C_EFFECTIVE_DATE = "effective_date";
    public static final String C_UPDATE_TIME = "update_time";
    public static final String C_IMPORT_ID = "import_id";
}
