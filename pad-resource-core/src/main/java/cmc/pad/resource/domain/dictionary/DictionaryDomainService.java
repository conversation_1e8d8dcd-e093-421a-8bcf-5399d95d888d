package cmc.pad.resource.domain.dictionary;

import cmc.location.front.service.dto.CityDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/4/2 14:39
 * @Version 1.0
 */
public interface DictionaryDomainService {

    List<DictEntry.BusinessType> allBusinessTypes();

    List<String> allMovieHallTypeCodes();

    Map<String, String> allMovieHallTypeDict();

    List<DictEntry.MovieHallType> allMovieHallTypes();

    /**
     * 目前对应城市地区级别
     * @return
     */
    List<String> allCityLevelCodes();

    /**
     * 目前对应城市地区级别
     * @return
     */
    Map<String, String> allCityLevelDict();

    /**
     * 目前对应城市地区级别
     * @return
     */
    List<DictEntry.CityLevel> allCityLevel();

    List<String> allCinemaLevelCodes();

    Map<String, String> allCinemaLevelDict();

    List<DictEntry.CinemaLevel> allCinemaLevel();

    List<CityDto.City> allCity();

    Map<String, String> findDictMap(String dictType);
}
