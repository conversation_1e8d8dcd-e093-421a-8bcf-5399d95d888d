package cmc.pad.resource.domain.inventory.point;

import mtime.lark.util.lang.EnumValueSupport;
import mtime.lark.util.lang.Enums;

/**
 * Created by fuyuanpu on 2022/5/11.
 */
public enum ContractType implements EnumValueSupport {
    NEW_CONTRACT(1),
    ALTER_CONTRACT(2);

    private final int value;

    ContractType(int value) {
        this.value = value;
    }

    @Override
    public int value() {
        return value;
    }

    public static ContractType valueOf(int value) {
        return Enums.valueOf(ContractType.class, value);
    }
}