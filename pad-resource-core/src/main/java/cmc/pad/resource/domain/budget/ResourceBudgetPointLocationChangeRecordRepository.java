package cmc.pad.resource.domain.budget;

import cmc.pad.resource.common.repository.jsd.JsdRepository;

import java.util.List;

/**
 * <AUTHOR>
 */
abstract public class ResourceBudgetPointLocationChangeRecordRepository extends JsdRepository<ResourceBudgetPointLocationChangeRecord, Long> {

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    public abstract int batchInsert(List<ResourceBudgetPointLocationChangeRecord> list);

    public abstract List<ResourceBudgetPointLocationChangeRecordView> getList(String year, String regionCode, String cinemaInnerCode, String resourceCode);
}
