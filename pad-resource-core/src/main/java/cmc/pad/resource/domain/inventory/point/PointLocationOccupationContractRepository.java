package cmc.pad.resource.domain.inventory.point;

import cmc.pad.resource.common.repository.jsd.JsdRepository;
import mtime.lark.db.jsd.Transaction;

/**
 * Created by fuyuanpu on 2022/2/26.
 */
public abstract class PointLocationOccupationContractRepository extends JsdRepository<PointLocationOccupationContract, String> {
    public abstract PointLocationOccupationContract query(String contractNo);

    public abstract PointLocationOccupationContract query(String contractNo, boolean isIncludeContractDetails);

    public abstract void update(Transaction tx, String contractNo, ContractStatus contractStatus, ProcessStatus status);

    public abstract void update(String contractNo, ProcessStatus status);

    public abstract void update(Transaction tx, String contractNo, ProcessStatus status);

    public abstract void reOccupyUpdate(Transaction tx, PointLocationOccupationContract contractBaseInfo);

    public abstract void cancel(Transaction tx, PointLocationOccupationContract contractBaseInfo);

    public abstract void active(PointLocationOccupationContract contractBaseInfo, ProcessStatus processStatus);

    public abstract void active(Transaction tx, PointLocationOccupationContract contractBaseInfo, ProcessStatus processStatus);

//    public abstract void rollback(Transaction tx, PointLocationOccupationContract contractBaseInfo);

    public abstract void insert(PointLocationOccupationContract contractBaseInfo);

    public abstract void insert(Transaction tx, PointLocationOccupationContract contractBaseInfo);

}