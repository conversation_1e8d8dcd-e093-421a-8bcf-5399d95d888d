package cmc.pad.resource.domain.price;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * 宣传点位报价
 *
 * <AUTHOR>
 * @Date 2019/3/20 18:11
 * @Version 1.0
 */
@Setter
@Getter
@JsdTable(nameStyle = NameStyle.LOWER)
public class AdvertisingPointLeasingPrice extends Price {
    private String cityLevel;
    private String cinemaLevel;
    private Integer minArea;
    private Integer minTotalPrice;
    private Integer expandedUnitPrice;

    public static final String T_ADVERTISING_POINT_LEASING_PRICE = "advertising_point_leasing_price";
    public static final String C_CITY_LEVEL = "city_level";
    public static final String C_CINEMA_LEVEL = "cinema_level";
}
