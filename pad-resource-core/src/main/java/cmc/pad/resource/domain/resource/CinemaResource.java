package cmc.pad.resource.domain.resource;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
public class CinemaResource {
    private String cinemaCode;
    private Float marketingPointLeasableArea;
    private Float outerAreaLeasableArea;
    private Float fixedPointLeasableArea;
    private Integer advertisingPointLeasableQuantity;
}
