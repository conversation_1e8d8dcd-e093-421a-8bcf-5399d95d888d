package cmc.pad.resource.adapter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import mx.common.file.service.dto.FileDto;
import mx.common.file.service.iface.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileServiceAdapter {
    private static final String PUBLIC = "public";
    private static final int DEFAULT_TYPE = 0;
    private final FileService fileService;

    public byte[] downloadFile(String fileId) {
        log.info(">>>开始下载文件 fileId:{}", fileId);
        try {
            FileDto.DownloadFileRequest downloadFileRequest = new FileDto.DownloadFileRequest();
            downloadFileRequest.setFileId(fileId);
            downloadFileRequest.setStorageType(PUBLIC);
            byte[] fileContent = fileService.downloadFile(downloadFileRequest).getFileContent();
            log.info(">>>完成下载文件 fileId:{}", fileId);
            return fileContent;
        } catch (Exception e) {
            log.error(">>> 下载上传的文件异常", e);
            if (e.getMessage().contains("NoSuchKey")) {
                throw new FaultException("上传的文件过期, 请重新上传文件");
            }
            throw new FaultException("从文件服务获取上传文件异常, 请稍后重试");
        }
    }

    public String uploadFile(byte[] file, String fileName) {
        log.info(">>>开始上传文件, 文件名:{}", fileName);
        FileDto.UploadFileRequest uploadFileRequest = new FileDto.UploadFileRequest();
        uploadFileRequest.setFileName(fileName);
        uploadFileRequest.setStorageType(PUBLIC);
        uploadFileRequest.setFileContent(file);
        uploadFileRequest.setFileCategory(DEFAULT_TYPE);
        FileDto.UploadFileResponse response = fileService.uploadFile(uploadFileRequest);
        if (!response.isSuccess()) {
            throw new FaultException("上传文件失败");
        }
        log.info(">>>完成上传文件, 文件名:{} fileId:{}", fileName, response.getFileId());
        return response.getFileId();
    }

    public boolean deleteFile(String fileId) {
        log.info(">>>开始删除文件, id:{}", fileId);
        FileDto.RemoveFileRequest removeFileRequest = new FileDto.RemoveFileRequest();
        removeFileRequest.setFileId(fileId);
        removeFileRequest.setStorageType(PUBLIC);
        FileDto.RemoveFileResponse response = fileService.removeFile(removeFileRequest);
        log.info(">>>完成删除文件, id:{}, response:{}", fileId, response.isSuccess());
        return response.isSuccess();
    }

}
