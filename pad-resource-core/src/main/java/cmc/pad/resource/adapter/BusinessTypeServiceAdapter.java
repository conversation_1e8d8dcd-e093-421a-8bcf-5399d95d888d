package cmc.pad.resource.adapter;

import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static cmc.pad.resource.infrastructures.service.dictionary.DictionaryDomainServiceAdapter.BUSINESS_TYPE_DICT_TYPE;

/**
 * Created by fuwei on 2022/3/31.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusinessTypeServiceAdapter {
    private final DictionaryDomainService dictionaryService;

    public Map<String, String> pointLocationBusinessTypeMap() {
        Map<String, String> businessTypeMap = dictionaryService.findDictMap(BUSINESS_TYPE_DICT_TYPE);
        businessTypeMap.remove("YT");
        businessTypeMap.remove("GMT");
        return businessTypeMap;
    }
}