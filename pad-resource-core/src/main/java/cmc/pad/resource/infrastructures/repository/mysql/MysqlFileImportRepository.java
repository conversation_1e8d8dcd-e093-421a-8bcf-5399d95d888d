package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.importing.FileImportRepository;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SimpleResult;
import mtime.lark.util.data.PageResult;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static mtime.lark.db.jsd.Shortcut.*;

@Repository
public class MysqlFileImportRepository extends FileImportRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public int saveGenerateId(FileImport file) {
        Database database = getDatabase();
        List<Object> keys = database.insert(file).result(true).getKeys();
        return ((Long) keys.get(0)).intValue();
    }

    @Override
    public boolean updateStatus(int id, int status, LocalDateTime endTime) {
        return update(id, status, endTime);
    }

    @Override
    public boolean updateStatus(int id, int status) {
        return update(id, status, null);
    }

    @Override
    public PageResult<FileImport> findPage(int category, int pageIdx, int pageSize) {
        return findPage(f(FileImport.C_FILE_CATEGORY, category), pageIdx, pageSize);
    }

    @Override
    public List<FileImport> list(FileCategory category) {
        return findMany(f(FileImport.C_FILE_CATEGORY, category), s(SortType.DESC, FileImport.C_BEGIN_TIME));
    }

    private boolean update(int id, int status, LocalDateTime endTime) {
        Database database = getDatabase();
        UpdateValues values = uv(FileImport.C_STATUS, status);
        if (Objects.nonNull(endTime)) {
            values.add(FileImport.C_END_TIME, endTime);
        }
        Filter f = f(FileImport.C_ID, id);
        SimpleResult result = database.update(FileImport.T_FILE_IMPORT_RECORD).set(values).where(f).result();
        return result.getAffectedRows() == 1;
    }
}
