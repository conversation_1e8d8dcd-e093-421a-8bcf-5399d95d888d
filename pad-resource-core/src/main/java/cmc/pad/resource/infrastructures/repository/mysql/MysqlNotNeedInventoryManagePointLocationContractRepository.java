package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.point.NotNeedInventoryManagePointLocationContract;
import cmc.pad.resource.domain.inventory.point.NotNeedInventoryManagePointLocationContractRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.SortType;
import mtime.lark.db.jsd.Sorters;
import org.springframework.stereotype.Repository;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/6/30.
 */
@Repository
public class MysqlNotNeedInventoryManagePointLocationContractRepository extends NotNeedInventoryManagePointLocationContractRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public NotNeedInventoryManagePointLocationContract queryRecentOne(String contractNo) {
        return getDatabase()
                .select(NotNeedInventoryManagePointLocationContract.class)
                .where(f("contract_no", contractNo))
                .orderBy(new Sorters(SortType.DESC, "id"))
                .limit(0, 1)
                .result()
                .one(NotNeedInventoryManagePointLocationContract.class);
    }

    @Override
    public void insert(NotNeedInventoryManagePointLocationContract obj) {
        getDatabase().insert(obj).result();
    }
}
