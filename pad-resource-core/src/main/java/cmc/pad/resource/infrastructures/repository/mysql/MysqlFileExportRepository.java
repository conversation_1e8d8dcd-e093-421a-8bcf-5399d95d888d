package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.common.FileExportRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.springframework.stereotype.Repository;

@Repository
public class MysqlFileExportRepository extends FileExportRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }
}
