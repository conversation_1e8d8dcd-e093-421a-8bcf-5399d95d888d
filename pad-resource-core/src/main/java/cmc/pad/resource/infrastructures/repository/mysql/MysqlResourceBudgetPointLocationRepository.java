package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocation;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationRepository;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationView;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SelectResult;
import mtime.lark.util.data.PageResult;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.*;

@Slf4j
@Repository
public class MysqlResourceBudgetPointLocationRepository extends ResourceBudgetPointLocationRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    private static final String TABLE_NAME = "resource_budget_point_location";
    private static final Table t1 = t(TABLE_NAME);
    private static final Table t2 = t("point_location_info");

    @Override
    public int batchInsert(List<ResourceBudgetPointLocation> list) {
        int affect = 0;
        List<Long> repeatIds = getRepeatList(list);
        Database database = getDatabase();
        Transaction transaction = database.begin();
        try {
            if (!CollectionUtils.isEmpty(repeatIds)) {
                transaction.delete(TABLE_NAME).where(f("id", FilterType.IN, repeatIds.toArray(new Long[repeatIds.size()]))).result().getAffectedRows();
            }
            affect = transaction.insert(list).result().getAffectedRows();
            transaction.commit();
        } catch (Exception ex) {
            log.error("导入影厅资源预算异常", ex);
        } finally {
            transaction.close();
        }
        return affect;
    }


    @Override
    public int update(ResourceBudgetPointLocation hall) {
        Database database = getDatabase();
        return database.update(hall).result().getAffectedRows();
    }

    @Override
    public List<ResourceBudgetPointLocation> getList(int pageSize, int page, LocalDateTime startTime, LocalDateTime endTime) {
        Database db = getDatabase();
        List<ResourceBudgetPointLocation> list = db.select(ResourceBudgetPointLocation.class).where(f("update_time",FilterType.GTE,startTime).and(f("update_time",FilterType.LTE,endTime))).page(page, pageSize).result().all(ResourceBudgetPointLocation.class);
        return list;
    }

    private List<Long> getRepeatList(List<ResourceBudgetPointLocation> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> result = new ArrayList<>();
        for (ResourceBudgetPointLocation hall : list) {
            List<Long> ids = getRepeatList(hall.getYear(), hall.getCinemaInnerCode(), hall.getResourceCode());
            result.addAll(ids);
        }
        return result;
    }

    private List<Long> getRepeatList(String year, String cinemaInnerCode, String resourceType) {
        Filter filter = getRepeatFilter(year, cinemaInnerCode, resourceType);
        Database db = getDatabase();
        SelectResult dbResult = db.select("id").from(TABLE_NAME).where(filter).result();
        List<Long> result = new ArrayList<>();
        dbResult.each(reader -> {
            result.add(reader.getLong("id"));
        });
        return result;
    }

    private Filter getListFilter(String year, String regionCode, String cinemaInnerCode, String resourceType,String resourceCode) {
        Filter filter = f(t1, "year", FilterType.EQ, year);
        if (!StringUtils.isEmpty(regionCode) && !regionCode.equals("-1")) {
            filter = filter.and(f(t1, "region_code", FilterType.EQ, regionCode));
        }
        if (!StringUtils.isEmpty(cinemaInnerCode) && !cinemaInnerCode.equals("-1")) {
            filter = filter.and(f(t1, "cinema_inner_code", FilterType.EQ, cinemaInnerCode));
        }
        if (!StringUtils.isEmpty(resourceType) && !resourceType.equals("-1")) {
            filter = filter.and(f(t2, "business_type_code", FilterType.EQ, resourceType));
        }
        if (!StringUtils.isEmpty(resourceCode)) {
            filter = filter.and(f(t2, "code", FilterType.EQ, resourceCode));
        }
        return filter;
    }
    private Filter getRepeatFilter(String year, String cinemaInnerCode, String resourceCode) {
        Filter filter = f("year", year);

        if (!StringUtils.isEmpty(cinemaInnerCode) && !cinemaInnerCode.equals("-1")) {
            filter = filter.and(f("cinema_inner_code", cinemaInnerCode));
        }
        if (!StringUtils.isEmpty(resourceCode) && !resourceCode.equals("-1")) {
            filter = filter.and(f("resource_code", resourceCode));
        }
        return filter;
    }


    @Override
    public PageResult<ResourceBudgetPointLocationView> getPageList(String year, String regionCode, String cinemaInnerCode, String resourceType,String resourceCode, int pageSize, int pageIndex) {
        Filter filter = getListFilter(year, regionCode, cinemaInnerCode, resourceType,resourceCode);
        Database db = getDatabase();
        Long totalCount = (Long) db.select(Shortcut.count()).from(t1).leftJoin(t2, f(t1, "cinema_inner_code", t2, "cinema_inner_code").and(f(t1, "resource_code", t2, "code"))).where(filter).result().value();
        List<ResourceBudgetPointLocationView> list = db.select(c(t1, "*").addWithAlias(t2, "business_type_code","resource_type").addWithAlias(t2,"resource_ownership_code","resource_affiliation").addWithAlias(t2,"location_desc","location_description").addWithAlias(t2,"plan_use","use_plan").addWithAlias(t2,"landing_mode","land_mode").addWithAlias(t2,"sell_area","area_size"))
                .from(t1).leftJoin(t2, f(t1, "cinema_inner_code", t2, "cinema_inner_code").and(f(t1, "resource_code", t2, "code"))).where(filter).page(pageIndex, pageSize).result().all(ResourceBudgetPointLocationView.class);
        PageResult<ResourceBudgetPointLocationView> result = new PageResult<>();
        result.setTotalCount(totalCount.intValue());
        result.setItems(list);
        return result;
    }
}
