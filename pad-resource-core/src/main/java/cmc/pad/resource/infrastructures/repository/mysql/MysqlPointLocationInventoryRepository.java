package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.infrastructures.model.IdMapTable;
import cmc.pad.resource.infrastructures.model.ShardTable;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.config.ShardConfig;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SimpleResult;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cmc.pad.resource.domain.inventory.point.PointLocationInventory.*;
import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.FilterType.LTE;
import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;

/**
 * Created by fuwei on 2022/1/21.
 */
@Slf4j
@Repository
public class MysqlPointLocationInventoryRepository extends PointLocationInventoryRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void insert(PointLocationInfo pointLocationInfo, LocalDate start, LocalDate end) {
        insert(pointLocationInfo, start, end, true);
    }

    @Override
    public void insert(PointLocationInfo pointLocationInfo, LocalDate start, LocalDate end, boolean isBatchInsert) {
        log.info(">>>写入id:{}点位库存, 日期范围:{} - {}", pointLocationInfo.getId(), start, end);
        TableQuery table = getDatabase().table(TABLE_NAME, pointLocationInfo.getId());
        PointLocationInventory pointLocationInventory = new PointLocationInventory();
        pointLocationInventory.setPointLocationId(pointLocationInfo.getId());
        pointLocationInventory.setSellArea(pointLocationInfo.getSellArea());
        pointLocationInventory.setNotSellArea(pointLocationInfo.getSellArea());
        pointLocationInventory.setUpdateTime(LocalDateTime.now());
        List<PointLocationInventory> list = Lists.newArrayList();
        while (true) {
            if (start.isAfter(end))
                break;
            PointLocationInventory clone = (PointLocationInventory) pointLocationInventory.clone();
            clone.setDate(start);
            list.add(clone);
            log.info(">>>{}", JSON.toJSONString(clone));
            start = start.plusDays(1);
        }
        if (list.isEmpty()) {
            log.info(">>>insert list 是空，无可写记录");
        } else {
            if (isBatchInsert) {
                table.insert(list).result();
            } else {
                list.stream().forEach(item -> {
                    try {
                        table.insert(item).result();
                        log.info(">>>>单条写入库存{}", JSON.toJSONString(item));
                    } catch (JsdException e) {
                        log.error(">>>>单条写入库存失败{}", e.getMessage());
                    }
                });
            }
        }
    }

    @Override
    public List<PointLocationInventory> query(int pointLocationId, LocalDate start, LocalDate end) {
        TableQuery table = getDatabase().table(TABLE_NAME, pointLocationId);
        return table
                .select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId).add(PL_INVENTORY_DATE, GTE, start).add(PL_INVENTORY_DATE, LTE, end))
                .result()
                .all(PointLocationInventory.class);
    }

    @Override
    public PointLocationInventory query(int pointLocationId, LocalDate date) {
        TableQuery table = getDatabase().table(TABLE_NAME, pointLocationId);
        return table
                .select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId).add(PL_INVENTORY_DATE, date))
                .result()
                .one(PointLocationInventory.class);
    }

    @Override
    public boolean queryHaveOccupy(int pointLocationId) {
        TableQuery table = getDatabase().table(TABLE_NAME, pointLocationId);
        return table
                .select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId).add("sell_area <> not_sell_area"))
                .limit(0, 1)
                .result().one(PointLocationInventory.class) != null;
    }

    @Override
    public PointLocationInventory queryMinRemainderArea(int pointLocationId, LocalDate adjustDate) {
        TableQuery table = getDatabase().table(TABLE_NAME, pointLocationId);
        return table.select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId).add(PL_INVENTORY_DATE, GTE, adjustDate))
                .orderBy(new Sorters(SortType.ASC, "not_sell_area"))
                .limit(0, 1)
                .result()
                .one(PointLocationInventory.class);
    }

    @Override
    public PointLocationInventory queryMaxDateInventory(int pointLocationId) {
        return getDatabase().table(TABLE_NAME, pointLocationId)
                .select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId))
                .orderBy(new Sorters(SortType.DESC, PL_INVENTORY_DATE))
                .limit(0, 1)
                .result()
                .one(PointLocationInventory.class);
    }

    @Override
    public PointLocationInventory queryMinDateInventory(int pointLocationId) {
        return getDatabase().table(TABLE_NAME, pointLocationId)
                .select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId))
                .orderBy(new Sorters(SortType.ASC, PL_INVENTORY_DATE))
                .limit(0, 1)
                .result()
                .one(PointLocationInventory.class);
    }

    @Override
    public PointLocationInventory queryUsableAreaInventory(int pointLocationId, LocalDate start, LocalDate end) {
        return getDatabase().table(TABLE_NAME, pointLocationId)
                .select(PointLocationInventory.class)
                .where(f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId).add(PL_INVENTORY_DATE, GTE, start).add(PL_INVENTORY_DATE, LTE, end))
                .orderBy(new Sorters(SortType.ASC, PL_INVENTORY_NOT_SELL_AREA))
                .limit(0, 1)
                .result()
                .one(PointLocationInventory.class);
    }

    @Override
    public void updateSellArea(int pointLocationId, float newSellArea, LocalDate adjustDate) {
        TableQuery table = getDatabase().table(TABLE_NAME, pointLocationId);
        UpdateValues uv = uv();
        uv.add(PL_INVENTORY_SELL_AREA, newSellArea);
        uv.xp(PL_INVENTORY_NOT_SELL_AREA, "not_sell_area + (" + newSellArea + " - sell_area)");
        uv.add(PL_INVENTORY_UPDATE_TIME, LocalDateTime.now());
        SimpleResult result = table.update(uv).where(
                f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId)
                        .add(PL_INVENTORY_DATE, GTE, adjustDate)
        ).result();
        log.info(">>>更新id:{}点位,更新面积:{},调整时间:{} 影响记录数:{}",
                pointLocationId,
                newSellArea,
                adjustDate,
                result.getAffectedRows());
    }

    @Override
    public void updateNotSellArea(Transaction tx, int pointLocationId, float occupySellArea, LocalDate start, LocalDate end) {
        TableQuery table = tx.table(TABLE_NAME, pointLocationId);
        UpdateValues uv = uv();
        uv.inc(PL_INVENTORY_NOT_SELL_AREA, occupySellArea);
        uv.add(PL_INVENTORY_UPDATE_TIME, LocalDateTime.now());
        table.update(uv)
                .where(
                        f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId)
                                .add(PL_INVENTORY_DATE, GTE, start)
                                .add(PL_INVENTORY_DATE, LTE, end))
                .result();
    }

    @Override
    public void revertNotSellArea(int pointLocationId, LocalDate start, LocalDate end) {
        getDatabase().begin((Transaction tx) -> revertNotSellArea(tx, pointLocationId, start, end));
    }

    @Override
    public void revertNotSellArea(Transaction tx, int pointLocationId, LocalDate start, LocalDate end) {
        TableQuery table = tx.table(TABLE_NAME, pointLocationId);
        UpdateValues uv = uv();
        uv.xp(PL_INVENTORY_NOT_SELL_AREA, "sell_area");
        uv.add(PL_INVENTORY_UPDATE_TIME, LocalDateTime.now());
        table.update(uv)
                .where(
                        f(PL_INVENTORY_POINT_LOCATION_ID, pointLocationId)
                                .add(PL_INVENTORY_DATE, GTE, start)
                                .add(PL_INVENTORY_DATE, LTE, end))
                .result();
    }

    public String getTableName(int pointLocationId) {
        ShardConfig.Node node = ShardConfig.getDefault().getNode(TABLE_NAME, pointLocationId);
        return TABLE_NAME + node.getSuffix();
    }

    public Map<String, List<IdMapTable>> reduceIdHashTable(List<Integer> pointLocationIds) {
        return pointLocationIds.stream().map(id -> new IdMapTable(id, getTableName(id))).collect(Collectors.groupingBy(IdMapTable::getTable));
    }

    @Override
    public List<ShardTable> tableNodes() {
        List<ShardConfig.Node> nodes = ShardConfig.getDefault().getNodes(TABLE_NAME);
        List<ShardTable> shardTables = Lists.newArrayList();
        for (int i = 0; i < nodes.size(); i++) {
            shardTables.add(new ShardTable(getDatabase(), TABLE_NAME + nodes.get(i).getSuffix(), getDatabase().table(TABLE_NAME, i)))
            ;
        }
        return shardTables;
    }
}