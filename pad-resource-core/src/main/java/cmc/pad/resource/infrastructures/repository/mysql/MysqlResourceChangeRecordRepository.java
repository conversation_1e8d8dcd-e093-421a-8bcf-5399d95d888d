package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.resource.ResourceChangeRecord;
import cmc.pad.resource.domain.resource.ResourceChangeRecordRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlResourceChangeRecordRepository extends ResourceChangeRecordRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    @Override
    public int saveResourceChangeRecord(Transaction tx, ResourceChangeRecord resourceChangeRecord) {
        BuildResult insertInto = tx.insert(resourceChangeRecord).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = tx.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows(); // 1 means insert, 2 means update
        }
    }
}
