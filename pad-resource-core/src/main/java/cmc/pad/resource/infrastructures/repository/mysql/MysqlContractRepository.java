package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.contract.*;
import mtime.lark.db.jsd.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static mtime.lark.db.jsd.FilterType.GTE;
import static mtime.lark.db.jsd.FilterType.LTE;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2024/3/15.
 */
@Repository
public class MysqlContractRepository extends ContractRepository {
    @Override
    public List<Contract> pageList(String contractNo, LocalDateTime start, LocalDateTime end, int pageNo, int pageSize) {
        BasicFilter f = f();
        if (StringUtils.isNotBlank(contractNo))
            f.add("contract_no", contractNo);
        if (start != null)
            f.add("update_time", GTE, start);
        if (end != null)
            f.add("update_time", LTE, end);
        Database db = getDatabase();
        List<Contract> contractList = db.select(Contract.class).where(f).page(pageNo, pageSize).result().all(Contract.class);
        contractList.stream().forEach(item -> {
            List<ContractCollectionPlan> planList = db.select(ContractCollectionPlan.class).where(f("contract_no", item.getContractNo())).orderBy(new Sorters(SortType.ASC, "plan_collection_date")).result().all(ContractCollectionPlan.class);
            item.setContractCollectionPlanList(planList);
        });
        return contractList;
    }

    @Override
    public void insertOrUpdate(Contract contract) {
        contract.setId(null);
        contract.setUpdateTime(LocalDateTime.now());
        Contract existContract = super.findOne(f("contract_no", contract.getContractNo())).orElse(null);
        if (existContract != null) {
            getDatabase().begin(tx -> {
                tx.delete("contract").where(f("contract_no", contract.getContractNo())).result();
                tx.delete("contract_collection_plan").where(f("contract_no", contract.getContractNo())).result();
                tx.insert(contract).result();
                tx.insert(contract.getContractCollectionPlanList()).result();
            });
        } else {
            getDatabase().begin(tx -> {
                tx.insert(contract).result();
                tx.insert(contract.getContractCollectionPlanList()).result();
            });
        }
    }

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

}
