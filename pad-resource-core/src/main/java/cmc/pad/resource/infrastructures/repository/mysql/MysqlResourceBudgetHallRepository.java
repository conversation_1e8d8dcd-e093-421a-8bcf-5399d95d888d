package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.domain.budget.ResourceBudgetHallRepository;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SelectResult;
import mtime.lark.util.data.PageResult;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

@Slf4j
@Repository
public class MysqlResourceBudgetHallRepository extends ResourceBudgetHallRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    private static final String TABLE_NAME = "resource_budget_hall";

    @Override
    public int batchInsert(List<ResourceBudgetHall> list) {
        int affect = 0;
        List<Long> repeatIds = getRepeatList(list);
        Database database = getDatabase();
        Transaction transaction = database.begin();
        try {
            if (!CollectionUtils.isEmpty(repeatIds)) {
                transaction.delete(TABLE_NAME).where(f("id", FilterType.IN, repeatIds.toArray(new Long[repeatIds.size()]))).result().getAffectedRows();
            }
            affect = transaction.insert(list).result().getAffectedRows();
            transaction.commit();
        } catch (Exception ex) {
            log.error("导入影厅资源预算异常", ex);
        } finally {
            transaction.close();
        }
        return affect;
    }


    @Override
    public int update(ResourceBudgetHall hall) {
        Database database = getDatabase();
        return database.update(hall).result().getAffectedRows();
    }

    private List<Long> getRepeatList(List<ResourceBudgetHall> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> result = new ArrayList<>();
        for (ResourceBudgetHall hall : list) {
            List<Long> ids = getRepeatList(hall.getYear(), hall.getRegionCode(), hall.getCinemaInnerCode(), hall.getResourceType());
            result.addAll(ids);
        }
        return result;
    }

    private List<Long> getRepeatList(String year, String regionCode, String cinemaInnerCode, String resourceType) {
        Filter filter = getListFilter(year, regionCode, cinemaInnerCode, resourceType);
        Database db = getDatabase();
        SelectResult dbResult = db.select("id").from(TABLE_NAME).where(filter).result();
        List<Long> result = new ArrayList<>();
        dbResult.each(reader -> {
            result.add(reader.getLong("id"));
        });
        return result;
    }

    private Filter getListFilter(String year, String regionCode, String cinemaInnerCode, String resourceType) {
        Filter filter = f("year", year);
        if (!StringUtils.isEmpty(regionCode) && !regionCode.equals("-1")) {
            filter = filter.and(f("region_code", regionCode));
        }
        if (!StringUtils.isEmpty(cinemaInnerCode) && !cinemaInnerCode.equals("-1")) {
            filter = filter.and(f("cinema_inner_code", cinemaInnerCode));
        }
        if (!StringUtils.isEmpty(resourceType) && !resourceType.equals("-1")) {
            filter = filter.and(f("resource_type", resourceType));
        }
        return filter;
    }


    @Override
    public List<ResourceBudgetHall> getList(int pageSize, int page, LocalDateTime startTime, LocalDateTime endTime) {
        Database db = getDatabase();
        List<ResourceBudgetHall> list = db.select(ResourceBudgetHall.class).where(f("update_time",FilterType.GTE,startTime).and(f("update_time",FilterType.LTE,endTime))).page(page, pageSize).result().all(ResourceBudgetHall.class);
        return list;
    }

    @Override
    public PageResult<ResourceBudgetHall> getPageList(String year, String regionCode, String cinemaInnerCode, String resourceType, int pageSize, int pageIndex) {
        Filter filter = getListFilter(year, regionCode, cinemaInnerCode, resourceType);
        Database db = getDatabase();
        Long totalCount = (Long) db.select(Shortcut.count()).from(TABLE_NAME).where(filter).result().value();
        List<ResourceBudgetHall> list = db.select(ResourceBudgetHall.class).where(filter).page(pageIndex, pageSize).result().all(ResourceBudgetHall.class);

        PageResult<ResourceBudgetHall> result = new PageResult<>();
        result.setTotalCount(totalCount.intValue());
        result.setItems(list);
        return result;
    }
}
