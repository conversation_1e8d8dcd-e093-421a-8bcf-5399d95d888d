package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlCinemaResourceRepository extends CinemaResourceRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public int updateCinemaResourceByCode(Transaction tx, String cinemaCode, Float marketingPointLeasableArea, Float outerAreaLeasableArea, Float fixedPointLeasableArea, Integer advertisingPointLeasableQuantity) {
        return tx.update("cinema_resource").set
                (uv("advertising_point_leasable_quantity", advertisingPointLeasableQuantity)
                        .add("marketing_point_leasable_area", marketingPointLeasableArea)
                        .add("outer_area_leasable_area", outerAreaLeasableArea)
                        .add("fixed_point_leasable_area", fixedPointLeasableArea))
                .where(f("cinema_code", cinemaCode)).result().getAffectedRows();
    }

    @Override
    public boolean insertCinemaResource(Transaction tx, CinemaResource cinemaResource) {
        BuildResult insertInto = tx.insert(cinemaResource).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = tx.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows() == 1; // 1 means insert, 2 means update
        }
    }
}
