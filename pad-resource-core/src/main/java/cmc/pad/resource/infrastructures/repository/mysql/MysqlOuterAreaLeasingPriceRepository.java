package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.domain.price.OuterAreaLeasingPriceRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.FilterType;
import mtime.lark.db.jsd.Table;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.*;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:26
 * @Version 1.0
 */
@Repository
public class MysqlOuterAreaLeasingPriceRepository extends OuterAreaLeasingPriceRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void batchInsert(List<OuterAreaLeasingPrice> list) {
        Database database = getDatabase();
        BuildResult insertInto = database.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = database.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            result.getAffectedRows();
        }
    }

    public LocalDate latestEffectiveDate() {
        Database database = getDatabase();
        Table t = t(OuterAreaLeasingPrice.T_OUTER_AREA_LEASING_PRICE, "T");
        return database.select(c("max(effective_date)", OuterAreaLeasingPrice.C_EFFECTIVE_DATE)).from(t)
                .where(f(OuterAreaLeasingPrice.C_EFFECTIVE_DATE, FilterType.LTE, LocalDate.now()))
                .result().value(LocalDate.class);
    }

}
