package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationChangeRecord;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationChangeRecordRepository;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationChangeRecordView;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationView;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.*;

@Repository
public class MysqlResourceBudgetPointLocationChangeRecordRepository extends ResourceBudgetPointLocationChangeRecordRepository {

    private static final Table t1 = t("resource_budget_point_location_change_record");
    private static final Table t2 = t("point_location_info");

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    @Override
    public int batchInsert(List<ResourceBudgetPointLocationChangeRecord> list) {
        Database database = getDatabase();
        BuildResult insertInto = database.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = database.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }

    @Override
    public List<ResourceBudgetPointLocationChangeRecordView> getList(String year, String regionCode, String cinemaInnerCode, String resourceCode) {
        Filter filter = getListFilter(year, regionCode, cinemaInnerCode, resourceCode);
        Database db = getDatabase();
        List<ResourceBudgetPointLocationChangeRecordView> list = db.select(c(t1, "*").addWithAlias(t2, "business_type_code","resource_type").addWithAlias(t2,"resource_ownership_code","resource_affiliation").addWithAlias(t2,"location_desc","location_description").addWithAlias(t2,"plan_use","use_plan").addWithAlias(t2,"landing_mode","land_mode").addWithAlias(t2,"sell_area","area_size"))
                .from(t1).leftJoin(t2, f(t1, "cinema_inner_code", t2, "cinema_inner_code").and(f(t1, "resource_code", t2, "code"))).where(filter).result().all(ResourceBudgetPointLocationChangeRecordView.class);
        return list;
    }


    private Filter getListFilter(String year, String regionCode, String cinemaInnerCode, String resourceCode) {
        Filter filter = f(t1, "year", FilterType.EQ, year);
        if (!StringUtils.isEmpty(regionCode) && !regionCode.equals("-1")) {
            filter = filter.and(f(t1, "region_code", FilterType.EQ, regionCode));
        }
        if (!StringUtils.isEmpty(cinemaInnerCode) && !cinemaInnerCode.equals("-1")) {
            filter = filter.and(f(t1, "cinema_inner_code", FilterType.EQ, cinemaInnerCode));
        }
//        if (!StringUtils.isEmpty(resourceType) && !resourceType.equals("-1")) {
//            filter = filter.and(f(t2, "business_type_code", FilterType.EQ, resourceType));
//        }
        if (!StringUtils.isEmpty(resourceCode)) {
            filter = filter.and(f(t2, "code", FilterType.EQ, resourceCode));
        }
        return filter;



}}
