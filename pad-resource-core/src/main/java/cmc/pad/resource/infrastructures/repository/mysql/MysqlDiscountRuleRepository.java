package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.discount.DiscountRule;
import cmc.pad.resource.domain.discount.DiscountRuleRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlDiscountRuleRepository extends DiscountRuleRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public int batchInsert(Transaction tx, List<DiscountRule> list) {
        BuildResult insertInto = tx.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = tx.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }

}
