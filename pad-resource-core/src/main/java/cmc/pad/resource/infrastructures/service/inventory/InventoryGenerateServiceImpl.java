package cmc.pad.resource.infrastructures.service.inventory;

import cmc.pad.resource.application.query.data.OccupationCount;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryGenerateService;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SelectResult;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static mtime.lark.db.jsd.Shortcut.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryGenerateServiceImpl implements InventoryGenerateService {

    private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private final CinemaResourceRepository cinemaResourceRepository;
    private final InventoryRepository inventoryRepository;
    private final CinemaRepository cinemaRepository;

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void generate(String cinemaCode, String startDate, String endDate) {
        log.info(">>>阵地广告的资源库存开始生成，影院内码{}，开始日期{}，结束日期{}", cinemaCode, startDate, endDate);
        //传入cinemaCode生成指定影院的库存，不传生成所有影院
        int daysBetween;
        LocalDate startLocalDate;
        try {
            Date start = sdf.parse(startDate);
            Date end = sdf.parse(endDate);
            daysBetween = Integer.parseInt(String.valueOf((end.getTime() - start.getTime() + 1000000) / (60 * 60 * 24 * 1000)));
            Instant instant = start.toInstant();
            ZoneId zone = ZoneId.systemDefault();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
            startLocalDate = localDateTime.toLocalDate();
        } catch (Exception e) {
            log.error("日期格式错误");
            return;
        }
        if (!Strings.isNullOrEmpty(cinemaCode)) {
            generateSingleCinemaInventory(cinemaCode, daysBetween, startLocalDate);
        } else {
            //获取所有影院
            List<Cinema> cinemas = cinemaRepository.findMany(Filter.create());
            for (Cinema cinema : cinemas) {
                generateSingleCinemaInventory(cinema.getCode(), daysBetween, startLocalDate);
            }

        }
        log.info(">>>阵地广告的资源库存开始结束，影院内码{}，开始日期{}，结束日期{}", cinemaCode, startDate, endDate);
    }

    private void generateSingleCinemaInventory(String cinemaCode, int daysBetween, LocalDate startLocalDate) {
        List<Inventory> inventoryList = new ArrayList<>();
        for (int i = 0; i <= daysBetween; i++) {
            Inventory inventory = new Inventory();
            inventory.setCinemaCode(cinemaCode);
            inventory.setDate(startLocalDate.plusDays(i));
            Optional<CinemaResource> optional = cinemaResourceRepository.findOne(Filter.create("cinema_code", cinemaCode));
            if (optional.isPresent()) {
                //查询影院资源，获取各业务类型的总量
                CinemaResource resource = optional.get();
                inventory.setTotalMarketingPointLeasableArea(resource.getMarketingPointLeasableArea());
                inventory.setTotalOuterAreaLeasableArea(resource.getOuterAreaLeasableArea());
                inventory.setTotalFixedPointLeasableArea(resource.getFixedPointLeasableArea());
                inventory.setTotalAdvertisingPointLeasableQuantity(resource.getAdvertisingPointLeasableQuantity());
                inventory.setSoldMarketingPointLeasableArea(resource.getMarketingPointLeasableArea());
                inventory.setSoldOuterAreaLeasableArea(resource.getOuterAreaLeasableArea());
                inventory.setSoldFixedPointLeasableArea(resource.getFixedPointLeasableArea());
                inventory.setSoldAdvertisingPointLeasableQuantity(resource.getAdvertisingPointLeasableQuantity());
            }
            //查询查合同，获取各业务类型的已售量
            List<OccupationCount> countList = countSoldAmountByDateAndCinemaCode(startLocalDate.plusDays(i), cinemaCode);
            if (!CollectionUtils.isEmpty(countList)) {
                for (OccupationCount count : countList) {
                    if ("yx".equalsIgnoreCase(count.getBusinessType())) {
                        inventory.setSoldMarketingPointLeasableArea(inventory.getTotalMarketingPointLeasableArea() - count.getTotal());
                    } else if ("gd".equalsIgnoreCase(count.getBusinessType())) {
                        inventory.setSoldFixedPointLeasableArea(inventory.getTotalFixedPointLeasableArea() - count.getTotal());
                    } else if ("xc".equalsIgnoreCase(count.getBusinessType())) {
                        inventory.setSoldAdvertisingPointLeasableQuantity(inventory.getTotalAdvertisingPointLeasableQuantity() - count.getTotal().intValue());
                    } else if ("wz".equalsIgnoreCase(count.getBusinessType())) {
                        inventory.setSoldOuterAreaLeasableArea(inventory.getTotalOuterAreaLeasableArea() - count.getTotal());
                    }
                }
            }
            inventoryList.add(inventory);
        }
        getDatabase().begin((Transaction tx) -> {
            inventoryRepository.batchInsert(tx, inventoryList);
        });


    }

    private List<OccupationCount> countSoldAmountByDateAndCinemaCode(LocalDate date, String cinemaCode) {
        Database db = getDatabase();
        Table t1 = t("occupation", "O");
        SelectResult result = db.select(c(t1, "business_type").add("sum(O.amount)", "Count"))
                .from(t1)
                .where(f(t1, "start_date", FilterType.LTE, date)
                        .and(f(t1, "end_date", FilterType.GTE, date)).and(f("status", "1"))
                        .and(f("cinema_code", cinemaCode)))
                .groupBy(g(t1, "business_type"))
                .result();
        List<OccupationCount> list = new ArrayList<>();
        result.each(reader -> {
            OccupationCount model = new OccupationCount();
            model.setBusinessType(reader.getString("business_type"));
            model.setTotal(reader.getFloat("Count"));
            list.add(model);
        });
        return list;

    }
}
