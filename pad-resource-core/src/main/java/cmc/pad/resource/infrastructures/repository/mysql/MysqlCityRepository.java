package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.domain.city.CityRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlCityRepository extends CityRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    @Override
    public int batchInsert(List<City> list) {
        Database database = getDatabase();
        BuildResult insertInto = database.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = database.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }

    @Override
    public boolean updateCityLevelByCode(String code, String cityLevel) {
        Database db = getDatabase();
        return db.update("city").set(uv("city_level", cityLevel)).where(f("code", code)).result().getAffectedRows() == 1;
    }
}
