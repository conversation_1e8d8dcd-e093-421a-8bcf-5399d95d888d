package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.application.query.data.ResultList;
import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.domain.city.CityDistrict;
import cmc.pad.resource.domain.city.CityDistrictListModel;
import cmc.pad.resource.domain.city.CityDistrictRepository;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.util.List;

import static cmc.pad.resource.domain.city.City.*;
import static cmc.pad.resource.domain.city.CityDistrict.C_CODE;
import static cmc.pad.resource.domain.city.CityDistrict.C_NAME;
import static cmc.pad.resource.domain.city.CityDistrict.*;
import static mtime.lark.db.jsd.Shortcut.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class MysqlCityDistrictRepository extends CityDistrictRepository {

    private static final Table TABLE_DISTRICT = t(T_CITY_DISTRICT, "district");
    private static final Table TABLE_CITY = t(T_CITY, "city");

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    @Override
    public void batchInsert(List<CityDistrict> list) {
        Database database = getDatabase();
        BuildResult insertInto = database.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = database.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            log.info(">>>写入城市区域影响行数:{}", result.getAffectedRows());
        }
    }

    @Override
    public void updateLevelByCode(String code, String level) {
        getDatabase()
                .update(T_CITY_DISTRICT)
                .set(uv("level", level))
                .where(f("code", code))
                .result();
    }

    @Override
    public ResultList<CityDistrictListModel> find(String regionCode, String superiorCode,
                                                  String cityCode, String districtCode,
                                                  String districtName, String districtLevel,
                                                  int pageIndex, int pageSize
    ) {
        BasicFilter f = f();
        if (!"0".equals(regionCode))
            f.add(TABLE_CITY, C_REGION_CODE, FilterType.EQ, regionCode);
        if (!"0".equals(superiorCode))
            f.add(TABLE_CITY, C_SUPERIOR_CODE, FilterType.EQ, superiorCode);
        if (!"0".equals(cityCode))
            f.add(TABLE_DISTRICT, C_CITY_CODE, FilterType.EQ, cityCode);
        if (!Strings.isNullOrEmpty(districtCode))
            f.add(TABLE_DISTRICT, C_CODE, FilterType.EQ, districtCode);
        if (!Strings.isNullOrEmpty(districtName))
            f.add(TABLE_DISTRICT, C_NAME, FilterType.LK, districtName);
        if (!Strings.isNullOrEmpty(districtLevel) && !"0".equals(districtLevel))
            f.add(TABLE_DISTRICT, C_CITY_DISTRICT_LEVEL, FilterType.EQ, districtLevel);
        ResultList<CityDistrictListModel> result = new ResultList<>();
        List<CityDistrictListModel> list =
                getDatabase()
                        .select(
                                c(TABLE_DISTRICT, C_CODE, C_CITY_DISTRICT_LEVEL)
                                        .addWithAlias(TABLE_DISTRICT, C_NAME, "dName")
                                        .add(TABLE_CITY, C_REGION_NAME, C_SUPERIOR_NAME)
                                        .addWithAlias(TABLE_CITY, City.C_NAME, "cName")
                        )
                        .from(TABLE_DISTRICT)
                        .leftJoin(TABLE_CITY, f(TABLE_DISTRICT, C_CITY_CODE, TABLE_CITY, C_CODE))
                        .where(f)
                        .orderBy(s(SortType.ASC, TABLE_DISTRICT, C_CODE))
                        .page(pageIndex, pageSize)
                        .result()
                        .all(CityDistrictListModel.class);
        result.setList(list);
        result.setTotalCount((int) findCount(f));
        return result;
    }

    private long findCount(BasicFilter f) {
        return (long) getDatabase()
                .select(Shortcut.count())
                .from(TABLE_DISTRICT)
                .leftJoin(T_CITY, f(TABLE_DISTRICT, C_CITY_CODE, TABLE_CITY, C_CODE))
                .where(f)
                .orderBy(s(SortType.ASC, TABLE_DISTRICT, C_CODE))
                .result()
                .value();
    }
}
