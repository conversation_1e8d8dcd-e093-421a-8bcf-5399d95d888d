package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.budget.ResourceBudgetHallChangeRecord;
import cmc.pad.resource.domain.budget.ResourceBudgetHallChangeRecordRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.SortType;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.s;

@Repository
public class MysqlResourceBudgetHallChangeRecordRepository extends ResourceBudgetHallChangeRecordRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }



    @Override
    public int batchInsert(List<ResourceBudgetHallChangeRecord> list) {
        Database database = getDatabase();
        BuildResult insertInto = database.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = database.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }





    private Filter getListFilter(String year, String regionCode, String cinemaInnerCode, String resourceType) {
        Filter filter = f("year", year);
        if (!StringUtils.isEmpty(regionCode) && !regionCode.equals("-1")) {
            filter = filter.and(f("region_code", regionCode));
        }
        if (!StringUtils.isEmpty(cinemaInnerCode) && !cinemaInnerCode.equals("-1")) {
            filter = filter.and(f("cinema_inner_code", cinemaInnerCode));
        }
        if (!StringUtils.isEmpty(resourceType) && !resourceType.equals("-1")) {
            filter = filter.and(f("resource_type", resourceType));
        }
        return filter;
    }


    @Override
    public List<ResourceBudgetHallChangeRecord> getList(String year, String regionCode, String cinemaInnerCode, String resourceType) {
        Filter filter = getListFilter(year, regionCode, cinemaInnerCode, resourceType);
        Database db = getDatabase();
        List<ResourceBudgetHallChangeRecord> list = db.select(ResourceBudgetHallChangeRecord.class).where(filter).orderBy(s(SortType.DESC,"id")).result().all(ResourceBudgetHallChangeRecord.class);
        return list;
    }
}
