package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

import static cmc.pad.resource.domain.resource.PointLocationInfo.*;
import static mtime.lark.db.jsd.FilterType.IN;
import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;

@Slf4j
@Repository
public class MysqlPointLocationInfoRepository extends PointLocationInfoRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void batchInsert(List<PointLocationInfo> list) {
        getDatabase().insert(list).result();
    }

    @Override
    public int saveReturnId(PointLocationInfo pointLocationInfo) {
        if (pointLocationInfo.getId() == null || pointLocationInfo.getId() == 0) {
            Long id = (Long) getDatabase().insert(pointLocationInfo).result(true).getKeys().get(0);
            return id.intValue();
        } else {
            save(pointLocationInfo);
            return pointLocationInfo.getId();
        }
    }

    @Override
    public List<PointLocationInfo> batchQuery(List<PointLocationModel.BatchQueryParam> paramList) {
        List<PointLocationInfo> list = Collections.synchronizedList(Lists.newArrayList());
        paramList.parallelStream().forEach(param -> {
            PointLocationInfo pointLocationInfo = getDatabase().select(PointLocationInfo.class)
                    .where(f(PLI_CODE, param.getCode()).add(PLI_CINEMA_INNER_CODE, param.getCinemaInnerCode()))
                    .result().one(PointLocationInfo.class);
            if (pointLocationInfo != null)
                list.add(pointLocationInfo);
        });
        return list;
    }

    @Override
    public void updateInventoryStatus(int id, ProcessStatus status) {
        getDatabase().update(PLI_TABLE).set(uv(PLI_INVENTORY_STATUS, status)).where(f(PLI_ID, id)).result();
    }

    @Override
    public void batchUpdateInventoryProcessStatus(Transaction tx, List<Integer> pointLocationIds, ProcessStatus processStatus) {
        tx
                .update(PLI_TABLE)
                .set(uv(PLI_INVENTORY_STATUS, processStatus))
                .where(f("id", IN, pointLocationIds.toArray(new Integer[pointLocationIds.size()])))
                .result();
    }

    @Override
    public void batchUpdateInventoryProcessStatus(List<Integer> pointLocationIds, ProcessStatus processStatus) {
        getDatabase().begin((Transaction tx) -> batchUpdateInventoryProcessStatus(tx, pointLocationIds, processStatus));
    }

    @Override
    public void updateLargeWard(int pointLocationId, String largeWardCode) {
        getDatabase().update(PLI_TABLE).set(uv(PLI_LARGE_WARD_CODE, largeWardCode)).where(f(PLI_ID, pointLocationId)).result();
    }

    @Override
    public List<String> queryAllCinemaInnerCode() {
        List<String> cinemaInnerCodeList = Lists.newArrayList();
        try (ExecuteResult result = getDatabase().execute("select distinct(cinema_inner_code) from point_location_info").result()) {
            result.each(reader -> cinemaInnerCodeList.add(reader.getString(1)));
        }
        return cinemaInnerCodeList;
    }

    @Override
    public List<String> queryAllRegionCode() {
        List<String> regionCodeList = Lists.newArrayList();
        try (ExecuteResult result = getDatabase().execute("select distinct(region_code) from point_location_info").result()) {
            result.each(reader -> regionCodeList.add(reader.getString(1)));
        }
        return regionCodeList;
    }

    @Override
    public void updateRegionCode(String regionCode, String cinemaInnerCode) {
        getDatabase().update("point_location_info")
                .set(uv("region_code", regionCode))
                .where(f("cinema_inner_code", cinemaInnerCode))
                .result();
    }

    @Override
    public void updateLargeWardCode(String largeWardCode, String regionCode) {
        getDatabase().update("point_location_info")
                .set(uv("large_ward_code", largeWardCode))
                .where(f("region_code", regionCode))
                .result();
    }
}