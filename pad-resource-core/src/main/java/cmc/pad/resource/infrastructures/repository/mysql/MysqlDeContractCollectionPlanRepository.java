package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.contract.DeContractCollectionPlan;
import cmc.pad.resource.domain.contract.DeContractCollectionPlanRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static mtime.lark.db.jsd.FilterType.IN;
import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;

/**
 * Created by fuwei on 2024/3/15.
 */
@Repository
public class MysqlDeContractCollectionPlanRepository extends DeContractCollectionPlanRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void batchInsert(List<DeContractCollectionPlan> list) {
        getDatabase().insert(list).result();
    }

    @Override
    public void flagOverdue(List<Long> ids) {
        getDatabase()
                .update(DeContractCollectionPlan.TABLE)
                .set(uv("overdue_state", DeContractCollectionPlan.OverdueState.OVERDUE.value()).add("update_time", LocalDateTime.now()))
                .where(f("id", IN, ids.toArray(new Long[ids.size()]))).result();
    }
}
