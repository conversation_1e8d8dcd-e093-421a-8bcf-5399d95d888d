package cmc.pad.resource.infrastructures.service.dictionary;

import cmc.basic.datadict.service.dto.DataDictDto;
import cmc.basic.datadict.service.facade.DataDictServiceFacade;
import cmc.location.front.service.dto.CityDto;
import cmc.location.front.service.iface.CityService;
import cmc.location.front.service.iface.RegionService;
import cmc.mdm.cinema.front.contract.iface.CinemaFrontService;
import cmc.pad.resource.domain.dictionary.DictEntry;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DictionaryDomainServiceAdapter implements DictionaryDomainService {
    //城市级别维数据类型编码 目前对应 城市地区级别
    public static final String CITY_LEVEL_DICT_TYPE = "417";
    //影城级别维数据类型编码
    public static final String CINEMA_LEVEL_DICT_TYPE = "418";
    //业务类型维数据类型编码
    public static final String BUSINESS_TYPE_DICT_TYPE = "419";
    //影厅类型维数据类型编码
    public static final String MOVIE_HALL_TYPE_DICT_TYPE = "113";
    //资源归属
    public static final String RESOURCE_OWNER_SHIP_DICT_TYPE = "452";

    private final RegionService regionService;
    private final CinemaFrontService cinemaFrontService;
    private final DataDictServiceFacade dataDictServiceFacade;
    private final CityService cityService;

    public Map<String, String> allBusinessTypeMap() {
        return allBusinessTypes().stream().collect(Collectors.toMap(DictEntry.BusinessType::getCode, businessType -> businessType.getName()));
    }

    @Override
    public List<DictEntry.BusinessType> allBusinessTypes() {
        List<DataDictDto.DataDictItem> dict = findDict(BUSINESS_TYPE_DICT_TYPE);
        return dict.stream().map(item -> new DictEntry.BusinessType(item.getDictCode(), item.getDictName())).collect(Collectors.toList());
    }

    @Override
    public List<DictEntry.MovieHallType> allMovieHallTypes() {
        List<DataDictDto.DataDictItem> dict = findDict(MOVIE_HALL_TYPE_DICT_TYPE);
        return dict.stream().map(item -> new DictEntry.MovieHallType(item.getDictCode(), item.getDictName())).collect(Collectors.toList());
    }

    @Override
    public List<String> allMovieHallTypeCodes() {
        return allMovieHallTypes().stream().map(DictEntry.MovieHallType::getCode).map(String::trim).map(String::toUpperCase).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> allMovieHallTypeDict() {
        return allMovieHallTypes().stream().collect(Collectors.toMap(DictEntry.MovieHallType::getCode, DictEntry.MovieHallType::getName));
    }


    @Override
    public List<DictEntry.CityLevel> allCityLevel() {//目前对应城市地区级别
        List<DataDictDto.DataDictItem> dict = findDict(CITY_LEVEL_DICT_TYPE);
        return dict.stream().map(item -> new DictEntry.CityLevel(item.getDictCode(), item.getDictName())).collect(Collectors.toList());
    }

    @Override
    public List<String> allCityLevelCodes() {//目前对应城市地区级别
        return allCityLevel().stream().map(DictEntry.CityLevel::getCode).map(String::trim).map(String::toUpperCase).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> allCityLevelDict() {//目前对应城市地区级别
        return allCityLevel().stream().collect(Collectors.toMap(DictEntry.CityLevel::getCode, DictEntry.CityLevel::getName));
    }


    @Override
    public List<DictEntry.CinemaLevel> allCinemaLevel() {
        List<DataDictDto.DataDictItem> dict = findDict(CINEMA_LEVEL_DICT_TYPE);
        return dict.stream().map(item -> new DictEntry.CinemaLevel(item.getDictCode(), item.getDictName())).collect(Collectors.toList());
    }

    @Override
    public List<CityDto.City> allCity() {
        return cityService.findCitys(new CityDto.FindCitysRequest()).getItems();
    }

    @Override
    public List<String> allCinemaLevelCodes() {
        return allCinemaLevel().stream().map(DictEntry.CinemaLevel::getCode).map(String::trim).map(String::toUpperCase).collect(Collectors.toList());
    }

    @Override
    public Map<String, String> allCinemaLevelDict() {
        return allCinemaLevel().stream().collect(Collectors.toMap(DictEntry.CinemaLevel::getCode, DictEntry.CinemaLevel::getName));
    }

    public List<DataDictDto.DataDictItem> findDict(String dictType) {
        DataDictDto.DataDict dataDict = dataDictServiceFacade.getDataDict(dictType);
        return dataDict.getDataDictItems();
    }

    public Map<String, String> findDictMap(String dictType) {
        return findDict(dictType)
                .stream()
                .collect(Collectors.toMap(DataDictDto.DataDictItem::getDictCode, dataDictItem -> dataDictItem.getDictName()));
    }
}
