package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.domain.resource.*;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * Created by fuwei on 2022/1/21.
 */
@Repository
public class MysqlPointLocationInfoChangeRecordRepository extends PointLocationInfoChangeRecordRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void saveChangeRecord(String operator, PointLocationInfo pointLocation) {
        PointLocationInfoChangeRecord changeRecord = CopyUtil.copy(pointLocation, PointLocationInfoChangeRecord.class);
        changeRecord.setId(null);
        changeRecord.setPointLocationId(pointLocation.getId());
        changeRecord.setUpdater(operator);
        changeRecord.setCreateTime(LocalDateTime.now());
        save(changeRecord);
    }
}
