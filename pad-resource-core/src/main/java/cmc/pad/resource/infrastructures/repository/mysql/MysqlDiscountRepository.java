package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.discount.Discount;
import cmc.pad.resource.domain.discount.DiscountRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlDiscountRepository extends DiscountRepository {


    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public int updateDiscount(Transaction tx, Discount discount) {
        BuildResult insertInto = tx.insert(discount).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = tx.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }

    @Override
    public int insertDiscount(Transaction tx, Discount discount) {
        List<Object> keys = tx.insert(discount).result(true).getKeys();
        return ((Long) keys.get(0)).intValue();
    }

}
