package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.cinema.CinemaLevel;
import cmc.pad.resource.domain.cinema.CinemaLevelRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.FilterType;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

@Repository
public class MysqlCinemaLevelRepository extends CinemaLevelRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    @Override
    public void deleteByImportId(int importId) {
        Database database = getDatabase();
        database.delete(CinemaLevel.T_CINEMA_LEVEL).where(f(CinemaLevel.C_IMPORT_ID, importId)).result().getAffectedRows();
    }

    @Override
    public int batchInsert(List<CinemaLevel> list) {
        Database database = getDatabase();
        BuildResult insertInto = database.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = database.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }

    @Override
    public List<Integer> getAllOldImportId(int newImportId) {
        List<Integer> ids = new ArrayList<>();
        Database database = getDatabase();
        database.select(CinemaLevel.C_IMPORT_ID)
                .from(CinemaLevel.T_CINEMA_LEVEL)
                .where(f(CinemaLevel.C_IMPORT_ID, FilterType.LT, newImportId))
                .result()
                .each(rs -> ids.add(rs.getInt(CinemaLevel.C_IMPORT_ID)));
        return ids;
    }
}
