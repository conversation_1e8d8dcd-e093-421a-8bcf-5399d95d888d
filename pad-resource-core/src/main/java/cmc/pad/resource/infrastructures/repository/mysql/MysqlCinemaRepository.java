package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.cinema.CinemaRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlCinemaRepository extends CinemaRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }
}
