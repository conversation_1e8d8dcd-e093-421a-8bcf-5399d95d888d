package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.domain.inventory.OccupationRepository;
import cmc.pad.resource.domain.inventory.OccupationStatus;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.*;
import static mtime.lark.db.jsd.SortType.DESC;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlOccupationRepository extends OccupationRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    @Override
    public int insertOccupation(Transaction tx, Occupation occupation) {
        occupation.setUpdateTime(LocalDateTime.now());
        List<Object> keys = tx.insert(occupation).result(true).getKeys();
        return ((Long) keys.get(0)).intValue();
    }

    @Override
    public int cancelOccupation(Transaction tx, String contractNo) {
        return tx.update("occupation").set(uv("status", 2).add("update_time", LocalDateTime.now())).where(f("contract_no", contractNo)).result().getAffectedRows();
    }

    @Override
    public int updateOccupation(Transaction tx, Occupation occupation) {
        return tx.update("occupation").set(uv("amount", occupation.getAmount())
                .add("start_date", occupation.getStartDate())
                .add("end_date", occupation.getEndDate())
                .add("update_time", LocalDateTime.now()))
                .where(f("contract_no", occupation.getContractNo())
                        .add("cinema_code", occupation.getCinemaCode())
                        .add("business_type", occupation.getBusinessType())).result().getAffectedRows();
    }

    @Override
    public int updateOccupationStatus(Transaction tx, String contractNo, OccupationStatus status) {
        return tx.update("occupation")
                .set(uv("status", status)
                        .add("update_time", LocalDateTime.now()))
                .where(f("contract_no", contractNo))
                .result()
                .getAffectedRows();
    }

    @Override
    public int updateOccupationStatus(String contractNo, OccupationStatus status) {
        return getDatabase().begin((Transaction tx) -> updateOccupationStatus(tx, contractNo, status));
    }

    @Override
    public int batchInsert(Transaction tx, List<Occupation> list) {
        LocalDateTime now = LocalDateTime.now();
        list.forEach(item -> item.setUpdateTime(now));
        BuildResult insertInto = tx.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = tx.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }

    @Override
    public int delete(Transaction tx, String contractNo) {
        return tx.delete("occupation").where(Filter.create("contract_no", contractNo)).result().getAffectedRows();
    }

    @Override
    public Occupation queryRecentlyOccupation(String contractNo) {
        return getDatabase()
                .select(Occupation.class)
                .where(f("contract_no", contractNo))
                .orderBy(s(DESC, "update_time"))
                .limit(0, 1)
                .result()
                .one(Occupation.class)
                ;
    }
}
