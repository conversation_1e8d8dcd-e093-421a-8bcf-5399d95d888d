package cmc.pad.resource.infrastructures.service.cinema;

import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.RegionService;
import cmc.mdm.cinema.admin.contract.dto.CinemaAdminDto;
import cmc.mdm.cinema.admin.contract.iface.CinemaAdminService;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.cinema.CinemaSyncDomainService;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.FilterType;
import mtime.lark.util.msg.Publisher;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaSyncDomainServiceImpl implements CinemaSyncDomainService {

    private final CinemaAdminService cinemaAdminService;
    private final CinemaRepository cinemaRepository;
    private final RegionService regionService;

    @Override
    public void synchronize() {
        log.info(">>>阵地广告的影城信息同步开始");
        CinemaAdminDto.QueryCinemaInfoListRequest request = new CinemaAdminDto.QueryCinemaInfoListRequest();
        CinemaAdminDto.QueryCinemaInfoListResponse response = cinemaAdminService.queryCinemaInfoList(request);
        List<CinemaAdminDto.Cinema> cinemas = response.getQueryCinemaList();
        LocalDateTime syncTime = LocalDateTime.now();
        if (!CollectionUtils.isEmpty(cinemas)) {
            RegionDto.FindRegionsRequest regionsRequest = new RegionDto.FindRegionsRequest();
            RegionDto.FindRegionsResponse regionsResponse = this.regionService.findRegions(regionsRequest);
            Map<String, RegionDto.Region> regionMap = regionsResponse.getItems().stream().collect(Collectors.toMap(RegionDto.Region::getId, a -> a));
            cinemas.forEach(item -> {
                Cinema cinema = new Cinema();
                cinema.setCode(item.getInnerCode());
                cinema.setName(Strings.isNullOrEmpty(item.getOuterName()) ? "" : item.getOuterName());
                cinema.setRegionCode(Strings.isNullOrEmpty(item.getRegionId()) ? "" : item.getRegionId());
                RegionDto.Region region = regionMap.get(item.getRegionId());
                cinema.setRegionName(region == null ? "" : region.getName());
                cinema.setCityCode(String.valueOf(item.getCity()));
                cinema.setCityDistrictCode(String.valueOf(item.getArea()));
                cinema.setSyncTime(syncTime);
                Boolean flag = cinemaRepository.save(cinema);
                if (flag) {
                    Publisher.get().publish("PAD_RESOURCE_ADD_CINEMA_TOPIC", cinema.getCode());
                }
            });
        }
        String syncTimeStr = syncTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        //处理：主数据已删除的影院,对应咱们的影院也得删除
        Filter filter = Filter.create("sync_time", FilterType.LT, syncTimeStr);
        List<Cinema> toDeleteCinemaList = cinemaRepository.findMany(filter);
        for (Cinema cinema : toDeleteCinemaList) {
            log.info("删除影城信息, code:{}", cinema.getCode());
            cinemaRepository.delete(Filter.create("code", cinema.getCode()));
            Publisher.get().publish("PAD_RESOURCE_DELETE_CINEMA_TOPIC", cinema.getCode());
        }
        log.info(">>>阵地广告的影城信息同步结束");
    }
}
