package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlInventoryRepository extends InventoryRepository {

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    @Override
    public int batchInsert(Transaction tx, List<Inventory> list) {
        LocalDateTime now = LocalDateTime.now();
        list.forEach(item -> item.setUpdateTime(now));
        BuildResult insertInto = tx.insert(list).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = tx.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows();
        }
    }
}
