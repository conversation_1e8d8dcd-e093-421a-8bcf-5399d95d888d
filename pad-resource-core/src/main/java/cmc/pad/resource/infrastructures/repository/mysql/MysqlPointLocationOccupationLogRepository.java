package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationLog;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationLogRepository;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.SortType;
import mtime.lark.db.jsd.Sorters;
import org.springframework.stereotype.Repository;

import java.util.List;

import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationLog.*;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class MysqlPointLocationOccupationLogRepository extends PointLocationOccupationLogRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    @Override
    public List<PointLocationOccupationLog> queryRecentApprovalContractLog(String contractNo) {
        List<PointLocationOccupationLog> logs = findMany(f(PLO_LOG_CONTRACT_NO, contractNo)
                .add(PLO_LOG_CONTRACT_STATUS, ContractStatus.APPROVAL), new Sorters(SortType.DESC, PLO_LOG_CREATE_TIME), 1);
        if (logs.isEmpty()) {
            return Lists.newArrayList();
        }
        List<PointLocationOccupationLog> logList = getDatabase()
                .select(PointLocationOccupationLog.class)
                .where(
                        f(PLO_LOG_CONTRACT_NO, contractNo)
                                .add(PLO_LOG_CONTRACT_STATUS, ContractStatus.APPROVAL)
                                .add(PLO_LOG_VERSION, logs.get(0).getVersion())
                )
                .result()
                .all(PointLocationOccupationLog.class);
        log.info(">>>查询上一版审核合同详情, 合同编号：{}, 详情:{}", contractNo, JSON.toJSONString(logList, true));
        return logList;
    }

}
