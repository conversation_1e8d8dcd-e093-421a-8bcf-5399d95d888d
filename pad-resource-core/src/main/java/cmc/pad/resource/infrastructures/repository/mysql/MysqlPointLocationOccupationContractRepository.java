package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.domain.inventory.point.*;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.db.jsd.UpdateValues;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract.PLO_C_CONTRACT_NO;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.*;
import static cmc.pad.resource.domain.inventory.point.ProcessStatus.CREATE;
import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.Shortcut.uv;

/**
 * Created by fuyuanpu on 2022/2/27.
 */
@Repository
public class MysqlPointLocationOccupationContractRepository extends PointLocationOccupationContractRepository {
    @Autowired
    private PointLocationOccupationLogRepository pointLocationOccupationLogRepository;

    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    @Override
    public PointLocationOccupationContract query(String contractNo) {
        return query(contractNo, false);
    }

    @Override
    public PointLocationOccupationContract query(String contractNo, boolean isIncludeContractDetails) {
        Database db = getDatabase();
        PointLocationOccupationContract contract =
                db.select(PointLocationOccupationContract.class)
                        .where(f(PLO_C_CONTRACT_NO, contractNo))
                        .result()
                        .one(PointLocationOccupationContract.class);
        if (contract == null) return null;
        if (isIncludeContractDetails) {
            List<PointLocationOccupationContractDetail> detailList = db.select(PointLocationOccupationContractDetail.class)
                    .where(f(PLO_C_D_CONTRACT_NO, contractNo))
                    .result()
                    .all(PointLocationOccupationContractDetail.class);
            contract.setOccupationDetailList(detailList);
        }
        return contract;
    }

    @Override
    public void update(Transaction tx, String contractNo, ContractStatus contractStatus, ProcessStatus processStatus) {
        UpdateValues uv = uv("update_time", LocalDateTime.now());
        if (contractStatus != null)
            uv.add("status", contractStatus);
        if (processStatus != null)
            uv.add("process_status", processStatus);
        tx.update(PointLocationOccupationContract.TABLE)
                .set(uv)
                .where(f("contract_no", contractNo))
                .result();
    }

    @Override
    public void update(String contractNo, ProcessStatus status) {
        getDatabase().begin((Transaction tx) -> update(tx, contractNo, null, status));
    }

    @Override
    public void update(Transaction tx, String contractNo, ProcessStatus status) {
        update(tx, contractNo, null, status);
    }

    @Override
    public void reOccupyUpdate(Transaction tx, PointLocationOccupationContract contractBaseInfo) {
        contractBaseInfo.setProcessStatus(CREATE);
        contractBaseInfo.setStatus(ContractStatus.SUBMIT);
        contractBaseInfo.setUpdateTime(LocalDateTime.now());
        contractBaseInfo.setVersion(contractBaseInfo.getVersion() + 1);

        tx.update(contractBaseInfo).result();
        tx.delete(PointLocationOccupationContractDetail.TABLE).where(f(PLO_C_D_CONTRACT_NO, contractBaseInfo.getContractNo()).add(PLO_C_D_VERSION, contractBaseInfo.getVersion() - 1)).result();

        List<PointLocationOccupationContractDetail> occupationDetailList = contractBaseInfo.getOccupationDetailList();
        occupationDetailList.forEach(occupationDetail -> occupationDetail.setVersion(contractBaseInfo.getVersion()));
        tx.insert(occupationDetailList).result();

        List<PointLocationOccupationLog> occupationLogList = CopyUtil.listCopy(occupationDetailList, PointLocationOccupationLog.class);
        tx.insert(occupationLogList).result();
    }

    @Override
    public void active(PointLocationOccupationContract contractBaseInfo, ProcessStatus processStatus) {
        getDatabase().begin((Transaction tx) -> active(tx, contractBaseInfo, processStatus));
    }

    @Override
    public void active(Transaction tx, PointLocationOccupationContract contractBaseInfo, ProcessStatus processStatus) {
        String contractNo = contractBaseInfo.getContractNo();
        LocalDateTime now = LocalDateTime.now();
        contractBaseInfo.setUpdateTime(now);
        contractBaseInfo.setVersion(contractBaseInfo.getVersion() + 1);
        contractBaseInfo.setStatus(ContractStatus.APPROVAL);
        contractBaseInfo.setProcessStatus(processStatus);
        List<PointLocationOccupationContractDetail> occupationList = getDatabase()
                .select(PointLocationOccupationContractDetail.class)
                .where(f(PLO_C_D_CONTRACT_NO, contractNo))
                .result()
                .all(PointLocationOccupationContractDetail.class);

        UpdateValues uv = uv(PLO_C_D_UPDATE_TIME, now);
        uv.add(PLO_C_D_CONTRACT_STATUS, ContractStatus.APPROVAL);
        uv.add(PLO_C_D_VERSION, contractBaseInfo.getVersion());

        List<PointLocationOccupationLog> occupationLogList = CopyUtil.listCopy(occupationList, PointLocationOccupationLog.class);
        occupationLogList.forEach(occupationLog -> {
            occupationLog.setId(null);
            occupationLog.setContractStatus(ContractStatus.APPROVAL);
            occupationLog.setCreateTime(now);
            occupationLog.setVersion(contractBaseInfo.getVersion());
        });

        tx.update(PointLocationOccupationContractDetail.TABLE).set(uv).where(f(PLO_C_D_CONTRACT_NO, contractNo)).result();
        tx.insert(occupationLogList).result();
        tx.update(contractBaseInfo).result();
    }

//    @Override
//    public void rollback(Transaction tx, PointLocationOccupationContract contractBaseInfo) {
//        String contractNo = contractBaseInfo.getContractNo();
//        LocalDateTime now = LocalDateTime.now();
//        contractBaseInfo.setStatus(ACTIVE);
//        contractBaseInfo.setProcessStatus(CREATE);
//        contractBaseInfo.setUpdateTime(now);
//        contractBaseInfo.setVersion(contractBaseInfo.getVersion() + 1);
//
//        List<PointLocationOccupationLog> recentApprovalContractLogList = pointLocationOccupationLogRepository.queryRecentApprovalContractLog(contractNo);
//        recentApprovalContractLogList.forEach(occupationLog -> {
//            occupationLog.setId(null);
//            occupationLog.setCreateTime(now);
//            occupationLog.setVersion(contractBaseInfo.getVersion());
//        });
//
//        List<PointLocationOccupationContractDetail> occupationDetailList = CopyUtil.listCopy(recentApprovalContractLogList, PointLocationOccupationContractDetail.class);
//        occupationDetailList.stream().forEach(detail -> detail.setUpdateTime(detail.getCreateTime()));
//
//        tx.delete(PointLocationOccupationContractDetail.TABLE).where(f(PLO_C_D_CONTRACT_NO, contractBaseInfo.getContractNo()).add(PLO_C_D_VERSION, contractBaseInfo.getVersion() - 1)).result();
//        tx.insert(occupationDetailList).result();
//        tx.insert(recentApprovalContractLogList).result();
//        tx.update(contractBaseInfo).result();
//    }

    @Override
    public void cancel(Transaction tx, PointLocationOccupationContract contractBaseInfo) {
        String contractNo = contractBaseInfo.getContractNo();
        LocalDateTime now = LocalDateTime.now();
        contractBaseInfo.setStatus(ContractStatus.CANCEL);
        contractBaseInfo.setProcessStatus(CREATE);
        contractBaseInfo.setUpdateTime(now);
        contractBaseInfo.setVersion(contractBaseInfo.getVersion() + 1);

        List<PointLocationOccupationContractDetail> occupationDetailList = getDatabase()
                .select(PointLocationOccupationContractDetail.class)
                .where(f(PLO_C_D_CONTRACT_NO, contractNo))
                .result()
                .all(PointLocationOccupationContractDetail.class);
        UpdateValues uv = uv(PLO_C_D_UPDATE_TIME, now);
        uv.add(PLO_C_D_CONTRACT_STATUS, ContractStatus.CANCEL);
        uv.add(PLO_C_D_VERSION, contractBaseInfo.getVersion());

        List<PointLocationOccupationLog> occupationLogList = CopyUtil.listCopy(occupationDetailList, PointLocationOccupationLog.class);
        occupationLogList.forEach(occupationLog -> {
            occupationLog.setId(null);
            occupationLog.setCreateTime(now);
            occupationLog.setContractStatus(ContractStatus.CANCEL);
            occupationLog.setVersion(contractBaseInfo.getVersion());
        });

        tx.update(PointLocationOccupationContractDetail.TABLE).set(uv).where(f(PLO_C_D_CONTRACT_NO, contractNo)).result();
        tx.insert(occupationLogList).result();
        tx.update(contractBaseInfo).result();
    }

    @Override
    public void insert(PointLocationOccupationContract contractBaseInfo) {
        getDatabase().begin((Transaction tx) -> insert(tx, contractBaseInfo));
    }

    @Override
    public void insert(Transaction tx, PointLocationOccupationContract contractBaseInfo) {
        List<PointLocationOccupationContractDetail> occupationDetailList = contractBaseInfo.getOccupationDetailList();
        List<PointLocationOccupationLog> occupationLogList = CopyUtil.listCopy(occupationDetailList, PointLocationOccupationLog.class);
        tx.insert(contractBaseInfo).result();
        tx.insert(occupationDetailList).result();
        tx.insert(occupationLogList).result();
    }
}