package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetailRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import org.springframework.stereotype.Repository;

import java.util.List;

import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_NO;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_STATUS;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Repository
public class MysqlPointLocationOccupationContractDetailRepository extends PointLocationOccupationContractDetailRepository {
    @Override
    protected Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    @Override
    public List<PointLocationOccupationContractDetail> query(String contractNo, ContractStatus status) {
        return getDatabase().select(PointLocationOccupationContractDetail.class)
                .where(f(PLO_C_D_CONTRACT_NO, contractNo).add(PLO_C_D_CONTRACT_STATUS, status))
                .result()
                .all(PointLocationOccupationContractDetail.class);
    }
}
