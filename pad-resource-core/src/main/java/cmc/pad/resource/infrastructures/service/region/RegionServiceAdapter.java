package cmc.pad.resource.infrastructures.service.region;

import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.domain.region.Region;
import cmc.pad.resource.domain.region.RegionDomainService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RegionServiceAdapter implements RegionDomainService {

    private final RegionService regionService;

    //id->name
    @Override
    public Map<String, String> allRegionDict() {
        List<RegionDto.Region> regionList = getRegions();
        return regionList.stream().collect(Collectors.toMap(RegionDto.Region::getId, RegionDto.Region::getName));
    }

    @Override
    public List<Region> allRegions() {
        List<RegionDto.Region> regionList = getRegions();
        return regionList.stream().map(region -> new Region(region.getId(), region.getName())).collect(Collectors.toList());
    }

    private List<RegionDto.Region> getRegions() {
        RegionDto.FindRegionsRequest request = new RegionDto.FindRegionsRequest();
        RegionDto.FindRegionsResponse response = regionService.findRegions(request);
        return response.getItems();
    }

    @Override
    public String getLargeWardCode(String regionCode) {
        log.info(">>>query region code:{}", regionCode);
        RegionDto.GetRegionRequest request = new RegionDto.GetRegionRequest();
        request.setId(regionCode);
        RegionDto.GetRegionResponse response = regionService.getRegion(request);
        log.info(">>>query region code:{} -> region:{}", regionCode, JSON.toJSONString(response));
        return response.getRegion() == null ? null : response.getRegion().getLargeWard();
    }
}
