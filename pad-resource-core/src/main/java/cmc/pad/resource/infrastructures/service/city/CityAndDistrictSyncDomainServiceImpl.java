package cmc.pad.resource.infrastructures.service.city;

import cmc.location.admin.service.dto.DistrictInfoDto;
import cmc.location.admin.service.iface.DistrictInfoService;
import cmc.location.front.service.dto.CityDto;
import cmc.location.front.service.dto.ProvinceDto;
import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.CityService;
import cmc.location.front.service.iface.ProvinceService;
import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.domain.city.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.FilterType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CityAndDistrictSyncDomainServiceImpl implements CityAndDistrictSyncDomainService {
    private final RegionService regionService;
    private final ProvinceService provinceService;
    private final CityService cityService;
    private final DistrictInfoService districtInfoService;
    private final CityRepository cityRepository;
    private final CityDistrictRepository cityDistrictRepository;

    @Override
    public void synchronize(String initCityDistrictLevel) {
        LocalDateTime syncTime = LocalDateTime.now();
        synCity(syncTime);
        synCityDistrict(syncTime, initCityDistrictLevel);
    }

    private void synCityDistrict(LocalDateTime syncTime, String initCityDistrictLevel) {
        log.info(">>>阵地广告的城市地区信息同步开始");
        Map<String, String> districtCodeMapLevel = districtCodeMapLevel(initCityDistrictLevel);
        List<CityDistrict> list = districtInfoService.findDistrictInfos(new DistrictInfoDto.FindDistrictInfosRequest())
                .getItems()
                .stream()
                .map(districtInfo ->
                        new CityDistrict(
                                String.valueOf(districtInfo.getId()),
                                districtInfo.getName(),
                                String.valueOf(districtInfo.getCity()),
                                districtCodeMapLevel.get(String.valueOf(districtInfo.getId())),
                                syncTime
                        )
                ).collect(Collectors.toList());
        Lists.partition(list, 5000).forEach(pList -> cityDistrictRepository.batchInsert(pList));
        //处理主数据已删除的城市地区
        int deleteNum = cityDistrictRepository.delete(Filter.create("sync_time", FilterType.LT, syncTime.plusSeconds(-1)));
        log.info(">>>处理主数据已删除的城市地区，刪除个数:{}", deleteNum);
        log.info(">>>阵地广告的城市地区信息同步结束");
    }

    private Map<String, String> districtCodeMapLevel(String initCityDistrictLevel) {
        Map<String, String> initDistrictCodeMapLevel = Maps.newHashMap();
        if (!Strings.isNullOrEmpty(initCityDistrictLevel)) {
            Lists.newArrayList(initCityDistrictLevel.split(";"))
                    .forEach(cityDistrictLevel -> {
                        String[] districtLevel = cityDistrictLevel.split(",");
                        initDistrictCodeMapLevel.put(districtLevel[0], districtLevel[1]);
                    });
        }
        Map<String, String> localDistrictCodeMapLevel = cityDistrictRepository.findMany(Filter.create().add("level is not null"))
                .stream()
                .collect(Collectors.toMap(CityDistrict::getCode, CityDistrict::getLevel));
        localDistrictCodeMapLevel.putAll(initDistrictCodeMapLevel);
        return localDistrictCodeMapLevel;
    }

    private void synCity(LocalDateTime syncTime) {
        log.info(">>>阵地广告的城市信息同步开始");
        CityDto.FindCitysRequest request = new CityDto.FindCitysRequest();
        CityDto.FindCitysResponse response = cityService.findCitys(request);
        RegionDto.FindRegionsRequest regionsRequest = new RegionDto.FindRegionsRequest();
        RegionDto.FindRegionsResponse regionsResponse = this.regionService.findRegions(regionsRequest);
        Map<String, RegionDto.Region> regionMap = regionsResponse.getItems().stream().collect(Collectors.toMap(RegionDto.Region::getId, a -> a));
        ProvinceDto.FindProvincesByOldIdsRequest provincesByOldIdsRequest = new ProvinceDto.FindProvincesByOldIdsRequest();
        ProvinceDto.FindProvincesByOldIdsResponse provincesByOldIdsResponse = provinceService.findProvincesByOldIds(provincesByOldIdsRequest);
        Map<Integer, ProvinceDto.Province> provinceMap = provincesByOldIdsResponse.getProvinces().stream().collect(Collectors.toMap(ProvinceDto.Province::getId, a -> a));
        List<City> cityList = new ArrayList<>();
        List<City> originalCityList = cityRepository.findMany(Filter.create());
        Map<String, String> cityLevelMap = new HashMap<>();
        originalCityList.forEach(city -> cityLevelMap.put(city.getCode(), city.getCityLevel()));
        response.getItems().forEach(item -> {
            City city = new City();
            city.setCode(Strings.isNullOrEmpty(String.valueOf(item.getId())) ? "" : String.valueOf(item.getId()));
            city.setName(Strings.isNullOrEmpty(item.getNameCN()) ? "" : item.getNameCN());
            city.setRegionCode(Strings.isNullOrEmpty(item.getRegion()) ? "" : item.getRegion());
            RegionDto.Region region = regionMap.get(item.getRegion());
            city.setRegionName(region == null ? "" : region.getName());
            city.setSuperiorCode(Strings.isNullOrEmpty(String.valueOf(item.getProvince())) ? "" : String.valueOf(item.getProvince()));
            ProvinceDto.Province province = provinceMap.get(item.getProvince());
            city.setSuperiorName(province == null ? "" : province.getNameCN());
            city.setCityLevel(cityLevelMap.get(Strings.isNullOrEmpty(String.valueOf(item.getId())) ? "" : String.valueOf(item.getId())));
            city.setSyncTime(syncTime);
            cityList.add(city);
        });
        cityRepository.batchInsert(cityList);
        //处理：主数据已删除的城市,对应咱们的城市也得删除
        List<City> deletedCityList = cityRepository.findMany(Filter.create("sync_time", FilterType.LT, syncTime.plusSeconds(-1)));
        deletedCityList.forEach(city -> {
            cityRepository.delete(Filter.create("code", city.getCode()));
            log.info(">>>删除城市:cityCode:{}, cityName:{}", city.getCode(), city.getName());
            cityDistrictRepository.delete(Filter.create("city_code", city.getCode()));
            log.info(">>>删除城市对应的地区:cityCode:{}", city.getCode());
        });
        log.info(">>>阵地广告的城市信息同步结束");
    }
}
