package cmc.pad.resource.application.query.data;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ResourceSoldData {
    private String cinemaCode;
    private Date date;
    private Float marketingPointLeasableAreaHaveSoldAmount;
    private Float outerAreaLeasableAreaHaveSoldAmount;
    private Float fixedPointLeasableAreaHaveSoldAmount;
    private Integer advertisingPointLeasableQuantityHaveSoldAmount;
}
