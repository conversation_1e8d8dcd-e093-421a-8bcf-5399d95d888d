package cmc.pad.resource.application.command.point;

import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import cmc.pad.resource.domain.resource.*;
import cmc.pad.resource.infrastructures.service.dictionary.DictionaryDomainServiceAdapter;
import cmc.pad.resource.infrastructures.service.region.RegionServiceAdapter;
import cmc.pad.resource.util.RedisLockSupport;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.msg.Publisher;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.DateUtil.toDateStr;
import static cmc.pad.resource.constant.TopicName.POINT_LOCATION_GENERATE_INVENTORY_TOPIC;
import static cmc.pad.resource.constant.TopicName.POINT_LOCATION_UPDATE_INVENTORY_TOPIC;
import static cmc.pad.resource.domain.resource.PointLocationInfo.*;
import static cmc.pad.resource.infrastructures.service.dictionary.DictionaryDomainServiceAdapter.BUSINESS_TYPE_DICT_TYPE;
import static cmc.pad.resource.infrastructures.service.dictionary.DictionaryDomainServiceAdapter.RESOURCE_OWNER_SHIP_DICT_TYPE;
import static mtime.lark.db.jsd.FilterType.IN;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/1/12.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationManageService {
    private final PointSaveChecker checker;
    private final CinemaRepository cinemaRepository;
    private final RegionServiceAdapter regionServiceAdapter;
    private final PointLocationInfoRepository pointLocationInfoRep;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;
    private final DictionaryDomainServiceAdapter dictionaryDomainServiceAdapter;
    private final PointLocationInfoChangeRecordRepository pointLocationInfoChangeRecordRep;

    public void save(PointLocationModel.SaveParam info) {
        checker.checkCode(info.getCode(), info.getCinemaInnerCode(), info.getId());
        PointLocationInfo newPointLocationInfo = new PointLocationInfo();
        BeanUtils.copyProperties(info, newPointLocationInfo);
        newPointLocationInfo.setRegionCode(cinema(info.getCinemaInnerCode()).getRegionCode());
        newPointLocationInfo.setLargeWardCode(getLargeWard(newPointLocationInfo.getRegionCode()));
        LocalDateTime now = LocalDateTime.now();
        newPointLocationInfo.setUpdateTime(now);
        if (info.getId() == 0) {//添加
            newPointLocationInfo.setCreateTime(now);
            int id = saveIncludeSellAreaUpdate(info.getUserName(), newPointLocationInfo);
            sendGenerateInventoryTopicMsg(id);
        }
        if (info.getId() != 0) {//更新
            PointLocationInfo originPointLocationInfo = pointLocationInfoRep.get(info.getId());
            checker.checkBusinessTypeCode(originPointLocationInfo, info.getBusinessTypeCode());
            newPointLocationInfo.setCreateTime(originPointLocationInfo.getCreateTime());
            newPointLocationInfo.setInventoryStatus(originPointLocationInfo.getInventoryStatus());

            if (!originPointLocationInfo.getSellArea().equals(info.getSellArea())) {//如果更新点位库存面积
                newPointLocationInfo.setSellAreaAdjustDate(LocalDate.parse(info.getSellAreaAdjustDate()));
                RedisLockSupport.lockTemplate(originPointLocationInfo.getId().toString(), () -> {
                    pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(originPointLocationInfo.getId());
                    checker.checkRemainderInventory(originPointLocationInfo, info.getSellArea(), info.getSellAreaAdjustDate());
                    int id = saveIncludeSellAreaUpdate(info.getUserName(), newPointLocationInfo);
                    sendUpdateInventoryTopicMsg(id);
                });
            } else {
                newPointLocationInfo.setSellAreaAdjustDate(originPointLocationInfo.getSellAreaAdjustDate());
                save(info.getUserName(), newPointLocationInfo);
            }
        }
    }

    private String getLargeWard(String regionCode) {
        return regionServiceAdapter.getLargeWardCode(regionCode);
    }

    private void sendGenerateInventoryTopicMsg(int id) {
        log.info(">>>发送id:{}点位创建库存消息:{}", id, POINT_LOCATION_GENERATE_INVENTORY_TOPIC);
        Publisher.get().publish(POINT_LOCATION_GENERATE_INVENTORY_TOPIC, id);
    }

    private void sendUpdateInventoryTopicMsg(int id) {
        log.info(">>>发送id:{}点位更新库存消息:{}", id, POINT_LOCATION_UPDATE_INVENTORY_TOPIC);
        Publisher.get().publish(POINT_LOCATION_UPDATE_INVENTORY_TOPIC, id);
    }

    private int saveIncludeSellAreaUpdate(String loginUserName, PointLocationInfo pointLocation) {
        pointLocation.setInventoryStatus(ProcessStatus.CREATE);
        return save(loginUserName, pointLocation);
    }

    private int save(String loginUserName, PointLocationInfo pointLocation) {
        log.info(">>>用户:{} {}点位 -> {}", loginUserName, (pointLocation.getId() == null || pointLocation.getId() == 0) ? "添加" : "更新", JSON.toJSONString(pointLocation));
        int id = pointLocationInfoRep.saveReturnId(pointLocation);
        pointLocation.setId(id);
        pointLocationInfoChangeRecordRep.saveChangeRecord(loginUserName, pointLocation);
        return id;
    }

    public void batchSave(String loginUserName, List<PointLocationModel.ImportExcel> list) {
        Map<String, Cinema> cinemaCache = Maps.newHashMap();
        Map<String, String> largeWardCache = Maps.newHashMap();
        List<PointLocationInfo> insertList = Lists.newArrayList();
        List<PointLocationInfo> updateList = Lists.newArrayList();
        list.stream().forEach(item -> {
            String regionCode = cinema(cinemaCache, item.getCinemaInnerCode()).getRegionCode();
            String largeWardCode = getLargeWardCode(largeWardCache, regionCode);
            PointLocationInfo pl = new PointLocationInfo(item.getResourceCode(), largeWardCode, regionCode, item.getCinemaInnerCode(), item.getResourceType(), item.getResourceOwnership(), item.getLocationDesc(), item.getPlanUse(),
                    item.getLandingMode(), Float.valueOf(item.getSellArea()),
                    item.getAvailablePeriod(), item.getFloor(), item.getFloorHeight(),
                    item.getIsSplittable(), item.getWaterSupply(), item.getPowerSupply(),
                    item.getFireFacilities(), item.getDecoration()
            );
            Optional<PointLocationInfo> existPl = pointLocationInfoRep.findOne(f(PLI_CODE, pl.getCode()).add(PLI_CINEMA_INNER_CODE, pl.getCinemaInnerCode()));
            if (existPl.isPresent()) {
                pl.setId(existPl.get().getId());
                pl.setCreateTime(existPl.get().getCreateTime());
                pl.setInventoryStatus(existPl.get().getInventoryStatus());
                updateList.add(pl);
            } else {
                insertList.add(pl);
            }

        });
        insertList.stream().forEach(pl -> {
            log.info(">>>批量导入点位信息, 新增 - {}", JSON.toJSONString(pl));
            int id = saveIncludeSellAreaUpdate(loginUserName, pl);
            sendGenerateInventoryTopicMsg(id);
        });
        updateList.parallelStream().forEach(pl -> {
            log.info(">>>批量导入点位信息, 更新 - {}", JSON.toJSONString(pl));
            int id = pointLocationInfoRep.saveReturnId(pl);
            pl.setId(id);
            pointLocationInfoChangeRecordRep.saveChangeRecord(loginUserName, pl);
//            sendUpdateInventoryTopicMsg(id); 批量更新售卖面积不能修改， 不做库存处理
        });
    }

    public PointLocationModel.PointLocation get(int id) {
        PointLocationInfo pointLocationInfo = pointLocationInfoRep.get(id);
        PointLocationModel.PointLocation pl = new PointLocationModel.PointLocation();
        BeanUtils.copyProperties(pointLocationInfo, pl);
        pl.setCinemaName(cinema(pl.getCinemaInnerCode()).getName());
        return pl;
    }

    public PageResult<PointLocationModel.ListDataParam> list(PointLocationModel.ListQueryParam param) {
        Map<String, String> businessTypeMap = dictMap(BUSINESS_TYPE_DICT_TYPE);
        Map<String, String> ownerShipMap = dictMap(RESOURCE_OWNER_SHIP_DICT_TYPE);
        BasicFilter f = f();
        if (!Strings.isNullOrEmpty(param.getCode())) {
            f.add(PLI_CODE, param.getCode());
        }
        if (!"-1".equals(param.getCinema())) {
            f.add(PLI_CINEMA_INNER_CODE, param.getCinema());
        }
        if (!"-1".equals(param.getRegion())) {
            f.add(PLI_REGION_CODE, param.getRegion());
        }
        if (!"-1".equals(param.getBusinessTypeCode())) {
            f.add(PLI_BUSINESS_TYPE_CODE, param.getBusinessTypeCode());
        }
        if (!"-1".equals(param.getResourceOwnershipCode())) {
            f.add(PLI_RESOURCE_OWNERSHIP_CODE, param.getResourceOwnershipCode());
        }
        PointLocationModel.UserRankInfo userRankInfo = param.getUserRankInfo();
        int rank = userRankInfo.getRank();
        if (rank == 1) {
            if ("0".equals(userRankInfo.getLargeWardCode())) {
                if ("0".equals(param.getLargeWardSelect())) {
                    f.add(PLI_LARGE_WARD_CODE, IN, new String[]{"1", "2"});
                }
                if ("1".equals(param.getLargeWardSelect())) {
                    f.add(PLI_LARGE_WARD_CODE, "1");
                }
                if ("2".equals(param.getLargeWardSelect())) {
                    f.add(PLI_LARGE_WARD_CODE, "2");
                }
            } else {
                f.add(PLI_LARGE_WARD_CODE, userRankInfo.getLargeWardCode());
            }
        }
        if (rank == 2) {
            f.add(PLI_REGION_CODE, userRankInfo.getAreaCode());
        }
        if (rank == 3) {
            f.add(PLI_CINEMA_INNER_CODE, userRankInfo.getCinemaInnerCode());
        }
        PageResult<PointLocationInfo> result = pointLocationInfoRep.findPage(f, new Sorters(SortType.DESC, PLI_UPDATE_TIME), param.getPageSize(), param.getPageIndex());
        return new PageResult(transfer(result.getItems(), businessTypeMap, ownerShipMap), result.getTotalCount());
    }

    private List<PointLocationModel.ListDataParam> transfer(List<PointLocationInfo> list, Map<String, String> businessTypeMap, Map<String, String> ownerShipMap) {
        return list.stream().map(r -> {
            Cinema cinema = cinema(r.getCinemaInnerCode());
            return new PointLocationModel.ListDataParam(
                    r.getId(), r.getCode(), r.getRegionCode(), r.getCinemaInnerCode(),
                    r.getBusinessTypeCode(), r.getResourceOwnershipCode(), r.getLocationDesc(), r.getPlanUse(),
                    r.getLandingMode(), r.getSellArea(), toDateStr(r.getSellAreaAdjustDate()),
                    cinema.getRegionName(), cinema.getName(),
                    businessTypeMap.get(r.getBusinessTypeCode()), ownerShipMap.get(r.getResourceOwnershipCode())
                    , r.getAvailablePeriod(), r.getFloor(), r.getFloorHeight()
                    , r.getIsSplittableText(), r.getWaterSupplyText(), r.getPowerSupplyText(), r.getFireFacilitiesText()
                    , r.getDecoration()
            );
        }).collect(Collectors.toList());
    }

    public List<PointLocationModel.ExportExcel> allMapExportExcelList(PointLocationListQueryParam param) {
        Map<String, String> businessTypeMap = dictMap(BUSINESS_TYPE_DICT_TYPE);
        Map<String, String> ownerShipMap = dictMap(RESOURCE_OWNER_SHIP_DICT_TYPE);
        List<PointLocationModel.ExportExcel> list = Lists.newArrayList();
        int page = 1;
        while (true) {
            BasicFilter f = f();
            if ("0".equals(param.getLargeWard()) && param.getLargeWardSelect().length != 0)
                f.add(PLI_LARGE_WARD_CODE, IN, param.getLargeWardSelect());
            if (!"0".equals(param.getLargeWard()))
                f.add(PLI_LARGE_WARD_CODE, param.getLargeWard());
            if (!"-1".equals(param.getRegion()))
                f.add(PLI_REGION_CODE, param.getRegion());
            if (!"-1".equals(param.getCinema()))
                f.add(PLI_CINEMA_INNER_CODE, param.getCinema());
            if (!"-1".equals(param.getBusinessTypeCode()))
                f.add(PLI_BUSINESS_TYPE_CODE, param.getBusinessTypeCode());
            if (!"-1".equals(param.getResourceOwnershipCode()))
                f.add(PLI_RESOURCE_OWNERSHIP_CODE, param.getResourceOwnershipCode());
            if (!Strings.isNullOrEmpty(param.getCode()))
                f.add(PLI_CODE, param.getCode());

            PageResult<PointLocationInfo> pageResult = pointLocationInfoRep.findPage(f, 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            List<PointLocationModel.ExportExcel> collect = pageResult.getItems().stream().map(r -> {
                Cinema cinema = cinema(r.getCinemaInnerCode());
                return new PointLocationModel.ExportExcel(
                        r.getRegionCode(), cinema.getRegionName()
                        , r.getCinemaInnerCode(), cinema.getName()
                        , r.getBusinessTypeCode(), businessTypeMap.get(r.getBusinessTypeCode())
                        , r.getResourceOwnershipCode(), ownerShipMap.get(r.getResourceOwnershipCode())
                        , r.getCode()
                        , r.getAvailablePeriod(), r.getFloor()
                        , r.getLocationDesc(), r.getPlanUse()
                        , r.getLandingMode(), String.valueOf(r.getSellArea())
                        , r.getFloorHeightText()
                        , r.getIsSplittableText(), r.getWaterSupplyText(), r.getPowerSupplyText()
                        , r.getFireFacilitiesText(), r.getDecoration()
                );
            }).collect(Collectors.toList());
            list.addAll(collect);
        }
        return list;
    }

    private Cinema cinema(String code) {
        return cinemaRepository.findOne(f(Cinema.C_CODE, code)).get();
    }

    private Cinema cinema(Map<String, Cinema> cache, String code) {
        if (cache.containsKey(code))
            return cache.get(code);
        Cinema cinema = cinemaRepository.findOne(f(Cinema.C_CODE, code)).get();
        cache.put(code, cinema);
        return cinema;
    }

    private String getLargeWardCode(Map<String, String> cache, String regionCode) {
        if (cache.containsKey(regionCode))
            return cache.get(regionCode);
        String largeWardCode = regionServiceAdapter.getLargeWardCode(regionCode);
        cache.put(regionCode, largeWardCode);
        return largeWardCode;
    }

    public Map<String, String> dictMap(String dictType) {
        return dictionaryDomainServiceAdapter.findDictMap(dictType);
    }

    public Map<String, String> businessTypeDictMap() {
        return dictMap(BUSINESS_TYPE_DICT_TYPE);
    }

    public Map<String, String> ownerShipDictMap() {
        return dictMap(RESOURCE_OWNER_SHIP_DICT_TYPE);
    }

    public List<PointLocationModel.ListDataParam> batchQuery(List<PointLocationModel.BatchQueryParam> paramList) {
        List<PointLocationInfo> list = pointLocationInfoRep.batchQuery(paramList);
        Map<String, String> businessTypeMap = dictMap(BUSINESS_TYPE_DICT_TYPE);
        Map<String, String> ownerShipMap = dictMap(RESOURCE_OWNER_SHIP_DICT_TYPE);
        List<PointLocationModel.ListDataParam> mapList = list.stream().map(r -> {
            Cinema cinema = cinema(r.getCinemaInnerCode());
            return new PointLocationModel.ListDataParam(
                    r.getId(), r.getCode(), r.getRegionCode(), r.getCinemaInnerCode(),
                    r.getBusinessTypeCode(), r.getResourceOwnershipCode(), r.getLocationDesc(), r.getPlanUse(),
                    r.getLandingMode(), r.getSellArea(), toDateStr(r.getSellAreaAdjustDate()),
                    cinema.getRegionName(), cinema.getName(),
                    businessTypeMap.get(r.getBusinessTypeCode()), ownerShipMap.get(r.getResourceOwnershipCode())
                    , r.getAvailablePeriod(), r.getFloor(), r.getFloorHeight()
                    , r.getIsSplittableText(), r.getWaterSupplyText(), r.getPowerSupplyText()
                    , r.getFireFacilitiesText(), r.getDecoration()
            );
        }).collect(Collectors.toList());
        return mapList;
    }
}