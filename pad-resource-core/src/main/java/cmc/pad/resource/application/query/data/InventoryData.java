package cmc.pad.resource.application.query.data;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class InventoryData {
    private String regionName;
    private String cinemaName;
    private String cinemaCode;
    private Date date;
    private Float totalMarketingPointLeasableArea;
    private Float totalOuterAreaLeasableArea;
    private Float totalFixedPointLeasableArea;
    private Integer totalAdvertisingPointLeasableQuantity;
    private Float soldMarketingPointLeasableArea;
    private Float soldOuterAreaLeasableArea;
    private Float soldFixedPointLeasableArea;
    private Integer soldAdvertisingPointLeasableQuantity;
}
