package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.cinema.CinemaSyncDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaAppService {

    private final CinemaSyncDomainService cinemaSyncDomainService;

    public void synchronizeCinemas() {
        cinemaSyncDomainService.synchronize();
    }


}
