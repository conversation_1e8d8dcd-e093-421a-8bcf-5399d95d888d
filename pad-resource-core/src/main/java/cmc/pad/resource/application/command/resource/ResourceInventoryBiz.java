package cmc.pad.resource.application.command.resource;

import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by fuwei on 2022/2/5.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ResourceInventoryBiz {
    private final InventoryRepository inventoryRepository;

    public void updateInventory(Transaction tx, String businessType, Float amount, List<Inventory> inventoryList, String type) {
        //更新合同期限内的库存
        List<Inventory> toModified = new ArrayList<>();
        for (Inventory inventory : inventoryList) {
            if ("YX".equalsIgnoreCase(businessType)) {
                if ("add".equalsIgnoreCase(type)) {
                    //新增库存占用
                    inventory.setSoldMarketingPointLeasableArea(inventory.getSoldMarketingPointLeasableArea() - amount);
                } else if ("cancel".equalsIgnoreCase(type)) {
                    //取消库存占用
                    inventory.setSoldMarketingPointLeasableArea(inventory.getSoldMarketingPointLeasableArea() + amount);
                }
            } else if ("GD".equalsIgnoreCase(businessType)) {
                if ("add".equalsIgnoreCase(type)) {
                    inventory.setSoldFixedPointLeasableArea(inventory.getSoldFixedPointLeasableArea() - amount);
                } else if ("cancel".equalsIgnoreCase(type)) {
                    inventory.setSoldFixedPointLeasableArea(inventory.getSoldFixedPointLeasableArea() + amount);
                }
            } else if ("XC".equalsIgnoreCase(businessType)) {
                if ("add".equalsIgnoreCase(type)) {
                    inventory.setSoldAdvertisingPointLeasableQuantity(inventory.getSoldAdvertisingPointLeasableQuantity() - amount.intValue());
                } else if ("cancel".equalsIgnoreCase(type)) {
                    inventory.setSoldAdvertisingPointLeasableQuantity(inventory.getSoldAdvertisingPointLeasableQuantity() + amount.intValue());
                }
            } else if ("WZ".equalsIgnoreCase(businessType)) {
                if ("add".equalsIgnoreCase(type)) {
                    inventory.setSoldOuterAreaLeasableArea(inventory.getSoldOuterAreaLeasableArea() - amount);
                } else if ("cancel".equalsIgnoreCase(type)) {
                    inventory.setSoldOuterAreaLeasableArea(inventory.getSoldOuterAreaLeasableArea() + amount);
                }
            }
            toModified.add(inventory);
        }
        if (toModified.size() > 0) {
            inventoryRepository.batchInsert(tx, toModified);
        }
    }
}
