package cmc.pad.resource.application.query.data;

import cmc.pad.resource.domain.inventory.OccupationStatus;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OccupationData {
    private Integer id;
    private String cinemaCode;
    private String contractNo;
    private String businessType;
    private String amount;
    private LocalDate startDate;
    private LocalDate endDate;
    private OccupationStatus status;
}
