package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData.DateOccupyArea;
import cmc.pad.resource.util.DateUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by fuwei on 2022/3/26.
 */
public class AlterDataChecker {


    public static boolean isExpand(DateOccupyArea newData, DateOccupyArea oldData) {
        return
                dateRangeAndAreaAllIsExpand(newData, oldData)
                        || (dateRangeIsEqual(newData, oldData) && areaIsExpand(newData, oldData))
                        || (dateRangeExpand(newData, oldData) && areaIsEqual(newData, oldData));
    }

    public static boolean isShrink(DateOccupyArea newData, DateOccupyArea oldData) {
        return
                dateRangeAndAreaAllIsShrink(newData, oldData)
                        || (dateRangeIsEqual(newData, oldData) && areaIsShrink(newData, oldData))
                        || (dateRangeShrink(newData, oldData) && areaIsEqual(newData, oldData));
    }

    public static boolean dateRangeAndAreaAllIsExpand(DateOccupyArea newData, DateOccupyArea oldData) {
        return dateRangeExpand(newData, oldData) && areaIsExpand(newData, oldData);
    }

    public static boolean dateRangeAndAreaAllIsShrink(DateOccupyArea newData, DateOccupyArea oldData) {
        return dateRangeShrink(newData, oldData) && areaIsShrink(newData, oldData);
    }

    public static boolean dateRangeIsExpandButAreaIsShrink(DateOccupyArea newData, DateOccupyArea oldData) {
        return dateRangeExpand(newData, oldData) && areaIsShrink(newData, oldData);
    }

    public static boolean dateRangeIsShrinkButAreaIsExpand(DateOccupyArea newData, DateOccupyArea oldData) {
        return dateRangeShrink(newData, oldData) && areaIsExpand(newData, oldData);
    }

    public static boolean areaIsExpand(DateOccupyArea newData, DateOccupyArea oldData) {
        return newData.getOccupyArea() > oldData.getOccupyArea();
    }

    public static boolean areaIsShrink(DateOccupyArea newData, DateOccupyArea oldData) {
        return newData.getOccupyArea() < oldData.getOccupyArea();
    }

    public static boolean areaIsEqual(DateOccupyArea newData, DateOccupyArea oldData) {
        return newData.getOccupyArea().equals(oldData.getOccupyArea());
    }

    public static boolean dateRangeExpand(DateOccupyArea newData, DateOccupyArea oldData) {
        return
                (
                        (newData.getStartDate().isBefore(oldData.getStartDate()) && newData.getEndDate().isEqual(oldData.getEndDate()))
                                || (newData.getStartDate().isEqual(oldData.getStartDate()) && newData.getEndDate().isAfter(oldData.getEndDate()))
                                || (newData.getStartDate().isBefore(oldData.getStartDate()) && newData.getEndDate().isAfter(oldData.getEndDate()))
                );
    }

    public static List<DateUtil.DateRange> expandDateRange(DateOccupyArea big, DateOccupyArea small) {
        List<DateUtil.DateRange> dateRanges = Lists.newArrayList();
        if (big.getStartDate().isBefore(small.getStartDate())) {
            dateRanges.add(new DateUtil.DateRange(big.getStartDate(), small.getStartDate().minusDays(1)));
        }
        if (big.getEndDate().isAfter(small.getEndDate())) {
            dateRanges.add(new DateUtil.DateRange(small.getEndDate().plusDays(1), big.getEndDate()));
        }
        return dateRanges;
    }

    public static Float expandArea(Float newArea, Float oldArea) {
        return newArea.floatValue() - oldArea.floatValue();
    }

    public static boolean dateRangeShrink(DateOccupyArea newData, DateOccupyArea oldData) {
        return
                (
                        (newData.getStartDate().isAfter(oldData.getStartDate()) && newData.getEndDate().isEqual(oldData.getEndDate()))
                                || (newData.getStartDate().isEqual(oldData.getStartDate()) && newData.getEndDate().isBefore(oldData.getEndDate()))
                                || (newData.getStartDate().isAfter(oldData.getStartDate()) && newData.getEndDate().isBefore(oldData.getEndDate()))
                );
    }

    public static boolean dateRangeIsEqual(DateOccupyArea newData, DateOccupyArea oldData) {
        return (newData.getStartDate().isEqual(oldData.getStartDate()) && newData.getEndDate().isEqual(oldData.getEndDate()));
    }

    public static boolean dateRangeIsNew(DateOccupyArea newData, DateOccupyArea oldData) {
        return (newData.getEndDate().isBefore(oldData.getStartDate())) || (newData.getStartDate().isAfter(oldData.getEndDate()));
    }

    public static boolean dateLeftCrossing(DateOccupyArea newData, DateOccupyArea oldData) {
        return (
                newData.getStartDate().isBefore(oldData.getStartDate())
                &&
                (
                  (newData.getEndDate().isAfter(oldData.getStartDate()) && newData.getEndDate().isBefore(oldData.getEndDate()))
                        ||
                  newData.getEndDate().isEqual(oldData.getStartDate())
                )
        );
    }

    public static boolean dateRightCrossing(DateOccupyArea newData, DateOccupyArea oldData) {
        return (
                newData.getEndDate().isAfter(oldData.getEndDate())
                &&
                (
                  (newData.getStartDate().isAfter(oldData.getStartDate()) && newData.getStartDate().isBefore(oldData.getEndDate()))
                        ||
                   newData.getStartDate().isEqual(oldData.getEndDate())
                )
        );
    }

}
