package cmc.pad.resource.application.command.point.inventory.occupy.init.contract;

import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractRepository;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker.checkActiveContractStatus;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;

/**
 * Created by fuwei on 2022/2/28.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NewContractApprovalBiz {
    private final PointLocationOccupationContractRepository contractBaseInfoRep;

    public void approve(String contractNo) {
        log.info(">>>开始激活扣减, 合同编号:{}", contractNo);
        lockTemplate(contractNo, () -> {
            PointLocationOccupationContract contractBaseInfo = contractBaseInfoRep.get(contractNo);
            checkActiveContractStatus(contractBaseInfo);
            contractBaseInfoRep.active(contractBaseInfo, ProcessStatus.SUCESS);
        });
        log.info(">>>完成激活扣减, 合同编号:{}", contractNo);
    }
}