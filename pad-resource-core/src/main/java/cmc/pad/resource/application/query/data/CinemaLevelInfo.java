package cmc.pad.resource.application.query.data;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.db.jsd.NameStyle;
import mtime.lark.db.jsd.annotation.JsdTable;

/**
 *
 *
 * <AUTHOR>
 * @Date 2019/3/15 13:32
 * @Version 1.0
 */
@Getter
@Setter
@JsdTable(nameStyle = NameStyle.LOWER)
@ToString
public class CinemaLevelInfo {
    //区域名
    private String regionName;
    //影城编码
    private String cinemaCode;
    //影城名
    private String cinemaName;
    //影城级别
    private String cinemaLevel;
    //城市名
    private String cityName;
    //城市级别
    private String cityLevel;
    //城市地区名
    private String cityDistrictName;
    //城市地区级别
    private String cityDistrictLevel;
}
