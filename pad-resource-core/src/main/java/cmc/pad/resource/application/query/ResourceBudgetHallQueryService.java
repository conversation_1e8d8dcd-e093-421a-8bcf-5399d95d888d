package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.domain.budget.ResourceBudgetHallRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @create 2022/1/28 8:52
 */
@Service
public class ResourceBudgetHallQueryService {

    @Autowired
    private ResourceBudgetHallRepository resourceBudgetHallRepository;

    public void eachAll(Consumer<List<ResourceBudgetHall>> consumer, LocalDateTime startTime, LocalDateTime endTime) {
        int page = 1;
        while (true) {
            List<ResourceBudgetHall> list = resourceBudgetHallRepository.getList(1000, page++, startTime, endTime);
            if (list.isEmpty()) {
                break;
            }
            consumer.accept(list);
        }
    }
}
