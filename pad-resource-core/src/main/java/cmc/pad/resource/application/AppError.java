package cmc.pad.resource.application;

import mtime.lark.util.lang.ErrorInfo;
import mtime.lark.util.lang.FaultException;

/**
 * <AUTHOR>
 */
public enum AppError implements ErrorInfo {
    CINEMA_NOT_EXIST(2004, "影城不存在"),
    CINEMA_NO_LEVEL(2005, "影城未评级"),
    CINEMA_LEVEL_NOT_EXIST(2006, "影城级别不存在"),
    CINEMA_LEVEL_NOT_CONFIG(2007, "影城没有对应的影城级别"),
    CITY_NO_LEVEL(3001, "城市未评级"),
    CITY_DISTRICT_NO_LEVEL(3002, "城市地区未评级"),
    CITY_LEVEL_NOT_EXIST(3006, "城市级别不存在"),
    LEASING_METHOD_NOT_EXIST(4006, "租赁方式编码不存在"),
    MOVIE_HALL_TYPE_NOT_EXIST(5006, "影厅类型编码不存在"),
    BUSINESS_TYPE_NOT_EXIST(5001, "不存在的广告业务类型"),
    DISCOUNT_FACTOR_NOT_MATCHED(6001, "没有匹配的折扣系数"),
    DISCOUNT_METHOD_NOT_SUPPORT(6002, "不支持的折扣类别"),
    DISCOUNT_BUSINESS_TYPE_NOT_NULL(6003, "广告业务类型不能为空"),
    DISCOUNT_AREA_NOT_NULL(6003, "查询面积折扣系数时，面积数不能为空"),
    DISCOUNT_DURATION_NOT_NULL(6004, "查询时长折扣系数时，时长不能为空"),
    DISCOUNT_DURATION_AREA_NOT_NULL(6005, "查询折扣系数时，时长和面积数不能同时为空"),
    OCCUPATION_NOT_EXIST(7001, "库存占用不存在"),
    OCCUPATION_DETAILS_NOT_NULL(7002, "库存占用明细不能为空且明细中参数必填"),
    MARKETING_POINT_NOT_ENOUGH(7003, "营销点位面积库存不足"),
    OUTER_AREA_NOT_ENOUGH(7004, "外租区域面积库存不足"),
    FIXED_AREA_NOT_ENOUGH(7005, "固定点位面积库存不足"),
    ADVERTSING_POINT_NOT_ENOUGH(7006, "宣传点位个数库存不足");


    private int code;
    private String message;

    AppError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public FaultException toException() {
        return FaultException.of(this);
    }

    public FaultException toException(String message) {
        return new FaultException(this.getCode(), message);
    }

    public FaultException toException(String messageFormat, Object... messageArgs) {
        return new FaultException(this.getCode(), messageFormat, messageArgs);
    }
}
