package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaQueryService {

    private final CinemaRepository cinemaRepository;

    public List<Cinema> queryAllCinema() {
        return cinemaRepository.findMany(Filter.create());
    }

    public void eachAll(Consumer<List<Cinema>> consumer) {
        int page = 1;
        while (true) {
            PageResult<Cinema> pageResult = cinemaRepository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
