package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Created by fuyuanpu on 2022/5/16.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RealOccupyData {
    Integer pointLocationId;
    DateOccupyArea dateOccupyArea;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DateOccupyArea {
        @JSONField(ordinal = 1)
        LocalDate startDate;
        @JSONField(ordinal = 2)
        LocalDate endDate;
        @JSONField(ordinal = 3)
        Float occupyArea;

        public static DateOccupyArea of(LocalDate startDate, LocalDate endDate, Float occupyArea) {
            return new DateOccupyArea(startDate, endDate, occupyArea);
        }
    }
}
