package cmc.pad.resource.application.command.point.inventory.occupy.init.contract;

import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper;
import cmc.pad.resource.domain.inventory.point.*;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker.checkCancelContractStatus;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper.InventoryOperate.REVERT;
import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_NO;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/2/3.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CancelBiz {
    private final UpdateInventoryHelper updateInventoryHelper;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;
    private final PointLocationOccupationContractDetailRepository occupationDetailRepo;

    private Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    public void checkAndCancelContract(String contractNo) {
        lockTemplate(contractNo, () -> {
            PointLocationOccupationContract contractBaseInfo = contractBaseInfoRepo.query(contractNo);
            checkCancelContractStatus(contractBaseInfo);
            Set<Integer> pointLocationIds = occupationDetailRepo.findMany(f(PLO_C_D_CONTRACT_NO, contractNo)).stream().map(occupation -> occupation.getPointLocationId()).collect(Collectors.toSet());
            lockTemplate(pointLocationIds, () -> {
                getDatabase().begin((Transaction tx) -> {
                    pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(pointLocationIds);
                    contractBaseInfoRepo.cancel(tx, contractBaseInfo);
                    pointLocationLockOperateHelper.batchLockPointLocation(tx, pointLocationIds);
                });
            });
        });
    }

    public void revertInventory(String contractNo) {
        log.info(">>>取消占用,合同编码为{}", contractNo);
        List<PointLocationOccupationContractDetail> occupyDetailList = occupationDetailRepo.findMany(Filter.create(PLO_C_D_CONTRACT_NO, contractNo));
        try {
            getDatabase().begin((Transaction tx) -> {
                log.info("步骤1 开始库存恢复, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(occupyDetailList));
                updateInventoryHelper.updateInventory(REVERT, tx, occupyDetailList);
                log.info("步骤2 库存恢复完成, contractNo:{}", contractNo);
                contractBaseInfoRepo.update(tx, contractNo, ProcessStatus.SUCESS);
                log.info("步骤3 库存取消占用合同执行完成, contractNo:{}", contractNo);
                pointLocationLockOperateHelper.batchUnLockPointLocation(tx, occupyDetailList.stream().map(occupyDetail -> occupyDetail.getPointLocationId()).collect(Collectors.toSet()));
                log.info("步骤4 解锁合同点位完成, contractNo:{}", contractNo);
            });
            log.info("恢复库存占用 成功提交事务, contractNo:{}", contractNo);
        } catch (Exception e) {
            log.error("恢复库存占用异常, contractNo:{}", contractNo, e);
            contractBaseInfoRepo.update(contractNo, ProcessStatus.FAIL);
        }
    }

//                            if (occupation.getPointLocationId() == null) { //处理没有点位占用的库存历史数据
//                                    List<Inventory> inventoryList = inventoryRepo.findMany(Filter.create("cinema_code", occupation.getCinemaCode())
//        .add("date", FilterType.GTE, occupation.getStartDate()).add("date", FilterType.LTE, occupation.getEndDate()));
//        resourceInventoryBiz.updateInventory(tx, occupation.getBusinessType(), occupation.getAmount(), inventoryList, "cancel");
}