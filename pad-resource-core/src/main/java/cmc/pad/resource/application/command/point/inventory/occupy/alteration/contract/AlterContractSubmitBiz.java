package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract;

import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.*;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealSubmitOccupyData;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker.checkProcessing;
import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;

/**
 * Created by fuwei on 2022/3/26.
 * 提交变更合同库存操作步骤
 * 1.还原上个审批版本点位库存
 * 2.重新占用本次提交实际规格的点位库存
 * - 占用状态 重新占用
 * - 新增状态 重新占用
 * - 作废状态 重新占用
 * - 更新状态
 * ** - 日期和面积扩大 重新占用
 * ** - 日期和面积缩小 占用上一版日期和面积库存
 * ** - 日期变大面积变小 占用当前变大日期和上一版面积
 * ** - 日期变小面积变大 占用上一版日期和变大的面积
 * ** - 日期是新范围 重新占用 并 在追加 占用上一版日期和面积库存
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AlterContractSubmitBiz {
    private final OccupationBuilder occupationBuilder;
    private final InventoryChecker inventoryChecker;
    private final OccupyInventoryBiz occupyInventoryBiz;
    private final PointLocationOccupationLogRepository occupationLogRepository;
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;

    private Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    public List<PointLocationOccupationContractDetail> checkAndSaveContract(String contractNo, PointLocationModel.InventoryOccupationContractParam param) {
        List<PointLocationOccupationLog> recentApprovalContractLogs = occupationLogRepository.queryRecentApprovalContractLog(contractNo);
        checkIsAlterContract(recentApprovalContractLogs);
        Map<String, PointLocationOccupationLog> recentApprovalContractLogsMap = recentApprovalContractLogs.stream().collect(Collectors.toMap(PointLocationOccupationLog::getDetailId, a -> a));

        return (List) lockTemplate(contractNo, () -> {
            PointLocationOccupationContract contractBaseInfo = contractBaseInfoRepo.query(contractNo, true);
            List<PointLocationOccupationContractDetail> occupationDetails = occupationBuilder.buildAlterContractDetailList(param, recentApprovalContractLogsMap);
            log.info(">>>提交变更合同详情:{}", JSON.toJSONString(occupationDetails, true));
            checkIdempotent(contractBaseInfo, occupationDetails);
            checkAlterDetail(occupationDetails, recentApprovalContractLogsMap);
            checkProcessing(contractBaseInfo);
            Set<Integer> pointLocationIds = occupationDetails.stream().map(PointLocationOccupationContractDetail::getPointLocationId).collect(Collectors.toSet());
            lockTemplate(pointLocationIds, () -> {
                pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(pointLocationIds);

                inventoryChecker.checkInventoryUsableArea(toAlterContractCheckOccupyData(occupationDetails));
                getDatabase().begin((Transaction tx) -> {
                    contractBaseInfo.setOccupationDetailList(occupationDetails);
                    contractBaseInfo.setContractType(ContractType.ALTER_CONTRACT);
                    contractBaseInfoRepo.reOccupyUpdate(tx, contractBaseInfo);
                    pointLocationLockOperateHelper.batchLockPointLocation(tx, pointLocationIds);
                    log.info(">>>完成变更合同提交保存, contractNo:{}", contractNo);
                });
            });
            return occupationDetails;
        });
    }

    private List<RealOccupyData> toAlterContractCheckOccupyData(List<PointLocationOccupationContractDetail> occupationDetails) {
        List<RealOccupyData> checkOccupyDataList = Lists.newArrayList();
        occupationDetails
                .stream()
                .filter(detail -> detail.getAlterStatus() == AlterStatus.ADD || detail.getAlterStatus() == AlterStatus.UPDATE)
                .forEach(detail -> {
                    AlterContractUpdateDetailRemark remark = AlterContractUpdateDetailRemark.parseJson(detail.getAlterRemark());
                    if (detail.getAlterStatus() == AlterStatus.ADD) {
                        remark.getSubmitExecData().forEach(submitExecData -> checkOccupyDataList.add(new RealOccupyData(detail.getPointLocationId(), submitExecData)));
                    }
                    if (detail.getAlterStatus() == AlterStatus.UPDATE) {
                        remark.getExpandCheckData().forEach(checkData -> checkOccupyDataList.add(new RealOccupyData(detail.getPointLocationId(), checkData)));
                    }
                });
        checkOccupyDataList.stream().forEach(checkData -> log.info(">>>检查追加的库存占用数据:{}", JSON.toJSONString(checkData)));
        return checkOccupyDataList;
    }

    public void occupyInventory(String contractNo) {
        PointLocationOccupationContract contract = contractBaseInfoRepo.query(contractNo, true);
        List<RealOccupyData> realOccupyData = toRealSubmitOccupyData(contract.getOccupationDetailList());
        Set<Integer> pointLocationIds = contract.getOccupationDetailList().stream().map(PointLocationOccupationContractDetail::getPointLocationId).collect(Collectors.toSet());
        getDatabase().begin((Transaction tx) -> {
            occupyInventoryBiz.revertLastVersionApprovalOccupy(tx, contractNo);
            occupyInventoryBiz.occupyInventory(tx, contractNo, pointLocationIds, realOccupyData);
        });
    }

    private void checkIdempotent(PointLocationOccupationContract originContractBaseInfo, List<PointLocationOccupationContractDetail> newOccupationList) {
        IdempotentChecker.check(originContractBaseInfo, newOccupationList);
    }

    private void checkAlterDetail(List<PointLocationOccupationContractDetail> occupationDetails, Map<String, PointLocationOccupationLog> occupationLogMap) {
        if (occupationDetails.size() < occupationLogMap.size())
            throw new FaultException("变更合同明细数据有丢失");
        occupationDetails.stream().filter(detail -> AlterStatus.ADD == detail.getAlterStatus()).forEach(addDetail -> {
            if (occupationLogMap.containsKey(addDetail.getDetailId()))
                throw new FaultException("uuid:" + addDetail.getDetailId() + ", pid:" + addDetail.getPointLocationId() + "不是追加明细");
        });
    }

    private void checkIsAlterContract(List<PointLocationOccupationLog> activeContractLogs) {
        if (activeContractLogs.isEmpty())
            throw new FaultException("该合同不是变更合同,不能执行此操作");
    }

}