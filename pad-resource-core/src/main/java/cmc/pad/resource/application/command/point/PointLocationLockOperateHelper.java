package cmc.pad.resource.application.command.point;

import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

import static cmc.pad.resource.domain.inventory.point.ProcessStatus.CREATE;
import static cmc.pad.resource.domain.inventory.point.ProcessStatus.PROCESSING;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;
import static mtime.lark.db.jsd.FilterType.IN;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/3/22.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationLockOperateHelper {
    private final PointLocationInfoRepository pointLocationInfoRep;

    private Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    public void batchLockPointLocation(Transaction tx, Set<Integer> pointLocationIds) {
        pointLocationInfoRep.batchUpdateInventoryProcessStatus(tx, Lists.newArrayList(pointLocationIds), PROCESSING);
    }

    public void batchUnLockPointLocation(Transaction tx, Set<Integer> pointLocationIds) {
        pointLocationInfoRep.batchUpdateInventoryProcessStatus(tx, Lists.newArrayList(pointLocationIds), ProcessStatus.SUCESS);
    }

    public void checkPointLocationInventoryIsUpdating(Integer pointLocationId) {
        checkPointLocationInventoryIsUpdating(Sets.newHashSet(pointLocationId));
    }

    public void batchLockPointLocation(Integer pointLocationId) {
        getDatabase().begin((Transaction tx) ->
                batchLockPointLocation(tx, Sets.newHashSet(pointLocationId))
        );
    }

    public void batchUnLockPointLocation(Integer pointLocationId) {
        getDatabase().begin((Transaction tx) ->
                batchUnLockPointLocation(tx, Sets.newHashSet(pointLocationId))
        );
    }

    public void checkPointLocationInventoryIsUpdating(Set<Integer> pointLocationIds) {
        pointLocationInfoRep.findMany(f("id", IN, pointLocationIds.toArray(new Integer[pointLocationIds.size()])))
                .stream()
                .filter(p -> (CREATE == p.getInventoryStatus() || PROCESSING == p.getInventoryStatus()))
                .forEach(p -> {
                    throw new FaultException("id:" + p.getId() + "点位库存在更新中, 暂时不能此操作");
                });
    }

    public void integralLock(int pointLocationId, Runnable chunk) {
        lockTemplate(String.valueOf(pointLocationId), () -> {
            checkPointLocationInventoryIsUpdating(pointLocationId);
            batchLockPointLocation(pointLocationId);
            log.info(">>>pid:{}上锁.", pointLocationId);
        });
        chunk.run();
        batchUnLockPointLocation(pointLocationId);
        log.info(">>>pid:{}解锁.", pointLocationId);
    }

}