package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.importing.FileCategory;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2019/3/15 13:18
 * @Version 1.0
 */
@Getter
@Setter
public class SaveImportCommand {
    //文件ID
    private String fileId;
    //文件名
    private String fileName;
    //文件类型
    private FileCategory fileCategory;
    //版本
    private String version;
    //生效日期
    private LocalDate effectiveDate;
    //上传人ID
    private Integer importer;
    //备注
    private String remark;
    //大区编码
    private String largeWardCode;
    //区域编码
    private String regionCode;
    //影城内码
    private String cinemaInnerCode;
}
