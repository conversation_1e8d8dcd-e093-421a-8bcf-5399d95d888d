package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.domain.inventory.OccupationRepository;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import cmc.pad.resource.domain.resource.ResourceChangeRecord;
import cmc.pad.resource.domain.resource.ResourceChangeRecordRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static cmc.pad.resource.application.DateUtil.isContainDate;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaResourceAppService {

    private final CinemaResourceRepository cinemaResourceRepository;
    private final ResourceChangeRecordRepository resourceChangeRecordRepository;
    private final InventoryRepository inventoryRepository;
    private final OccupationRepository occupationRepository;

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    public void modifyCinemaResource(UpdateCinemaResourceCommand command) {
        Optional<CinemaResource> optional = cinemaResourceRepository.findOne(Filter.create("cinema_code", command.getCinemaCode()));
        if (optional.isPresent()) {
            CinemaResource resource = optional.get();
            if (resource.getAdvertisingPointLeasableQuantity().equals(command.getAdvertisingPointLeasableQuantity())
                    && resource.getFixedPointLeasableArea().equals(command.getFixedPointLeasableArea())
                    && resource.getMarketingPointLeasableArea().equals(command.getMarketingPointLeasableArea())
                    && resource.getOuterAreaLeasableArea().equals(command.getOuterAreaLeasableArea())) {
                return;
            }
        }
        getDatabase().begin((Transaction tx) -> {
            //1、更新影城资源记录
            cinemaResourceRepository.updateCinemaResourceByCode(tx, command.getCinemaCode(),
                    command.getMarketingPointLeasableArea(), command.getOuterAreaLeasableArea(),
                    command.getFixedPointLeasableArea(), command.getAdvertisingPointLeasableQuantity());
            //2、更新操作时间之后的所有业务类型的库存
            updateInventoryAfterModifyResource(tx, command);
            //3、添加资源变更记录
            ResourceChangeRecord resourceChangeRecord = new ResourceChangeRecord();
            resourceChangeRecord.setCinemaCode(command.getCinemaCode());
            resourceChangeRecord.setAdvertisingPointLeasableQuantity(command.getAdvertisingPointLeasableQuantity());
            resourceChangeRecord.setFixedPointLeasableArea(command.getFixedPointLeasableArea());
            resourceChangeRecord.setMarketingPointLeasableArea(command.getMarketingPointLeasableArea());
            resourceChangeRecord.setOuterAreaLeasableArea(command.getOuterAreaLeasableArea());
            resourceChangeRecord.setUpdateTime(LocalDateTime.now());
            resourceChangeRecord.setUpdator(command.getUpdator());
            resourceChangeRecordRepository.saveResourceChangeRecord(tx, resourceChangeRecord);
        });
    }

    private void updateInventoryAfterModifyResource(Transaction tx, UpdateCinemaResourceCommand command) {
        LocalDate now = LocalDate.now();
        Filter filter = Filter.create("cinema_code", command.getCinemaCode());
        filter = filter.and(f("date", FilterType.GTE, now));
        List<Inventory> inventoryList = inventoryRepository.findMany(filter);
        List<Occupation> occupationList = occupationRepository.findMany(Filter.create("cinema_code", command.getCinemaCode())
                .add("status", 1).add("end_date", FilterType.GTE, now));
        List<Inventory> toModified = new ArrayList<>();
        for (Inventory inventory : inventoryList) {
            inventory.setTotalMarketingPointLeasableArea(command.getMarketingPointLeasableArea());
            inventory.setTotalOuterAreaLeasableArea(command.getOuterAreaLeasableArea());
            inventory.setTotalFixedPointLeasableArea(command.getFixedPointLeasableArea());
            inventory.setTotalAdvertisingPointLeasableQuantity(command.getAdvertisingPointLeasableQuantity());
            inventory.setSoldMarketingPointLeasableArea(command.getMarketingPointLeasableArea());
            inventory.setSoldOuterAreaLeasableArea(command.getOuterAreaLeasableArea());
            inventory.setSoldFixedPointLeasableArea(command.getFixedPointLeasableArea());
            inventory.setSoldAdvertisingPointLeasableQuantity(command.getAdvertisingPointLeasableQuantity());
            if (CollectionUtils.isNotEmpty(occupationList)) {
                Float hasSoldMarketingPoint = 0f;
                Float hasSoldFixedPoint = 0f;
                Float hasSoldOuterArea = 0f;
                Integer hasSoldAdvertisingPoint = 0;
                for (Occupation occupation : occupationList) {
                    if (isContainDate(inventory.getDate(), occupation.getStartDate(), occupation.getEndDate())) {
                        if ("YX".equalsIgnoreCase(occupation.getBusinessType())) {
                            hasSoldMarketingPoint = hasSoldMarketingPoint + occupation.getAmount();
                        } else if ("GD".equalsIgnoreCase(occupation.getBusinessType())) {
                            hasSoldFixedPoint = hasSoldFixedPoint + occupation.getAmount();
                        } else if ("WZ".equalsIgnoreCase(occupation.getBusinessType())) {
                            hasSoldOuterArea = hasSoldOuterArea + occupation.getAmount();
                        } else if ("XC".equalsIgnoreCase(occupation.getBusinessType())) {
                            hasSoldAdvertisingPoint = hasSoldAdvertisingPoint + occupation.getAmount().intValue();
                        }
                    }
                }
                inventory.setSoldMarketingPointLeasableArea(inventory.getTotalMarketingPointLeasableArea() - hasSoldMarketingPoint);
                inventory.setSoldFixedPointLeasableArea(inventory.getTotalFixedPointLeasableArea() - hasSoldFixedPoint);
                inventory.setSoldOuterAreaLeasableArea(inventory.getTotalOuterAreaLeasableArea() - hasSoldOuterArea);
                inventory.setSoldAdvertisingPointLeasableQuantity(inventory.getTotalAdvertisingPointLeasableQuantity() - hasSoldAdvertisingPoint);
            }
            toModified.add(inventory);
        }
        if (toModified.size() > 0) {
            inventoryRepository.batchInsert(tx, toModified);
        }
    }
}
