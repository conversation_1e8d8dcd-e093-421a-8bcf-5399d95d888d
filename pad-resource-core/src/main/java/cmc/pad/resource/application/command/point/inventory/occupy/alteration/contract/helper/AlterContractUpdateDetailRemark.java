package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by fuyuanpu on 2022/5/12.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlterContractUpdateDetailRemark {
    public static final String OCCUPY_ = "occupy";
    public static final String ADD_ = "add";
    public static final String DESTROY_ = "destroy";
    public static final String EXPAND_ALL = "expand all";
    public static final String SHRINK_ALL = "shrink all;";
    public static final String EXPAND_DATE_RANGE_SHRINK_AREA = "expand date range shrink area";
    public static final String EXPAND_AREA_SHRINK_DATE_RANGE = "expand area shrink date range";
    public static final String EXPAND_NEW_DATE_RANGE_AND_AREA = "expand new date range and area";
    public static final String CROSS_DATE_RANGE = "cross date range";
    private String desc;
    private List<RealOccupyData.DateOccupyArea> submitExecData = Lists.newArrayList();
    private List<RealOccupyData.DateOccupyArea> expandCheckData = Lists.newArrayList();

    public AlterContractUpdateDetailRemark(String desc) {
        this.desc = desc;
    }

    public static AlterContractUpdateDetailRemark of(String desc, RealOccupyData.DateOccupyArea... submitExecDataList) {
        return new AlterContractUpdateDetailRemark(desc, Lists.newArrayList(submitExecDataList), Lists.newArrayList());
    }

    public String toJson() {
        return JSON.toJSONString(this, SerializerFeature.DisableCircularReferenceDetect);
    }

    public static AlterContractUpdateDetailRemark parseJson(String json) {
        return JSONObject.parseObject(json, AlterContractUpdateDetailRemark.class);
    }

    public static void main(String[] args) {
        System.out.println(parseJson(null));
//        System.out.println(parseJson("{\"desc\":\"destroy\",\"submitExecData\":[{\"startDate\":\"2022-05-17\",\"endDate\":\"2022-05-20\",\"occupyArea\":10.0}]}"));
//
//
//        System.out.println(AlterContractUpdateDetailRemark.of(EXPAND_ALL,
//                RealOccupyData.DateOccupyArea.of(LocalDate.now(), LocalDate.now().plusDays(1), 10f),
//                RealOccupyData.DateOccupyArea.of(LocalDate.now(), LocalDate.now().plusDays(2), 20f)
//                )
//                        .toJson()
//        );
    }
}