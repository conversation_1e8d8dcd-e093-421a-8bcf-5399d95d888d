package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.MovieHallNamingQuery;
import cmc.pad.resource.domain.price.MovieHallNamingLeasingPriceRepository;
import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MovieHallNamingLeasingPriceQueryService {
    private final MovieHallNamingLeasingPriceRepository repository;

    public PageResult<MovieHallNamingPrice> effectivePage(MovieHallNamingQuery query) {
        LocalDate latestEffectiveDate = repository.latestEffectiveDate();
        if (Objects.isNull(latestEffectiveDate)) {
            return new PageResult<>();
        }
        Filter filter = f(MovieHallNamingPrice.C_EFFECTIVE_DATE, latestEffectiveDate);
        String cinemaCode = query.getCinemaCode();
        if (StringUtils.isNotBlank(cinemaCode) && !"0".equals(cinemaCode)) {
            filter = filter.and(f(MovieHallNamingPrice.C_CINEMA_CODE, cinemaCode));
        }
        String movieHallType = query.getMovieHallType();
        if (StringUtils.isNotBlank(movieHallType) && !"0".equals(movieHallType)) {
            filter = filter.and(f(MovieHallNamingPrice.C_MOVIE_HALL_TYPE, movieHallType));
        }
        return repository.findPage(filter, query.getPageSize(), query.getPageIndex());
    }


    public List<MovieHallNamingPrice> list(String cinemaCode, String movieHallType) {
        return repository.findMany(f(MovieHallNamingPrice.C_CINEMA_CODE, cinemaCode).add(MovieHallNamingPrice.C_MOVIE_HALL_TYPE, movieHallType));
    }

    public LocalDate latestEffectiveDate() {
        return repository.latestEffectiveDate();
    }

    public PageResult<MovieHallNamingPrice> effectivePageBack(MovieHallNamingQuery query) {
        LocalDate latestEffectiveDate = repository.latestEffectiveDate();
        if (Objects.isNull(latestEffectiveDate)) {
            return new PageResult<>();
        }
        Filter filter = f(MovieHallNamingPrice.C_EFFECTIVE_DATE, latestEffectiveDate);
        String cinemaCode = query.getCinemaCode();
        if (StringUtils.isNotBlank(cinemaCode) && !"0".equals(cinemaCode)) {
            filter = filter.and(f(MovieHallNamingPrice.C_CINEMA_CODE, cinemaCode));
        }
        filter = filter.and(f(MovieHallNamingPrice.C_MOVIE_HALL_TYPE, ""));
        return repository.findPage(filter, query.getPageSize(), query.getPageIndex());
    }

    public void eachAll(Consumer<List<MovieHallNamingPrice>> consumer) {
        int page = 1;
        while (true) {
            PageResult<MovieHallNamingPrice> pageResult = repository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
