package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.OccupyException;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.OccupyException.CONTRACT_IDEMPOTENT_DIFF;
import static cmc.pad.resource.application.command.point.inventory.occupy.OccupyException.CONTRACT_IDEMPOTENT_SAME;

/**
 * Created by fuwei on 2022/3/28.
 */
@Slf4j
public class IdempotentChecker {
    private IdempotentChecker() {
    }

    //幂等性检查
    public static void check(PointLocationOccupationContract originContractBaseInfo, List<PointLocationOccupationContractDetail> newOccupationList) {
        List<PointLocationOccupationContractDetail> originOccupationList = originContractBaseInfo.getOccupationDetailList();
        boolean detailListSizeSame = (newOccupationList.size() == originOccupationList.size());
        Set<PointLocationOccupationContractDetail> newOccupyDetailSet = newOccupationList.stream().collect(Collectors.toSet());
        List<PointLocationOccupationContractDetail> diffOccupyDetails = originOccupationList.stream().filter(originOccupyDetail -> !newOccupyDetailSet.contains(originOccupyDetail)).collect(Collectors.toList());
        log.info(">>>{}幂等校验, detailListSizeSame:{}, diff size:{}", newOccupationList.get(0).getContractNo(), detailListSizeSame, diffOccupyDetails.size());
        if (ContractStatus.SUBMIT == originContractBaseInfo.getStatus()) {
            if (detailListSizeSame && diffOccupyDetails.isEmpty())
                throw new OccupyException(CONTRACT_IDEMPOTENT_SAME);
            else
                throw new OccupyException(CONTRACT_IDEMPOTENT_DIFF);
        }
    }
}
