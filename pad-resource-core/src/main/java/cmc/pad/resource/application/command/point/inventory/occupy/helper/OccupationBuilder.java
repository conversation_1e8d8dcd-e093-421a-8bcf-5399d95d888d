package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark;
import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.DateCrossUpdateInventoryCalculation;
import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.NewDateUpdateInventoryCalculation;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark.*;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterDataChecker.*;
import static cmc.pad.resource.domain.inventory.point.AlterStatus.*;

/**
 * Created by fuyuanpu on 2022/2/26.
 */
@Slf4j
@Component
public class OccupationBuilder {
    public List<PointLocationOccupationContractDetail> buildNewContractDetailList(PointLocationModel.InventoryOccupationContractParam param) {
        return param.getDetails()
                .stream()
                .map(paramDetail -> {
                    if (paramDetail.getStatus() != 1)
                        throw new FaultException("提交新合同明细id:" + paramDetail.getId() + "占用状态错误");
                    if (paramDetail.getAmount() <= 0)
                        throw new FaultException("提交新合同明细id:" + paramDetail.getId() + "占用面积" + paramDetail.getAmount() + "错误");
                    return buildOccupation(param.getContractNo(), param.getBusinessTypeCode(), param.getContractType(), paramDetail, null);
                })
                .collect(Collectors.toList());
    }

    public List<PointLocationOccupationContractDetail> buildAlterContractDetailList(PointLocationModel.InventoryOccupationContractParam param, Map<String, PointLocationOccupationLog> recentApprovalContractLogsMap) {
        return param.getDetails()
                .stream()
                .map(paramDetail -> {
                    PointLocationOccupationLog recentApprovalDetailLog = recentApprovalContractLogsMap.get(paramDetail.getId());
                    if (paramDetail.getStatus() == 3)
                        paramDetail.setStatus(4);
                    check(paramDetail, recentApprovalDetailLog);
                    return buildOccupation(param.getContractNo(),
                            param.getBusinessTypeCode(),
                            param.getContractType(),
                            paramDetail,
                            getRealSubmitExecDataRemark(paramDetail, recentApprovalDetailLog));
                })
                .collect(Collectors.toList());
    }

    private void check(PointLocationModel.InventoryOccupationContractParam.OccupationDetail paramDetail, PointLocationOccupationLog recentApprovalDetailLog) {
        if (paramDetail.getAmount() <= 0)
            throw new FaultException("提交变更合同明细id:" + paramDetail.getId() + "占用面积" + paramDetail.getAmount() + "错误");
        AlterStatus newAlterStatus = valueOf(paramDetail.getStatus());
        if ((OCCUPY == newAlterStatus || DESTROY == newAlterStatus) && recentApprovalDetailLog == null) {
            throw new FaultException("变更合同提交的 " + newAlterStatus.name() + " 明细(id:" + paramDetail.getId() + ")数据点位id:" + paramDetail.getPointLocationId() + "，在上一版本未找到");
        }
        if (ADD == newAlterStatus && recentApprovalDetailLog != null) {
            throw new FaultException("变更合同提交的 " + newAlterStatus.name() + " 明细(id:" + paramDetail.getId() + ")数据点位id:" + paramDetail.getPointLocationId() + "，在上一版本存在，不是新加明细");
        }
        if (DESTROY == newAlterStatus) {
            RealOccupyData.DateOccupyArea originDetail = new RealOccupyData.DateOccupyArea(recentApprovalDetailLog.getStartDate(), recentApprovalDetailLog.getEndDate(), recentApprovalDetailLog.getAmount());
            RealOccupyData.DateOccupyArea newDetail = toDateOccupyArea(paramDetail);
            if (!dateRangeIsEqual(newDetail, originDetail)) {
                throw new FaultException("点位id:" + paramDetail.getPointLocationId() + "作废  明细(id:" + paramDetail.getId() + ")数据 占用时间 和 上一版本" + originDetail.getStartDate() + " - " + originDetail.getEndDate() + "不一致");
            }
            if (!areaIsEqual(newDetail, originDetail)) {
                throw new FaultException("点位id:" + paramDetail.getPointLocationId() + "作废  明细(id:" + paramDetail.getId() + ")数据 占用面积 和 上一版本" + originDetail.getOccupyArea() + "不一致");
            }
        }
    }

    /**
     * - 占用状态 重新占用
     * - 新增状态 重新占用
     * - 作废状态 重新占用
     * - 更新状态
     * ** - 日期和面积扩大 重新占用
     * ** - 日期和面积缩小 占用上一版日期和面积库存
     * ** - 日期变大面积变小 占用当前变大日期和上一版面积
     * ** - 日期变小面积变大 占用上一版日期和变大的面积
     * ** - 日期是新范围 重新占用 并 在追加 占用上一版日期和面积库存
     */
    private String getRealSubmitExecDataRemark(PointLocationModel.InventoryOccupationContractParam.OccupationDetail paramDetail, PointLocationOccupationLog recentApprovalDetailLog) {
        if (ADD == AlterStatus.valueOf(paramDetail.getStatus())) {
            return AlterContractUpdateDetailRemark.of(ADD_, toDateOccupyArea(paramDetail)).toJson();
        }
        if (DESTROY == AlterStatus.valueOf(paramDetail.getStatus())) {
            if (DESTROY == recentApprovalDetailLog.getAlterStatus())//上个审核版本作废就不做处理了
                return null;
            return AlterContractUpdateDetailRemark.of(DESTROY_, toDateOccupyArea(paramDetail)).toJson();
        }
        if (OCCUPY == AlterStatus.valueOf(paramDetail.getStatus())) {//如果是占用，要区分是不变的和变更的
            String remark = AlterContractUpdateDetailRemark.of(OCCUPY_, toDateOccupyArea(paramDetail)).toJson();
            RealOccupyData.DateOccupyArea originDetail = new RealOccupyData.DateOccupyArea(recentApprovalDetailLog.getStartDate(), recentApprovalDetailLog.getEndDate(), recentApprovalDetailLog.getAmount());
            RealOccupyData.DateOccupyArea newDetail = toDateOccupyArea(paramDetail);
            if (!(dateRangeIsEqual(newDetail, originDetail) && areaIsEqual(newDetail, originDetail))) {
                paramDetail.setStatus(AlterStatus.UPDATE.value());
                remark = remarkAlertContractDetailUpdateData(newDetail, originDetail);
            }
            return remark;
        }
        throw new FaultException("设置实际提交执行的占用数据备注异常，点位占用状态值错误：status=" + paramDetail.getStatus());
    }

    private RealOccupyData.DateOccupyArea toDateOccupyArea(PointLocationModel.InventoryOccupationContractParam.OccupationDetail paramDetail) {
        return RealOccupyData.DateOccupyArea.of(paramDetail.getStartDate(), paramDetail.getEndDate(), paramDetail.getAmount());
    }

    public String remarkAlertContractDetailUpdateData(RealOccupyData.DateOccupyArea newDetail, RealOccupyData.DateOccupyArea originDetail) {
        if (isExpand(newDetail, originDetail)) {
            AlterContractUpdateDetailRemark remark = of(EXPAND_ALL, newDetail);
            List<RealOccupyData.DateOccupyArea> expandCheckDataLst = remark.getExpandCheckData();
            expandDateRange(newDetail, originDetail).stream().forEach(expandDateRange ->
                    expandCheckDataLst.add(RealOccupyData.DateOccupyArea.of(expandDateRange.getStart(), expandDateRange.getEnd(), newDetail.getOccupyArea()))
            );
            Float expandArea = expandArea(newDetail.getOccupyArea(), originDetail.getOccupyArea());
            if (expandArea > 0)
                expandCheckDataLst.add(RealOccupyData.DateOccupyArea.of(originDetail.getStartDate(), originDetail.getEndDate(), expandArea));
            return remark.toJson();
        }

        if (isShrink(newDetail, originDetail))
            return AlterContractUpdateDetailRemark.of(SHRINK_ALL, originDetail).toJson();

        if (dateRangeIsExpandButAreaIsShrink(newDetail, originDetail)) {
            AlterContractUpdateDetailRemark remark = new AlterContractUpdateDetailRemark(EXPAND_DATE_RANGE_SHRINK_AREA);

            List<RealOccupyData.DateOccupyArea> submitExecData = remark.getSubmitExecData();
            submitExecData.add(RealOccupyData.DateOccupyArea.of(originDetail.getStartDate(), originDetail.getEndDate(), originDetail.getOccupyArea()));

            List<RealOccupyData.DateOccupyArea> expandCheckDataLst = remark.getExpandCheckData();
            expandDateRange(newDetail, originDetail).stream().forEach(expandDateRange -> {
                RealOccupyData.DateOccupyArea expandDateAndArea = RealOccupyData.DateOccupyArea.of(expandDateRange.getStart(), expandDateRange.getEnd(), newDetail.getOccupyArea());
                submitExecData.add(expandDateAndArea);
                expandCheckDataLst.add(expandDateAndArea);
            });
            return remark.toJson();
        }

        if (dateRangeIsShrinkButAreaIsExpand(newDetail, originDetail)) {
            AlterContractUpdateDetailRemark remark = new AlterContractUpdateDetailRemark(EXPAND_AREA_SHRINK_DATE_RANGE);
            List<RealOccupyData.DateOccupyArea> submitExecData = remark.getSubmitExecData();
            submitExecData.add(RealOccupyData.DateOccupyArea.of(newDetail.getStartDate(), newDetail.getEndDate(), newDetail.getOccupyArea()));
            expandDateRange(originDetail, newDetail).stream().forEach(expandDateRange -> {
                RealOccupyData.DateOccupyArea expandDateAndArea = RealOccupyData.DateOccupyArea.of(expandDateRange.getStart(), expandDateRange.getEnd(), originDetail.getOccupyArea());
                submitExecData.add(expandDateAndArea);
            });
            Float expandArea = expandArea(newDetail.getOccupyArea(), originDetail.getOccupyArea());
            remark.getExpandCheckData().add(RealOccupyData.DateOccupyArea.of(newDetail.getStartDate(), newDetail.getEndDate(), expandArea));
            return remark.toJson();
        }

        if (dateLeftCrossing(newDetail, originDetail) || dateRightCrossing(newDetail, originDetail)) {
            return DateCrossUpdateInventoryCalculation.exec(newDetail, originDetail).toJson();
        }

        if (dateRangeIsNew(newDetail, originDetail)) {
            return NewDateUpdateInventoryCalculation.exec(newDetail, originDetail).toJson();
        }
        throw new FaultException("变更合同修改明细数据有错");
    }


    private PointLocationOccupationContractDetail buildOccupation(
            String contractNo, String businessTypeCode, int contractType,
            PointLocationModel.InventoryOccupationContractParam.OccupationDetail paramDetail, String remark) {
        PointLocationOccupationContractDetail contractDetail = new PointLocationOccupationContractDetail();
        contractDetail.setCinemaCode(paramDetail.getCinemaInnerCode());
        contractDetail.setContractNo(contractNo);
        contractDetail.setContractType(ContractType.valueOf(contractType));
        contractDetail.setBusinessType(businessTypeCode);
        contractDetail.setPointLocationId(paramDetail.getPointLocationId());
        contractDetail.setAmount(paramDetail.getAmount());
        contractDetail.setStartDate(paramDetail.getStartDate());
        contractDetail.setEndDate(paramDetail.getEndDate());
        contractDetail.setContractStatus(ContractStatus.SUBMIT);
        contractDetail.setDetailId(paramDetail.getId());
        contractDetail.setAlterStatus(AlterStatus.valueOf(paramDetail.getStatus()));
        LocalDateTime now = LocalDateTime.now();
        contractDetail.setCreateTime(now);
        contractDetail.setUpdateTime(now);
        contractDetail.setAlterRemark(remark);
        return contractDetail;
    }
}
