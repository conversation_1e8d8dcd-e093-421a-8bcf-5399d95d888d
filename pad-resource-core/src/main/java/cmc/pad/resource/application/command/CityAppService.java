package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.city.CityAndDistrictSyncDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CityAppService {

    private final CityAndDistrictSyncDomainService citySyncDomainService;

    public void synchronizeCities(String initCityDistrictLevel) {
        citySyncDomainService.synchronize(initCityDistrictLevel);
    }
}
