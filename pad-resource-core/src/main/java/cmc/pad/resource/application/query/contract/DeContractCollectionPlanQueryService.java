package cmc.pad.resource.application.query.contract;

import cmc.pad.resource.domain.contract.DeContractCollectionPlan;
import cmc.pad.resource.domain.contract.DeContractCollectionPlanRepository;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.BasicFilter;
import mtime.lark.db.jsd.Sorters;
import mtime.lark.util.data.PageInfo;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static mtime.lark.db.jsd.FilterType.*;
import static mtime.lark.db.jsd.Shortcut.f;
import static mtime.lark.db.jsd.SortType.ASC;

/**
 * Created by fuwei on 2024/3/15.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DeContractCollectionPlanQueryService {
    private final DeContractCollectionPlanRepository deContractCollectionPlanRepository;

    public PageResult<DeContractCollectionPlan> pageList(String largeAreaCode, String areaCode, String operator, String customer,
                                                         String contractNo, int contractState, int delayState,
                                                         LocalDateTime start, LocalDateTime end, PageInfo pageInfo) {
        log.info(">>>逾期合同收款计划列表, param:largeAreaCode = " + largeAreaCode + ", areaCode = " + areaCode + ", operator = " + operator + ", customer = " + customer + ", contractNo = " + contractNo + ", contractState = " + contractState + ", delayState = " + delayState + ", start = " + start + ", end = " + end + ", pageInfo = " + pageInfo);
        BasicFilter f = f();
        if (!Strings.isNullOrEmpty(largeAreaCode) && !"0".equals(largeAreaCode))
            f.add("large_ward_code", IN, largeAreaCode.split(","));
        if (!"-1".equals(areaCode))
            f.add("area_code", areaCode);
        if (StringUtils.isNotBlank(operator))
            f.add("operator_name", operator);
        if (StringUtils.isNotBlank(customer))
            f.add("customer_name", LK, customer);
        if (contractState != -1)
            f.add("contract_state", contractState);
        if (delayState != -1)
            f.add("overdue_state", delayState);
        if (StringUtils.isNotBlank(contractNo))
            f.add("contract_no", contractNo);
        if (start != null)
            f.add("plan_collection_date", GTE, start);
        if (end != null)
            f.add("plan_collection_date", LTE, end);
        return deContractCollectionPlanRepository.findPage(f, new Sorters(ASC, "update_time").add(ASC, "contract_no").add(ASC, "plan_collection_date"), pageInfo.getPageSize(), pageInfo.getPageIndex());
    }
}