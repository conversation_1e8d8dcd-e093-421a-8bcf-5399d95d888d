package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.domain.inventory.point.OccupyOrCancelParam;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.msg.Publisher;

import static cmc.pad.resource.constant.TopicName.POINT_LOCATION_INVENTORY_OCCUPY_OR_CANCEL_TOPIC;

/**
 * Created by fuy<PERSON><PERSON> on 2022/5/26.
 */
@Slf4j
public class PublishUtil {
    private PublishUtil() {
    }

    public static void send(OccupyOrCancelParam param) {
        try {
            log.info(">>>发送点位占用库存消息, msg={}", JSON.toJSONString(param, true));
            Publisher.get().publish(POINT_LOCATION_INVENTORY_OCCUPY_OR_CANCEL_TOPIC, param);
        } catch (Exception e) {
            log.error(">>>发送点位占用库存消息异常, msg={}", JSON.toJSONString(param, true), e);
        }
    }

    public static void send(String topic, Object param) {
        try {
            log.info(">>>发送{}消息, msg={}", topic, JSON.toJSONString(param, true));
            Publisher.get().publish(topic, param);
        } catch (Exception e) {
            log.error(">>>发送{}消息异常, msg={}", topic, JSON.toJSONString(param, true), e);
        }
    }
}
