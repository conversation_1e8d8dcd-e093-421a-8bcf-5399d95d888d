package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract;

import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import cmc.pad.resource.domain.inventory.point.AlterStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by fuyuanpu on 2022/5/16.
 */
@Slf4j
public class AlterContractRealSubmitOccupyDataTransfer {
    public static RealOccupyData toRealSubmitOccupyData(PointLocationOccupationContractDetail occupationDetail) {
        return toRealSubmitOccupyData(Lists.newArrayList(occupationDetail)).stream().findFirst().orElse(null);
    }

    public static List<RealOccupyData> toRealSubmitOccupyData(List<PointLocationOccupationContractDetail> occupationDetails) {
        List<RealOccupyData> realOccupyDataList = Lists.newArrayList();
        occupationDetails.stream().forEach(detail -> {
            AlterContractUpdateDetailRemark alterContractUpdateDetailRemark = AlterContractUpdateDetailRemark.parseJson(detail.getAlterRemark());
            if (alterContractUpdateDetailRemark != null) {
                alterContractUpdateDetailRemark.getSubmitExecData().forEach(submitExecData -> {
                    realOccupyDataList.add(new RealOccupyData(detail.getPointLocationId(), submitExecData));
                });
            }
        });
        return realOccupyDataList;
    }

    public static RealOccupyData toRealOccupyData(PointLocationOccupationContractDetail occupationDetail) {
        return toRealOccupyData(Lists.newArrayList(occupationDetail)).stream().findFirst().orElse(null);
    }

    public static List<RealOccupyData> toRealOccupyData(List<PointLocationOccupationContractDetail> occupationDetails) {
        return occupationDetails
                .stream()
                .filter(detail -> AlterStatus.DESTROY != detail.getAlterStatus())
                .map(detail ->
                        new RealOccupyData(detail.getPointLocationId(), RealOccupyData.DateOccupyArea.of(detail.getStartDate(), detail.getEndDate(), detail.getAmount()))
                ).collect(Collectors.toList());
    }

    public static void main(String[] args) {
        System.out.println(Lists.newArrayList().stream().findFirst().orElse(null));
    }
}