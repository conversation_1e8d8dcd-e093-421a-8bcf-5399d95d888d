package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.AdvertisingPointLeasingPrice;
import cmc.pad.resource.domain.price.AdvertisingPointLeasingPriceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AdvertisingPointLeasingAppService {
    private final AdvertisingPointLeasingPriceRepository repository;

    public void importData(List<AdvertisingPointLeasingPrice> list) {
        repository.batchInsert(list);
    }

    public void discard(int importId) {
        repository.delete(f(AdvertisingPointLeasingPrice.C_IMPORT_ID, importId));
    }
}
