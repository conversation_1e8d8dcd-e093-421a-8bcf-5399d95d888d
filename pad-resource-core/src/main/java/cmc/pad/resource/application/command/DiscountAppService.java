package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.discount.*;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import mtime.lark.db.jsd.*;
import mtime.lark.util.lang.FaultException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountAppService {

    private final DiscountRepository discountRepository;
    private final DiscountRuleRepository discountRuleRepository;

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    public void saveDiscount(SaveDiscountCommand command) {
        validateDiscountRule(command.getDiscountRules());//验证是否存在重叠的规则
        if (command.getId() != null && command.getId() > 0) {
            updateDiscount(command);//修改折扣系数
        } else {
            addDiscount(command);//添加折扣系数
        }
    }

    private void addDiscount(SaveDiscountCommand command) {
        List<Discount> discounts = discountRepository.findMany(Filter.create("business_type", command.getBusinessType())
                .add("discount_type", command.getDiscountType()));
        if (!CollectionUtils.isEmpty(discounts)) {
            throw new FaultException(BusinessType.valueOf(command.getBusinessType()).displayName() + "已经存在" + DiscountType.valueOf(command.getDiscountType()).displayName());
        }
        Discount discount = new Discount();
        discount.setBusinessType(command.getBusinessType());
        discount.setDiscountType(DiscountType.valueOf(command.getDiscountType()));
        LocalDateTime now = LocalDateTime.now();
        discount.setCreator(command.getUpdator());
        discount.setCreateTime(now);
        discount.setUpdator(command.getUpdator());
        discount.setUpdateTime(now);
        getDatabase().begin((Transaction tx) -> {
            //1：保存折扣基本信息
            int discountId = discountRepository.insertDiscount(tx, discount);
            if (discountId > 0) {
                List<DiscountRule> discountRules = new ArrayList<>();
                command.getDiscountRules().forEach(rule -> {
                    DiscountRule discountRule = new DiscountRule();
                    discountRule.setComparisonSymbol(rule.getComparisonSymbol());
                    discountRule.setDiscountId(discountId);
                    discountRule.setMin(rule.getMin());
                    discountRule.setMax(rule.getMax());
                    discountRule.setFactor(rule.getFactor());
                    discountRules.add(discountRule);
                });
                discountRuleRepository.batchInsert(tx, discountRules);
            }
        });
    }

    private void updateDiscount(SaveDiscountCommand command) {
        Optional<Discount> optionalDiscount = discountRepository.findOne(Filter.create("id", command.getId()));
        if (optionalDiscount.isPresent()) {
            List<DiscountRule> list = command.getDiscountRules();
            List<DiscountRule> saveList = new ArrayList<>();
            List<Integer> ruleIds = new ArrayList<>();
            list.forEach(rule -> {
                DiscountRule discountRule = new DiscountRule();
                BeanUtils.copyProperties(rule, discountRule);
                discountRule.setDiscountId(command.getId());
                saveList.add(discountRule);
                if (rule.getId() != null) {
                    ruleIds.add(rule.getId());//记录在修改时未删除的折扣规则ID
                }
            });
            getDatabase().begin((Transaction tx) -> {
                if (CollectionUtils.isEmpty(ruleIds)) {
                    Filter filter = Filter.create("discount_id", command.getId());
                    tx.delete("discount_rule").where(filter).result();
                } else {
                    //删除修改折扣时，删除的那些折扣规则
                    Filter filter = Filter.create("id", FilterType.NIN, listToString(ruleIds)).add("discount_id", command.getId());
                    tx.delete("discount_rule").where(filter).result();
                }
                Discount discount = optionalDiscount.get();
                discount.setUpdateTime(LocalDateTime.now());
                discount.setUpdator(command.getUpdator());
                int row = this.discountRepository.updateDiscount(tx, discount);
                if (row == 2) {
                    discountRuleRepository.batchInsert(tx, saveList);
                }
            });

        }
    }

    private void validateDiscountRule(List<DiscountRule> rules) {
        if (rules.size() > 1) {
            List<DiscountRule> getList = new ArrayList<>();
            List<DiscountRule> letList = new ArrayList<>();
            rules.forEach(rule -> {
                if (rule.getComparisonSymbol() == 3) {
                    letList.add(rule);
                }
                if (rule.getComparisonSymbol() == 4) {
                    getList.add(rule);
                }
            });
            if (letList.size() > 1 || getList.size() > 1) {
                throw new FaultException(">=或者<=的折扣规则至多允许存在1个");
            }
            List<DiscountRule> validatedList = new ArrayList<>(rules.size());
            boolean flag = false;
            for (DiscountRule rule : rules) {
                for (DiscountRule discountRule : validatedList) {
                    boolean conflict = comparedRange(convertToPojo(rule), convertToPojo(discountRule));
                    if (conflict) {
                        flag = true;
                    }
                }
                validatedList.add(rule);
            }
            if (flag) {
                throw new FaultException("存在重叠的规则");
            }
        }

    }

    private boolean comparedRange(ComparedPojo pojo1, ComparedPojo pojo2) {
        float max, min;
        float start1 = Float.parseFloat(pojo1.getStart());
        float end1 = Float.parseFloat(pojo1.getEnd());
        float start2 = Float.parseFloat(pojo2.getStart());
        float end2 = Float.parseFloat(pojo2.getEnd());
        max = start1 > start2 ? start1 : start2;
        min = end1 < end2 ? end1 : end2;
        boolean result = max <= min;//true  闭区间冲突，开区间不一定冲突
        if (result) {
            if (start1 < start2 && pojo1.getType()) {
                if (end1 <= start2) {
                    result = false;
                }
            } else if (start1 > start2 && pojo2.getType()) {
                if (end2 == start1) {
                    result = false;
                }
            }
        }
        return result;

    }

    private ComparedPojo convertToPojo(DiscountRule rule) {
        ComparedPojo pojo = new ComparedPojo();
        Integer symbol = rule.getComparisonSymbol();
        if (symbol == 1) {    //"区间"
            pojo.setStart(rule.getMin().toString());
            pojo.setEnd(rule.getMax().toString());
            pojo.setType(true);
        } else if (symbol == 2) {   //"=="
            pojo.setStart(rule.getMin().toString());
            pojo.setEnd(rule.getMin().toString());
            pojo.setType(false);
        } else if (symbol == 3) {   //"<="
            pojo.setStart("0");
            pojo.setEnd(rule.getMin().toString());
            pojo.setType(false);
        } else if (symbol == 4) {   //">="
            pojo.setStart(rule.getMin().toString());//"区间"
            pojo.setEnd(String.valueOf(Float.MAX_VALUE));
            pojo.setType(true);
        }
        return pojo;
    }

    private String listToString(List list) {
        StringBuilder builder = new StringBuilder();
        if (!CollectionUtils.isEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                builder.append(list.get(i));
                if (i < list.size() - 1) {
                    builder.append(",");
                }
            }
        }
        return builder.toString();
    }

    public void deleteDiscount(Integer id) {
        getDatabase().begin((Transaction tx) -> {
            int row = tx.delete("discount").where(Filter.create("id", id)).result().getAffectedRows();
            if (row == 1) {
                tx.delete("discount_rule").where(Filter.create("discount_id", id)).result().getAffectedRows();
            }
        });
    }

    @Getter
    @Setter
    private static class ComparedPojo {
        private Boolean type;   //是否是开区间,是为true
        private String start;
        private String end;
    }
}
