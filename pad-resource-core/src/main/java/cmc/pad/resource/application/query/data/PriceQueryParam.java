package cmc.pad.resource.application.query.data;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:51
 * @Version 1.0
 */
public class PriceQueryParam {
    //目前对应城市地区级别
    @Getter
    @Setter
    private String cityLevel;
    //影城级别
    @Getter
    @Setter
    private String cinemaLevel;
    @Setter
    private int pageIndex;
    @Setter
    private int pageSize;

    public int getPageIndex() {
        if (0 >= this.pageIndex)
            return 1;
        return pageIndex;
    }

    public int getPageSize() {
        if (0 >= this.pageSize)
            return 10;
        return pageSize;
    }
}
