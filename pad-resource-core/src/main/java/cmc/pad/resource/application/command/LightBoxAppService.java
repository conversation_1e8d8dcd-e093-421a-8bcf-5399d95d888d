package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.LightBoxPrice;
import cmc.pad.resource.domain.price.LightBoxPriceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LightBoxAppService {
    private final LightBoxPriceRepository repository;

    public void importData(List<LightBoxPrice> list) {
        repository.batchInsert(list);
    }

    public void discard(int importId) {
        repository.delete(f(LightBoxPrice.C_IMPORT_ID, importId));
    }
}
