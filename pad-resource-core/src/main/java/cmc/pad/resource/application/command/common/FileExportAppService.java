package cmc.pad.resource.application.command.common;

import cmc.pad.resource.domain.common.FileExportRecord;
import cmc.pad.resource.domain.common.FileExportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by fuwei on 2022/1/20.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileExportAppService {
    private final FileExportRepository fileExportRepository;

    public int save(FileExportRecord fileExportRecord) {
        return fileExportRepository.insert(fileExportRecord);
    }

    public FileExportRecord get(int id) {
        return fileExportRepository.get(id);
    }

    public void update(FileExportRecord fileExportRecord) {
        fileExportRepository.save(fileExportRecord);
    }
}