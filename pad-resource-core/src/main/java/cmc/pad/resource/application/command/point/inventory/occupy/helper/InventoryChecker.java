package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by fuwei on 2022/2/3.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryChecker {
    private final PointLocationInventoryRepository pointLocationInventoryRepo;

    public void checkInventoryUsableArea(List<RealOccupyData> realOccupyDataList) {
        realOccupyDataList.parallelStream().forEach(occupation -> {
            RealOccupyData.DateOccupyArea dateOccupyArea = occupation.getDateOccupyArea();
            List<PointLocationInventory> endPointLocationInventoryLst = pointLocationInventoryRepo.query(occupation.getPointLocationId(), dateOccupyArea.getEndDate(), dateOccupyArea.getEndDate());
            if (endPointLocationInventoryLst.isEmpty())
                throw new FaultException("id:" + occupation.getPointLocationId() + "点位结束时间库存" + dateOccupyArea.getEndDate() + "有缺失, 需要补充库存数据");
            List<PointLocationInventory> startPointLocationInventoryLst = pointLocationInventoryRepo.query(occupation.getPointLocationId(), dateOccupyArea.getStartDate(), dateOccupyArea.getStartDate());
            if (startPointLocationInventoryLst.isEmpty())
                throw new FaultException("id:" + occupation.getPointLocationId() + "点位开始时间库存" + dateOccupyArea.getStartDate() + "有缺失, 需要补充库存数据");
            PointLocationInventory pointLocationInventory = pointLocationInventoryRepo.queryUsableAreaInventory(occupation.getPointLocationId(), dateOccupyArea.getStartDate(), dateOccupyArea.getEndDate());
            if (dateOccupyArea.getOccupyArea() > pointLocationInventory.getNotSellArea())
                throw new FaultException("id:" + occupation.getPointLocationId() + "点位库存面积不足, 当前库存可使用面积:" + pointLocationInventory.getNotSellArea());
        });
    }

    public void checkInventoryUsableArea(int pid, LocalDate adjustDate) {
        List<PointLocationInventory> endPointLocationInventoryLst = pointLocationInventoryRepo.query(pid, adjustDate, adjustDate);
        if (endPointLocationInventoryLst.isEmpty())
            throw new FaultException("id:" + pid + "点位 时间库存" + adjustDate + "有缺失, 需要补充库存数据");
    }
}