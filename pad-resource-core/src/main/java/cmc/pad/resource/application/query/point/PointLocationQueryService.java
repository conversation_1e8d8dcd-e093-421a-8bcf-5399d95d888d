package cmc.pad.resource.application.query.point;

import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.BasicFilter;
import mtime.lark.db.jsd.SortType;
import mtime.lark.db.jsd.Sorters;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

import static cmc.pad.resource.domain.resource.PointLocationInfo.*;
import static mtime.lark.db.jsd.FilterType.LK;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/1/28.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationQueryService {
    private final PointLocationInfoRepository pointLocationInfoRep;
    private final PointLocationInventoryRepository pointLocationInventoryRep;

    public PageResult<PointLocationModel.QueryPointLocationUsableAreaData> query(int pageIndex, int pageSize, String code,
                                                                                 String cinemaInnerCode, String businessTypeCode,
                                                                                 LocalDate start, LocalDate end) {
        PageResult<PointLocationInfo> resultPage = list(pageIndex, pageSize, code, cinemaInnerCode, businessTypeCode);
        List<PointLocationModel.QueryPointLocationUsableAreaData> resultList =
                resultPage.getItems()
                        .parallelStream()
                        .map(pointLocationInfo -> {
                            PointLocationModel.QueryPointLocationUsableAreaData pointLocationUsableArea
                                    = new PointLocationModel.QueryPointLocationUsableAreaData(
                                    pointLocationInfo.getId(), pointLocationInfo.getCode(), pointLocationInfo.getBusinessTypeCode(), pointLocationInfo.getCinemaInnerCode(),
                                    pointLocationInfo.getResourceOwnershipCode(), pointLocationInfo.getLocationDesc(), pointLocationInfo.getPlanUse(), pointLocationInfo.getLandingMode()
                            );
                            PointLocationInventory pointLocationInventory = pointLocationInventoryRep.queryUsableAreaInventory(pointLocationInfo.getId(), start, end);
                            if (pointLocationInventory == null)
                                log.warn(">>>pointId:{}在{}-{}内无库存", pointLocationInfo.getId(), start, end);
                            if (pointLocationInventory != null)
                                pointLocationUsableArea.setUsableArea(pointLocationInventory.getNotSellArea());
                            return pointLocationUsableArea;
                        })
                        .collect(Collectors.toList());
        return new PageResult<>(resultList, resultPage.getTotalCount());
    }

    public PageResult<PointLocationInfo> list(int pageIndex, int pageSize, String code, String cinemaInnerCode, String businessTypeCode) {
        BasicFilter f = f();
        if (!Strings.isNullOrEmpty(cinemaInnerCode))
            f.add(PLI_CINEMA_INNER_CODE, cinemaInnerCode);
        if (!Strings.isNullOrEmpty(businessTypeCode))
            f.add(PLI_BUSINESS_TYPE_CODE, businessTypeCode);
        if (!Strings.isNullOrEmpty(code))
            f.add(PLI_CODE, LK, code);
        return pointLocationInfoRep.findPage(f, new Sorters(SortType.DESC, PLI_UPDATE_TIME), pageSize, pageIndex);
    }
}
