package cmc.pad.resource.application.command.point.inventory.occupy;

import cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterationContractOccupyServiceComponent;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.NotNeedInventoryManageChecker;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.OccupyDetailParamChecker;
import cmc.pad.resource.application.command.point.inventory.occupy.init.contract.NewContractOccupyServiceComponent;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static cmc.pad.resource.domain.inventory.point.ContractStatus.APPROVAL;
import static cmc.pad.resource.domain.inventory.point.ContractType.ALTER_CONTRACT;
import static cmc.pad.resource.domain.inventory.point.ContractType.NEW_CONTRACT;

/**
 * Created by fuwei on 2022/1/21.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointInventoryOccupationService {
    private final OccupyDetailParamChecker occupyDetailParamChecker;
    private final NotNeedInventoryManageChecker notNeedInventoryManageChecker;
    private final PointLocationOccupationContractRepository contractRepository;
    private final NewContractOccupyServiceComponent newContractOccupyServiceComponent;
    private final AlterationContractOccupyServiceComponent alterationContractOccupyServiceComponent;

    public void occupy(PointLocationModel.InventoryOccupationContractParam param) {
        try {
            occupyDetailParamChecker.checkAllPointLocationBusinessTypeSame(param);
            notNeedInventoryManageChecker.occupyCheck(param);
            occupyDetailParamChecker.checkDateRange(param.getDetails());
            int contractType = param.getContractType();
            if (contractType != 1 && contractType != 2)
                throw new FaultException("合同类型错误, contractType:" + contractType);
            if (contractType == 1)
                newContractOccupyServiceComponent.submit(param);
            if (contractType == 2)
                alterationContractOccupyServiceComponent.submit(param);
        } catch (NotNeedInventoryManageException e) {
            log.warn(">>>{}", e.getMessage());
        }
    }

    public void updateStatus(PointLocationModel.UpdateStatusParam param) {
        try {
            notNeedInventoryManageChecker.updateCheck(param);
            if (APPROVAL != param.getStatus() && ContractStatus.CANCEL != param.getStatus())
                throw new FaultException("状态值错误, status:" + param.getStatus());
            PointLocationOccupationContract contract = contractRepository.query(param.getContractNo());
            if (contract == null)
                throw new FaultException("合同编号不存在,不能操作");
            log.info(">>>开始审批/撤销合同：{}", JSON.toJSONString(contract));
            ContractType contractType = contract.getContractType();
            if (NEW_CONTRACT == contractType) {
                log.info(">>>{} 新合同：{}", param.getStatus() == APPROVAL ? "开始审批" : "开始撤销/驳回", contract.getContractNo());
                newContractOccupyServiceComponent.updateStatus(param);
            }
            if (ALTER_CONTRACT == contractType) {
                log.info(">>>{} 变更合同：{}", param.getStatus() == APPROVAL ? "开始审批" : "开始撤销/驳回", contract.getContractNo());
                alterationContractOccupyServiceComponent.updateStatus(param);
            }
        } catch (NotNeedInventoryManageException e) {
            log.warn(">>>{}", e.getMessage());
        }
    }
}