package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.CinemaResourceData;
import cmc.pad.resource.application.query.data.CinemaResourceQueryParam;
import cmc.pad.resource.application.query.data.ResourceChangeRecordData;
import cmc.pad.resource.domain.resource.CinemaResource;
import com.google.common.base.Strings;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.ExecuteResult;
import mtime.lark.db.jsd.result.SelectResult;
import mtime.lark.util.data.PageResult;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.*;
import static mtime.lark.db.jsd.SortType.ASC;
import static mtime.lark.db.jsd.SortType.DESC;

/**
 * <AUTHOR>
 */
@Service
public class CinemaResourceQueryService {

    private final static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    public PageResult<CinemaResourceData> queryCinemaResourceByPage(CinemaResourceQueryParam queryParam) {
        PageResult<CinemaResourceData> pageResult = new PageResult<>();
        Database db = getDatabase();
        Table t1 = t("cinema_resource", "CR");
        Table t2 = t("cinema", "C");
        Filter filter = Filter.create();
        if (!Strings.isNullOrEmpty(queryParam.getCinemaCode()) && !"-1".equalsIgnoreCase(queryParam.getCinemaCode())) {
            filter = filter.and(f("cinema_code", queryParam.getCinemaCode()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getRegionCode()) && !"-1".equalsIgnoreCase(queryParam.getRegionCode())) {
            filter = filter.and(f("region_code", queryParam.getRegionCode()));
        }

        SelectResult result = db.select(c(t1, "cinema_Code", "advertising_point_leasable_quantity", "marketing_point_leasable_area",
                "outer_area_leasable_area", "fixed_point_leasable_area").add(t2, "name", "region_name"))
                .from(t1).join(t2, f(t1, "cinema_code", t2, "code"))
                .where(filter)
                .orderBy(s(ASC, t1, "cinema_code"))
                .limit((queryParam.getPageIndex() - 1) * queryParam.getPageSize(), queryParam.getPageSize())
                .result();
        List<CinemaResourceData> list = new ArrayList<>();
        result.each(reader -> {
            CinemaResourceData model = new CinemaResourceData();
            model.setCinemaCode(reader.getString("cinema_code"));
            model.setCinemaName(reader.getString("name"));
            model.setRegionName(reader.getString("region_name"));
            model.setAdvertisingPointLeasableQuantity(reader.getInt("advertising_point_leasable_quantity"));
            model.setFixedPointLeasableArea(reader.getFloat("fixed_point_leasable_area"));
            model.setMarketingPointLeasableArea(reader.getFloat("marketing_point_leasable_area"));
            model.setOuterAreaLeasableArea(reader.getFloat("outer_area_leasable_area"));
            list.add(model);
        });
        final long[] count = {0L};
        SelectResult countSql = db.select((new Columns()).add("count(cinema_code)", "Count"))
                .from(t1).join(t2, f(t1, "cinema_code", t2, "code"))
                .where(filter)
                .result();
        countSql.each(reader -> {
            count[0] = reader.getLong("Count");
        });
        pageResult.setItems(list);
        pageResult.setTotalCount((int) count[0]);
        return pageResult;
    }


    public List<ResourceChangeRecordData> queryResourceChangeRecordList(String cinemaCode) {
        Database db = getDatabase();
        Table t1 = t("resource_change_record", "RCR");
        Table t2 = t("cinema", "C");
        SelectResult result = db.select(c(t1, "marketing_point_leasable_area", "outer_area_leasable_area", "fixed_point_leasable_area",
                "advertising_point_leasable_quantity", "update_time").add(t2, "name", "region_name"))
                .from(t1).join(t2, f(t1, "cinema_code", t2, "code"))
                .where(Filter.create("cinema_code", cinemaCode))
                .orderBy(s(DESC, t1, "update_time"))
                .limit(0, 10)
                .result();
        List<ResourceChangeRecordData> list = new ArrayList<>();
        result.each(reader -> {
            ResourceChangeRecordData model = new ResourceChangeRecordData();
            model.setCinemaName(reader.getString("name"));
            model.setRegionName(reader.getString("region_name"));
            model.setAdvertisingPointLeasableQuantity(reader.getInt("advertising_point_leasable_quantity"));
            model.setFixedPointLeasableArea(reader.getFloat("fixed_point_leasable_area"));
            model.setMarketingPointLeasableArea(reader.getFloat("marketing_point_leasable_area"));
            model.setOuterAreaLeasableArea(reader.getFloat("outer_area_leasable_area"));
            Timestamp updateTime = reader.getTimestamp("update_time");
            String updateTimeStr = format.format(updateTime);
            model.setUpdateTimeStr(updateTimeStr);
            list.add(model);
        });
        return list;
    }

    public void eachAll(Consumer<CinemaResource> consumer) {
        String sql = "select" +
                " cinema_code" +
                ",advertising_point_leasable_quantity" +
                ",marketing_point_leasable_area" +
                ",outer_area_leasable_area" +
                ",fixed_point_leasable_area" +
                " from cinema_resource";
        try (ExecuteResult result = getDatabase().execute(sql).result()) {
            result.each(reader -> {
                CinemaResource cinemaResource = new CinemaResource();
                cinemaResource.setCinemaCode(reader.getString("cinema_code"));
                cinemaResource.setMarketingPointLeasableArea(reader.getFloat("marketing_point_leasable_area"));
                cinemaResource.setOuterAreaLeasableArea(reader.getFloat("outer_area_leasable_area"));
                cinemaResource.setFixedPointLeasableArea(reader.getFloat("fixed_point_leasable_area"));
                cinemaResource.setAdvertisingPointLeasableQuantity(reader.getInt("advertising_point_leasable_quantity"));
                consumer.accept(cinemaResource);
            });
        }
    }
}
