package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.application.command.point.inventory.occupy.CancelException;
import cmc.pad.resource.domain.inventory.point.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.CancelException.RE_EXEC;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealOccupyData;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealSubmitOccupyData;
import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.domain.inventory.point.ContractStatus.CANCEL;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_NO;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/2/3.
 * 驳回或取消变更合同，执行库存回滚操作步骤
 * 1.还原提交点位实际占用库存
 * 2.执行上一版本审批库存占用
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RollbackBiz {
    private final OccupyInventoryBiz occupyInventoryBiz;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;
    private final PointLocationOccupationContractDetailRepository occupationDetailRepo;
    private final PointLocationOccupationLogRepository occupationLogRepo;

    private Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    public void checkAndCancelContract(String contractNo) {
        lockTemplate(contractNo, () -> {
            PointLocationOccupationContract contractBaseInfo = contractBaseInfoRepo.query(contractNo);
            if (CANCEL == contractBaseInfo.getStatus())
                throw new CancelException(RE_EXEC);
            if (ContractStatus.APPROVAL == contractBaseInfo.getStatus())
                throw new FaultException("合同已审批，不能取消");
            Set<Integer> pointLocationIds = occupationDetailRepo.findMany(f(PLO_C_D_CONTRACT_NO, contractNo)).stream().map(occupation -> occupation.getPointLocationId()).collect(Collectors.toSet());
            lockTemplate(pointLocationIds, () -> {
                getDatabase().begin((Transaction tx) -> {
                    pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(pointLocationIds);
                    contractBaseInfoRepo.cancel(tx, contractBaseInfo);
                    pointLocationLockOperateHelper.batchLockPointLocation(tx, pointLocationIds);
                });
            });
        });
    }

    public void rollbackOccupy(String contractNo) {
        log.info(">>>开始回滚变更合同库存, 合同编号:{}", contractNo);
        PointLocationOccupationContract contract = contractBaseInfoRepo.query(contractNo, true);
        Set<Integer> pointLocationIds = contract.getOccupationDetailList().stream().map(detail -> detail.getPointLocationId()).collect(Collectors.toSet());
        getDatabase().begin((Transaction tx) -> {
            occupyInventoryBiz.revertRealSubmitOccupyData(tx, contractNo, toRealSubmitOccupyData(contract.getOccupationDetailList()));

            List<PointLocationOccupationLog> lastVersionApprovalOccupyLogList = occupationLogRepo.queryRecentApprovalContractLog(contractNo);
            List<PointLocationOccupationContractDetail> lastVersionApprovalOccupyList = CopyUtil.listCopy(lastVersionApprovalOccupyLogList, PointLocationOccupationContractDetail.class);
            occupyInventoryBiz.occupyInventory(tx, contractNo, pointLocationIds, toRealOccupyData(lastVersionApprovalOccupyList));
        });
        log.info(">>>完成回滚变更合同库存, 合同编号:{}", contractNo);
    }

}