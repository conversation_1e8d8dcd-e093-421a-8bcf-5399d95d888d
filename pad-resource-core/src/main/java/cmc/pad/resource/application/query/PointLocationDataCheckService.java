package cmc.pad.resource.application.query;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.result.ExecuteResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 点位数据检查服务
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationDataCheckService {

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    /**
     * 检查指定影城的点位数据情况
     * @param cinemaInnerCode 影城内部编码
     * @return 检查结果
     */
    public PointLocationCheckResult checkPointLocationData(String cinemaInnerCode) {
        PointLocationCheckResult result = new PointLocationCheckResult();
        result.setCinemaInnerCode(cinemaInnerCode);
        
        // 第一步：查询符合条件的点位数量
        int pointCount = getPointLocationCount(cinemaInnerCode);
        result.setTotalPointCount(pointCount);
        
        log.info("影城{}符合条件的点位数量: {}", cinemaInnerCode, pointCount);
        
        if (pointCount >= 100) {
            result.setMeetsCondition(true);
            // 第二步：查询没有库存数据的点位
            List<PointLocationInfo> pointsWithoutInventory = getPointsWithoutInventory(cinemaInnerCode);
            result.setPointsWithoutInventory(pointsWithoutInventory);
            result.setPointsWithoutInventoryCount(pointsWithoutInventory.size());
            
            log.info("影城{}没有库存数据的点位数量: {}", cinemaInnerCode, pointsWithoutInventory.size());
        } else {
            result.setMeetsCondition(false);
            log.info("影城{}的点位数量{}不满足>=100的条件", cinemaInnerCode, pointCount);
        }
        
        return result;
    }

    /**
     * 获取符合条件的点位数量
     */
    private int getPointLocationCount(String cinemaInnerCode) {
        String sql = "SELECT COUNT(*) as count FROM point_location_info " +
                    "WHERE cinema_inner_code = ? " +
                    "AND business_type_code NOT IN ('YT','GMT','QT','CMDX','WBZY')";
        
        try (ExecuteResult result = getDatabase().execute(sql, cinemaInnerCode).result()) {
            final int[] count = {0};
            result.each(reader -> {
                count[0] = reader.getInt("count");
            });
            return count[0];
        }
    }

    /**
     * 获取没有库存数据的点位列表
     */
    private List<PointLocationInfo> getPointsWithoutInventory(String cinemaInnerCode) {
        // 首先获取所有符合条件的点位
        String getPointsSql = "SELECT id, code FROM point_location_info " +
                             "WHERE cinema_inner_code = ? " +
                             "AND business_type_code NOT IN ('YT','GMT','QT','CMDX','WBZY')";
        
        List<PointLocationInfo> allPoints = new ArrayList<>();
        try (ExecuteResult result = getDatabase().execute(getPointsSql, cinemaInnerCode).result()) {
            result.each(reader -> {
                PointLocationInfo point = new PointLocationInfo();
                point.setId(reader.getInt("id"));
                point.setCode(reader.getString("code"));
                allPoints.add(point);
            });
        }
        
        // 检查每个点位是否有库存数据
        List<PointLocationInfo> pointsWithoutInventory = new ArrayList<>();
        for (PointLocationInfo point : allPoints) {
            if (!hasInventoryData(point.getId())) {
                pointsWithoutInventory.add(point);
            }
        }
        
        return pointsWithoutInventory;
    }

    /**
     * 检查指定点位是否有库存数据
     * 由于point_location_inventory是分片表，需要查询所有可能的分片
     */
    private boolean hasInventoryData(int pointLocationId) {
        // 查询所有分片表
        for (int i = 0; i < 10; i++) { // 假设有10个分片，实际数量需要根据配置确定
            String tableName = "point_location_inventory_" + String.format("%02d", i);
            String sql = "SELECT COUNT(*) as count FROM " + tableName + " WHERE point_location_id = ?";
            
            try (ExecuteResult result = getDatabase().execute(sql, pointLocationId).result()) {
                final int[] count = {0};
                result.each(reader -> {
                    count[0] = reader.getInt("count");
                });
                if (count[0] > 0) {
                    return true; // 找到库存数据
                }
            } catch (Exception e) {
                // 如果表不存在或查询失败，继续查询下一个分片
                log.debug("查询分片表{}失败: {}", tableName, e.getMessage());
            }
        }
        return false; // 所有分片都没有找到数据
    }

    /**
     * 点位信息简化类
     */
    public static class PointLocationInfo {
        private Integer id;
        private String code;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        @Override
        public String toString() {
            return "PointLocationInfo{" +
                    "id=" + id +
                    ", code='" + code + '\'' +
                    '}';
        }
    }

    /**
     * 检查结果类
     */
    public static class PointLocationCheckResult {
        private String cinemaInnerCode;
        private int totalPointCount;
        private boolean meetsCondition;
        private List<PointLocationInfo> pointsWithoutInventory;
        private int pointsWithoutInventoryCount;

        public String getCinemaInnerCode() {
            return cinemaInnerCode;
        }

        public void setCinemaInnerCode(String cinemaInnerCode) {
            this.cinemaInnerCode = cinemaInnerCode;
        }

        public int getTotalPointCount() {
            return totalPointCount;
        }

        public void setTotalPointCount(int totalPointCount) {
            this.totalPointCount = totalPointCount;
        }

        public boolean isMeetsCondition() {
            return meetsCondition;
        }

        public void setMeetsCondition(boolean meetsCondition) {
            this.meetsCondition = meetsCondition;
        }

        public List<PointLocationInfo> getPointsWithoutInventory() {
            return pointsWithoutInventory;
        }

        public void setPointsWithoutInventory(List<PointLocationInfo> pointsWithoutInventory) {
            this.pointsWithoutInventory = pointsWithoutInventory;
        }

        public int getPointsWithoutInventoryCount() {
            return pointsWithoutInventoryCount;
        }

        public void setPointsWithoutInventoryCount(int pointsWithoutInventoryCount) {
            this.pointsWithoutInventoryCount = pointsWithoutInventoryCount;
        }

        @Override
        public String toString() {
            return "PointLocationCheckResult{" +
                    "cinemaInnerCode='" + cinemaInnerCode + '\'' +
                    ", totalPointCount=" + totalPointCount +
                    ", meetsCondition=" + meetsCondition +
                    ", pointsWithoutInventoryCount=" + pointsWithoutInventoryCount +
                    ", pointsWithoutInventory=" + pointsWithoutInventory +
                    '}';
        }
    }
}
