package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper;
import cmc.pad.resource.domain.inventory.point.*;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper.InventoryOperate.OCCUPY;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper.InventoryOperate.REVERT;
import static cmc.pad.resource.constant.Constant.DB_NAME;

/**
 * Created by fuyuanpu on 2022/5/13.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OccupyInventoryBiz {
    private final UpdateInventoryHelper updateInventoryHelper;
    private final PointLocationOccupationLogRepository occupationLogRepo;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;

    private Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    public void revertLastVersionApprovalOccupy(Transaction tx, String contractNo) {
        List<PointLocationOccupationLog> lastVersionApprovalOccupyList = occupationLogRepo.queryRecentApprovalContractLog(contractNo);
        List<PointLocationOccupationContractDetail> revertOccupyList =
                CopyUtil.listCopy(lastVersionApprovalOccupyList, PointLocationOccupationContractDetail.class)
                        .stream()
                        .filter(detail -> AlterStatus.DESTROY != detail.getAlterStatus())
                        .collect(Collectors.toList());
        log.info("先恢复上一版审批的库存占用, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(revertOccupyList));
        updateInventoryHelper.updateInventory(REVERT, tx, revertOccupyList);
        log.info("恢复上一版审批的库存占用完成, contractNo:{}", contractNo);
    }

    public void revertRealSubmitOccupyData(Transaction tx, String contractNo, List<RealOccupyData> realSubmitOccupyData) {
        log.info("先恢复提交点位实际占用库存, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(realSubmitOccupyData));
        updateInventoryHelper.updateInventory(REVERT, tx, contractNo, realSubmitOccupyData);
        log.info("恢复提交点位实际占用库存完成, contractNo:{}", contractNo);
    }

    public void occupyInventory(Transaction tx, String contractNo, Set<Integer> pointLocationIds, List<RealOccupyData> realOccupyData) {
        log.info(">>>变更合同 占用点位库存, contractNo:{}", contractNo);
        try {
            log.info("步骤1 开始库存占用, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(realOccupyData));
            updateInventoryHelper.updateInventory(OCCUPY, tx, contractNo, realOccupyData);
            log.info("步骤2 库存占用完成, contractNo:{}", contractNo);
            contractBaseInfoRepo.update(tx, contractNo, ProcessStatus.SUCESS);
            log.info("步骤3 库存占用合同执行完成, contractNo:{}", contractNo);
            pointLocationLockOperateHelper.batchUnLockPointLocation(tx, pointLocationIds);
            log.info("步骤4 解锁合同点位完成, contractNo:{}", contractNo);
            log.info("库存占用 成功提交事务, contractNo:{}", contractNo);
        } catch (Exception e) {
            log.error("库存占用异常, contractNo:{}", contractNo, e);
            contractBaseInfoRepo.update(contractNo, ProcessStatus.FAIL);
        }
    }

    public void occupyInventory(Transaction tx, String contractNo, List<RealOccupyData> realOccupyData) {
        log.info(">>>变更合同 占用点位库存, contractNo:{}", contractNo);
        try {
            log.info("步骤1 开始库存占用, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(realOccupyData));
            updateInventoryHelper.updateInventory(OCCUPY, tx, contractNo, realOccupyData);
            log.info("步骤2 库存占用完成, contractNo:{}", contractNo);
            contractBaseInfoRepo.update(tx, contractNo, ProcessStatus.SUCESS);
            log.info("步骤3 库存占用合同执行完成, contractNo:{}", contractNo);
            log.info("库存占用 成功提交事务, contractNo:{}", contractNo);
        } catch (Exception e) {
            log.error("库存占用异常, contractNo:{}", contractNo, e);
            contractBaseInfoRepo.update(contractNo, ProcessStatus.FAIL);
        }
    }
}
