package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.AdvertisingPointPriceQuery;
import cmc.pad.resource.domain.price.AdvertisingPointLeasingPrice;
import cmc.pad.resource.domain.price.AdvertisingPointLeasingPriceRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AdvertisingPointLeasingPriceQueryService {
    private final AdvertisingPointLeasingPriceRepository repository;

    public PageResult<AdvertisingPointLeasingPrice> effectivePage(AdvertisingPointPriceQuery query) {
        LocalDate latestEffectiveDate = repository.latestEffectiveDate();
        if (Objects.isNull(latestEffectiveDate)) {
            return new PageResult<>();
        }
        Filter filter = f(AdvertisingPointLeasingPrice.C_EFFECTIVE_DATE, latestEffectiveDate);
        String cityLevel = query.getCityLevel();
        if (StringUtils.isNotBlank(cityLevel) && !"0".equals(cityLevel)) {
            filter = filter.and(f(AdvertisingPointLeasingPrice.C_CITY_LEVEL, cityLevel));
        }
        String cinemaLevel = query.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaLevel) && !"0".equals(cinemaLevel)) {
            filter = filter.and(f(AdvertisingPointLeasingPrice.C_CINEMA_LEVEL, cinemaLevel));
        }
        return repository.findPage(filter, query.getPageSize(), query.getPageIndex());
    }


    public List<AdvertisingPointLeasingPrice> list(String cityLevel, String cinemaLevel) {
        return repository.findMany(f(AdvertisingPointLeasingPrice.C_CITY_LEVEL, cityLevel).add(AdvertisingPointLeasingPrice.C_CINEMA_LEVEL, cinemaLevel));
    }

    public LocalDate latestEffectiveDate() {
        return repository.latestEffectiveDate();
    }

    public void eachAll(Consumer<List<AdvertisingPointLeasingPrice>> consumer) {
        int page = 1;
        while (true) {
            PageResult<AdvertisingPointLeasingPrice> pageResult = repository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
