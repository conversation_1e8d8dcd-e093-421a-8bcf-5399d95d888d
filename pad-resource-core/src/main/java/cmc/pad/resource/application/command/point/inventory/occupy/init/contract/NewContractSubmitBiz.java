package cmc.pad.resource.application.command.point.inventory.occupy.init.contract;

import cmc.common.utility.copy.CopyUtil;
import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.*;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker.checkOccupyContractStatus;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper.InventoryOperate.OCCUPY;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.UpdateInventoryHelper.InventoryOperate.REVERT;
import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_CONTRACT_NO;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail.PLO_C_D_VERSION;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationLog.*;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/2/3.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NewContractSubmitBiz {
    private final InventoryChecker inventoryChecker;
    private final OccupationBuilder occupationBuilder;
    private final UpdateInventoryHelper updateInventoryHelper;
    private final PointLocationOccupationLogRepository occupationLogRepo;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;
    private final PointLocationOccupationContractDetailRepository occupationContractDetailRepo;

    private Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    public void checkAndSaveContractBaseInfo(String contractNo, PointLocationModel.InventoryOccupationContractParam param) {
        List<PointLocationOccupationContractDetail> occupationList = occupationBuilder.buildNewContractDetailList(param);
        log.info(">>>保存点位占用合同信息, contractNo:{} occupationList:{}", contractNo, JSON.toJSONString(occupationList));
        lockTemplate(contractNo, () -> {
            PointLocationOccupationContract contractBaseInfo = contractBaseInfoRepo.query(contractNo, true);
            checkIdempotent(contractBaseInfo, occupationList);
            checkOccupyContractStatus(contractBaseInfo);
            Set<Integer> pointLocationIds = occupationList.stream().map(occupation -> occupation.getPointLocationId()).collect(Collectors.toSet());
            lockTemplate(pointLocationIds, () -> {
                pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(pointLocationIds);
                inventoryChecker.checkInventoryUsableArea(toRealSubmitOccupyData(occupationList));
                getDatabase().begin((Transaction tx) -> {
                    if (contractBaseInfo == null) {//合同不存在时 写入
                        contractBaseInfoRepo.insert(tx, new PointLocationOccupationContract(contractNo, ContractStatus.SUBMIT, occupationList));
                        log.info(">>>完成保存点位占用合同信息, contractNo:{}", contractNo);
                        return;
                    }
                    contractBaseInfo.setOccupationDetailList(occupationList);
                    contractBaseInfoRepo.reOccupyUpdate(tx, contractBaseInfo);
                    pointLocationLockOperateHelper.batchLockPointLocation(tx, pointLocationIds);
                    log.info(">>>完成更新点位占用合同信息, contractNo:{}", contractNo);
                });
            });
        });
    }

    public List<RealOccupyData> toRealSubmitOccupyData(List<PointLocationOccupationContractDetail> occupationDetails) {
        return occupationDetails.stream().map(detail ->
                new RealOccupyData(detail.getPointLocationId(), new RealOccupyData.DateOccupyArea(detail.getStartDate(), detail.getEndDate(), detail.getAmount()))
        ).collect(Collectors.toList());
    }

    //幂等性检查
    private void checkIdempotent(PointLocationOccupationContract originContractBaseInfo, List<PointLocationOccupationContractDetail> newOccupationList) {
        if (originContractBaseInfo == null) return;
        IdempotentChecker.check(originContractBaseInfo, newOccupationList);
    }

    void occupyInventory(PointLocationOccupationContract contractBaseInfo) {
        log.info(">>>占用点位库存, contractNo:{}", contractBaseInfo.getContractNo());
        String contractNo = contractBaseInfo.getContractNo();
        List<PointLocationOccupationContractDetail> newOccupationList = occupationContractDetailRepo.findMany(f(PLO_C_D_CONTRACT_NO, contractNo).add(PLO_C_D_VERSION, contractBaseInfo.getVersion()));
        List<PointLocationOccupationLog> preVersionLockOccupationLogList = queryPreVersionSubmitOccupationLogList(contractNo, contractBaseInfo.getVersion() - 1);
        try {
            getDatabase().begin(tx -> {
                if (!preVersionLockOccupationLogList.isEmpty()) {
                    List<PointLocationOccupationContractDetail> revertOccupList = CopyUtil.listCopy(preVersionLockOccupationLogList, PointLocationOccupationContractDetail.class);
                    log.info("先恢复上一次提交的库存占用, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(revertOccupList));
                    updateInventoryHelper.updateInventory(REVERT, tx, revertOccupList);
                    log.info("恢复上一次提交的库存占用完成, contractNo:{}", contractNo);
                }
                log.info("步骤1 开始库存占用, contractNo:{}, 列表:{}", contractNo, JSON.toJSONString(newOccupationList));
                updateInventoryHelper.updateInventory(OCCUPY, tx, newOccupationList);
                log.info("步骤2 库存占用完成, contractNo:{}", contractNo);
                contractBaseInfoRepo.update(tx, contractNo, ProcessStatus.SUCESS);
                log.info("步骤3 库存占用合同执行完成, contractNo:{}", contractNo);
                pointLocationLockOperateHelper.batchUnLockPointLocation(tx, newOccupationList.stream().map(occupation -> occupation.getPointLocationId()).collect(Collectors.toSet()));
                log.info("步骤4 解锁合同点位完成, contractNo:{}", contractNo);
            });
            log.info("库存占用 成功提交事务, contractNo:{}", contractNo);
        } catch (Exception e) {
            log.error("库存占用异常, contractNo:{}", contractNo, e);
            contractBaseInfoRepo.update(contractNo, ProcessStatus.FAIL);
        }
    }

    private List<PointLocationOccupationLog> queryPreVersionSubmitOccupationLogList(String contractNo, int version) {
        return occupationLogRepo.findMany(f(PLO_LOG_CONTRACT_NO, contractNo).add(PLO_LOG_CONTRACT_STATUS, ContractStatus.SUBMIT).add(PLO_LOG_VERSION, version));
    }

}