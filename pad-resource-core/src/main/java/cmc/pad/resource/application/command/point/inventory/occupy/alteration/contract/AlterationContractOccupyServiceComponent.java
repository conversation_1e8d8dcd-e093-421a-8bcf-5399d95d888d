package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract;

import cmc.pad.resource.application.command.point.inventory.occupy.ApprovalException;
import cmc.pad.resource.application.command.point.inventory.occupy.CancelException;
import cmc.pad.resource.application.command.point.inventory.occupy.OccupyException;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.OccupyOrCancelParam;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cmc.pad.resource.application.command.point.inventory.occupy.OccupyException.CONTRACT_IDEMPOTENT_SAME;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.PublishUtil.send;

/**
 * Created by fuwei on 2022/3/26.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AlterationContractOccupyServiceComponent {
    private final AlterContractSubmitBiz alterContractSubmitBiz;
    private final AlterContractApprovalBiz alterContractApprovalBiz;
    private final RollbackBiz rollbackBiz;
    private final ContractStatusChecker contractStatusChecker;

    public void submit(PointLocationModel.InventoryOccupationContractParam param) {
        log.info(">>>提交变更合同, 占用库存, param:{}", JSON.toJSONString(param));
        try {
            alterContractSubmitBiz.checkAndSaveContract(param.getContractNo(), param);
//            submitOccupy(param.getContractNo());
            send(new OccupyOrCancelParam(ContractType.ALTER_CONTRACT.value(), param.getContractNo(), ContractStatus.SUBMIT.value()));
        } catch (OccupyException e) {
            if (e.getMessage().equals(CONTRACT_IDEMPOTENT_SAME))
                log.warn("重复提交合同, 不做处理");
            else
                throw e;
        }
    }

    public void updateStatus(PointLocationModel.UpdateStatusParam param) {
        String contractNo = param.getContractNo();
        if (ContractStatus.APPROVAL == param.getStatus()) {
            try {
                alterContractApprovalBiz.checkAndApprove(contractNo);
//            approvalOccupy(contractNo);
                send(new OccupyOrCancelParam(ContractType.ALTER_CONTRACT.value(), param.getContractNo(), ContractStatus.APPROVAL.value()));
            } catch (ApprovalException e) {
                log.warn(">>>{}", e.getMessage());
            }
        }
        if (ContractStatus.CANCEL == param.getStatus()) {
            try {
                rollbackBiz.checkAndCancelContract(contractNo);
//                rollbackOccupy(contractNo);
                send(new OccupyOrCancelParam(ContractType.ALTER_CONTRACT.value(), param.getContractNo(), ContractStatus.CANCEL.value()));
            } catch (CancelException e) {
                log.warn(">>>{}", e.getMessage());
            }
        }
    }

    public void submitOccupy(String contractNo) {
        contractStatusChecker.checkAndUpdateContractProcessing(contractNo, ContractStatus.SUBMIT);
        alterContractSubmitBiz.occupyInventory(contractNo);
    }

    public void approvalOccupy(String contractNo) {
        contractStatusChecker.checkAndUpdateContractProcessing(contractNo, ContractStatus.APPROVAL);
        alterContractApprovalBiz.occupyInventory(contractNo);
    }

    public void rollbackOccupy(String contractNo) {
        contractStatusChecker.checkAndUpdateContractProcessing(contractNo, ContractStatus.CANCEL);
        rollbackBiz.rollbackOccupy(contractNo);
    }
}