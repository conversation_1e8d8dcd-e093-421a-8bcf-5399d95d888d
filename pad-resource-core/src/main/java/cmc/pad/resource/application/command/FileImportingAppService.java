package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.importing.FileImportRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 文件导入记录
 *
 * <AUTHOR>
 * @Date 2019/3/15 12:57
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileImportingAppService {

    private final FileImportRepository repository;

    public int importStarted(SaveImportCommand command) {
        FileImport fileImport = createFileImport(command);
        return repository.saveGenerateId(fileImport);
    }

    private FileImport createFileImport(SaveImportCommand command) {
        FileImport fileImport = new FileImport();
        BeanUtils.copyProperties(command, fileImport);
        fileImport.importStartd();
        return fileImport;
    }

    public void importCompleted(int recordId, String fileId) {
        FileImport fileImport = repository.get(recordId);
        fileImport.importSucceeded();
        fileImport.setFileId(fileId);
        repository.save(fileImport);
    }

    public void importCompleted(int recordId) {
        FileImport fileImport = repository.get(recordId);
        fileImport.importSucceeded();
        repository.save(fileImport);
    }

    public void importFailed(int recordId) {
        FileImport fileImport = repository.get(recordId);
        fileImport.importFailed();
        repository.save(fileImport);
    }

    public void importFailed(int recordId, String remark) {
        FileImport fileImport = repository.get(recordId);
        fileImport.importFailed();
        fileImport.setRemark(remark);
        repository.save(fileImport);
    }

    public void importFailedFiled(int recordId, String fileId) {
        FileImport fileImport = repository.get(recordId);
        fileImport.importFailed();
        fileImport.setFileId(fileId);
        repository.save(fileImport);
    }


    public void discardFile(int recordId) {
        FileImport fileImport = repository.get(recordId);
        fileImport.importObsolete();
        repository.save(fileImport);
    }

}
