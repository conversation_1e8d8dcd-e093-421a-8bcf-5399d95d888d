package cmc.pad.resource.application.command.point.inventory;

import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

import static cmc.pad.resource.domain.inventory.point.ProcessStatus.CREATE;
import static cmc.pad.resource.domain.inventory.point.ProcessStatus.PROCESSING;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;

/**
 * Created by fuwei on 2022/1/21.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointInventoryManageService {
    private final PointLocationInfoRepository pointLocationInfoRepo;
    private final PointLocationInventoryRepository pointLocationInventoryRepo;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;

    public void initInventory(int pointLocationId, int year) {
        log.info(">>>开始初始化创建id为{},点位库存", pointLocationId);
        PointLocationInfo pointLocationInfo = checkAndUpdatePointLocationProcessing(pointLocationId);
        LocalDate start = LocalDate.now().minusMonths(12);
        LocalDate end = start.plusYears(year).withMonth(12).withDayOfMonth(31);
        pointLocationInventoryRepo.insert(pointLocationInfo, start, end);
        pointLocationInfoRepo.updateInventoryStatus(pointLocationId, ProcessStatus.SUCESS);
        log.info(">>>id为{}点位库存初始化创建完成", pointLocationId);
    }

    public void updateInventory(int pointLocationId) {
        log.info(">>>开始更新id为{}点位库存", pointLocationId);
        PointLocationInfo pointLocationInfo = checkAndUpdatePointLocationProcessing(pointLocationId);
        pointLocationInventoryRepo.updateSellArea(
                pointLocationId,
                pointLocationInfo.getSellArea(),
                pointLocationInfo.getSellAreaAdjustDate());
        pointLocationInfoRepo.updateInventoryStatus(pointLocationId, ProcessStatus.SUCESS);
        log.info(">>>id为{}点位库存更新完成", pointLocationId);
    }

    private PointLocationInfo checkAndUpdatePointLocationProcessing(int pointLocationId) {
        return
                (PointLocationInfo) lockTemplate(String.valueOf(pointLocationId), () -> {
                    PointLocationInfo pointLocationInfo = pointLocationInfoRepo.get(pointLocationId);
                    if (CREATE != pointLocationInfo.getInventoryStatus())
                        throw new FaultException("点位id:" + pointLocationId + ",当前库存执行状态:" + pointLocationInfo.getInventoryStatus() + ",不是创建状态, 不能执行");
                    pointLocationInfoRepo.updateInventoryStatus(pointLocationId, PROCESSING);
                    return pointLocationInfo;
                });
    }

    public void addInventoryAndLockPointLocation(int pointLocationId, LocalDate start, LocalDate end, int defaultAddMonthNum, boolean isBatchInsert) {
        pointLocationLockOperateHelper.integralLock(pointLocationId, () -> {
            try {
                addInventory(pointLocationId, start, end, defaultAddMonthNum, isBatchInsert);
            } catch (Exception e) {
                log.error(">>>点位{}追加库存异常", pointLocationId, e);
            }
        });
    }

    private void addInventory(int pointLocationId, LocalDate start, LocalDate end, int defaultAddMonthNum, boolean isBatchInsert) {
        log.info(">>>开始追加id为{},点位库存, start:{} end:{} defaultAddMonthNum:{}",
                pointLocationId, start, end, defaultAddMonthNum);
        PointLocationInfo pointLocationInfo = pointLocationInfoRepo.get(pointLocationId);
        if (start == null && end == null) {
            PointLocationInventory maxDateInventory = pointLocationInventoryRepo.queryMaxDateInventory(pointLocationId);
            start = maxDateInventory.getDate().plusDays(1);
            end = start.plusMonths(defaultAddMonthNum);

        }
        if (start != null && end == null) {
            PointLocationInventory minDateInventory = pointLocationInventoryRepo.queryMinDateInventory(pointLocationId);
            end = minDateInventory.getDate().minusDays(1);
        }
        if (start == null && end != null) {
            PointLocationInventory maxDateInventory = pointLocationInventoryRepo.queryMaxDateInventory(pointLocationId);
            start = maxDateInventory.getDate().plusDays(1);
        }
        log.info(">>追加点位id:{}, {} - {} 库存数据", pointLocationId, start, end);
        pointLocationInventoryRepo.insert(pointLocationInfo, start, end, isBatchInsert);
        pointLocationInfoRepo.updateInventoryStatus(pointLocationId, ProcessStatus.SUCESS);
        log.info(">>>id为{}点位库存追加完成", pointLocationId);
    }
}