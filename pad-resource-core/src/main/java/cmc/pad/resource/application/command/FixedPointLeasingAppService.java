package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.FixedPointLeasingPrice;
import cmc.pad.resource.domain.price.FixedPointLeasingPriceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FixedPointLeasingAppService {
    private final FixedPointLeasingPriceRepository repository;

    public void importData(List<FixedPointLeasingPrice> list) {
        repository.batchInsert(list);
    }

    public void discard(int importId) {
        repository.delete(f(FixedPointLeasingPrice.C_IMPORT_ID, importId));
    }
}
