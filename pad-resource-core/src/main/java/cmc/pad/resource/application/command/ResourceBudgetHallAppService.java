package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.domain.budget.ResourceBudgetHallChangeRecord;
import cmc.pad.resource.domain.budget.ResourceBudgetHallChangeRecordRepository;
import cmc.pad.resource.domain.budget.ResourceBudgetHallRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/1/14 10:03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ResourceBudgetHallAppService {
    private final ResourceBudgetHallRepository resourceBudgetHallRepository;
    private final ResourceBudgetHallChangeRecordRepository resourceBudgetHallChangeRecordRepository;


    public void importData(List<ResourceBudgetHall> list) {
        resourceBudgetHallRepository.batchInsert(list);
        List<ResourceBudgetHallChangeRecord> result = new ArrayList<>();
        for (ResourceBudgetHall hall : list) {
            ResourceBudgetHallChangeRecord record = new ResourceBudgetHallChangeRecord();
            BeanUtils.copyProperties(hall, record);
            result.add(record);
        }
        resourceBudgetHallChangeRecordRepository.batchInsert(result);
    }
    public void editData(ResourceBudgetHall hall){
        resourceBudgetHallRepository.update(hall);
        ResourceBudgetHallChangeRecord record = new ResourceBudgetHallChangeRecord();
        BeanUtils.copyProperties(hall, record);
        List<ResourceBudgetHallChangeRecord> result = new ArrayList<>();
        result.add(record);
        resourceBudgetHallChangeRecordRepository.batchInsert(result);
    }
    public ResourceBudgetHall get(long id){
       return resourceBudgetHallRepository.get(id);
    }
    public PageResult<ResourceBudgetHall> getPageList(String year, String regionCode, String cinemaInnerCode, String resourceType, int pageSize, int pageIndex){
       return resourceBudgetHallRepository.getPageList(year,regionCode,cinemaInnerCode,resourceType,pageSize,pageIndex);
    }

    public List<ResourceBudgetHallChangeRecord> getChangeRecordList(String year, String regionCode, String cinemaInnerCode, String resourceType){
        return resourceBudgetHallChangeRecordRepository.getList(year,regionCode,cinemaInnerCode,resourceType);
    }

}
