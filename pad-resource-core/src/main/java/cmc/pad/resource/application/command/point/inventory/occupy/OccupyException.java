package cmc.pad.resource.application.command.point.inventory.occupy;

import mtime.lark.util.lang.FaultException;

/**
 * Created by fuwei on 2022/3/4.
 */
public class OccupyException extends FaultException {
    public final static String CONTRACT_IDEMPOTENT_SAME = "提交合同已存在";
    public final static String CONTRACT_IDEMPOTENT_DIFF = "合同明细改变, 需撤销后重新提交";

    public OccupyException(String msg) {
        super(msg);
    }
}