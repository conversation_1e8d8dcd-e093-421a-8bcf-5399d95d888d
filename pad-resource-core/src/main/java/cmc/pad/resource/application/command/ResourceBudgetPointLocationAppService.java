package cmc.pad.resource.application.command;

import cmc.pad.resource.application.command.point.PointLocationManageService;
import cmc.pad.resource.domain.budget.*;
import lombok.RequiredArgsConstructor;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/1/14 10:03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ResourceBudgetPointLocationAppService {
    private final ResourceBudgetPointLocationRepository ResourceBudgetPointLocationRepository;
    private final ResourceBudgetPointLocationChangeRecordRepository ResourceBudgetPointLocationChangeRecordRepository;

    @Autowired
    private PointLocationManageService pointLocationManageService;

    public void importData(List<ResourceBudgetPointLocation> list) {
        ResourceBudgetPointLocationRepository.batchInsert(list);
        List<ResourceBudgetPointLocationChangeRecord> result = new ArrayList<>();
        for (ResourceBudgetPointLocation pointLocation : list) {
            ResourceBudgetPointLocationChangeRecord record = new ResourceBudgetPointLocationChangeRecord();
            BeanUtils.copyProperties(pointLocation, record);
            result.add(record);
        }
        ResourceBudgetPointLocationChangeRecordRepository.batchInsert(result);
    }

    public void editData(ResourceBudgetPointLocation pointLocation) {
        ResourceBudgetPointLocationRepository.update(pointLocation);
        ResourceBudgetPointLocationChangeRecord record = new ResourceBudgetPointLocationChangeRecord();
        BeanUtils.copyProperties(pointLocation, record);
        List<ResourceBudgetPointLocationChangeRecord> result = new ArrayList<>();
        result.add(record);
        ResourceBudgetPointLocationChangeRecordRepository.batchInsert(result);
    }

    public ResourceBudgetPointLocation get(long id) {
        return ResourceBudgetPointLocationRepository.get(id);
    }

    public PageResult<ResourceBudgetPointLocationView> getPageList(String year, String regionCode, String cinemaInnerCode, String resourceType,String resourceCode, int pageSize, int pageIndex) {
        PageResult<ResourceBudgetPointLocationView> result = ResourceBudgetPointLocationRepository.getPageList(year, regionCode, cinemaInnerCode, resourceType,resourceCode, pageSize, pageIndex);
        Map<String, String> businessTypeMap = pointLocationManageService.businessTypeDictMap();
        Map<String, String> ownerShipMap = pointLocationManageService.ownerShipDictMap();
        for (ResourceBudgetPointLocationView item : result.getItems()) {
            if(item.getResourceType()!=null) {
                item.setResourceType(businessTypeMap.get(item.getResourceType()));
            }
            if(item.getResourceAffiliation()!=null) {
                item.setResourceAffiliation(ownerShipMap.get(item.getResourceAffiliation()));
            }
        }
        return result;

    }

    public List<ResourceBudgetPointLocationChangeRecordView> getChangeRecordList(String year, String regionCode, String cinemaInnerCode, String resourceCode) {
        List<ResourceBudgetPointLocationChangeRecordView> result= ResourceBudgetPointLocationChangeRecordRepository.getList(year, regionCode, cinemaInnerCode, resourceCode);
        Map<String, String> businessTypeMap = pointLocationManageService.businessTypeDictMap();
        Map<String, String> ownerShipMap = pointLocationManageService.ownerShipDictMap();
        for (ResourceBudgetPointLocationChangeRecordView item : result) {
            if(item.getResourceType()!=null) {
                item.setResourceType(businessTypeMap.get(item.getResourceType()));
            }
            if(item.getResourceAffiliation()!=null) {
                item.setResourceAffiliation(ownerShipMap.get(item.getResourceAffiliation()));
            }
        }
        return result;
    }

    public Map<String, String> getBusinessMap(){
        return pointLocationManageService.businessTypeDictMap();
    }


}
