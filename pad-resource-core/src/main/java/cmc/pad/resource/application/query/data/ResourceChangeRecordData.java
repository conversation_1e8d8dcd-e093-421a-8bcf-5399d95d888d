package cmc.pad.resource.application.query.data;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ResourceChangeRecordData {
    private String regionName;
    private String cinemaName;
    private Float marketingPointLeasableArea;
    private Float outerAreaLeasableArea;
    private Float fixedPointLeasableArea;
    private Integer advertisingPointLeasableQuantity;
    private LocalDateTime updateTime;
    private String updateTimeStr;
}
