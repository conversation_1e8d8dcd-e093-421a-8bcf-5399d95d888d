package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cmc.pad.resource.domain.resource.PointLocationInfo.PLI_ID;
import static java.util.stream.Collectors.toList;
import static mtime.lark.db.jsd.FilterType.IN;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuyuanpu on 2022/6/30.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OccupyDetailParamChecker {
    private final PointLocationInfoRepository pointLocationInfoRepository;

    public void checkAllPointLocationBusinessTypeSame(PointLocationModel.InventoryOccupationContractParam param) {
        Object[] pids = param.getDetails().stream().map(d -> d.getPointLocationId()).toArray();
        List<PointLocationInfo> diffBusinessTypePointLocationList = pointLocationInfoRepository.findMany(f(PLI_ID, IN, pids))
                .stream()
                .filter(p -> !p.getBusinessTypeCode().equals(param.getBusinessTypeCode()))
                .collect(toList());
        if (!diffBusinessTypePointLocationList.isEmpty())
            throw new FaultException("点位id:" + diffBusinessTypePointLocationList.stream().map(p -> p.getId()).collect(toList()) + "业务类型和入参定义不一致");
    }

    public void checkDateRange(List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> occupationDetailList) {
        checkValidDateRange(occupationDetailList);
        checkPointLocationDateIntersection(occupationDetailList);
    }

    private void checkValidDateRange(List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> occupationDetailList) {
        occupationDetailList.stream().forEach(detail -> {
            if (detail.getEndDate().isBefore(detail.getStartDate()))
                throw new FaultException("点位id:" + detail.getPointLocationId() + ", 占用日期范围错误:" + String.format("%s - %s", detail.getStartDate(), detail.getEndDate()));
        });
    }

    private void checkPointLocationDateIntersection(List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> occupationDetailList) {
        groupByPointLocationId(occupationDetailList).forEach((pointLocationId, occupations) -> {
            List<DateRange> dateRangeList = Lists.newArrayList();
            if (occupations.size() > 1) {//同一个点位占用库存 有多个时间段时,做时间段交集校验
                occupations.stream().forEach(occupation -> {
                    try {
                        checkDateIntersection(dateRangeList, occupation.getStartDate(), occupation.getEndDate());
                    } catch (Exception e) {
                        throw new FaultException("点位id:" + pointLocationId + ", msg:" + e.getMessage());
                    }
                });
            }
        });
    }

    private Map<Integer, List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail>> groupByPointLocationId(List<PointLocationModel.InventoryOccupationContractParam.OccupationDetail> occupationDetailList) {
        return occupationDetailList.stream().collect(Collectors.groupingBy(PointLocationModel.InventoryOccupationContractParam.OccupationDetail::getPointLocationId));
    }

    private void checkDateIntersection(List<DateRange> dateRangeList,
                                       LocalDate start, LocalDate end) {
        dateRangeList.stream().forEach(dateRange -> {
            if (start.isEqual(dateRange.start) || start.isEqual(dateRange.end)) {
                throw new FaultException(start + "存在交集, 请将时间段拆分");
            }
            if (end.isEqual(dateRange.end) || end.isEqual(dateRange.start)) {
                throw new FaultException(end + "存在交集, 请将时间段拆分");
            }
            if (start.isAfter(dateRange.start) && end.isBefore(dateRange.end)) {
                throw new FaultException(start + " - " + end + "存在交集, 请将时间段拆分");
            }
            if (start.isAfter(dateRange.start) && start.isBefore(dateRange.end)) {
                throw new FaultException(start + "存在交集, 请将时间段拆分");
            }
            if (end.isAfter(dateRange.start) && end.isBefore(dateRange.end)) {
                throw new FaultException(end + "存在交集, 请将时间段拆分");
            }
        });
        dateRangeList.add(new DateRange(start, end));
    }

    @Data
    @AllArgsConstructor
    static class DateRange {
        LocalDate start;
        LocalDate end;
    }
}