package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import cmc.pad.resource.util.DateUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import mtime.lark.util.lang.FaultException;

import java.util.List;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterDataChecker.*;

/**
 * Created by fuyuanpu on 2022/6/24.
 * 时间交叉库存占用计算器
 */
public class DateCrossUpdateInventoryCalculation {
    private DateCrossUpdateInventoryCalculation() {
    }

    public static AlterContractUpdateDetailRemark exec(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        DateUtil.DateRange expandDateRange = expandDateRange(newData, oldData);
        DateUtil.DateRange shrinkDateRange = shrinkDateRange(newData, oldData);
        DateUtil.DateRange intersectionDateRange = intersectionDateRange(newData, oldData);

        CalcResult calcResult = new CalcResult();
        RealOccupyData.DateOccupyArea expandData = RealOccupyData.DateOccupyArea.of(expandDateRange.getStart(), expandDateRange.getEnd(), newData.getOccupyArea());
        calcResult.expandCheckData.add(expandData);

        RealOccupyData.DateOccupyArea shrinkData = RealOccupyData.DateOccupyArea.of(shrinkDateRange.getStart(), shrinkDateRange.getEnd(), oldData.getOccupyArea());
        calcResult.submitExecData.addAll(Lists.newArrayList(expandData, shrinkData));

        if (areaIsEqual(newData, oldData) || areaIsShrink(newData, oldData)) {
            RealOccupyData.DateOccupyArea intersectionData = RealOccupyData.DateOccupyArea.of(intersectionDateRange.getStart(), intersectionDateRange.getEnd(), oldData.getOccupyArea());
            calcResult.submitExecData.add(intersectionData);
        }

        if (areaIsExpand(newData, oldData)) {
            RealOccupyData.DateOccupyArea intersectionCheckData = RealOccupyData.DateOccupyArea.of(intersectionDateRange.getStart(), intersectionDateRange.getEnd(), expandArea(newData.getOccupyArea(), oldData.getOccupyArea()));
            calcResult.expandCheckData.add(intersectionCheckData);

            RealOccupyData.DateOccupyArea intersectionData = RealOccupyData.DateOccupyArea.of(intersectionDateRange.getStart(), intersectionDateRange.getEnd(), newData.getOccupyArea());
            calcResult.submitExecData.add(intersectionData);
        }

        String crossDesc = null;
        if (dateLeftCrossing(newData, oldData))
            crossDesc = "left";
        if (dateRightCrossing(newData, oldData))
            crossDesc = "right";
        String areaSizeDesc = null;
        if (areaIsEqual(newData, oldData))
            areaSizeDesc = "area is equal";
        if (areaIsShrink(newData, oldData))
            areaSizeDesc = "area is shrink";
        if (areaIsExpand(newData, oldData))
            areaSizeDesc = "area is expand";

        return new AlterContractUpdateDetailRemark(
                String.format("%s %s %s", crossDesc, AlterContractUpdateDetailRemark.CROSS_DATE_RANGE, areaSizeDesc),
                calcResult.getSubmitExecData(),
                calcResult.getExpandCheckData()
        );
    }

    private static DateUtil.DateRange expandDateRange(RealOccupyData.DateOccupyArea neww, RealOccupyData.DateOccupyArea old) {
        if (dateLeftCrossing(neww, old))
            return new DateUtil.DateRange(neww.getStartDate(), old.getStartDate().minusDays(1));
        if (dateRightCrossing(neww, old))
            return new DateUtil.DateRange(old.getEndDate().plusDays(1), neww.getEndDate());
        throw new FaultException("交叉日期提取扩展日期错误");
    }

    private static DateUtil.DateRange shrinkDateRange(RealOccupyData.DateOccupyArea neww, RealOccupyData.DateOccupyArea old) {
        if (dateLeftCrossing(neww, old))
            return new DateUtil.DateRange(neww.getEndDate().plusDays(1), old.getEndDate());
        if (dateRightCrossing(neww, old))
            return new DateUtil.DateRange(old.getStartDate(), neww.getStartDate().minusDays(1));
        throw new FaultException("交叉日期提取缩小日期错误");
    }

    private static DateUtil.DateRange intersectionDateRange(RealOccupyData.DateOccupyArea neww, RealOccupyData.DateOccupyArea old) {
        if (dateLeftCrossing(neww, old)) {
            if (neww.getEndDate().isEqual(old.getStartDate()))
                return new DateUtil.DateRange(neww.getEndDate(), neww.getEndDate());
            if (!neww.getEndDate().isEqual(old.getStartDate()))
                return new DateUtil.DateRange(old.getStartDate(), neww.getEndDate());
        }
        if (dateRightCrossing(neww, old)) {
            if (neww.getStartDate().isEqual(old.getEndDate()))
                return new DateUtil.DateRange(neww.getStartDate(), neww.getStartDate());
            if (!neww.getStartDate().isEqual(old.getEndDate()))
                return new DateUtil.DateRange(neww.getStartDate(), old.getEndDate());
        }
        throw new FaultException("交叉日期提取交集日期错误");
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CalcResult {
        private List<RealOccupyData.DateOccupyArea> submitExecData = Lists.newArrayList();
        private List<RealOccupyData.DateOccupyArea> expandCheckData = Lists.newArrayList();
    }
}
