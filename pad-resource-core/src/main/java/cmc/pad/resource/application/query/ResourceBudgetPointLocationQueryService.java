package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.budget.ResourceBudgetPointLocation;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @create 2022/1/28 8:53
 */
@Service
public class ResourceBudgetPointLocationQueryService {

    @Autowired
    private ResourceBudgetPointLocationRepository resourceBudgetPointLocationRepository;

    public void eachAll(Consumer<List<ResourceBudgetPointLocation>> consumer, LocalDateTime startTime, LocalDateTime endTime) {
        int page = 1;
        while (true) {
            List<ResourceBudgetPointLocation> list = resourceBudgetPointLocationRepository.getList(1000, page++, startTime, endTime);
            if (list.isEmpty()) {
                break;
            }
            consumer.accept(list);
        }
    }

}
