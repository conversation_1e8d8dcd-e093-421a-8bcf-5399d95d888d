package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.application.query.data.CinemaLevelQuery;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaLevel;
import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.domain.city.CityDistrict;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.ExecuteResult;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.*;

/**
 * 影城级别查询服务
 *
 * <AUTHOR>
 * @Date 2019/3/15 11:34
 * @Version 1.0
 */
@Service
public class CinemaLevelQueryService {

    private final static Table tCinema = t(Cinema.T_CINEMA, "CA");
    private final static Table tCity = t(City.T_CITY, "CY");
    private final static Table tCinemaLevel = t(CinemaLevel.T_CINEMA_LEVEL, "CL");
    private final static Table tDistrict = t(CityDistrict.T_CITY_DISTRICT, "DT");

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    public CinemaLevelInfo getCinema(String cinemaCode) {
        Database database = getDatabase();
        Filter filter = f(tCinemaLevel, CinemaLevel.C_INNER_CODE, FilterType.EQ, cinemaCode);

        List<CinemaLevelInfo> list = new ArrayList<>();
        Columns columns = c(tCinema, Cinema.C_NAME, Cinema.C_REGION_NAME)
                .add(tCity, City.C_NAME, City.C_CITY_LEVEL)
                .add(tCinemaLevel, CinemaLevel.C_INNER_CODE, CinemaLevel.C_LEVEL)
                .add(tDistrict, CityDistrict.C_NAME, CityDistrict.C_CITY_DISTRICT_LEVEL);

        database.select(columns)
                .from(tCinemaLevel)
                .leftJoin(tCinema, f(tCinemaLevel, CinemaLevel.C_INNER_CODE, tCinema, Cinema.C_CODE))
                .leftJoin(tCity, f(tCity, City.C_CODE, tCinema, Cinema.C_CITY_CODE))
                .leftJoin(tDistrict, f(tDistrict, CityDistrict.C_CODE, tCinema, Cinema.C_CITY_DISTRICT_CODE))
                .where(filter)
                .result()
                .each(rs -> list.add(getCinemaLevelInfo(rs)));
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);

    }

    private CinemaLevelInfo getCinemaLevelInfo(DataReader rs) {
        CinemaLevelInfo view = new CinemaLevelInfo();
        view.setCinemaName(rs.getString(1));
        view.setRegionName(rs.getString(2));
        view.setCityName(rs.getString(3));
        view.setCityLevel(rs.getString(4));
        view.setCinemaCode(rs.getString(5));
        view.setCinemaLevel(rs.getString(6));
        view.setCityDistrictName(rs.getString(7));
        view.setCityDistrictLevel(rs.getString(8));
        return view;
    }

    public PageResult<CinemaLevelInfo> queryCinema(CinemaLevelQuery query) {
        Integer pageIndex = query.getPageIndex();
        if (Objects.isNull(pageIndex) || 0 >= pageIndex) {
            query.setPageIndex(1);
        }
        Integer pageSize = query.getPageSize();
        if (Objects.isNull(pageSize) || 0 >= pageSize) {
            query.setPageSize(15);
        }

        PageResult<CinemaLevelInfo> result = new PageResult<>();
        Database database = getDatabase();
        Filter f = f();
        String cinemaCode = query.getCinemaCode();
        if (StringUtils.isNotBlank(cinemaCode) && !"0".equals(cinemaCode)) {
            f = f.and(f(tCinemaLevel, CinemaLevel.C_INNER_CODE, FilterType.EQ, cinemaCode));
        }
        String cinemaLevel = query.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaLevel) && !"0".equals(cinemaLevel)) {
            f = f.and(f(tCinemaLevel, CinemaLevel.C_LEVEL, FilterType.EQ, cinemaLevel));
        }

        String cityDistrictLevel = query.getCityDistrictLevel();
        if (StringUtils.isNotBlank(cityDistrictLevel) && !"0".equals(cityDistrictLevel)) {
            f = f.and(f(tDistrict, CityDistrict.C_CITY_DISTRICT_LEVEL, FilterType.EQ, cityDistrictLevel));
        }


        long total = (long) database.select(Shortcut.count())
                .from(tCinemaLevel)
                .leftJoin(tCinema, f(tCinemaLevel, CinemaLevel.C_INNER_CODE, tCinema, Cinema.C_CODE))
                .leftJoin(tCity, f(tCity, City.C_CODE, tCinema, Cinema.C_CITY_CODE))
                .leftJoin(tDistrict, f(tDistrict, CityDistrict.C_CODE, tCinema, Cinema.C_CITY_DISTRICT_CODE))
                .where(f)
                .result().value();
        result.setTotalCount(Long.valueOf(total).intValue());

        List<CinemaLevelInfo> list = new ArrayList<>();
        Columns columns = c(tCinema, Cinema.C_NAME, Cinema.C_REGION_NAME)
                .add(tCity, City.C_NAME, City.C_CITY_LEVEL)
                .add(tCinemaLevel, CinemaLevel.C_INNER_CODE, CinemaLevel.C_LEVEL)
                .add(tDistrict, CityDistrict.C_NAME, CityDistrict.C_CITY_DISTRICT_LEVEL);

        database.select(columns)
                .from(tCinemaLevel)
                .leftJoin(tCinema, f(tCinemaLevel, CinemaLevel.C_INNER_CODE, tCinema, Cinema.C_CODE))
                .leftJoin(tCity, f(tCity, City.C_CODE, tCinema, Cinema.C_CITY_CODE))
                .leftJoin(tDistrict, f(tDistrict, CityDistrict.C_CODE, tCinema, Cinema.C_CITY_DISTRICT_CODE))
                .where(f)
                .page(query.getPageIndex(), query.getPageSize())
                .result()
                .each(rs -> list.add(getCinemaLevelInfo(rs)));

        result.setItems(list);
        return result;
    }

    public void eachAll(Consumer<CinemaLevel> consumer) {
        try (ExecuteResult result = getDatabase().execute("select inner_code,level,import_id from cinema_level").result()) {
            result.each(reader -> {
                CinemaLevel cinemaLevel = new CinemaLevel();
                cinemaLevel.setInnerCode(reader.getString("inner_code"));
                cinemaLevel.setLevel(reader.getString("level"));
                cinemaLevel.setImportId(reader.getInt("import_id"));
                consumer.accept(cinemaLevel);
            });
        }
    }

}
