package cmc.pad.resource.application.command.point;

import cmc.pad.resource.application.DateUtil;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.InventoryChecker;
import cmc.pad.resource.domain.inventory.point.PointLocationInventory;
import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetailRepository;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import cmc.pad.resource.domain.resource.PointLocationInfo;
import cmc.pad.resource.domain.resource.PointLocationInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.BasicFilter;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Optional;

import static mtime.lark.db.jsd.FilterType.NE;

/**
 * Created by fuwei on 2022/1/18.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointSaveChecker {
    private final PointLocationOccupationContractDetailRepository pointLocationOccupationContractDetailRepository;
    private final InventoryChecker inventoryChecker;
    private final PointLocationInventoryRepository pointLocationInventoryRepository;
    private final PointLocationInfoRepository pointLocationInfoRep;

    public void checkBusinessTypeCode(PointLocationInfo pointLocationInfo, String businessTypeCode) {
        if (!pointLocationInfo.getBusinessTypeCode().equals(businessTypeCode)) {//资源类型修改 校验合同
//            List<PointLocationOccupationContractDetail> occupationList = pointLocationOccupationContractDetailRepository.findMany(f("point_location_id", pointLocationInfo.getId()).add(PLO_C_D_CONTRACT_STATUS, NE, ContractStatus.CANCEL));
            boolean haveOccupy = pointLocationInventoryRepository.queryHaveOccupy(pointLocationInfo.getId());
            if (haveOccupy)
                throw new FaultException("点位已被占用,资源类型不允许更改");
        }
    }

    public void checkBusinessTypeCodeAndSellArea(String code, String cinemaInnerCode, String curBusinessTypeCode, String curSellArea) {
        Optional<PointLocationInfo> existPointLocation = pointLocationInfoRep.findOne(pointLocationInfoRep.getFilter(code, cinemaInnerCode));
        if (existPointLocation.isPresent()) {
            PointLocationInfo pointLocationInfo = existPointLocation.get();
            if (!pointLocationInfo.getSellArea().toString().equals(curSellArea)) {
                throw new FaultException("可售面积和系统可售面积不一致,");
            }
            checkBusinessTypeCode(pointLocationInfo, curBusinessTypeCode);
        }
    }

    public void checkCode(String code, String cinemaInnerCode, int id) {
        BasicFilter filter = pointLocationInfoRep.getFilter(code, cinemaInnerCode);
        if (id != 0)
            filter.add("id", NE, id);
        Optional<PointLocationInfo> optional = pointLocationInfoRep.findOne(filter);
        if (optional.isPresent())
            throw new FaultException("同一影城资源编码不能重复");
    }

    /**
     * 检查剩余库存
     *
     * @param editSellArea
     * @param adjustDate
     */
    public void checkRemainderInventory(PointLocationInfo pointLocationInfo, Float editSellArea, String adjustDate) {
        if (pointLocationInfo.getSellArea().equals(editSellArea))
            return;
        if (ProcessStatus.PROCESSING == pointLocationInfo.getInventoryStatus())
            throw new FaultException("库存数据更新中, 请稍后再试");
//        List<PointLocationOccupationContractDetail> occupationList = pointLocationOccupationContractDetailRepository
//                .findMany(
//                        f(PLO_C_D_POINT_LOCATION_ID, pointLocationInfo.getId())
//                                .add(PLO_C_D_CONTRACT_STATUS, NE, ContractStatus.CANCEL)
//                                .add(PLO_C_D_END_DATE, LTE, DateUtil.parseDate(adjustDate))
//                );
//        PointLocationInventory pointLocationInventory = pointLocationInventoryRepository.queryUsableAreaInventory(pointLocationInfo.getId(), DateUtil.parseDate(adjustDate), DateUtil.parseDate(adjustDate));
//        if (dateOccupyArea.getOccupyArea() > pointLocationInventory.getNotSellArea())
//
//        for (PointLocationOccupationContractDetail occupation : occupationList) {
//            totalAmount += occupation.getAmount();
//        }
        LocalDate adjustDateL = DateUtil.parseDate(adjustDate);
        inventoryChecker.checkInventoryUsableArea(pointLocationInfo.getId(), adjustDateL);
        PointLocationInventory pointLocationInventory = pointLocationInventoryRepository.queryMinRemainderArea(pointLocationInfo.getId(), adjustDateL);
        float totalAmount = pointLocationInventory.getSellArea() - pointLocationInventory.getNotSellArea();
        if (editSellArea.floatValue() < totalAmount)
            throw new FaultException("修改的售卖面积不能小于当前" + totalAmount + "面积(锁定+已售)");
    }
}