package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.DiscountData;
import cmc.pad.resource.application.query.data.DiscountQueryParam;
import cmc.pad.resource.domain.discount.Discount;
import cmc.pad.resource.domain.discount.DiscountRepository;
import cmc.pad.resource.domain.discount.DiscountRule;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlDiscountRuleRepository;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SelectResult;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;


/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountQueryService {

    private final DiscountRepository discountRepository;
    private final MysqlDiscountRuleRepository mysqlDiscountRuleRepository;

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    public PageResult<DiscountData> queryDiscountByPage(DiscountQueryParam queryParam) {
        PageResult<DiscountData> pageResult = new PageResult<>();
        Database db = getDatabase();
        Filter filter = Filter.create();
        if (!Strings.isNullOrEmpty(queryParam.getBusinessType()) && !"0".equalsIgnoreCase(queryParam.getBusinessType())) {
            filter = filter.and(f("business_type", queryParam.getBusinessType()));
        }
        Sorters sorters = new Sorters();
        PageResult<Discount> discounts = this.discountRepository.findPage(filter, sorters.add(SortType.DESC, "update_time"), queryParam.getPageSize(), queryParam.getPageIndex());
        pageResult.setTotalCount(discounts.getTotalCount());
        List<DiscountData> list = new ArrayList<>();
        for (Discount discount : discounts.getItems()) {
            DiscountData data = new DiscountData();
            data.setId(discount.getId());
            data.setBusinessType(discount.getBusinessType());
            data.setDiscountType(discount.getDiscountType().displayName());
            StringBuilder factorDesc = new StringBuilder();
            SelectResult result = db.select("id", "comparison_symbol", "min", "max", "factor")
                    .from("discount_rule")
                    .where(Filter.create("discount_id", discount.getId()))
                    .result();
            result.each(reader -> {
                if (reader.getInt("comparison_symbol") == 1) {
                    factorDesc.append("<p>在大于等于" + reader.getFloat("min") + "，小于" + reader.getFloat("max") + "之间，折扣系数" + reader.getFloat("factor") + "；</p>");
                } else if (reader.getInt("comparison_symbol") == 2) {
                    factorDesc.append("<p>等于" + reader.getFloat("min") + "，折扣系数" + reader.getFloat("factor") + "；</p>");
                } else if (reader.getInt("comparison_symbol") == 3) {
                    factorDesc.append("<p>小于等于" + reader.getFloat("min") + "，折扣系数" + reader.getFloat("factor") + "；</p>");
                } else if (reader.getInt("comparison_symbol") == 4) {
                    factorDesc.append("<p>大于等于" + reader.getFloat("min") + "，折扣系数" + reader.getFloat("factor") + "；</p>");
                }
            });
            data.setDiscountDesc(factorDesc.toString());
            list.add(data);
        }
        pageResult.setItems(list);
        return pageResult;
    }

    public DiscountData findDiscountById(Integer id) {
        DiscountData data = new DiscountData();
        Optional<Discount> optional = this.discountRepository.findOne(Filter.create("id", id));
        if (optional.isPresent()) {
            Discount discount = optional.get();
            data.setId(discount.getId());
            data.setBusinessType(discount.getBusinessType());
            data.setDiscountType(String.valueOf(discount.getDiscountType().value()));
            Database db = getDatabase();
            List<DiscountRule> rules = new ArrayList<>();
            SelectResult result = db.select("id", "comparison_symbol", "min", "max", "factor", "discount_id")
                    .from("discount_rule")
                    .where(Filter.create("discount_id", discount.getId()))
                    .result();
            result.each(reader -> {
                DiscountRule rule = new DiscountRule();
                rule.setId(reader.getInt("id"));
                rule.setDiscountId(reader.getInt("discount_id"));
                rule.setComparisonSymbol(reader.getInt("comparison_symbol"));
                rule.setMin(reader.getFloat("min"));
                rule.setMax(reader.getFloat("max"));
                rule.setFactor(reader.getFloat("factor"));
                rules.add(rule);
            });
            data.setDiscountRules(rules);
        }
        return data;
    }

    public void eachAll(Consumer<List<Discount>> consumer) {
        int page = 1;
        while (true) {
            PageResult<Discount> pageResult = discountRepository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }

    public void eachAllRule(Consumer<List<DiscountRule>> consumer) {
        int page = 1;
        while (true) {
            PageResult<DiscountRule> pageResult = mysqlDiscountRuleRepository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }



}
