package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPriceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MovieHallSeatLeasingAppService {
    private final MovieHallSeatLeasingPriceRepository repository;

    public void importData(List<MovieHallSeatLeasingPrice> list) {
        repository.batchInsert(list);
    }

    public void discard(int importId) {
        repository.delete(f(MovieHallSeatLeasingPrice.C_IMPORT_ID, importId));
    }
}
