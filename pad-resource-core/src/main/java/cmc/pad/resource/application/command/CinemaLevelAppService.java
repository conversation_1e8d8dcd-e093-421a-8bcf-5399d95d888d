package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.cinema.CinemaLevel;
import cmc.pad.resource.domain.cinema.CinemaLevelRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 影城级别服务
 *
 * <AUTHOR>
 * @Date 2019/3/15 10:50
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaLevelAppService {
    private final CinemaLevelRepository cinemaLevelRepository;

    public void importData(List<CinemaLevel> list) {
        int importId = list.get(0).getImportId();
        cinemaLevelRepository.batchInsert(list);
        List<Integer> allOldImportId = cinemaLevelRepository.getAllOldImportId(importId);
        if (Objects.nonNull(allOldImportId) && !allOldImportId.isEmpty()) {
            allOldImportId.stream().distinct().forEach(cinemaLevelRepository::deleteByImportId);
        }
    }
}
