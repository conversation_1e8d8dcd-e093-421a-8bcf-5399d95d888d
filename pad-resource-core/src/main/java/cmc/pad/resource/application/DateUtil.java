package cmc.pad.resource.application;

import com.google.common.base.Strings;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {
    public static final DateTimeFormatter FORMATE = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter FORMATE_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static String toDateTimeStr(LocalDateTime dateTime) {
        if (dateTime == null)
            return "";
        return dateTime.format(FORMATE);
    }

    public static String toDateStr(LocalDate date) {
        if (date == null)
            return "";
        return date.format(FORMATE_DATE);
    }

    public static LocalDate parseDate(String date) {
        return date == null || Strings.isNullOrEmpty(date) ? null : LocalDate.parse(date, FORMATE_DATE);
    }

    public static LocalDateTime parseDateTime(String dateTime) {
        return dateTime == null ? null : LocalDateTime.parse(dateTime, FORMATE);
    }

    public static boolean isContainDate(LocalDate comparedDate, LocalDate startDate, LocalDate endDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        Date compared = Date.from(comparedDate.atStartOfDay(zoneId).toInstant());
        Date start = Date.from(startDate.atStartOfDay(zoneId).toInstant());
        Date end = Date.from(endDate.atStartOfDay(zoneId).toInstant());
        if (compared.getTime() == start.getTime()
                || compared.getTime() == end.getTime()) {
            return true;
        }
        Calendar comparedCalendar = Calendar.getInstance();
        comparedCalendar.setTime(compared);
        Calendar beginCalendar = Calendar.getInstance();
        beginCalendar.setTime(start);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(end);
        return comparedCalendar.after(beginCalendar) && comparedCalendar.before(endCalendar);
    }


}
