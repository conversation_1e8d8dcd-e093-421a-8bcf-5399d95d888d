package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.importing.*;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.*;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

import static cmc.pad.resource.domain.importing.FileImport.*;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:45
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileImportingQueryService {
    private final FileImportRepository repository;

    public PageResult<FileImport> page(FileCategory category, int pageIdx, int pageSize) {
        return repository.findPage(category.value(), pageIdx, pageSize);
    }

    public PageResult<FileImport> page(FileCategory category, Sorters sorters, int pageIdx, int pageSize) {
        return repository.findPage(f(FileImport.C_FILE_CATEGORY, category), sorters, pageIdx, pageSize);
    }

    public PageResult<FileImport> page(FileCategory category, String largeWardCode, String regionCode, String cinemaInnerCode, Sorters sorters, int pageIdx, int pageSize) {
        BasicFilter f = f(FileImport.C_FILE_CATEGORY, category);
        if (!largeWardCode.equals("0"))
            f.add(C_LARGE_WARD_CODE, largeWardCode);
        if (!Strings.isNullOrEmpty(regionCode))
            f.add(C_REGION_CODE, regionCode);
        if (!Strings.isNullOrEmpty(cinemaInnerCode))
            f.add(C_CINEMA_INNER_CODE, cinemaInnerCode);
        return repository.findPage(f, sorters, pageIdx, pageSize);
    }

    public List<FileImport> list(FileCategory category) {
        return repository.list(category);
    }

    public List<FileImport> list(FileCategory category, FileImportStatus status) {
        return repository.findMany(f("file_category", category).add("status", status));
    }

    public boolean checkDuplicateFile(FileCategory fileCategory, LocalDate effectiveDate) {
        return repository.findOne(f(FileImport.C_FILE_CATEGORY, fileCategory.value())
                .and(f(FileImport.C_EFFECTIVE_DATE, effectiveDate))
                .and(f(FileImport.C_STATUS, FilterType.IN, new int[]{FileImportStatus.UNDERWAY.value(), FileImportStatus.SUCCEEDED.value()})))
                .isPresent();
    }

    public FileImport get(int id) {
        return repository.get(id);
    }
}
