package cmc.pad.resource.application.command;

import cmc.pad.resource.application.command.resource.ResourceInventoryBiz;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.inventory.*;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.AppError.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryAppService {

    private final static DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final InventoryGenerateService inventoryGenerateService;
    private final OccupationRepository occupationRepository;
    private final ResourceInventoryBiz resourceInventoryBiz;
    private final InventoryRepository inventoryRepository;
    private final CinemaRepository cinemaRepository;
    private final CinemaResourceRepository cinemaResourceRepository;


    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }

    public void GenerateInventory(String cinemaCode, String startDate, String endDate) {
        inventoryGenerateService.generate(cinemaCode, startDate, endDate);
    }

    public Boolean cancelOccupation(String contractNo) {
        log.info(">>>取消库存占用, contractNo:{}", contractNo);
        Boolean flag = false;
        List<Occupation> list = occupationRepository.findMany(Filter.create("contract_no", contractNo));
        if (CollectionUtils.isEmpty(list)) {
            throw OCCUPATION_NOT_EXIST.toException();
        } else {
            for (Occupation occupation : list) {
                if (occupation.getStatus() == OccupationStatus.CANCEL) {
                    continue;
                } else {
                    List<Inventory> inventoryList = inventoryRepository.findMany(Filter.create("cinema_code", occupation.getCinemaCode())
                            .add("date", FilterType.GTE, occupation.getStartDate()).add("date", FilterType.LTE, occupation.getEndDate()));
                    getDatabase().begin((Transaction tx) -> {
                        occupationRepository.cancelOccupation(tx, occupation.getContractNo());
                        updateInventory(tx, occupation.getBusinessType(), occupation.getAmount(), inventoryList, "cancel");
                    });
                }
                flag = true;
            }
        }
        return flag;
    }

    public Boolean saveOccupation(SaveOccupationCommand command) {
        Map<String, List<SaveOccupationCommand.OccupationDetail>> cinemaDetailsMap = command.getDetails().stream().collect(Collectors.groupingBy(SaveOccupationCommand.OccupationDetail::getCinemaCode));
        log.info(">>>合同包含的影城:{}", cinemaDetailsMap.keySet());
        cinemaDetailsMap.forEach((cinemaInnerCode, detailList) ->
                saveEveryCinemaOccupation(command.getContractNo(), command.getBusinessType(), cinemaInnerCode, detailList)
        );
        return true;
    }

    private void saveEveryCinemaOccupation(String contractNo, String businessType, String cinemaInnerCode, List<SaveOccupationCommand.OccupationDetail> newOccupationDetails) {
        List<Occupation> originOccupationDetails = occupationRepository.findMany(
                Filter.create("contract_no", contractNo)
                        .add("cinema_code", cinemaInnerCode)
                        .add("business_type", businessType)
                        .add("status", 1)
        );
        log.info(">>>{}影城,{}合同总数:{}", cinemaInnerCode, contractNo, originOccupationDetails.size());
        if (CollectionUtils.isEmpty(originOccupationDetails)) {
            save(contractNo, businessType, cinemaInnerCode, newOccupationDetails);
            return;
        }
        update(contractNo, businessType, cinemaInnerCode, newOccupationDetails, originOccupationDetails);
    }

    private void update(String contractNo, String businessType, String cinemaInnerCode, List<SaveOccupationCommand.OccupationDetail> newOccupationDetails, List<Occupation> originOccupationDetails) {
        //修改合同,检验新的明细通过，删除老的库存占用记录，再新增库存占用记录，记住老的合同各个明细占的库存
        log.info(">>>更新影城库存占用合同:{}-{}", cinemaInnerCode, contractNo);
        List<InventoryPojo> oldPojoList = new ArrayList<>();
        for (Occupation occupation : originOccupationDetails) {
            //每条明细占用的打散释放
            LocalDate start = occupation.getStartDate();
            LocalDate end = occupation.getEndDate();
            int between = (int) (end.toEpochDay() - start.toEpochDay());
            for (int i = 0; i <= between; i++) {
                InventoryPojo pojo = new InventoryPojo();
                pojo.setCinemaCode(occupation.getCinemaCode());
                pojo.setDate(start.plusDays(i));
                pojo.setCount(-occupation.getAmount());//释放每天的库存
                oldPojoList.add(pojo);
            }
        }
        Map<String, List<InventoryPojo>> oldOccupationMap = oldPojoList.stream().collect(Collectors.groupingBy(InventoryPojo::getCinemaCode));
        validate(contractNo, newOccupationDetails, oldOccupationMap);
        //删除旧的库存占用和释放对应的库存占用
        occupationRepository.delete(
                Filter.create("contract_no", contractNo)
                        .add("cinema_code", cinemaInnerCode)
                        .add("business_type", businessType)
                        .add("status", 1)
        );
        originOccupationDetails.forEach(occupation -> getDatabase().begin((Transaction tx) -> {
            List<Inventory> inventoryList = inventoryRepository.findMany(Filter.create("cinema_code", occupation.getCinemaCode())
                    .add("date", FilterType.GTE, occupation.getStartDate()).add("date", FilterType.LTE, occupation.getEndDate()));
            updateInventory(tx, businessType, occupation.getAmount(), inventoryList, "cancel");
        }));
        newOccupationDetails.forEach(detail -> saveOccupationDetails(contractNo, businessType, detail));
    }

    private void save(String contractNo, String businessType, String cinemaInnerCode, List<SaveOccupationCommand.OccupationDetail> details) {
        log.info(">>>保存影城库存占用合同:{}-{}", cinemaInnerCode, contractNo);
        validate(contractNo, details, null);
        details.forEach(detail -> saveOccupationDetails(contractNo, businessType, detail));
    }

    private void validate(String businessType, List<SaveOccupationCommand.OccupationDetail> details, Map<String, List<InventoryPojo>> oldOccupationMap) {
        Map<String, List<SaveOccupationCommand.OccupationDetail>> map = details.stream()
                .collect(Collectors.groupingBy(SaveOccupationCommand.OccupationDetail::getCinemaCode));
        if (map.size() == 0) {
            throw OCCUPATION_DETAILS_NOT_NULL.toException();
        }
        Set<Map.Entry<String, List<SaveOccupationCommand.OccupationDetail>>> entrySet = map.entrySet();
        Iterator<Map.Entry<String, List<SaveOccupationCommand.OccupationDetail>>> it = entrySet.iterator();
        StringBuffer marketingFailureDesc = new StringBuffer();//营销点位失败描述
        StringBuffer advertisingFailureDesc = new StringBuffer();//宣传点位失败描述
        StringBuffer fixedAreaFailureDesc = new StringBuffer();//固定点位失败描述
        StringBuffer outerAreaFailureDesc = new StringBuffer();//外租点位失败描述
        while (it.hasNext()) {
            List<InventoryPojo> pojoList = new ArrayList<>();
            Map.Entry<String, List<SaveOccupationCommand.OccupationDetail>> me = it.next();
            String cinemaCode = me.getKey();
            List<SaveOccupationCommand.OccupationDetail> occupationDetails = me.getValue();
            List<LocalDate> dateList = new ArrayList<>();
            List<Inventory> all = this.inventoryRepository.findMany(Filter.create("cinema_Code", cinemaCode));
            if (CollectionUtils.isNotEmpty(all)) {
                LocalDate first = all.get(0).getDate();//库里的第一个库存
                LocalDate last = all.get(all.size() - 1).getDate();//库里的最后一个库存
                for (SaveOccupationCommand.OccupationDetail detail : occupationDetails) {
                    String startDate = detail.getStartDate();
                    String endDate = detail.getEndDate();
                    LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    dateList.add(start);
                    dateList.add(end);
                    int between = (int) (end.toEpochDay() - start.toEpochDay());
                    for (int i = 0; i <= between; i++) {
                        InventoryPojo pojo = new InventoryPojo();
                        pojo.setCinemaCode(cinemaCode);
                        pojo.setDate(start.plusDays(i));
                        pojo.setCount(detail.getAmount());
                        //日期不在在数据库已有库存，不校验库存
                        if ((start.plusDays(i) == first || start.plusDays(i).isAfter(first))
                                && (start.plusDays(i) == last || start.plusDays(i).isBefore(last))) {
                            pojoList.add(pojo);
                        }
                    }
                }
            }

            dateList.sort((LocalDate h1, LocalDate h2) -> {
                if (h1.isAfter(h2))
                    return 1;
                else
                    return -1;
            });

            List<Inventory> inventoryList = inventoryRepository.findMany(Filter.create("cinema_code", cinemaCode)
                    .add("date", FilterType.GTE, dateList.get(0)).add("date", FilterType.LTE, dateList.get(dateList.size() - 1)));
            for (Inventory inventory : inventoryList) {
                InventoryPojo pojo = new InventoryPojo();
                pojo.setCinemaCode(inventory.getCinemaCode());
                pojo.setDate(inventory.getDate());
                Float count = 0.0f;
                if (businessType.equalsIgnoreCase("XC")) {
                    count = inventory.getSoldAdvertisingPointLeasableQuantity().floatValue();
                } else if (businessType.equalsIgnoreCase("YX")) {
                    count = inventory.getSoldMarketingPointLeasableArea();
                } else if (businessType.equalsIgnoreCase("GD")) {
                    count = inventory.getSoldFixedPointLeasableArea();
                } else if (businessType.equalsIgnoreCase("WZ")) {
                    count = inventory.getSoldOuterAreaLeasableArea();
                }
                pojo.setCount(-count);
                pojoList.add(pojo);
            }
            if (oldOccupationMap != null && oldOccupationMap.size() > 0) {
                pojoList.addAll(oldOccupationMap.get(cinemaCode));
            }

            List<Map.Entry<LocalDate, Double>> entries = pojoList.stream().collect(Collectors.groupingBy(InventoryPojo::getDate, Collectors.summingDouble(InventoryPojo::getCount))).entrySet()
                    .stream().filter(entry -> entry.getValue() > 0).collect(Collectors.toList());
            if (entries.size() > 0) {
                if (businessType.equalsIgnoreCase("YX")) {
                    cinemaRepository.findOne(Filter.create("code", cinemaCode)).ifPresent(
                            cinema -> cinemaResourceRepository.findOne(Filter.create("cinema_code", cinemaCode)).ifPresent(
                                    cinemaResource -> marketingFailureDesc.append(cinema.getName()).append("的营销点位总库存为").append(cinemaResource.getMarketingPointLeasableArea()).append("，当前明细已超过销售期间库存；")));
                } else if (businessType.equalsIgnoreCase("XC")) {
                    cinemaRepository.findOne(Filter.create("code", cinemaCode)).ifPresent(
                            cinema -> cinemaResourceRepository.findOne(Filter.create("cinema_code", cinemaCode)).ifPresent(
                                    cinemaResource -> advertisingFailureDesc.append(cinema.getName()).append("的宣传点位总库存为").append(cinemaResource.getAdvertisingPointLeasableQuantity()).append("，当前明细已超过销售期间库存；")));
                } else if (businessType.equalsIgnoreCase("GD")) {
                    cinemaRepository.findOne(Filter.create("code", cinemaCode)).ifPresent(
                            cinema -> cinemaResourceRepository.findOne(Filter.create("cinema_code", cinemaCode)).ifPresent(
                                    cinemaResource -> fixedAreaFailureDesc.append(cinema.getName()).append("的固定点位总库存为").append(cinemaResource.getFixedPointLeasableArea()).append("，当前明细已超过销售期间库存；")));
                } else if (businessType.equalsIgnoreCase("WZ")) {
                    cinemaRepository.findOne(Filter.create("code", cinemaCode)).ifPresent(
                            cinema -> cinemaResourceRepository.findOne(Filter.create("cinema_code", cinemaCode)).ifPresent(
                                    cinemaResource -> outerAreaFailureDesc.append(cinema.getName()).append("的外租区域总库存为").append(cinemaResource.getOuterAreaLeasableArea()).append("，当前明细已超过销售期间库存；")));
                }
            }
        }
        if (marketingFailureDesc.length() > 0) {
            throw MARKETING_POINT_NOT_ENOUGH.toException(marketingFailureDesc.toString());
        }
        if (advertisingFailureDesc.length() > 0) {
            throw ADVERTSING_POINT_NOT_ENOUGH.toException(advertisingFailureDesc.toString());
        }
        if (fixedAreaFailureDesc.length() > 0) {
            throw FIXED_AREA_NOT_ENOUGH.toException(fixedAreaFailureDesc.toString());
        }
        if (outerAreaFailureDesc.length() > 0) {
            throw OUTER_AREA_NOT_ENOUGH.toException(outerAreaFailureDesc.toString());
        }
    }

    private void saveOccupationDetails(String contractNo, String businessType, SaveOccupationCommand.OccupationDetail detail) {
        Occupation occupation = new Occupation();
        occupation.setContractNo(contractNo);
        occupation.setBusinessType(businessType);
        occupation.setStartDate(LocalDate.parse(detail.getStartDate(), fmt));
        occupation.setEndDate(LocalDate.parse(detail.getEndDate(), fmt));
        occupation.setCinemaCode(detail.getCinemaCode());
        occupation.setAmount(detail.getAmount());
        occupation.setStatus(OccupationStatus.ACTIVE);
        List<Inventory> inventoryList = inventoryRepository.findMany(Filter.create("cinema_code", detail.getCinemaCode())
                .add("date", FilterType.GTE, detail.getStartDate()).add("date", FilterType.LTE, detail.getEndDate()));
        getDatabase().begin((Transaction tx) -> {
            occupationRepository.insertOccupation(tx, occupation);
            updateInventory(tx, businessType, detail.getAmount(), inventoryList, "add");
        });
        log.info(">>>保存影城合同并更新库存,{}", occupation);
    }

    private void updateInventory(Transaction tx, String businessType, Float amount, List<Inventory> inventoryList, String type) {
        resourceInventoryBiz.updateInventory(tx, businessType, amount, inventoryList, type);
    }

    @Data
    private static class InventoryPojo {
        private String cinemaCode;
        private LocalDate date;
        private Float count;
    }

}
