package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.NotNeedInventoryManageException;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.ContractType;
import cmc.pad.resource.domain.inventory.point.NotNeedInventoryManagePointLocationContract;
import cmc.pad.resource.domain.inventory.point.NotNeedInventoryManagePointLocationContractRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Created by fuyuanpu on 2022/6/30.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NotNeedInventoryManageChecker {
    private final NotNeedInventoryManagePointLocationContractRepository notNeedInventoryManagePointLocationContractRepository;

    public void occupyCheck(PointLocationModel.InventoryOccupationContractParam param) {
        if (isNotNeedInventoryManageBusinessType(param.getBusinessTypeCode())) {
            saveNotNeedInventoryManagePointLocationContract(param);
            throw new NotNeedInventoryManageException("合同:" + param.getContractNo() + ", 业务类型:" + param.getBusinessTypeCode() + ",不做库存管理，忽略占用操作");
        }
    }

    public void updateCheck(PointLocationModel.UpdateStatusParam param) {
        NotNeedInventoryManagePointLocationContract contract = notNeedInventoryManagePointLocationContractRepository.queryRecentOne(param.getContractNo());
        if (contract == null)
            return;
        if (!isNotNeedInventoryManageBusinessType(contract.getBusinessType()))
            return;
        contract.setStatus(param.getStatus());
        contract.setId(null);
        notNeedInventoryManagePointLocationContractRepository.insert(contract);
        log.info(">>>记录不做库存管理合同备份完成");
        throw new NotNeedInventoryManageException("合同:" + param.getContractNo() + ", 业务类型:" + contract.getBusinessType() + ",不做库存管理，忽略库存" + contract.getStatus() + "更新操作");
    }

    private void saveNotNeedInventoryManagePointLocationContract(PointLocationModel.InventoryOccupationContractParam param) {
        NotNeedInventoryManagePointLocationContract contract = new NotNeedInventoryManagePointLocationContract();
        contract.setContractNo(param.getContractNo());
        contract.setStatus(ContractStatus.SUBMIT);
        contract.setBusinessType(param.getBusinessTypeCode());
        contract.setContractType(ContractType.valueOf(param.getContractType()));
        contract.setCreateTime(LocalDateTime.now());
        contract.setDetails(JSON.toJSONString(param.getDetails()));
        notNeedInventoryManagePointLocationContractRepository.insert(contract);
        log.info(">>>记录不做库存管理合同备份完成");
    }

    private boolean isNotNeedInventoryManageBusinessType(String businessTypeCodeParam) {
        return (
                "YT".equals(businessTypeCodeParam)
                        || "GMT".equals(businessTypeCodeParam)
                        || "QT".equals(businessTypeCodeParam)
                        || "CMDX".equals(businessTypeCodeParam)
                        || "WBZY".equals(businessTypeCodeParam)
        );
    }
}
