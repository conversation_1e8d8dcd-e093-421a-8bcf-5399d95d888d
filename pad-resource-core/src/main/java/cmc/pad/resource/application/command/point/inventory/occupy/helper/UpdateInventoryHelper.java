package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.domain.inventory.point.PointLocationInventoryRepository;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by fuyuanpu on 2022/5/11.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UpdateInventoryHelper {
    private final PointLocationInventoryRepository pointLocationInventoryRepo;

    public void updateInventory(InventoryOperate op, Transaction tx, String contractNo, List<RealOccupyData> realOccupyData) {
        realOccupyData.stream().forEach(occupation -> {
            RealOccupyData.DateOccupyArea dateOccupyArea = occupation.getDateOccupyArea();
            log.info(">>>合同:{}, 点位id:{}占用, 面积:{}, {} - {}", contractNo, occupation.getPointLocationId(), op.value * dateOccupyArea.getOccupyArea(), dateOccupyArea.getStartDate(), dateOccupyArea.getEndDate());
            pointLocationInventoryRepo.updateNotSellArea(
                    tx,
                    occupation.getPointLocationId(),
                    op.value * dateOccupyArea.getOccupyArea(),
                    dateOccupyArea.getStartDate(),
                    dateOccupyArea.getEndDate());
        });
    }

    public void updateInventory(InventoryOperate op, Transaction tx, PointLocationOccupationContractDetail occupationDetail) {
        updateInventory(op, tx, Lists.newArrayList(occupationDetail));
    }

    public void updateInventory(InventoryOperate op, Transaction tx, List<PointLocationOccupationContractDetail> occupationList) {
        occupationList.stream().forEach(occupation -> {
            log.info(">>>合同:{}, 点位id:{}占用, 面积:{}, {} - {}", occupation.getContractNo(), occupation.getPointLocationId(), op.value * occupation.getAmount(), occupation.getStartDate(), occupation.getEndDate());
            pointLocationInventoryRepo.updateNotSellArea(
                    tx,
                    occupation.getPointLocationId(),
                    op.value * occupation.getAmount(),
                    occupation.getStartDate(),
                    occupation.getEndDate());
        });
    }

    public enum InventoryOperate {
        REVERT(1),
        OCCUPY(-1);
        int value;

        InventoryOperate(int value) {
            this.value = value;
        }

        public int value() {
            return value;
        }
    }
}
