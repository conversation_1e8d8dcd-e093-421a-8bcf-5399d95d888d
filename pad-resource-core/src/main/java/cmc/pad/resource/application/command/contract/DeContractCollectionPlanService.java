package cmc.pad.resource.application.command.contract;

import cmc.pad.resource.domain.contract.*;
import cmc.pad.resource.infrastructures.service.region.RegionServiceAdapter;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

import static java.util.stream.Collectors.toList;
import static mtime.lark.db.jsd.FilterType.*;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2024/3/15.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DeContractCollectionPlanService {
    private final ContractRepository contractRepository;
    private final RegionServiceAdapter regionServiceAdapter;
    private final DeContractCollectionPlanRepository deContractCollectionPlanRepository;

    public void queryContractCollectPlanAndTransferSave(boolean isForceInit, String contractNo, LocalDateTime start, LocalDateTime end) {
        int pageNo = 1;
        LocalDateTime now = LocalDateTime.now();
        while (true) {
            List<Contract> contracts = contractRepository.pageList(contractNo, start, end, pageNo++, 1000);
            if (CollectionUtils.isEmpty(contracts))
                break;
            List<DeContractCollectionPlan> deContractCollectionPlans = contracts.stream().filter(contract -> {
                List<DeContractCollectionPlan> needUpdateDeContractPlan = deContractCollectionPlanRepository.findMany(f("contract_no", contract.getContractNo()));
                if (needUpdateDeContractPlan.isEmpty() || isForceInit) {
                    log.info(">>>需要初始化{}合同收款催缴计划", contract.getContractNo());
                    return true;
                } else {
                    boolean present = needUpdateDeContractPlan.stream()
                            .filter(dePlan -> dePlan.getUpdateTime().isBefore(contract.getUpdateTime()))
                            .findAny()
                            .isPresent();
                    if (present) {//存在老的没有更新的数据
                        log.info(">>>{}合同收款催缴计划不是最新的, 需要更新", contract.getContractNo());
                        return true;
                    }
                    log.info(">>>{}合同收款催缴计划是最新的, 不需要更新", contract.getContractNo());
                    return false;
                }
            }).flatMap(contract -> transfer(contract, now).stream()).collect(toList());
            if (!deContractCollectionPlans.isEmpty()) {
                List<String> needClearDeContractNoList = deContractCollectionPlans.stream().map(dePlan -> dePlan.getContractNo()).distinct().collect(toList());
                log.info("清除{}合同收款催缴计划", needClearDeContractNoList);
                deContractCollectionPlanRepository.delete(f("contract_no", IN, needClearDeContractNoList.toArray(new String[needClearDeContractNoList.size()])));
                Lists.partition(deContractCollectionPlans, 5000).stream().forEach(pList -> {
                    log.info(">>>批量写入合同收款计划, 数量:{}", pList.size());
                    deContractCollectionPlanRepository.batchInsert(pList);
                });
            }
        }
    }

    public List<DeContractCollectionPlan> transfer(Contract contract, LocalDateTime now) {
        AtomicReference<BigDecimal> accumulatedReceivableAmount = new AtomicReference<>(new BigDecimal(0));//合同款
        AtomicReference<BigDecimal> accumulatedReceivableBondAmount = new AtomicReference<>(new BigDecimal(0));//保证金
        contract.getContractCollectionPlanList().stream()
                .filter(plan -> DeContractCollectionPlan.CollectType.AMOUNT.value() != plan.getType().intValue() && DeContractCollectionPlan.CollectType.BOND_AMOUNT.value() != plan.getType().intValue())
                .forEach(plan -> log.error(">>>{}合同收款计划类型值{}错误", plan.getContractNo(), plan.getType()));
        return contract.getContractCollectionPlanList().stream()
                .filter(plan -> DeContractCollectionPlan.CollectType.AMOUNT.value() == plan.getType().intValue() || DeContractCollectionPlan.CollectType.BOND_AMOUNT.value() == plan.getType().intValue())
                .map(plan -> {
                    DeContractCollectionPlan deContractCollectionPlan = new DeContractCollectionPlan();
                    BeanUtils.copyProperties(contract, deContractCollectionPlan);
                    BeanUtils.copyProperties(plan, deContractCollectionPlan);
                    deContractCollectionPlan.setLargeWardCode(regionServiceAdapter.getLargeWardCode(deContractCollectionPlan.getAreaCode()));
                    if (DeContractCollectionPlan.CollectType.AMOUNT.value() == plan.getType().intValue())//认领金额
                        deContractCollectionPlan.setClaimedAmount(contract.getClaimedAmount());
                    if (DeContractCollectionPlan.CollectType.BOND_AMOUNT.value() == plan.getType().intValue())//认领保证金
                        deContractCollectionPlan.setClaimedAmount(contract.getClaimedBondAmount());
                    deContractCollectionPlan.setUpdateTime(now);
                    calcAccumulatedReceivableAmount(accumulatedReceivableAmount, accumulatedReceivableBondAmount, deContractCollectionPlan);
                    setDeContractCollectionPlanStatus(deContractCollectionPlan);
                    return deContractCollectionPlan;
                }).collect(toList());
    }

    public void calcAccumulatedReceivableAmount(
            AtomicReference<BigDecimal> accumulatedReceivableAmount, AtomicReference<BigDecimal> accumulatedReceivableBondAmount
            , DeContractCollectionPlan deContractCollectionPlan) {
        if (DeContractCollectionPlan.CollectType.AMOUNT.value() == deContractCollectionPlan.getType().intValue()) {
            addToAtomicBigDecimal(accumulatedReceivableAmount, deContractCollectionPlan.getPlanCollectionAmount());
            deContractCollectionPlan.setAccumulatedReceivableAmount(accumulatedReceivableAmount.get());//累计应收金额(元)
            log.info(">>>{}合同累积收款合同金额:{}", deContractCollectionPlan.getContractNo(), deContractCollectionPlan.getAccumulatedReceivableAmount());
        }
        if (DeContractCollectionPlan.CollectType.BOND_AMOUNT.value() == deContractCollectionPlan.getType().intValue()) {
            addToAtomicBigDecimal(accumulatedReceivableBondAmount, deContractCollectionPlan.getPlanCollectionAmount());
            deContractCollectionPlan.setAccumulatedReceivableAmount(accumulatedReceivableBondAmount.get());//累计应收保证金(元)
            log.info(">>>{}合同累积收款保证金额:{}", deContractCollectionPlan.getContractNo(), deContractCollectionPlan.getAccumulatedReceivableAmount());
        }
        BigDecimal unpaidAmount = deContractCollectionPlan.getClaimedAmount().subtract(deContractCollectionPlan.getAccumulatedReceivableAmount());
        deContractCollectionPlan.setAccumulatedUnpaidAmount(unpaidAmount.signum() < 0 ? unpaidAmount : new BigDecimal(0));
    }

    public void setDeContractCollectionPlanStatus(DeContractCollectionPlan deContractCollectionPlan) {
        deContractCollectionPlan.setOverdueState(DeContractCollectionPlan.OverdueState.NORMAL.value());
        LocalDate planCollectionDate = deContractCollectionPlan.getPlanCollectionDate();
        if (planCollectionDate != null && planCollectionDate.isBefore(LocalDate.now()) && deContractCollectionPlan.getAccumulatedUnpaidAmount().signum() < 0)
            deContractCollectionPlan.setOverdueState(DeContractCollectionPlan.OverdueState.OVERDUE.value());
    }

    public void queryPlanAndFlagOverdue() {
        while (true) {
            List<DeContractCollectionPlan> needFlagOverduePlans = deContractCollectionPlanRepository.findMany(
                    f("plan_collection_date", LT, LocalDate.now())
                            .add("accumulated_unpaid_amount", LT, 0)
                            .add("overdue_state", NE, DeContractCollectionPlan.OverdueState.OVERDUE.value()), 1000);
            if (needFlagOverduePlans.isEmpty()) {
                log.info(">>>需要标记逾期的计划为空,停止处理.");
                break;
            }
            List<Long> ids = needFlagOverduePlans.stream().map(DeContractCollectionPlan::getId).collect(toList());
            deContractCollectionPlanRepository.flagOverdue(ids);
            log.info(">>>标记逾期的计划 id:{}", ids);
        }
    }

    public static void addToAtomicBigDecimal(AtomicReference<BigDecimal> atomicBigDecimal, BigDecimal amount) {
        // 获取当前的BigDecimal值
        BigDecimal currentValue = atomicBigDecimal.get();
        // 执行加法操作
        BigDecimal newValue = currentValue.add(amount);
        // 尝试更新AtomicReference中的值
        while (!atomicBigDecimal.compareAndSet(currentValue, newValue)) {
            // 如果更新失败（因为有其他线程已经修改了值），则重新获取当前值并尝试更新
            currentValue = atomicBigDecimal.get();
            newValue = currentValue.add(amount);
        }
    }
}