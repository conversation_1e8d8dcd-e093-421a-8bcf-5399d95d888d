package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract;

import cmc.pad.resource.application.command.point.PointLocationLockOperateHelper;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractDetail;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractRepository;
import cmc.pad.resource.domain.inventory.point.ProcessStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.DatabaseFactory;
import mtime.lark.db.jsd.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealOccupyData;
import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.AlterContractRealSubmitOccupyDataTransfer.toRealSubmitOccupyData;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker.checkActiveContractStatus;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker.checkProcessing;
import static cmc.pad.resource.constant.Constant.DB_NAME;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;

/**
 * Created by fuwei on 2022/2/28.
 * 审批变更合同库存操作步骤
 * 1.还原提交点位实际占用库存
 * 2.对提交定制规格的点位特殊状态做库存处理
 * - 占用状态 重新占用
 * - 新增状态 重新占用
 * - 作废状态 不处理
 * - 更新状态
 * ** - 日期和面积扩大 重新占用
 * ** - 日期和面积缩小 重新占用
 * ** - 日期变大面积变小 重新占用
 * ** - 日期变小面积变大 重新占用
 * ** - 日期是新范围   重新占用
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AlterContractApprovalBiz {
    private final OccupyInventoryBiz occupyInventoryBiz;
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;
    private final PointLocationLockOperateHelper pointLocationLockOperateHelper;

    private Database getDatabase() {
        return DatabaseFactory.open(DB_NAME);
    }

    public List<PointLocationOccupationContractDetail> checkAndApprove(String contractNo) {
        log.info(">>>审批通过开始对变更合同库存更新, 合同编号:{}", contractNo);
        return (List) lockTemplate(contractNo, () -> {
            PointLocationOccupationContract contractBaseInfo = contractBaseInfoRepo.query(contractNo, true);
            checkActiveContractStatus(contractBaseInfo);
            checkProcessing(contractBaseInfo);
            List<PointLocationOccupationContractDetail> occupationDetails = contractBaseInfo.getOccupationDetailList();
            Set<Integer> pointLocationIds = occupationDetails.stream().map(occupation -> occupation.getPointLocationId()).collect(Collectors.toSet());
            lockTemplate(pointLocationIds, () -> {
                pointLocationLockOperateHelper.checkPointLocationInventoryIsUpdating(pointLocationIds);
                getDatabase().begin((Transaction tx) -> {
                    contractBaseInfoRepo.active(tx, contractBaseInfo, ProcessStatus.CREATE);
                    pointLocationLockOperateHelper.batchLockPointLocation(tx, pointLocationIds);
                });
            });
            log.info(">>>审批通过完成对变更合同库存更新, 合同编号:{}", contractNo);
            return occupationDetails;
        });
    }

    public void occupyInventory(String contractNo) {
        PointLocationOccupationContract contract = contractBaseInfoRepo.query(contractNo, true);
        Set<Integer> pointLocationIds = contract.getOccupationDetailList().stream().map(detail -> detail.getPointLocationId()).collect(Collectors.toSet());
        getDatabase().begin((Transaction tx) -> {
            occupyInventoryBiz.revertRealSubmitOccupyData(tx, contractNo, toRealSubmitOccupyData(contract.getOccupationDetailList()));

            occupyInventoryBiz.occupyInventory(tx, contractNo, pointLocationIds, toRealOccupyData(contract.getOccupationDetailList()));
        });
    }
}