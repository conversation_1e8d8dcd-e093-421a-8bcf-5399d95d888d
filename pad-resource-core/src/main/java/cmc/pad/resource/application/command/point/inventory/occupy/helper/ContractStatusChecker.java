package cmc.pad.resource.application.command.point.inventory.occupy.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.ApprovalException;
import cmc.pad.resource.application.command.point.inventory.occupy.CancelException;
import cmc.pad.resource.domain.inventory.point.ContractStatus;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContract;
import cmc.pad.resource.domain.inventory.point.PointLocationOccupationContractRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cmc.pad.resource.domain.inventory.point.ProcessStatus.CREATE;
import static cmc.pad.resource.domain.inventory.point.ProcessStatus.PROCESSING;
import static cmc.pad.resource.util.RedisLockSupport.lockTemplate;

/**
 * Created by fuwei on 2022/2/28.
 */
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ContractStatusChecker {
    private final PointLocationOccupationContractRepository contractBaseInfoRepo;

    public static void checkOccupyContractStatus(PointLocationOccupationContract contractBaseInfo) {
        if (contractBaseInfo == null) return;
        if (ContractStatus.APPROVAL == contractBaseInfo.getStatus())
            throw new FaultException("合同库存占用已扣减,不能重新占用. 合同编号:" + contractBaseInfo.getContractNo());
        checkProcessing(contractBaseInfo);
    }

    public static void checkActiveContractStatus(PointLocationOccupationContract contractBaseInfo) {
        if (contractBaseInfo == null)
            throw new FaultException("合同编号不存在,不能操作");
        if (ContractStatus.APPROVAL == contractBaseInfo.getStatus())
            throw new ApprovalException(ApprovalException.RE_EXEC);
//            throw new FaultException("合同已审批,不能操作.contractNo:" + contractBaseInfo.getContractNo());
        if (ContractStatus.CANCEL == contractBaseInfo.getStatus())
            throw new FaultException("合同已取消,不能操作.contractNo:" + contractBaseInfo.getContractNo());
        checkProcessing(contractBaseInfo);
    }

    public static void checkCancelContractStatus(PointLocationOccupationContract contractBaseInfo) {
        if (contractBaseInfo == null)
            throw new FaultException("合同编号不存在,不能操作.");
        if (ContractStatus.APPROVAL == contractBaseInfo.getStatus())
            throw new FaultException("合同已审批,不能操作.contractNo:" + contractBaseInfo.getContractNo());
        if (ContractStatus.CANCEL == contractBaseInfo.getStatus())
            throw new CancelException(CancelException.RE_EXEC);
        checkProcessing(contractBaseInfo);
    }

    public static void checkProcessing(PointLocationOccupationContract contractBaseInfo) {
        if (CREATE == contractBaseInfo.getProcessStatus() || PROCESSING == contractBaseInfo.getProcessStatus()) {
            throw new FaultException("合同库存处理中,不能操作.contractNo:" + contractBaseInfo.getContractNo());
        }
    }

    public PointLocationOccupationContract checkAndUpdateContractProcessing(String contractNo, ContractStatus occupyStatus) {
        return
                (PointLocationOccupationContract) lockTemplate(contractNo, () -> {
                    PointLocationOccupationContract contract = contractBaseInfoRepo.query(contractNo);
                    if (occupyStatus != contract.getStatus())
                        throw new FaultException("合同" + contractNo + "当前状态:" + contract.getStatus() + ",不能执行" + occupyStatus.name());
                    if (CREATE != contract.getProcessStatus())
                        throw new FaultException("合同" + contractNo + "当前执行状态:" + contract.getProcessStatus() + ",不是创建状态, 不能执行");
                    contractBaseInfoRepo.update(contractNo, PROCESSING);
                    return contract;
                });
    }
}
