package cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import com.google.common.collect.Lists;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterContractUpdateDetailRemark.EXPAND_NEW_DATE_RANGE_AND_AREA;

/**
 * Created by fuyuanpu on 2022/6/24.
 * 时间交叉库存占用计算器
 */
public class NewDateUpdateInventoryCalculation {
    private NewDateUpdateInventoryCalculation() {
    }

    public static AlterContractUpdateDetailRemark exec(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return new AlterContractUpdateDetailRemark(
                EXPAND_NEW_DATE_RANGE_AND_AREA,
                Lists.newArrayList(newData, oldData),
                Lists.newArrayList(RealOccupyData.DateOccupyArea.of(newData.getStartDate(), newData.getEndDate(), newData.getOccupyArea()))
        );
    }

}
