package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.OuterAreaPriceQuery;
import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.domain.price.OuterAreaLeasingPriceRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OuterAreaLeasingPriceQueryService {
    private final OuterAreaLeasingPriceRepository repository;

    public PageResult<OuterAreaLeasingPrice> effectivePage(OuterAreaPriceQuery query) {
        LocalDate latestEffectiveDate = repository.latestEffectiveDate();
        if (Objects.isNull(latestEffectiveDate)) {
            return new PageResult<>();
        }
        Filter filter = f(OuterAreaLeasingPrice.C_EFFECTIVE_DATE, latestEffectiveDate);
        String cityLevel = query.getCityLevel();
        if (StringUtils.isNotBlank(cityLevel) && !"0".equals(cityLevel)) {
            filter = filter.and(f(OuterAreaLeasingPrice.C_CITY_LEVEL, cityLevel));
        }
        String cinemaLevel = query.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaLevel) && !"0".equals(cinemaLevel)) {
            filter = filter.and(f(OuterAreaLeasingPrice.C_CINEMA_LEVEL, cinemaLevel));
        }
        return repository.findPage(filter, query.getPageSize(), query.getPageIndex());
    }


    public List<OuterAreaLeasingPrice> list(String cityLevel, String cinemaLevel) {
        return repository.findMany(f(OuterAreaLeasingPrice.C_CITY_LEVEL, cityLevel).add(OuterAreaLeasingPrice.C_CINEMA_LEVEL, cinemaLevel));
    }

    public LocalDate latestEffectiveDate() {
        return repository.latestEffectiveDate();
    }

    public void eachAll(Consumer<List<OuterAreaLeasingPrice>> consumer) {
        int page = 1;
        while (true) {
            PageResult<OuterAreaLeasingPrice> pageResult = repository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
