package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.InventoryData;
import cmc.pad.resource.application.query.data.InventoryQueryParam;
import cmc.pad.resource.application.query.data.ResourceSoldData;
import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.domain.inventory.OccupationRepository;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.result.SelectResult;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.*;
import static mtime.lark.db.jsd.SortType.ASC;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryQueryService {
    private final InventoryRepository inventoryRepository;
    private final OccupationRepository occupationRepository;

    protected Database getDatabase() {
        return DatabaseFactory.open("PadResource");
    }


    public PageResult<InventoryData> queryInventoryByPage(InventoryQueryParam queryParam) {
        PageResult<InventoryData> pageResult = new PageResult<>();
        Database db = getDatabase();
        Table t1 = t("inventory", "I");
        Table t2 = t("cinema", "C");
        Filter filter = Filter.create();
        if (!Strings.isNullOrEmpty(queryParam.getCinemaCode()) && !"-1".equalsIgnoreCase(queryParam.getCinemaCode())) {
            filter = filter.and(f("cinema_code", queryParam.getCinemaCode()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getRegionCode()) && !"-1".equalsIgnoreCase(queryParam.getRegionCode())) {
            filter = filter.and(f("region_code", queryParam.getRegionCode()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getStartDate())) {
            filter = filter.and(f("date", FilterType.GTE, queryParam.getStartDate()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getEndDate())) {
            filter = filter.and(f("date", FilterType.LTE, queryParam.getEndDate()));
        }

        SelectResult result = db.select(c(t1, "cinema_code", "date", "total_advertising_point_leasable_quantity",
                "total_outer_area_leasable_area", "total_fixed_point_leasable_area", "total_marketing_point_leasable_area",
                "sold_advertising_point_leasable_quantity", "sold_outer_area_leasable_area", "sold_fixed_point_leasable_area",
                "sold_marketing_point_leasable_area").add(t2, "name", "region_name"))
                .from(t1).join(t2, f(t1, "cinema_code", t2, "code"))
                .where(filter)
                .orderBy(s(ASC, t1, "cinema_code").add(ASC, t1, "date"))
                .limit((queryParam.getPageIndex() - 1) * queryParam.getPageSize(), queryParam.getPageSize())
                .result();
        List<InventoryData> list = new ArrayList<>();
        result.each(reader -> {
            InventoryData model = new InventoryData();
            model.setCinemaCode(reader.getString("cinema_code"));
            model.setCinemaName(reader.getString("name"));
            model.setRegionName(reader.getString("region_name"));
            model.setDate(new Date(reader.getDate("date").getTime()));
            model.setTotalAdvertisingPointLeasableQuantity(reader.getInt("total_advertising_point_leasable_quantity"));
            model.setTotalOuterAreaLeasableArea(reader.getFloat("total_outer_area_leasable_area"));
            model.setTotalFixedPointLeasableArea(reader.getFloat("total_fixed_point_leasable_area"));
            model.setTotalMarketingPointLeasableArea(reader.getFloat("total_marketing_point_leasable_area"));
            model.setSoldAdvertisingPointLeasableQuantity(reader.getInt("sold_advertising_point_leasable_quantity"));
            model.setSoldOuterAreaLeasableArea(reader.getFloat("sold_outer_area_leasable_area"));
            model.setSoldFixedPointLeasableArea(reader.getFloat("sold_fixed_point_leasable_area"));
            model.setSoldMarketingPointLeasableArea(reader.getFloat("sold_marketing_point_leasable_area"));
            list.add(model);
        });
        final long[] count = {0L};
        SelectResult countSql = db.select((new Columns()).add("count(cinema_code)", "Count"))
                .from(t1).join(t2, f(t1, "cinema_code", t2, "code"))
                .where(filter)
                .result();
        countSql.each(reader -> {
            count[0] = reader.getLong("Count");
        });
        pageResult.setItems(list);
        pageResult.setTotalCount((int) count[0]);
        return pageResult;
    }

    public long countInventory(InventoryQueryParam queryParam) {
        Database db = getDatabase();
        Filter filter = Filter.create();
        if (!Strings.isNullOrEmpty(queryParam.getCinemaCode()) && !"-1".equalsIgnoreCase(queryParam.getCinemaCode())) {
            filter = filter.and(f("cinema_code", queryParam.getCinemaCode()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getRegionCode()) && !"-1".equalsIgnoreCase(queryParam.getRegionCode())) {
            filter = filter.and(f("region_code", queryParam.getRegionCode()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getStartDate())) {
            filter = filter.and(f("date", FilterType.GTE, queryParam.getStartDate()));
        }
        if (!Strings.isNullOrEmpty(queryParam.getEndDate())) {
            filter = filter.and(f("date", FilterType.LTE, queryParam.getEndDate()));
        }
        final long[] count = {0L};
        Table t1 = t("inventory", "I");
        Table t2 = t("cinema", "C");
        SelectResult countSql = db.select((new Columns()).add("count(cinema_code)", "Count"))
                .from(t1).join(t2, f(t1, "cinema_code", t2, "code"))
                .where(filter)
                .result();
        countSql.each(reader -> {
            count[0] = reader.getLong("Count");
        });
        return count[0];
    }

    public List<ResourceSoldData> queryEachTypeSoldAmountAfterEditDate(String cinemaCode) {
        Database db = getDatabase();
        Filter filter = Filter.create("cinema_code", cinemaCode).add("date", FilterType.GTE, LocalDate.now());
        SelectResult result = db.select((new Columns().add("date", "Date").add("cinema_code", "CinemaCode"))
                .add("total_advertising_point_leasable_quantity - sold_advertising_point_leasable_quantity", "HaveSoldAdvertising")
                .add("total_marketing_point_leasable_area - sold_marketing_point_leasable_area", "HaveSoldMarketing")
                .add("total_outer_area_leasable_area - sold_outer_area_leasable_area", "HaveSoldOuterArea")
                .add("total_fixed_point_leasable_area - sold_fixed_point_leasable_area", "HaveSoldFixed")).from("inventory").where(filter).orderBy(s(ASC, "date")).result();
        List<ResourceSoldData> list = new ArrayList<>();
        result.each(reader -> {
            ResourceSoldData data = new ResourceSoldData();
            data.setDate(new Date(reader.getDate("Date").getTime()));
            data.setCinemaCode(reader.getString("CinemaCode"));
            data.setAdvertisingPointLeasableQuantityHaveSoldAmount(reader.getInt("HaveSoldAdvertising"));
            data.setFixedPointLeasableAreaHaveSoldAmount(reader.getFloat("HaveSoldFixed"));
            data.setMarketingPointLeasableAreaHaveSoldAmount(reader.getFloat("HaveSoldMarketing"));
            data.setOuterAreaLeasableAreaHaveSoldAmount(reader.getFloat("HaveSoldOuterArea"));
            list.add(data);
        });
        return list;
    }

    public void eachAllInventory(LocalDateTime startTime, Consumer<List<Inventory>> consumer) {
        int page = 1;
        BasicFilter f = f();
        if (startTime != null)
            f.add("update_time", FilterType.GTE, startTime);
        while (true) {
            PageResult<Inventory> pageResult = inventoryRepository.findPage(f, 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }

    public void eachAllOccupation(LocalDateTime startTime, Consumer<List<Occupation>> consumer) {
        int page = 1;
        BasicFilter f = f();
        if (startTime != null)
            f.add("update_time", FilterType.GTE, startTime);
        while (true) {
            PageResult<Occupation> pageResult = occupationRepository.findPage(f, 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
