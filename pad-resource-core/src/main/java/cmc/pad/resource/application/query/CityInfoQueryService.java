package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.domain.city.CityDistrict;
import cmc.pad.resource.domain.city.CityDistrictRepository;
import cmc.pad.resource.domain.city.CityRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2021/4/1.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CityInfoQueryService {
    private final CityRepository cityRepository;
    private final CityDistrictRepository cityDistrictRepository;

    public void eachAllCity(Consumer<List<City>> consumer) {
        int page = 1;
        while (true) {
            PageResult<City> pageResult = cityRepository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }

    public void eachAllCityDistrict(Consumer<List<CityDistrict>> consumer) {
        int page = 1;
        while (true) {
            PageResult<CityDistrict> pageResult = cityDistrictRepository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
