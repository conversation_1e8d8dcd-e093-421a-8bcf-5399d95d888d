package cmc.pad.resource.application.command.point.inventory.occupy.init.contract;

import cmc.pad.resource.application.command.point.inventory.occupy.ApprovalException;
import cmc.pad.resource.application.command.point.inventory.occupy.CancelException;
import cmc.pad.resource.application.command.point.inventory.occupy.OccupyException;
import cmc.pad.resource.application.command.point.inventory.occupy.helper.ContractStatusChecker;
import cmc.pad.resource.domain.inventory.point.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static cmc.pad.resource.application.command.point.inventory.occupy.OccupyException.CONTRACT_IDEMPOTENT_SAME;
import static cmc.pad.resource.application.command.point.inventory.occupy.helper.PublishUtil.send;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationLog.PLO_LOG_CONTRACT_NO;
import static cmc.pad.resource.domain.inventory.point.PointLocationOccupationLog.PLO_LOG_CONTRACT_STATUS;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/3/26.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class NewContractOccupyServiceComponent {
    private final CancelBiz cancelBiz;
    private final NewContractSubmitBiz newContractSubmitBiz;
    private final ContractStatusChecker contractStatusChecker;
    private final NewContractApprovalBiz newContractApprovalBiz;
    private final PointLocationOccupationLogRepository pointLocationOccupationLogRepo;

    public void submit(PointLocationModel.InventoryOccupationContractParam param) {
        log.info(">>>提交新合同, 占用库存, param:{}", JSON.toJSONString(param));
        try {
            checkIsAlreadyActiveContract(param.getContractNo());
            newContractSubmitBiz.checkAndSaveContractBaseInfo(param.getContractNo(), param);
//            occupyInventory(param.getContractNo());
            send(new OccupyOrCancelParam(ContractType.NEW_CONTRACT.value(), param.getContractNo(), ContractStatus.SUBMIT.value()));
        } catch (OccupyException e) {
            if (e.getMessage().equals(CONTRACT_IDEMPOTENT_SAME))
                log.warn("重复提交合同, 不做处理");
            else
                throw e;
        }
    }

    public void updateStatus(PointLocationModel.UpdateStatusParam param) {
        if (ContractStatus.APPROVAL == param.getStatus()) {
            try {
                newContractApprovalBiz.approve(param.getContractNo());
            } catch (ApprovalException e) {
                log.warn(">>>{}", e.getMessage());
            }
        }
        if (ContractStatus.CANCEL == param.getStatus()) {
            try {
                cancelBiz.checkAndCancelContract(param.getContractNo());
//                revertInventory(param.getContractNo());
                send(new OccupyOrCancelParam(ContractType.NEW_CONTRACT.value(), param.getContractNo(), ContractStatus.CANCEL.value()));
            } catch (CancelException e) {
                log.warn(">>>{}", e.getMessage());
            }
        }
    }

    public void occupyInventory(String contractNo) {
        PointLocationOccupationContract contract = contractStatusChecker.checkAndUpdateContractProcessing(contractNo, ContractStatus.SUBMIT);
        newContractSubmitBiz.occupyInventory(contract);
    }

    public void revertInventory(String contractNo) {
        contractStatusChecker.checkAndUpdateContractProcessing(contractNo, ContractStatus.CANCEL);
        cancelBiz.revertInventory(contractNo);
    }

    private void checkIsAlreadyActiveContract(String contractNo) {
        if (!pointLocationOccupationLogRepo.findMany(f(PLO_LOG_CONTRACT_NO, contractNo).add(PLO_LOG_CONTRACT_STATUS, ContractStatus.APPROVAL), 1).isEmpty())
            throw new FaultException("合同已经审批通过, 操作无效");
    }


    private void checkPointLocationId(List<PointLocationOccupationContractDetail> occupationList) {
        occupationList.stream().forEach(occupation -> {
            if (occupation.getPointLocationId() == null || occupation.getPointLocationId() == 0)
                throw new FaultException("点位id参数存在空值");
        });
    }
}
