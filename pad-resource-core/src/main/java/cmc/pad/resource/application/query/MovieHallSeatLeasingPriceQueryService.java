package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.MovieHallSeatPriceQuery;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPriceRepository;
import lombok.RequiredArgsConstructor;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MovieHallSeatLeasingPriceQueryService {
    private final MovieHallSeatLeasingPriceRepository repository;

    public PageResult<MovieHallSeatLeasingPrice> effectivePage(MovieHallSeatPriceQuery query) {
        LocalDate latestEffectiveDate = repository.latestEffectiveDate();
        if (Objects.isNull(latestEffectiveDate)) {
            return new PageResult<>();
        }
        Filter filter = f(MovieHallSeatLeasingPrice.C_EFFECTIVE_DATE, latestEffectiveDate);
        String cityLevel = query.getCityLevel();
        if (StringUtils.isNotBlank(cityLevel) && !"0".equals(cityLevel)) {
            filter = filter.and(f(MovieHallSeatLeasingPrice.C_CITY_LEVEL, cityLevel));
        }
        String cinemaLevel = query.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaLevel) && !"0".equals(cinemaLevel)) {
            filter = filter.and(f(MovieHallSeatLeasingPrice.C_CINEMA_LEVEL, cinemaLevel));
        }

        String movieHallType = query.getMovieHallType();
        if (StringUtils.isNotBlank(movieHallType) && !"0".equals(movieHallType)) {
            filter = filter.and(f(MovieHallSeatLeasingPrice.C_MOVIE_HALL_TYPE, movieHallType));
        }
        return repository.findPage(filter, query.getPageSize(), query.getPageIndex());
    }


    public List<MovieHallSeatLeasingPrice> list(String cityLevel, String cinemaLevel, String movieHallType) {
        Filter filter = f(MovieHallSeatLeasingPrice.C_CITY_LEVEL, cityLevel).add(MovieHallSeatLeasingPrice.C_CINEMA_LEVEL, cinemaLevel);
        filter = filter.and(f(MovieHallSeatLeasingPrice.C_MOVIE_HALL_TYPE, movieHallType));
        return repository.findMany(filter);
    }

    public LocalDate latestEffectiveDate() {
        return repository.latestEffectiveDate();
    }

    public PageResult<MovieHallSeatLeasingPrice> effectivePageBack(MovieHallSeatPriceQuery query) {
        LocalDate latestEffectiveDate = repository.latestEffectiveDate();
        if (Objects.isNull(latestEffectiveDate)) {
            return new PageResult<>();
        }
        Filter filter = f(MovieHallSeatLeasingPrice.C_EFFECTIVE_DATE, latestEffectiveDate);
        String cityLevel = query.getCityLevel();
        if (StringUtils.isNotBlank(cityLevel) && !"0".equals(cityLevel)) {
            filter = filter.and(f(MovieHallSeatLeasingPrice.C_CITY_LEVEL, cityLevel));
        }
        String cinemaLevel = query.getCinemaLevel();
        if (StringUtils.isNotBlank(cinemaLevel) && !"0".equals(cinemaLevel)) {
            filter = filter.and(f(MovieHallSeatLeasingPrice.C_CINEMA_LEVEL, cinemaLevel));
        }
        filter = filter.and(f(MovieHallSeatLeasingPrice.C_MOVIE_HALL_TYPE, ""));//兜底设置
        return repository.findPage(filter, query.getPageSize(), query.getPageIndex());
    }

    public void eachAll(Consumer<List<MovieHallSeatLeasingPrice>> consumer) {
        int page = 1;
        while (true) {
            PageResult<MovieHallSeatLeasingPrice> pageResult = repository.findPage(f(), 1000, page++);
            if (pageResult.getItems().isEmpty()) {
                break;
            }
            consumer.accept(pageResult.getItems());
        }
    }
}
