package cmc.pad.resource.common.repository.jsd;

import cmc.pad.resource.common.repository.Repository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Query;
import mtime.lark.db.jsd.Sorters;
import mtime.lark.util.data.PageResult;

import java.util.List;
import java.util.Optional;

import static cmc.pad.resource.common.repository.jsd.JsdHelper.EMPTY_SORTERS;

/**
 * <AUTHOR>
 */
public abstract class JsdRepository<Entity, ID> extends JsdRepositorySupport<Entity, ID> implements Repository<Entity, ID> {

    protected JsdRepository() {
        super();
    }

    protected JsdRepository(String tableName, String idColumnName, Class<Entity> entityType) {
        super(tableName, idColumnName, entityType);
    }

    @Override
    public Optional<Entity> getIfPresent(ID id) {
        return getIfPresent(id, getDatabase());
    }

    @Override
    public boolean save(Entity entity) {
        return save(entity, getDatabase());
    }

    @Override
    public boolean saveIfAbsent(Entity entity) {
        return saveIfAbsent(entity, getDatabase());
    }

    @Override
    public boolean exist(ID id) {
        return exist(id, getDatabase());
    }

    @Override
    public boolean delete(ID id) {
        return delete(id, getDatabase());
    }

    public long count(Filter filter) {
        return count(filter, getDatabase());
    }

    public List<Entity> findMany(Filter filter) {
        return findMany(filter, EMPTY_SORTERS, getDatabase());
    }

    public List<Entity> findMany(Filter filter, int limit) {
        return findMany(filter, EMPTY_SORTERS, limit, getDatabase());
    }

    public List<Entity> findMany(Filter filter, int offset, int limit) {
        return findMany(filter, EMPTY_SORTERS, offset, limit, getDatabase());
    }

    public PageResult<Entity> findPage(Filter filter, int pageSize, int pageIndex) {
        return findPage(filter, EMPTY_SORTERS, pageSize, pageIndex, getDatabase());
    }

    public List<Entity> findMany(Filter filter, Sorters sorters) {
        return findMany(filter, sorters, getDatabase());
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, int limit) {
        return findMany(filter, sorters, limit, getDatabase());
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, int offset, int limit) {
        return findMany(filter, sorters, offset, limit, getDatabase());
    }

    public PageResult<Entity> findPage(Filter filter, Sorters sorters, int pageSize, int pageIndex) {
        return findPage(filter, sorters, pageSize, pageIndex, getDatabase());
    }

    public Optional<Entity> findOne(Filter filter) {
        return findOne(filter, getDatabase());
    }

    public int delete(Filter filter) {
        return delete(filter, getDatabase());
    }

    public Query newQuery() {
        return getDatabase();
    }

    protected abstract Database getDatabase();

}
