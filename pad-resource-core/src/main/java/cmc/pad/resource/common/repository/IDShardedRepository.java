package cmc.pad.resource.common.repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface IDShardedRepository<Entity, ID>
        extends ShardedRepository<Entity, ID, ID>, Repository<Entity, ID> {

    default Entity get(ID id) {
        return get(id, id);
    }

    default Optional<Entity> getIfPresent(ID id) {
        return getIfPresent(id, id);
    }

    default boolean exist(ID id) {
        return exist(id, id);
    }

    default boolean delete(ID id) {
        return delete(id, id);
    }
}
