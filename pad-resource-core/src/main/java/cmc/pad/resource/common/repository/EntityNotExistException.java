package cmc.pad.resource.common.repository;

import lombok.Getter;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 */
@Getter
public class EntityNotExistException extends RuntimeException {

    private final String entityName;
    private final Object id;

    public EntityNotExistException(Class<?> entityType, Object id) {
        this(entityType.getSimpleName(), id);
    }

    public EntityNotExistException(String entityName, Object id) {
        super(MessageFormat.format("{0} with id value of ''{1}'' not exist", entityName, id));
        this.entityName = entityName;
        this.id = id;
    }
}
