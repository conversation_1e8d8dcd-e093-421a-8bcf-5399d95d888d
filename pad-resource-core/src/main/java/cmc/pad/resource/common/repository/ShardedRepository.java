package cmc.pad.resource.common.repository;


import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ShardedRepository<Entity, ID, ShardKey> {

    /**
     * Save entity
     *
     * @param entity entity to be saved
     * @return true when created a new entity, false when update an existed entity
     */
    boolean save(Entity entity);

    /**
     * Save entity
     *
     * @param entity entity to be saved
     * @return true when saved, false entity already exist.
     */
    boolean saveIfAbsent(Entity entity);

    /**
     * Get entity
     *
     * @param id       entity id
     * @param shardKey shardKey
     * @return entity with the given id
     * @throws EntityNotExistException if entity not exist
     */
    default Entity get(ID id, ShardKey shardKey) {
        return getIfPresent(id, shardKey).orElseThrow(() -> new EntityNotExistException(getEntityType(), id));
    }

    /**
     * Get entity
     *
     * @param id       entity id
     * @param shardKey shardKey
     * @return Optional value contains entity instance or null
     */
    Optional<Entity> getIfPresent(ID id, ShardKey shardKey);

    /**
     * Check if entity exist with the given id
     *
     * @param id       entity id
     * @param shardKey shardKey
     * @return <code>true</code> if exist, otherwise <code>false</code>
     */
    boolean exist(ID id, ShardKey shardKey);

    /**
     * Delete entity with the given id
     *
     * @param id       entity id
     * @param shardKey shardKey
     * @return <code>true</code> if deleted, otherwise <code>false</code>
     */
    boolean delete(ID id, ShardKey shardKey);

    /**
     * Get entity type
     *
     * @return entity type
     */
    Class<Entity> getEntityType();


}
