package cmc.pad.resource.common.repository.jsd;

import cmc.pad.resource.common.repository.ShardedRepository;
import mtime.lark.db.jsd.Database;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Query;
import mtime.lark.db.jsd.Sorters;
import mtime.lark.util.data.PageResult;

import java.util.List;
import java.util.Optional;

import static cmc.pad.resource.common.repository.jsd.JsdHelper.EMPTY_SORTERS;

/**
 * <AUTHOR>
 */
public abstract class JsdShardedRepository<Entity, ID, ShardKey> extends JsdRepositorySupport<Entity, ID> implements ShardedRepository<Entity, ID, ShardKey> {

    protected JsdShardedRepository() {
        super();
    }

    protected JsdShardedRepository(String tableName, String idColumnName, Class<Entity> entityType) {
        super(tableName, idColumnName, entityType);
    }

    @Override
    public boolean save(Entity entity) {
        return save(entity, newQuery(getShardKey(entity)));
    }

    @Override
    public boolean saveIfAbsent(Entity entity) {
        return saveIfAbsent(entity, newQuery(getShardKey(entity)));
    }

    @Override
    public Optional<Entity> getIfPresent(ID id, ShardKey shardKey) {
        return getIfPresent(id, newQuery(shardKey));
    }

    @Override
    public boolean exist(ID id, ShardKey shardKey) {
        return exist(id, newQuery(shardKey));
    }

    @Override
    public boolean delete(ID id, ShardKey shardKey) {
        return delete(id, newQuery(shardKey));
    }

    public int delete(Filter filter, ShardKey shardKey) {
        return delete(filter, newQuery(shardKey));
    }

    public List<Entity> findMany(Filter filter, ShardKey shardKey) {
        return findMany(filter, EMPTY_SORTERS, newQuery(shardKey));
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, ShardKey shardKey) {
        return findMany(filter, sorters, newQuery(shardKey));
    }

    public List<Entity> findMany(Filter filter, int limit, ShardKey shardKey) {
        return findMany(filter, EMPTY_SORTERS, limit, newQuery(shardKey));
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, int limit, ShardKey shardKey) {
        return findMany(filter, sorters, limit, newQuery(shardKey));
    }

    public List<Entity> findMany(Filter filter, int offset, int limit, ShardKey shardKey) {
        return findMany(filter, EMPTY_SORTERS, offset, limit, newQuery(shardKey));
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, int offset, int limit, ShardKey shardKey) {
        return findMany(filter, sorters, offset, limit, newQuery(shardKey));
    }

    public PageResult<Entity> findPage(Filter filter, int pageSize, int pageIndex, ShardKey shardKey) {
        return findPage(filter, EMPTY_SORTERS, pageSize, pageIndex, shardKey);
    }

    public PageResult<Entity> findPage(Filter filter, Sorters sorters, int pageSize, int pageIndex, ShardKey shardKey) {
        return findPage(filter, sorters, pageSize, pageIndex, newQuery(shardKey));
    }

    public Optional<Entity> findOne(Filter filter, ShardKey shardKey) {
        return findOne(filter, newQuery(shardKey));
    }

    public Query newQuery(ShardKey shardKey) {
        return getDatabase(shardKey);
    }

    protected abstract Database getDatabase(ShardKey shardKey);

    protected abstract ShardKey getShardKey(Entity entity);

}
