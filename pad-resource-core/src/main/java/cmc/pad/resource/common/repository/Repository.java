package cmc.pad.resource.common.repository;


import java.util.Optional;

/**
 * DDD Repository interface
 *
 * <AUTHOR>
 */
public interface Repository<Entity, ID> {

    /**
     * Save entity
     *
     * @param e entity to be saved
     * @return true when created a new entity, false when update an existed entity
     */
    boolean save(Entity e);

    /**
     * Save entity
     *
     * @param entity entity to be saved
     * @return true when saved, false entity already exist.
     */
    boolean saveIfAbsent(Entity entity);

    /**
     * Get entity
     *
     * @param id entity id
     * @return entity with the given id
     * @throws EntityNotExistException if entity not exist
     */
    default Entity get(ID id) {
        return getIfPresent(id).orElseThrow(() -> new EntityNotExistException(getEntityType(), id));
    }

    /**
     * Get entity if present
     *
     * @param id entity id
     * @return Optional value contains instance entity or null
     */
    Optional<Entity> getIfPresent(ID id);

    /**
     * Check if entity exist with the given id
     *
     * @param id entity id
     * @return <code>true</code> if exist, otherwise <code>false</code>
     */
    boolean exist(ID id);

    /**
     * Delete entity with the given id
     *
     * @param id entity id
     * @return <code>true</code> if deleted, otherwise <code>false</code>
     */
    boolean delete(ID id);

    /**
     * Get entity type
     *
     * @return entity type
     */
    Class<Entity> getEntityType();
}
