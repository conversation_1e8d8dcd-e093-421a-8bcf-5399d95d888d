package cmc.pad.resource.common.repository.jsd;

import lombok.Getter;
import mtime.lark.db.jsd.*;
import mtime.lark.db.jsd.clause.WhereClause;
import mtime.lark.db.jsd.result.BuildResult;
import mtime.lark.db.jsd.result.ExecuteResult;
import mtime.lark.db.jsd.result.SelectResult;
import mtime.lark.util.data.PageResult;
import org.springframework.core.ResolvableType;

import java.util.List;
import java.util.Optional;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Getter
public abstract class JsdRepositorySupport<Entity, ID> {

    private final static int NO_LIMIT = 0;
    private final static int NO_OFFSET = 0;
    private final String tableName;
    private final String idColumnName;
    private final Class<Entity> entityType;

    @SuppressWarnings("unchecked")
    protected JsdRepositorySupport() {
        ResolvableType resolvableType = ResolvableType.forClass(this.getClass());
        this.entityType = (Class<Entity>) resolvableType.getSuperType().getSuperType().getGeneric(0).resolve();
        Mapper.EntityInfo info = Mapper.getEntityInfo(entityType);
        this.tableName = info.getTable();
        this.idColumnName = info.getIdColumns() == null ? "id" : info.getIdColumns()[0];
    }

    protected JsdRepositorySupport(String tableName, String idColumnName, Class<Entity> entityType) {
        this.tableName = tableName;
        this.idColumnName = idColumnName;
        this.entityType = entityType;
    }

    public boolean save(Entity entity, Query query) {
        BuildResult insertInto = query.insert(entity).print();
        String replaceIntoSql = "REPLACE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = query.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows() == 1; // 1 means insert, 2 means update
        }
    }

    public boolean saveIfAbsent(Entity entity, Query query) {
        BuildResult insertInto = query.insert(entity).print();
        String replaceIntoSql = "INSERT IGNORE" + insertInto.getSql().substring(6);
        try (ExecuteResult result = query.execute(replaceIntoSql, insertInto.getArgs().toArray()).result()) {
            return result.getAffectedRows() == 1; // 1 means saved, 0 means ignored
        }
    }

    public Optional<Entity> getIfPresent(ID id, Query query) {
        return findOne(f(idColumnName, id), query);
    }

    public boolean exist(ID id, Query query) {
        return getIfPresent(id, query).isPresent();
    }

    public boolean delete(ID id, Query query) {
        return query.delete(tableName).where(f(idColumnName, id)).result().getAffectedRows() > 0;
    }

    public int delete(Filter filter, Query query) {
        return query.delete(tableName).where(filter).result().getAffectedRows();
    }

    public long count(Filter filter, Query query) {
        return query.select(Shortcut.count()).from(tableName).where(filter).result().value(Long.class);
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, Query query) {
        return findMany(filter, sorters, NO_OFFSET, NO_LIMIT, query);
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, int limit, Query query) {
        return findMany(filter, sorters, NO_OFFSET, limit, query);
    }

    public List<Entity> findMany(Filter filter, Sorters sorters, int offset, int limit, Query query) {
        SelectResult select;
        WhereClause where = query.select(entityType).where(filter);
        if (sorters != JsdHelper.EMPTY_SORTERS) {
            where.orderBy(sorters);
        }
        if (limit != NO_LIMIT) {
            select = where.limit(offset, limit).result();
        } else {
            select = where.result();
        }
        return select.all(entityType);
    }

    public Optional<Entity> findOne(Filter filter, Query query) {
        return Optional.ofNullable(query.select(entityType).where(filter).result().one(entityType));
    }

    public PageResult<Entity> findPage(Filter filter, Sorters sorters, int pageSize, int pageIndex, Query query) {
        PageResult<Entity> r = new PageResult<>();
        long total = count(filter, query);
        if (total == 0) {
            return r;
        }
        r.setTotalCount((int) total);
        r.setItems(findMany(filter, sorters, (pageIndex - 1) * pageSize, pageSize, query));
        return r;
    }

}
