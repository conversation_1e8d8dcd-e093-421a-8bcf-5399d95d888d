package cmc.pad.resource.util;

import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import mtime.lark.util.redis.RedisClient;

import java.util.Set;
import java.util.function.Supplier;

/**
 * Created by fuwei on 2017/8/3.
 */
@Slf4j
public class RedisLockSupport {
    private static final int EXPIRE_TIME = 20;//单位秒
    private static RedisLockSupport instance = new RedisLockSupport();
    private static RedisClient redisClient = new RedisClient("RedisCache");

    public static final String LOCKED = "TRUE";

    private RedisLockSupport() {
    }

    public static RedisLockSupport getInstance() {
        return instance;
    }

    public static void lockTemplate(Set<Integer> keys, Runnable chunk) {
        keys.stream().forEach(key -> getInstance().lock(key.toString()));
        try {
            chunk.run();
        } finally {
            keys.stream().forEach(key -> getInstance().unLock(key.toString()));
        }
    }

    public static void lockTemplate(String key, Runnable chunk) {
        getInstance().lock(key);
        try {
            chunk.run();
        } finally {
            getInstance().unLock(key);
        }
    }

    public static Object lockTemplate(String key, Supplier supplier) {
        getInstance().lock(key);
        try {
            return supplier.get();
        } finally {
            getInstance().unLock(key);
        }
    }

    public void lock(String key) {
        log.info("上锁,key:{}", key);
        long expire = System.currentTimeMillis() + (EXPIRE_TIME * 1000L);
        while (true) {
            if (isTimeout(expire))
                throw new FaultException("锁超时");
            if (getLock(key)) {
                break;
            }
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                log.error("lock key {} error", key, e);
                Thread.currentThread().interrupt();
                throw new FaultException("加锁中断失败");
            }
        }
    }

    private boolean getLock(String key) {
        long result = redisClient.invoke(shardedJedis -> shardedJedis.setnx(key, LOCKED));
        //锁不存在的话，设置锁并设置锁过期时间，即加锁
        if (result == 1) {
            redisClient.expire(key, EXPIRE_TIME);//设置锁过期时间是为了在没有释放
            //锁的情况下锁过期后消失，不会造成永久阻塞
            return true;
        }
        return false;
    }

    private boolean isTimeout(long expire) {
        return System.currentTimeMillis() - expire >= 0;
    }

    public void unLock(String key) {
        try {
            redisClient.delete(key);
            log.info("释放锁,key:{}", key);
        } catch (Exception e) {
            throw new FaultException("锁释放失败, key:" + key);
        }
    }
}
