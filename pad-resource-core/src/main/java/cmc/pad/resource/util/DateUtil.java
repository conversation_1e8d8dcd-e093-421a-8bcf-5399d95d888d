package cmc.pad.resource.util;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import mtime.lark.util.lang.StrKit;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by fuwei on 2022/2/24.
 */
@Slf4j
public class DateUtil {
    private static final String FOR_MAT_PATTEN = "yyyy-MM-dd HH:mm:ss";
    private static final String FOR_MAT_PATTEN_1 = "yyyyMMdd";
    private static final String FOR_MAT_PATTEN_date = "yyyy-MM-dd";

    public static DateRange queryMaxAndMin(Set<LocalDate> dateSet) {
        LocalDate minDate = dateSet.stream().min(Comparator.comparing(LocalDate::toEpochDay)).get();
        LocalDate maxDate = dateSet.stream().max(Comparator.comparing(LocalDate::toEpochDay)).get();
        return new DateRange(minDate, maxDate);
    }

    public static List<DateUtil.DateRange> expandDateRange(DateRange bigRange, DateRange smallRange) {
        List<DateUtil.DateRange> dateRanges = Lists.newArrayList();
        if (bigRange.getStart().isBefore(smallRange.getStart())) {
            dateRanges.add(new DateUtil.DateRange(bigRange.getStart(), smallRange.getStart().minusDays(1)));
        }
        if (bigRange.getEnd().isAfter(smallRange.getEnd())) {
            dateRanges.add(new DateUtil.DateRange(smallRange.getEnd().plusDays(1), bigRange.getEnd()));
        }
        return dateRanges;
    }

    public static List<DateRange> splitYearRange(LocalDate start, LocalDate end) {
        Period p = Period.between(start, end);
        int years = p.getYears();
        List<DateRange> list = Lists.newArrayList();
        if (years == 0) {
            list.add(new DateRange(start, end));
        } else {
            for (int i = 0; i <= years; i++) {
                if (i == 0) {
                    list.add(new DateRange(start, LocalDate.of(start.getYear(), 12, 31)));
                }
                if (i != 0 && i != years) {
                    start = LocalDate.of(start.getYear() + 1, 1, 1);
                    list.add(new DateRange(start, LocalDate.of(start.getYear(), 12, 31)));
                }
                if (i == years) {
                    list.add(new DateRange(LocalDate.of(end.getYear(), 1, 1), end));
                }
            }
        }
        return list;
    }

    public static List<LocalDate> getDates(LocalDate start, LocalDate end) {
        if (start.isAfter(end))
            throw new FaultException("时间段异常");
        List<LocalDate> list = Lists.newArrayList();
        while (true) {
            list.add(start);
            if (start.isEqual(end)) {
                return list;
            }
            start = start.plusDays(1);
        }
    }

    public static void main(String[] args) {
        expandDateRange(
                new DateRange(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 10)),
                new DateRange(LocalDate.of(2022, 1, 1), LocalDate.of(2022, 1, 3))
        );
        System.out.println();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DateRange {
        LocalDate start;
        LocalDate end;
    }

    public static long getUnixTime() {
        return System.currentTimeMillis() / 1000L;
    }

    /**
     * yyyy-MM-dd 转localDate
     */
    public static LocalDate dateStr2LocalDate(String dateStr) {
        if (StrKit.isBlank(dateStr)) {
            return null;
        }
        try {
            LocalDate.parse(dateStr);
        } catch (Exception e) {
            log.error("parse data has error  ...{}", e);
            return null;
        }
        return LocalDate.parse(dateStr);
    }

    /**
     * localDate转yyyy-MM-dd字符串
     */
    public static String localDate2Str(LocalDate date) {
        return localDate2Str(date, FOR_MAT_PATTEN_date);
    }


    /**
     * yyyy-MM-dd 转localDateTime
     */
    public static LocalDateTime dateStr2LocalDateTime(String dateStr) {
        if (null != dateStr && isDateTime(dateStr)) {
            return dateTimeStr2LocalDateTime(dateStr);
        }
        LocalDate date = dateStr2LocalDate(dateStr);
        return LocalDateTime.of(date, LocalTime.of(0, 0, 0));
    }

    public static LocalDateTime date2StartLocalDateTime(LocalDate date) {
        return LocalDateTime.of(date, LocalTime.of(0, 0, 0));
    }

    /**
     * localDate转指定格式的字符串
     *
     * @param date 时间
     * @param date 格式
     */
    public static String localDate2Str(LocalDate date, String format) {
        return date.format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * LocalDateTime 转str
     */
    public static String localDateTime2Str(LocalDateTime localDateTime) {
        if (null == localDateTime) return null;
        return localDateTime2Str(localDateTime, FOR_MAT_PATTEN);
    }

    /**
     * LocalDateTime 转str
     *
     * @param localDateTime 时间
     * @param pattern       pattern
     */
    public static String localDateTime2Str(LocalDateTime localDateTime, String pattern) {
        return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * yyyy-MM-dd HH:mm:ss 转localDate
     */
    public static LocalDate dateTimeStr2LocalDate(String dateTimeStr) {
        return LocalDate.parse(dateTimeStr, DateTimeFormatter.ofPattern(FOR_MAT_PATTEN));
    }

    /**
     * yyyy-MM-dd HH:mm:ss 转localDateTime
     */
    public static LocalDateTime dateTimeStr2LocalDateTime(String dateTimeStr) {
        if (Strings.isNullOrEmpty(dateTimeStr))
            return null;
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(FOR_MAT_PATTEN));
    }

    /**
     * yyyy-MM-dd 转localDateTime
     */
    public static LocalDateTime dateStr2StartLocalDateTime(String dateStr) {
        if (null != dateStr && isDateTime(dateStr)) {
            return dateTimeStr2LocalDateTime(dateStr);
        }
        LocalDate date = dateStr2LocalDate(dateStr);
        if (date == null) return null;
        return LocalDateTime.of(date, LocalTime.of(0, 0, 0));
    }


    /**
     * yyyy-MM-dd 转localDateTime
     */
    public static LocalDateTime dateStr2EndLocalDateTime(String dateStr) {
        LocalDate date = dateStr2LocalDate(dateStr);
        if (date == null) return null;
        return LocalDateTime.of(date, LocalTime.of(23, 59, 59));
    }

    public static LocalDateTime date2EndLocalDateTime(LocalDate date) {
        if (date == null) return null;
        return LocalDateTime.of(date, LocalTime.of(23, 59, 59));
    }

    /**
     * 日期转数字 yyyyMMddHH
     */
    public static Integer dateTime2Number(LocalDateTime dateTime) {
        return Integer.valueOf(dateTime.format(DateTimeFormatter.ofPattern("yyyyMMddHH")));
    }

    /**
     * 日期转 yyyyMM
     */
    public static Integer date2MonthNumber(LocalDate date) {
        return Integer.valueOf(date.format(DateTimeFormatter.ofPattern("yyyyMM")));
    }

    /**
     * 日期转数字 yyyyMMdd
     */
    public static Integer date2Number(LocalDate date) {
        return Integer.valueOf(date.format(DateTimeFormatter.ofPattern(FOR_MAT_PATTEN_1)));
    }

    /**
     * 数字转日期字符串
     */
    public static String number2LocalDateStr(Integer number) {
        return LocalDate.parse("" + number, DateTimeFormatter.ofPattern(FOR_MAT_PATTEN_1)).toString();
    }

    /**
     * 月份数字转日期字符串
     */
    public static String numberMonth2DateStr(Integer number) {
        int year = number / 100;
        int month = number - number / 100 * 100;
        return year + "-" + (month < 10 ? "0" + month : month);
    }

    /**
     * 获得日期所在第几周，指定周一到周日为一周
     */
    public static int getWeek(LocalDate date) {
        return date.get(WeekFields.of(DayOfWeek.MONDAY, 1).weekOfYear());
    }

    /**
     * 根据日期获取周的起止日期
     */
    public static LocalDate[] getWeekDays(LocalDate date) {
        return getWeekDays(getYearWeek(date));
    }

    /**
     * 获得日期所在年周，指定周一到周日为一周
     */
    public static int getYearWeek(LocalDate date) {
        int week = date.get(WeekFields.of(DayOfWeek.MONDAY, 1).weekOfYear());
        Integer year = date.getYear();
        String yearWeek = week < 10 ? year + "0" + week : year + "" + week;
        return Integer.parseInt(yearWeek);
    }

    /**
     * 根据年周获取起止时间
     */
    public static LocalDate[] getWeekDays(Integer yearWeek) {
        LocalDate startDay;
        LocalDate endDay;
        int year = yearWeek / 100;
        int weekNo = yearWeek - year * 100;
        LocalDate date = getFirstDayOfWeek(year, weekNo);
        // 第一周
        if (1 == weekNo && date != null) {// 1月1日到周日
            startDay = date.withDayOfYear(1);
            int weekDay = startDay.getDayOfWeek().getValue();
            endDay = date.plusDays(7l - weekDay);
        } else if (53 == weekNo && date != null) {// 53周可能为跨年周
            if (12 == date.plusDays(6).getMonthValue()) {// 如果是一整周，周一到周日
                startDay = date;
                endDay = date.plusDays(6);
            } else {// 拆开跨年周，周一到年尾
                endDay = date.plusMonths(1).withDayOfMonth(1).minusDays(1);
                int weekDay = endDay.getDayOfWeek().getValue();
                startDay = endDay.minusDays(weekDay).plusDays(1);
            }
        } else {
            startDay = date;
            endDay = date == null ? null : date.plusDays(6);
        }
        return new LocalDate[]{startDay, endDay};
    }

    /**
     * 获取某年的第几周的开始日期
     */
    private static LocalDate getFirstDayOfWeek(int year, int week) {
        Calendar c = new GregorianCalendar();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, Calendar.JANUARY);
        c.set(Calendar.DATE, 1);
        if (week == 1) {
            return LocalDate.of(year, Month.JANUARY, 1);
        }
        Calendar cal = (GregorianCalendar) c.clone();
        cal.add(Calendar.DATE, (week - 1) * 7);
        Date d = getFirstDayOfWeek(cal.getTime());
        return dateStr2LocalDate(new SimpleDateFormat(FOR_MAT_PATTEN_1).format(d));
    }

    /**
     * 获取当前时间所在周的开始日期
     */
    public static Date getFirstDayOfWeek(Date date) {
        Calendar c = new GregorianCalendar();
        c.setFirstDayOfWeek(Calendar.MONDAY);
        c.setTime(date);
        c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek()); // Monday
        return c.getTime();
    }

    /**
     * 获取两个时间内的所有小时
     */
    public static List<Integer> hoursBetween(LocalDateTime sDate, LocalDateTime eDate) {
        List<Integer> list = new ArrayList<>();
        while (sDate.isBefore(eDate) || sDate.equals(eDate)) {
            String hour = sDate.getHour() < 10 ? "0" + sDate.getHour() : String.valueOf(sDate.getHour());
            String day = sDate.getDayOfMonth() < 10 ? "0" + sDate.getDayOfMonth()
                    : String.valueOf(sDate.getDayOfMonth());
            String month = sDate.getMonthValue() < 10 ? "0" + sDate.getMonthValue()
                    : String.valueOf(sDate.getMonthValue());
            list.add(Integer.parseInt(sDate.getYear() + month + day + hour));
            sDate = sDate.plusHours(1);
        }
        return list;
    }

    /**
     * 获取两个时间之间所有日期
     */
    public static List<Integer> daysBetween(String sdate, String edate) {
        LocalDate sd = dateStr2LocalDate(sdate);
        LocalDate ed = dateStr2LocalDate(edate);
        return daysBetween(sd, ed);
    }

    /**
     * 获取两个时间之间所有日期
     */
    public static List<Integer> daysBetween(LocalDate sdate, LocalDate edate) {
        List<Integer> list = new ArrayList<>();
        while (sdate.isBefore(edate) || sdate.equals(edate)) {
            String day = sdate.getDayOfMonth() < 10 ? "0" + sdate.getDayOfMonth()
                    : String.valueOf(sdate.getDayOfMonth());
            String month = sdate.getMonthValue() < 10 ? "0" + sdate.getMonthValue()
                    : String.valueOf(sdate.getMonthValue());
            list.add(Integer.parseInt(sdate.getYear() + month + day));
            sdate = sdate.plusDays(1);
        }
        return list;
    }

    /**
     * 获取两个日期之间周数
     */
    public static List<Integer> weeksBetween(String sdate, String edate) {
        return weeksBetween(dateStr2LocalDate(sdate), dateStr2LocalDate(edate));
    }

    /**
     * 获取两个日期之间周数
     */
    public static List<Integer> weeksBetween(LocalDate sdate, LocalDate edate) {
        List<Integer> list = new ArrayList<>();
        Integer startYearWeek = getYearWeek(sdate);
        Integer endYearWeek = getYearWeek(edate);
        for (int i = startYearWeek; i <= endYearWeek; i++) {
            list.add(i);
            int week = i - i / 100 * 100;
            if (week == 53) {
                i = i / 100 * 100 + 100;
            }
        }
        return list;
    }

    /**
     * 获取两个日期之间月数
     */
    public static List<Integer> monthsBetween(String sdate, String edate) {
        return monthsBetween(dateStr2LocalDate(sdate), dateStr2LocalDate(edate));
    }

    /**
     * 获取两个日期之间月数
     */
    public static List<Integer> monthsBetween(LocalDate sdate, LocalDate edate) {
        List<Integer> list = new ArrayList<>();
        Integer startMonth = date2MonthNumber(sdate);
        Integer endMonth = date2MonthNumber(edate);
        for (int i = startMonth; i <= endMonth; i++) {
            list.add(i);
            int month = i - i / 100 * 100;
            if (month == 12) {
                i = i / 100 * 100 + 100;
            }
        }
        return list;
    }

    /**
     * 获取两个月份之间月数
     *
     * @param startMonth yyyyMM
     * @param endMonth   yyyyMM
     */
    public static List<Integer> monthsBetween(Integer startMonth, Integer endMonth) {
        List<Integer> list = new ArrayList<>();
        for (int i = startMonth; i <= endMonth; i++) {
            list.add(i);
            int month = i - i / 100 * 100;
            if (month == 12) {
                i = i / 100 * 100 + 100;
            }
        }
        return list;
    }

    /**
     * 获取下一个月份
     *
     * @param curMonth yyyyMM
     * @return nextMonth yyyyMM
     */
    public static Integer nextMonth(Integer curMonth) {
        int month = curMonth - curMonth / 100 * 100;
        int nextMonth = 0;
        if (month == 12) {
            nextMonth = curMonth / 100 * 100 + 100 + 1;
        } else {
            nextMonth = curMonth + 1;
        }
        return nextMonth;
    }

    /**
     * 获取两个日期之间年数
     */
    public static List<Integer> yearsBetween(LocalDate sdate, LocalDate edate) {
        return yearsBetween(sdate.getYear(), edate.getYear());
    }

    /**
     * 获取两个日期之间年数
     */
    public static List<Integer> yearsBetween(Integer syear, Integer eyear) {
        List<Integer> list = new ArrayList<>();
        for (int i = syear; i <= eyear; i++) {
            list.add(i);
        }
        return list;
    }

    /**
     * 获取两个日期之间年数
     *
     * @param sdate
     * @param edate
     * @return
     */
    /*public static List<Integer> yearsBetween(String sdate, String edate) {
        Integer startYear = dateStr2LocalDate(sdate).getYear();
		Integer endYear = dateStr2LocalDate(edate).getYear();
		return yearsBetween(startYear, endYear);
	}*/

    /**
     * 默认最大localDateTime
     */
    public static LocalDateTime defaultMaxLocalDateTime() {
        return LocalDateTime.of(LocalDate.now().plusYears(100), LocalTime.MAX);
    }

    /**
     * 数字日期转换成字符串yyyy-MM-dd 20160101->2016-01-01
     *
     * @param number 数字型的日期
     * @return20160101
     */
    public static String numberDate2DateStr(Integer number) {
        int year = number / 10000;
        int month = (number - (number / 10000 * 10000)) / 100;
        int day = (number - (number / 10000 * 10000)) - month * 100;
        return year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
    }

	/*public static void main(String[] args) {
        //System.out.println(DateUtil.monthsBetween(201612,201701));
		while (true) {
			try {
				LocalDateTime localDateTime = dateTimeStr2LocalDateTime("0000-00-00 00:00:00");
			} catch (Exception e) {
				System.out.println("异常");
				break;
			}

		}

		System.out.println("");
	}*/

    public static LocalDateTime getDateMax(LocalDate queryDate) {
        return LocalDateTime.of(queryDate, LocalTime.MAX);
    }

    public static LocalDateTime getDateMin(LocalDate queryDate) {
        return LocalDateTime.of(queryDate, LocalTime.MIN);
    }


    /**
     * 根据起始时间,结束时间,计算时间差,单位秒
     *
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 时差描述
     */
    public static long getTimeDifference(LocalDateTime startTime, LocalDateTime endTime) {
        Duration between = Duration.between(startTime, endTime);
        return between.getSeconds();
    }

    /**
     * 分转秒
     *
     * @param minute 分钟数
     * @return 秒数
     */
    public static long minuteConvertToSecond(long minute) {
        return minute * 60;
    }


    public static boolean isDateTime(String timeStr) {
        Pattern p = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$");
        Matcher m = p.matcher(timeStr);
        return m.matches();
    }
}
