package cmc.pad.resource.util;

import com.google.common.collect.Maps;

import java.time.LocalDate;
import java.util.Map;

/**
 * Created by f<PERSON><PERSON><PERSON> on 2022/5/30.
 */
public class DateMapSumAreaUtil {
    private Map<LocalDate, Float> dateMapSumArea = Maps.newConcurrentMap();

    public void add(LocalDate date, Float area) {
        if (dateMapSumArea.containsKey(date)) {
            dateMapSumArea.put(date, dateMapSumArea.get(date).floatValue() + area.floatValue());
        } else {
            dateMapSumArea.put(date, area);
        }
    }

    public Map<LocalDate, Float> getDateMapSumAreaResult() {
        return dateMapSumArea;
    }

    public static void main(String[] args) {
        DateMapSumAreaUtil dateMapSumAreaUtil = new DateMapSumAreaUtil();
        dateMapSumAreaUtil.add(LocalDate.of(2022, 5, 1), 10F);
        dateMapSumAreaUtil.add(LocalDate.of(2022, 5, 1), 20F);
        dateMapSumAreaUtil.add(LocalDate.of(2022, 5, 1), 30.5F);
        dateMapSumAreaUtil.add(LocalDate.of(2022, 5, 2), 99F);

        dateMapSumAreaUtil.getDateMapSumAreaResult().forEach((date, area) -> {
            System.out.println(date + " " + area);
        });

    }
}
