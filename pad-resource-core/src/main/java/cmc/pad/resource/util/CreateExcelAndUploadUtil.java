package cmc.pad.resource.util;

import cmc.pad.resource.adapter.FileServiceAdapter;
import cmc.pad.resource.domain.resource.PointLocationModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import mx.common.excel.ExportExcel;
import mx.common.excel.bean.XBeanExport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Created by fuwei on 2022/1/20.
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CreateExcelAndUploadUtil {
    private final FileServiceAdapter fileServiceAdapter;

    public String createExcelAndUpload(String fileName, List list, Class clazz) {
        return fileServiceAdapter.uploadFile(createExcel(fileName, list, clazz), fileName);
    }

    public byte[] createExcel(String fileName, List list, Class clazz) {
        XBeanExport xBean = ExportExcel.BeanExport(clazz);
        xBean.createBeanSheet(fileName.substring(0, fileName.indexOf(".")), null, PointLocationModel.ExportExcel.class).addData(list);
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            xBean.write(stream);
            xBean.dispose();
            return stream.toByteArray();
        } catch (IOException e) {
            log.info(">>>生成excel异常");
            throw new FaultException();
        }
    }
}
