package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.importing.FileImportStatus;
import cmc.pad.resource.test.TestBase;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public class MysqlFileImportRepositoryTest extends TestBase {

    private MysqlFileImportRepository repository = new MysqlFileImportRepository();

    @Test
    public void saveGenerateIdTest() {
        FileImport fileImport = new FileImport();
        fileImport.setFileId("fileID");
        fileImport.setFileName("fileName");
        fileImport.setFileCategory(FileCategory.NAMED_MOVIE_HALL_LEASING_PRICE);
        fileImport.setVersion("1.2");
        fileImport.setEffectiveDate(LocalDate.now());
        fileImport.setStatus(FileImportStatus.UNDERWAY);
        fileImport.setBeginTime(LocalDateTime.now());
        fileImport.setEndTime(LocalDateTime.now());
        fileImport.setImporter(1);
        int i = repository.saveGenerateId(fileImport);
        Assert.assertEquals(106, i);
    }

    @Test
    public void updateStatusTest() {
        boolean b = repository.updateStatus(105, 1);
        Assert.assertTrue(b);
        boolean b1 = repository.updateStatus(105, 1, LocalDateTime.now());
        Assert.assertTrue(b1);
    }

    @Test
    public void findPageTest() {
        PageResult<FileImport> page = repository.findPage(FileCategory.CINEMA_LEVEL.value(), 1, 1);
        Assert.assertEquals(1, page.getTotalCount());
    }

    @Test
    public void listTest() {
        List<FileImport> list = repository.list(FileCategory.CINEMA_LEVEL);
        Assert.assertEquals(1, list.size());
    }
}
