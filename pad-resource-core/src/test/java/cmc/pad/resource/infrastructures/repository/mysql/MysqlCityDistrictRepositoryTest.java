package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.application.query.data.ResultList;
import cmc.pad.resource.domain.city.CityDistrictListModel;
import cmc.pad.resource.test.TestBase;
import org.junit.Test;

public class MysqlCityDistrictRepositoryTest extends TestBase {
    private MysqlCityDistrictRepository repository = new MysqlCityDistrictRepository();

    @Test
    public void find() throws Exception {
        ResultList<CityDistrictListModel> resultList = repository.find(null, null,
                null, null,
                null, null,
                1, 10
        );
        System.out.println(resultList.getTotalCount());
        resultList.getList().forEach(item -> {
            System.out.println(item);
        });
    }
}