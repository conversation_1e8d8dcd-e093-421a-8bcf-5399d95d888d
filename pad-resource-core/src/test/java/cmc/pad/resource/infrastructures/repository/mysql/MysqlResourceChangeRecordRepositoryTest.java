package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.resource.ResourceChangeRecord;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import org.junit.Assert;
import org.junit.Test;

public class MysqlResourceChangeRecordRepositoryTest extends TestBase {

    private MysqlResourceChangeRecordRepository repository = new MysqlResourceChangeRecordRepository();

    @Test
    public void saveResourceChangeRecord() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            ResourceChangeRecord resourceChangeRecord = new ResourceChangeRecord();
            resourceChangeRecord.setCinemaCode("test001");
            resourceChangeRecord.setAdvertisingPointLeasableQuantity(1);
            resourceChangeRecord.setFixedPointLeasableArea(1f);
            resourceChangeRecord.setOuterAreaLeasableArea(1f);
            resourceChangeRecord.setMarketingPointLeasableArea(1f);
            int rows = repository.saveResourceChangeRecord(tx, resourceChangeRecord);
            Assert.assertEquals(1, rows);
        });

    }

}