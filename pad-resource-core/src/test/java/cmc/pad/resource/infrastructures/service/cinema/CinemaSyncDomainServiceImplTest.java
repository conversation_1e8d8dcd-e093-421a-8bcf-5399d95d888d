package cmc.pad.resource.infrastructures.service.cinema;

import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.RegionService;
import cmc.mdm.cinema.admin.contract.dto.CinemaAdminDto;
import cmc.mdm.cinema.admin.contract.iface.CinemaAdminService;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.cinema.CinemaSyncDomainService;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.msg.Publisher;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

@PrepareForTest({Publisher.class})
public class CinemaSyncDomainServiceImplTest extends TestBase {

    private CinemaAdminService cinemaAdminService = PowerMockito.mock(CinemaAdminService.class);
    private CinemaRepository cinemaRepository = new MysqlCinemaRepository();
    private RegionService regionService = PowerMockito.mock(RegionService.class);
    private Publisher publisher = PowerMockito.mock(Publisher.class);
    private CinemaSyncDomainService service = PowerMockito.spy(new CinemaSyncDomainServiceImpl(cinemaAdminService, cinemaRepository, regionService));

    @Test
    public void synchronize() throws Exception {
        PowerMockito.when(cinemaAdminService.queryCinemaInfoList(any())).thenReturn(buildCinemas());
        PowerMockito.when(regionService.findRegions(any())).thenReturn(buildRegions());
        PowerMockito.mockStatic(Publisher.class);
        PowerMockito.when(Publisher.class, "get").thenReturn(publisher);
        Cinema toDeleteCinema = new Cinema();
        toDeleteCinema.setCode("toDeleteCinema");
        toDeleteCinema.setSyncTime(LocalDateTime.now().minusDays(1));
        service.synchronize();
        Assert.assertTrue(cinemaRepository.findMany(Filter.create("code", "testSyncCinema")).size() == 1);
    }

    private RegionDto.FindRegionsResponse buildRegions() {
        RegionDto.FindRegionsResponse response = new RegionDto.FindRegionsResponse();
        List<RegionDto.Region> items = new ArrayList<>();
        RegionDto.Region region = new RegionDto.Region();
        region.setId("testSyncRegionForCinema");
        region.setName("testSyncRegionForCinema");
        items.add(region);
        response.setItems(items);
        response.setTotalCount(1);
        return response;
    }

    private CinemaAdminDto.QueryCinemaInfoListResponse buildCinemas() {
        CinemaAdminDto.QueryCinemaInfoListResponse response = new CinemaAdminDto.QueryCinemaInfoListResponse();
        List<CinemaAdminDto.Cinema> list = new ArrayList<>();
        CinemaAdminDto.Cinema cinema = new CinemaAdminDto.Cinema();
        cinema.setInnerCode("testSyncCinema");
        cinema.setInnerName("testSyncCinema");
        list.add(cinema);
        response.setQueryCinemaList(list);
        return response;
    }

}