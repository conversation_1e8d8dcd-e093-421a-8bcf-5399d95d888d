package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.cinema.CinemaLevel;
import cmc.pad.resource.test.TestBase;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class MysqlCinemaLevelRepositoryTest extends TestBase {

    private MysqlCinemaLevelRepository repository = new MysqlCinemaLevelRepository();


    @Test
    public void batchInsertTest() {
        List<CinemaLevel> list = new ArrayList<>();
        CinemaLevel cinemaLevel = new CinemaLevel();
        cinemaLevel.setInnerCode("333");
        cinemaLevel.setLevel("S");
        cinemaLevel.setImportId(1);
        list.add(cinemaLevel);
        int i = repository.batchInsert(list);
        Assert.assertEquals(1, i);
    }

    @Test
    public void getAllOldImportIdTest() {
        repository.getAllOldImportId(137);
    }

    @Test
    public void deleteByImportIdTest() {
        repository.deleteByImportId(117);
    }
}
