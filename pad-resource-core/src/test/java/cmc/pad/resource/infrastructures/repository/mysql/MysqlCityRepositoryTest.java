package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.test.TestBase;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static mtime.lark.db.jsd.Shortcut.f;

public class MysqlCityRepositoryTest extends TestBase {
    private MysqlCityRepository repository = new MysqlCityRepository();

    @Test
    public void batchInsert() throws Exception {
        List<City> list = new ArrayList<>();
        City city = new City();
        city.setCode("test001");
        City city2 = new City();
        city2.setCode("test002");
        list.add(city);
        list.add(city2);
        int rows = repository.batchInsert(list);
        Assert.assertEquals(2, rows);
    }

    @Test
    public void updateCityLevelByCode() throws Exception {
        City city = new City();
        city.setCode("test003");
        repository.save(city);
        boolean result = repository.updateCityLevelByCode("test003", "L3");
        Assert.assertTrue(result);
    }

    @Test
    public void find() {
        repository.findMany(f("code", 1)).stream().forEach(item -> {
            System.out.println(item.toString());
        });
    }

}