package cmc.pad.resource.infrastructures.service.city;

import cmc.location.admin.service.iface.DistrictInfoService;
import cmc.location.front.service.dto.CityDto;
import cmc.location.front.service.dto.ProvinceDto;
import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.CityService;
import cmc.location.front.service.iface.ProvinceService;
import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.domain.city.City;
import cmc.pad.resource.domain.city.CityAndDistrictSyncDomainService;
import cmc.pad.resource.domain.city.CityDistrictRepository;
import cmc.pad.resource.domain.city.CityRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCityDistrictRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCityRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Filter;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

public class CitySyncDomainServiceImplTest extends TestBase {

    private RegionService regionService = PowerMockito.mock(RegionService.class);
    private ProvinceService provinceService = PowerMockito.mock(ProvinceService.class);
    private CityService cityService = PowerMockito.mock(CityService.class);
    private DistrictInfoService districtInfoService = PowerMockito.mock(DistrictInfoService.class);
    private CityRepository cityRepository = new MysqlCityRepository();
    private CityDistrictRepository cityDistrictRepository = new MysqlCityDistrictRepository();
    private CityAndDistrictSyncDomainService service = PowerMockito.spy(new CityAndDistrictSyncDomainServiceImpl(regionService, provinceService, cityService, districtInfoService, cityRepository, cityDistrictRepository));

    @Test
    public void synchronize() throws Exception {
        PowerMockito.when(cityService.findCitys(any())).thenReturn(buildCities());
        PowerMockito.when(regionService.findRegions(any())).thenReturn(buildRegions());
        PowerMockito.when(provinceService.findProvincesByOldIds(any())).thenReturn(buildProvinces());
        City city = new City();
        city.setCode("toDeleteCity");
        city.setSyncTime(LocalDateTime.now().minusDays(1));//设置待删除
        cityRepository.save(city);
        Assert.assertTrue(cityRepository.findMany(Filter.create("code", 111111112)).size() == 1);
    }

    private ProvinceDto.FindProvincesByOldIdsResponse buildProvinces() {
        ProvinceDto.FindProvincesByOldIdsResponse response = new ProvinceDto.FindProvincesByOldIdsResponse();
        List<ProvinceDto.Province> provinces = new ArrayList<>();
        ProvinceDto.Province province = new ProvinceDto.Province();
        province.setNameCN("testSyncProvince");
        province.setId(1111111111);
        provinces.add(province);
        response.setProvinces(provinces);
        return response;
    }

    private RegionDto.FindRegionsResponse buildRegions() {
        RegionDto.FindRegionsResponse response = new RegionDto.FindRegionsResponse();
        List<RegionDto.Region> items = new ArrayList<>();
        RegionDto.Region region = new RegionDto.Region();
        region.setId("testSyncRegion");
        region.setName("testSyncRegion");
        items.add(region);
        response.setItems(items);
        response.setTotalCount(1);
        return response;
    }

    private CityDto.FindCitysResponse buildCities() {
        CityDto.FindCitysResponse response = new CityDto.FindCitysResponse();
        List<CityDto.City> items = new ArrayList<>();
        CityDto.City city = new CityDto.City();
        city.setId(111111112);
        city.setNameCN("testSyncCity");
        city.setRegion("testSyncRegion");
        items.add(city);
        response.setItems(items);
        response.setTotalCount(1);
        return response;
    }

}