package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class MysqlInventoryRepositoryTest extends TestBase {

    MysqlInventoryRepository repository = new MysqlInventoryRepository();

    @Test
    public void batchInsert() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            List<Inventory> list = new ArrayList<>();
            Inventory inventory = new Inventory();
            inventory.setCinemaCode("test001");
            inventory.setDate(LocalDate.now());
            inventory.setTotalAdvertisingPointLeasableQuantity(1);
            inventory.setTotalFixedPointLeasableArea(1f);
            inventory.setTotalOuterAreaLeasableArea(1f);
            inventory.setTotalMarketingPointLeasableArea(1f);
            inventory.setSoldAdvertisingPointLeasableQuantity(1);
            inventory.setSoldFixedPointLeasableArea(1f);
            inventory.setSoldOuterAreaLeasableArea(1f);
            inventory.setSoldMarketingPointLeasableArea(1f);
            list.add(inventory);
            int rows = repository.batchInsert(tx, list);
            Assert.assertEquals(1, rows);
        });
    }

}