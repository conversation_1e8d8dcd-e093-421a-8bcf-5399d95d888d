package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.discount.Discount;
import cmc.pad.resource.domain.discount.DiscountType;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import org.junit.Assert;
import org.junit.Test;

public class MysqlDiscountRepositoryTest extends TestBase {

    private MysqlDiscountRepository repository = new MysqlDiscountRepository();

    @Test
    public void testAll() throws Exception {
        insertDiscount();
        updateDiscount();
    }

    public void updateDiscount() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("YX");
            discount.setDiscountType(DiscountType.AREA);
            int rows = repository.updateDiscount(tx, discount);
            Assert.assertEquals(1, rows);
        });
    }

    private void insertDiscount() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("YX");
            discount.setDiscountType(DiscountType.AREA);
            Integer id = repository.insertDiscount(tx, discount);
            Assert.assertNotNull(id);
        });
    }

}