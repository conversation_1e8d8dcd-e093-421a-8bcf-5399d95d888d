package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.discount.DiscountRule;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class MysqlDiscountRuleRepositoryTest extends TestBase {

    private MysqlDiscountRuleRepository repository = new MysqlDiscountRuleRepository();

    @Test
    public void batchInsert() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            List<DiscountRule> list = new ArrayList();
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(1);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(10f);
            rule.setFactor(0.88f);
            list.add(rule);
            int rows = repository.batchInsert(tx, list);
            Assert.assertEquals(1, rows);
        });
    }

}