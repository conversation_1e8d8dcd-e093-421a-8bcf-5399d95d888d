package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import cmc.pad.resource.test.TestBase;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:26
 * @Version 1.0
 */
@Repository
public class MysqlMovieHallNamingLeasingPriceRepositoryTest extends TestBase {

    private MysqlMovieHallNamingLeasingPriceRepository repository = new MysqlMovieHallNamingLeasingPriceRepository();


    @Test
    public void batchInsertTest() {
        List<MovieHallNamingPrice> list = new ArrayList<>();
        MovieHallNamingPrice price = new MovieHallNamingPrice();
        price.setImportId(1);
        price.setUpdater(2);
        price.setUpdateTime(LocalDateTime.now());
        price.setEffectiveDate(LocalDate.now());
        price.setCinemaCode("333");
        price.setMovieHallType("I");
        price.setUnitPrice(5);
        list.add(price);
        repository.batchInsert(list);
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = repository.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }

}
