package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.test.TestBase;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:26
 * @Version 1.0
 */
@Repository
public class MysqlOuterAreaLeasingPriceRepositoryTest extends TestBase {

    private MysqlOuterAreaLeasingPriceRepository repository = new MysqlOuterAreaLeasingPriceRepository();

    @Test
    public void batchInsertTest() {
        List<OuterAreaLeasingPrice> list = new ArrayList<>();
        OuterAreaLeasingPrice price = new OuterAreaLeasingPrice();
        price.setImportId(1);
        price.setUpdater(2);
        price.setUpdateTime(LocalDateTime.now());
        price.setEffectiveDate(LocalDate.now());
        price.setCityLevel("L1");
        price.setCinemaLevel("S");
        price.setUnitPrice(5);
        list.add(price);
        repository.batchInsert(list);
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = repository.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }

}
