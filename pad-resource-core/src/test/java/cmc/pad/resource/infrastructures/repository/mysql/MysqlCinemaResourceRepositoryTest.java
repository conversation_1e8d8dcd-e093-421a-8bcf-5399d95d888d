package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import org.junit.Assert;
import org.junit.Test;

public class MysqlCinemaResourceRepositoryTest extends TestBase {

    private MysqlCinemaResourceRepository repository = new MysqlCinemaResourceRepository();

    @Test
    public void updateCinemaResourceByCode() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            int result = repository.updateCinemaResourceByCode(tx, "test003", 2f, 2f, 2f, 2);
            Assert.assertEquals(0, result);
        });
    }

    @Test
    public void insertCinemaResource() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            CinemaResource resource = new CinemaResource();
            resource.setCinemaCode("test333");
            resource.setAdvertisingPointLeasableQuantity(1);
            resource.setFixedPointLeasableArea(1f);
            resource.setMarketingPointLeasableArea(1f);
            resource.setOuterAreaLeasableArea(1f);
            boolean result = repository.insertCinemaResource(tx, resource);
            Assert.assertTrue(result);
        });

    }

}