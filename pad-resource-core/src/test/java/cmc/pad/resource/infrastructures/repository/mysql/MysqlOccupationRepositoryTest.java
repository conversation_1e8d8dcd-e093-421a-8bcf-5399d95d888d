package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.domain.inventory.OccupationStatus;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;

public class MysqlOccupationRepositoryTest extends TestBase {

    private MysqlOccupationRepository repository = new MysqlOccupationRepository();

    @Test
    public void testAll() throws Exception {
//        insertOccupation();
//        cancelOccupation();
        updateOccupation();
    }

    private void insertOccupation() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            Occupation occupation = new Occupation();
            occupation.setCinemaCode("test001");
            occupation.setContractNo("test001");
            occupation.setBusinessType("XC");
            occupation.setAmount(10f);
            occupation.setStartDate(LocalDate.now());
            occupation.setEndDate(LocalDate.now().plusDays(20));
            occupation.setStatus(OccupationStatus.ACTIVE);
            Integer id = repository.insertOccupation(tx, occupation);
            Assert.assertNotNull(id);
        });

    }

    private void cancelOccupation() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            int rows = repository.cancelOccupation(tx, "test001");
            Assert.assertEquals(1, rows);
        });
    }

    private void updateOccupation() throws Exception {
        repository.getDatabase().begin((Transaction tx) -> {
            Occupation occupation = new Occupation();
            occupation.setCinemaCode("test001");
            occupation.setContractNo("test001");
            occupation.setBusinessType("XC");
            occupation.setAmount(20f);
            occupation.setStartDate(LocalDate.now());
            occupation.setEndDate(LocalDate.now().plusDays(20));
            int rows = repository.updateOccupation(tx, occupation);
            Assert.assertEquals(1, rows);
        });

    }

}