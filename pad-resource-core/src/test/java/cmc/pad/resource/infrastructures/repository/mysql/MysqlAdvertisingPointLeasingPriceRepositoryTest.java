package cmc.pad.resource.infrastructures.repository.mysql;

import cmc.pad.resource.domain.price.AdvertisingPointLeasingPrice;
import cmc.pad.resource.test.TestBase;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:26
 * @Version 1.0
 */
public class MysqlAdvertisingPointLeasingPriceRepositoryTest extends TestBase {
    private MysqlAdvertisingPointLeasingPriceRepository repository = new MysqlAdvertisingPointLeasingPriceRepository();

    @Test
    public void batchInsertTest() {
        List<AdvertisingPointLeasingPrice> list = new ArrayList<>();
        AdvertisingPointLeasingPrice price = new AdvertisingPointLeasingPrice();
        price.setImportId(1);
        price.setUpdater(2);
        price.setUpdateTime(LocalDateTime.now());
        price.setEffectiveDate(LocalDate.now());
        price.setCityLevel("L1");
        price.setCinemaLevel("S");
        price.setMinArea(5);
        price.setMinTotalPrice(100);
        price.setExpandedUnitPrice(1);
        list.add(price);
        repository.batchInsert(list);
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = repository.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }
}
