package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.AdvertisingPointLeasingPrice;
import cmc.pad.resource.domain.price.AdvertisingPointLeasingPriceRepository;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;

/**
 * <AUTHOR>
 * @Date 2019/4/24 14:42
 * @Version 1.0
 */
public class AdvertisingPointLeasingAppServiceTest {
    private AdvertisingPointLeasingPriceRepository repository = PowerMockito.mock(AdvertisingPointLeasingPriceRepository.class);
    private AdvertisingPointLeasingAppService service = spy(new AdvertisingPointLeasingAppService(repository));

    @Test
    public void importDataTest() {
        List<AdvertisingPointLeasingPrice> list = PowerMockito.mock(List.class);
        service.importData(list);
    }

    @Test
    public void discardTest() {
        service.discard(1);
    }
}
