package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.FixedPointPriceQuery;
import cmc.pad.resource.domain.price.FixedPointLeasingPrice;
import cmc.pad.resource.domain.price.FixedPointLeasingPriceRepository;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
public class FixedPointLeasingPriceQueryServiceTest {

    private FixedPointLeasingPriceRepository repository = PowerMockito.mock(FixedPointLeasingPriceRepository.class);
    private FixedPointLeasingPriceQueryService service = PowerMockito.spy(new FixedPointLeasingPriceQueryService(repository));


    @Before
    public void before() {

        PageResult<FixedPointLeasingPrice> result = new PageResult<>();
        List<FixedPointLeasingPrice> list = new ArrayList<>();
        FixedPointLeasingPrice price = new FixedPointLeasingPrice();
        list.add(price);
        result.setTotalCount(1);
        result.setItems(list);
        PowerMockito.when(repository.findPage(any(), anyInt(), anyInt())).thenReturn(result);
        PowerMockito.when(repository.findMany(any())).thenReturn(Collections.EMPTY_LIST);
        PowerMockito.when(repository.latestEffectiveDate()).thenReturn(LocalDate.now());
    }

    @Test
    public void effectivePageTest() {
        FixedPointPriceQuery query = new FixedPointPriceQuery();
        query.setCinemaLevel("S");
        query.setCityLevel("L1");
        query.setPageIndex(1);
        query.setPageSize(10);
        PageResult<FixedPointLeasingPrice> result = service.effectivePage(query);
        Assert.assertEquals(1, result.getTotalCount());
    }


    @Test
    public void listTest() {
        List<FixedPointLeasingPrice> list = service.list("l1", "S");
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = service.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }
}
