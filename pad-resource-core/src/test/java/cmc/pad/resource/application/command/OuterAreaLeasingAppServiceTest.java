package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.domain.price.OuterAreaLeasingPriceRepository;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
public class OuterAreaLeasingAppServiceTest {

    private OuterAreaLeasingPriceRepository repository = PowerMockito.mock(OuterAreaLeasingPriceRepository.class);
    private OuterAreaLeasingAppService service = spy(new OuterAreaLeasingAppService(repository));


    @Test
    public void importDataTest() {
        List<OuterAreaLeasingPrice> list = PowerMockito.mock(List.class);
        service.importData(list);
    }

    @Test
    public void discardTest() {
        service.discard(1);
    }
}
