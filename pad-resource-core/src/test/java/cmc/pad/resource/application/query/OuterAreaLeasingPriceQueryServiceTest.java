package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.OuterAreaPriceQuery;
import cmc.pad.resource.domain.price.OuterAreaLeasingPrice;
import cmc.pad.resource.domain.price.OuterAreaLeasingPriceRepository;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
public class OuterAreaLeasingPriceQueryServiceTest {

    private OuterAreaLeasingPriceRepository repository = PowerMockito.mock(OuterAreaLeasingPriceRepository.class);
    private OuterAreaLeasingPriceQueryService service = PowerMockito.spy(new OuterAreaLeasingPriceQueryService(repository));

    @Before
    public void before() {

        PageResult<OuterAreaLeasingPrice> result = new PageResult<>();
        List<OuterAreaLeasingPrice> list = new ArrayList<>();
        OuterAreaLeasingPrice price = new OuterAreaLeasingPrice();
        list.add(price);
        result.setTotalCount(1);
        result.setItems(list);
        PowerMockito.when(repository.findPage(any(), anyInt(), anyInt())).thenReturn(result);
        PowerMockito.when(repository.findMany(any())).thenReturn(Collections.EMPTY_LIST);
        PowerMockito.when(repository.latestEffectiveDate()).thenReturn(LocalDate.now());
    }

    @Test
    public void effectivePageTest() {
        OuterAreaPriceQuery query = new OuterAreaPriceQuery();
        query.setCinemaLevel("S");
        query.setCityLevel("L1");
        query.setPageIndex(1);
        query.setPageSize(10);
        PageResult<OuterAreaLeasingPrice> result = service.effectivePage(query);
        Assert.assertEquals(1, result.getTotalCount());
    }


    @Test
    public void listTest() {
        List<OuterAreaLeasingPrice> list = service.list("l1", "S");

        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = service.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }
}
