package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.MovieHallNamingLeasingPriceRepository;
import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
public class MovieHallNamingLeasingAppServiceTest {

    private MovieHallNamingLeasingPriceRepository repository = PowerMockito.mock(MovieHallNamingLeasingPriceRepository.class);
    private MovieHallNamingLeasingAppService service = spy(new MovieHallNamingLeasingAppService(repository));


    @Test
    public void importDataTest() {
        List<MovieHallNamingPrice> list = PowerMockito.mock(List.class);
        service.importData(list);
    }

    @Test
    public void discardTest() {
        service.discard(1);
    }
}
