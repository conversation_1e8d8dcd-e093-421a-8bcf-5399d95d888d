package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.MarketingPointLeasingPrice;
import cmc.pad.resource.domain.price.MarketingPointLeasingPriceRepository;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
public class MarketingPointLeasingAppServiceTest {

    private MarketingPointLeasingPriceRepository repository = PowerMockito.mock(MarketingPointLeasingPriceRepository.class);
    private MarketingPointLeasingAppService service = spy(new MarketingPointLeasingAppService(repository));


    @Test
    public void importDataTest() {
        List<MarketingPointLeasingPrice> list = PowerMockito.mock(List.class);
        service.importData(list);
    }

    @Test
    public void discardTest() {
        service.discard(1);
    }
}
