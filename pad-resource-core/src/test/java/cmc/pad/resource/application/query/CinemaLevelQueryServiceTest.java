package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.application.query.data.CinemaLevelQuery;
import cmc.pad.resource.test.TestBase;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Test;

/**
 * 影城级别查询服务
 *
 * <AUTHOR>
 * @Date 2019/3/15 11:34
 * @Version 1.0
 */
public class CinemaLevelQueryServiceTest extends TestBase {


    private CinemaLevelQueryService service = new CinemaLevelQueryService();


    @Test
    public void getCinemaTest() {
        CinemaLevelInfo cinemaLevelInfo = service.getCinema("124");
        Assert.assertNotNull(cinemaLevelInfo);
    }

    @Test
    public void queryCinemaTest() {
        CinemaLevelQuery query = new CinemaLevelQuery();
        query.setCityDistrictLevel("L1");
        query.setCinemaLevel("S");
        query.setCinemaCode("124");
        query.setPageIndex(1);
        query.setPageSize(10);
        PageResult<CinemaLevelInfo> result = service.queryCinema(query);
        Assert.assertNotNull(result);
    }
}
