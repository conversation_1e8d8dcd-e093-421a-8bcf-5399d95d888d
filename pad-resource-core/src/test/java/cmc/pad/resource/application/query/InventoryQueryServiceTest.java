package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.InventoryData;
import cmc.pad.resource.application.query.data.InventoryQueryParam;
import cmc.pad.resource.application.query.data.ResourceSoldData;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.inventory.Inventory;
import cmc.pad.resource.domain.inventory.InventoryRepository;
import cmc.pad.resource.domain.inventory.OccupationRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlInventoryRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlOccupationRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

public class InventoryQueryServiceTest extends TestBase {

    private CinemaRepository cinemaRepository = new MysqlCinemaRepository();
    private InventoryRepository repository = new MysqlInventoryRepository();
    private OccupationRepository occupationRepository = new MysqlOccupationRepository();
    private InventoryQueryService service = new InventoryQueryService(repository, occupationRepository);

    @Test
    public void queryInventoryByPage() throws Exception {
        prepareData();
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(10);
        queryParam.setCinemaCode("test001");
        queryParam.setRegionCode("test001");
        queryParam.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        queryParam.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        PageResult<InventoryData> data = service.queryInventoryByPage(queryParam);
        Assert.assertEquals(1, data.getTotalCount());
    }

    @Test
    public void countInventory() throws Exception {
        prepareData();
        InventoryQueryParam queryParam = new InventoryQueryParam();
        queryParam.setPageIndex(1);
        queryParam.setPageSize(10);
        queryParam.setCinemaCode("test001");
        queryParam.setRegionCode("test001");
        queryParam.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        queryParam.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        long result = service.countInventory(queryParam);
        Assert.assertEquals(1, result);
    }

    @Test
    public void queryEachTypeSoldAmountAfterEditDate() throws Exception {
        prepareData();
        List<ResourceSoldData> data = service.queryEachTypeSoldAmountAfterEditDate("test001");
        Assert.assertEquals(1, data.size());
    }

    private void prepareData() {
        Cinema cinema = new Cinema();
        cinema.setCode("test001");
        cinema.setRegionCode("test001");
        cinemaRepository.save(cinema);
        Inventory inventory = new Inventory();
        inventory.setCinemaCode("test001");
        inventory.setDate(LocalDate.now());
        inventory.setTotalMarketingPointLeasableArea(1f);
        inventory.setTotalOuterAreaLeasableArea(1f);
        inventory.setTotalFixedPointLeasableArea(1f);
        inventory.setTotalAdvertisingPointLeasableQuantity(1);
        inventory.setSoldMarketingPointLeasableArea(1f);
        inventory.setSoldOuterAreaLeasableArea(1f);
        inventory.setSoldFixedPointLeasableArea(1f);
        inventory.setSoldAdvertisingPointLeasableQuantity(1);
        repository.save(inventory);
    }

}