package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.DiscountData;
import cmc.pad.resource.application.query.data.DiscountQueryParam;
import cmc.pad.resource.domain.discount.Discount;
import cmc.pad.resource.domain.discount.DiscountRule;
import cmc.pad.resource.domain.discount.DiscountType;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlDiscountRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlDiscountRuleRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class DiscountQueryServiceTest extends TestBase {

    private MysqlDiscountRepository discountRepository = new MysqlDiscountRepository();
    private MysqlDiscountRuleRepository ruleRepository = new MysqlDiscountRuleRepository();
    private DiscountQueryService discountQueryService = new DiscountQueryService(discountRepository, ruleRepository);


    @Test
    public void queryDiscountByPage() throws Exception {
        discountQueryService.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("YX");
            discount.setDiscountType(DiscountType.DURATION);
            int discountId = discountRepository.insertDiscount(tx, discount);
            List<DiscountRule> list = new ArrayList<>();
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(discountId);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(10f);
            rule.setFactor(0.88f);
            list.add(rule);
            ruleRepository.batchInsert(tx, list);
        });
        DiscountQueryParam param = new DiscountQueryParam();
        param.setBusinessType("YX");
        PageResult<DiscountData> data = discountQueryService.queryDiscountByPage(param);
        Assert.assertTrue(data.size() > 0);
    }


    @Test
    public void findDiscountById() throws Exception {
        final int[] discountId = {0};
        discountQueryService.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("YX");
            discount.setDiscountType(DiscountType.DURATION);
            discountId[0] = discountRepository.insertDiscount(tx, discount);
            List<DiscountRule> list = new ArrayList<>();
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(discountId[0]);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(10f);
            rule.setFactor(0.88f);
            list.add(rule);
            ruleRepository.batchInsert(tx, list);
        });
        DiscountData data = discountQueryService.findDiscountById(discountId[0]);
        Assert.assertNotNull(data.getId() == discountId[0]);
    }

}