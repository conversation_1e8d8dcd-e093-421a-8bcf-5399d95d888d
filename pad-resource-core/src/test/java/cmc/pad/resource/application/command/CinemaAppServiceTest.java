package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.cinema.CinemaSyncDomainService;
import cmc.pad.resource.infrastructures.service.cinema.CinemaSyncDomainServiceImpl;
import cmc.pad.resource.test.TestBase;
import org.junit.Test;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;


public class CinemaAppServiceTest extends TestBase {

    private CinemaSyncDomainService syncDomainService = mock(CinemaSyncDomainServiceImpl.class);
    private CinemaAppService service = spy(new CinemaAppService(syncDomainService));

    @Test
    public void synchronizeCinemas() throws Exception {
        service.synchronizeCinemas();
    }

}