package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.cinema.CinemaLevel;
import cmc.pad.resource.domain.cinema.CinemaLevelRepository;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 影城级别服务
 *
 * <AUTHOR>
 * @Date 2019/3/15 10:50
 * @Version 1.0
 */
public class CinemaLevelAppServiceTest {
    private CinemaLevelRepository repository = PowerMockito.mock(CinemaLevelRepository.class);
    private CinemaLevelAppService service = spy(new CinemaLevelAppService(repository));


    @Test
    public void importDataTest() {
        when(repository.getAllOldImportId(1)).thenReturn(Arrays.asList(1));
        List<CinemaLevel> list = new ArrayList<>();
        CinemaLevel level = new CinemaLevel();
        level.setImportId(1);
        level.setInnerCode("333");
        level.setLevel("S");
        list.add(level);
        service.importData(list);
    }
}
