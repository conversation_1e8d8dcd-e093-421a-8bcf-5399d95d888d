package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.CinemaResourceData;
import cmc.pad.resource.application.query.data.CinemaResourceQueryParam;
import cmc.pad.resource.application.query.data.ResourceChangeRecordData;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.ResourceChangeRecord;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaResourceRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlResourceChangeRecordRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.List;

public class CinemaResourceQueryServiceTest extends TestBase {
    private CinemaResourceQueryService service = new CinemaResourceQueryService();
    private MysqlCinemaRepository cinemaRepository = new MysqlCinemaRepository();
    private MysqlCinemaResourceRepository repository = new MysqlCinemaResourceRepository();
    private MysqlResourceChangeRecordRepository recordRepository = new MysqlResourceChangeRecordRepository();

    @Test
    public void testQueryCinemaResourceByPage() throws Exception {
        Cinema cinema = new Cinema();
        cinema.setCode("test001");
        cinema.setRegionCode("test001");
        cinemaRepository.save(cinema);
        CinemaResource resource = new CinemaResource();
        resource.setCinemaCode("test001");
        resource.setAdvertisingPointLeasableQuantity(1);
        resource.setFixedPointLeasableArea(11f);
        resource.setMarketingPointLeasableArea(11f);
        resource.setOuterAreaLeasableArea(11f);
        repository.save(resource);
        CinemaResourceQueryParam param = new CinemaResourceQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setCinemaCode("test001");
        param.setRegionCode("test001");
        PageResult<CinemaResourceData> data = service.queryCinemaResourceByPage(param);
        Assert.assertTrue(data.getTotalCount() == 1);
    }

    @Test
    public void testQueryResourceChangeRecordList() throws Exception {
        Cinema cinema = new Cinema();
        cinema.setCode("test001");
        cinema.setRegionCode("test001");
        cinemaRepository.save(cinema);
        CinemaResource resource = new CinemaResource();
        resource.setCinemaCode("test001");
        resource.setAdvertisingPointLeasableQuantity(1);
        resource.setFixedPointLeasableArea(11f);
        resource.setMarketingPointLeasableArea(11f);
        resource.setOuterAreaLeasableArea(11f);
        repository.save(resource);
        ResourceChangeRecord record = new ResourceChangeRecord();
        record.setCinemaCode("test001");
        record.setUpdator("test");
        record.setUpdateTime(LocalDateTime.now());
        record.setMarketingPointLeasableArea(1f);
        record.setOuterAreaLeasableArea(1f);
        record.setAdvertisingPointLeasableQuantity(1);
        record.setFixedPointLeasableArea(1f);
        recordRepository.save(record);
        List<ResourceChangeRecordData> list = service.queryResourceChangeRecordList("test001");
        Assert.assertTrue(list.size() == 1);
    }
}