package cmc.pad.resource.application.command;

import cmc.pad.resource.application.command.resource.ResourceInventoryBiz;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.inventory.*;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import cmc.pad.resource.infrastructures.repository.mysql.*;
import cmc.pad.resource.infrastructures.service.inventory.InventoryGenerateServiceImpl;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Filter;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static org.mockito.Mockito.spy;

public class InventoryAppServiceTest extends TestBase {

    private CinemaResourceRepository cinemaResourceRepository = new MysqlCinemaResourceRepository();
    private CinemaRepository cinemaRepository = new MysqlCinemaRepository();
    private OccupationRepository occupationRepository = new MysqlOccupationRepository();
    private InventoryRepository inventoryRepository = new MysqlInventoryRepository();
    private ResourceInventoryBiz resourceInventoryBiz = new ResourceInventoryBiz(inventoryRepository);
    private InventoryGenerateService inventoryGenerateService = new InventoryGenerateServiceImpl(cinemaResourceRepository, inventoryRepository, cinemaRepository);
    private InventoryAppService inventoryAppService = spy(new InventoryAppService(inventoryGenerateService, occupationRepository, resourceInventoryBiz, inventoryRepository, cinemaRepository, cinemaResourceRepository));

    @Test
    public void generateInventory() throws Exception {
        //准备数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testGen");
        cinemaResource.setFixedPointLeasableArea(10f);
        cinemaResource.setOuterAreaLeasableArea(10f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResourceRepository.save(cinemaResource);
        Occupation yx = new Occupation();
        yx.setStatus(OccupationStatus.ACTIVE);
        yx.setCinemaCode("testGen");
        yx.setBusinessType("YX");
        yx.setContractNo("1111111111111");
        yx.setAmount(2f);
        yx.setStartDate(LocalDate.now());
        yx.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(yx);

        Occupation gd = new Occupation();
        gd.setStatus(OccupationStatus.ACTIVE);
        gd.setCinemaCode("testGen");
        gd.setBusinessType("GD");
        gd.setContractNo("2222222");
        gd.setAmount(2f);
        gd.setStartDate(LocalDate.now());
        gd.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(gd);

        Occupation wz = new Occupation();
        wz.setStatus(OccupationStatus.ACTIVE);
        wz.setCinemaCode("testGen");
        wz.setBusinessType("WZ");
        wz.setContractNo("3333333333");
        wz.setAmount(2f);
        wz.setStartDate(LocalDate.now());
        wz.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(wz);

        Occupation xc = new Occupation();
        xc.setStatus(OccupationStatus.ACTIVE);
        xc.setCinemaCode("testGen");
        xc.setBusinessType("XC");
        xc.setContractNo("444444444");
        xc.setAmount(2f);
        xc.setStartDate(LocalDate.now());
        xc.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(xc);

        inventoryAppService.GenerateInventory("testGen", DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()),
                DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        Assert.assertTrue(inventoryRepository.findMany(Filter.create("cinema_code", "testGen")).size() > 0);

    }

    @Test
    public void createOccupation() throws Exception {
        CreateOccupationCommand command = new CreateOccupationCommand();
        command.setContractNo("testNo001");
        command.setAmount(2f);
        command.setBusinessType("XC");
        command.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setCinemaCode("test121");
//        int id = inventoryAppService.createOccupation(command);
//        Assert.assertTrue(id > 0);
    }

    /**
     * 覆盖测试创建同一份库存占用合同抛异常
     */
    @Test
    public void createSameOccupationThrowException() throws Exception {
        CreateOccupationCommand command = new CreateOccupationCommand();
        command.setContractNo("testNoSame");
        command.setAmount(2f);
        command.setBusinessType("XC");
        command.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setCinemaCode("test121");
//        inventoryAppService.createOccupation(command);
        try {
//            inventoryAppService.createOccupation(command);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().equals("库存占用已创建"));
        }
    }

    /**
     * 覆盖测试创建营销点位的库存占用合同，超出库存
     */
    @Test
    public void createMarketingOccupationBeyondInventory() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testBeyondYx");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testBeyondYx");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据
        Inventory first = new Inventory();
        first.setCinemaCode("testBeyondYx");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);
        CreateOccupationCommand command = new CreateOccupationCommand();
        command.setContractNo("testBeyondYx");
        command.setAmount(15f);//测试点：超出可售卖数量
        command.setBusinessType("YX");
        command.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setCinemaCode("testBeyondYx");
        try {
//            inventoryAppService.createOccupation(command);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().equals("营销点位面积库存不足"));
        }
    }

    /**
     * 覆盖测试创建固定点位的库存占用合同，超出库存
     */
    @Test
    public void createFixedOccupationBeyondInventory() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testBeyondGd");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testBeyondGd");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据
        Inventory first = new Inventory();
        first.setCinemaCode("testBeyondGd");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);
        CreateOccupationCommand command = new CreateOccupationCommand();
        command.setContractNo("testBeyondGd");
        command.setAmount(25f);//测试点：超出可售卖数量
        command.setBusinessType("GD");
        command.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setCinemaCode("testBeyondGd");
        try {
//            inventoryAppService.createOccupation(command);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().equals("固定点位面积库存不足"));
        }
    }

    /**
     * 覆盖测试创建外租点位的库存占用合同，超出库存
     */
    @Test
    public void createOuterOccupationBeyondInventory() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testBeyondWz");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testBeyondWz");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据
        Inventory first = new Inventory();
        first.setCinemaCode("testBeyondWz");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);
        CreateOccupationCommand command = new CreateOccupationCommand();
        command.setContractNo("testBeyondWz");
        command.setAmount(25f);//测试点：超出可售卖数量
        command.setBusinessType("WZ");
        command.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setCinemaCode("testBeyondWz");
        try {
//            inventoryAppService.createOccupation(command);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().equals("外租区域面积库存不足"));
        }
    }

    /**
     * 覆盖测试创建宣传点位的库存占用合同，超出库存
     */
    @Test
    public void createAdvertisingOccupationBeyondInventory() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testBeyondXc");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testBeyondXc");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据
        Inventory first = new Inventory();
        first.setCinemaCode("testBeyondXc");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);
        CreateOccupationCommand command = new CreateOccupationCommand();
        command.setContractNo("testBeyondXc");
        command.setAmount(15f);//测试点：超出可售卖数量
        command.setBusinessType("XC");
        command.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        command.setCinemaCode("testBeyondXc");
        try {
//            inventoryAppService.createOccupation(command);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().equals("宣传点位个数库存不足"));
        }
    }

    @Test
    public void cancelOccupation() throws Exception {
        Occupation o = new Occupation();
        o.setContractNo("testNo002");
        o.setAmount(2f);
        o.setBusinessType("YX");
        o.setStartDate(LocalDate.now());
        o.setEndDate(LocalDate.now());
        o.setCinemaCode("test121");
        o.setStatus(OccupationStatus.ACTIVE);
        occupationRepository.save(o);
//        Occupation occupation = inventoryAppService.cancelOccupation("testNo002");
//        Assert.assertTrue(occupation.getStatus() == OccupationStatus.CANCELLED);
    }

    /**
     * 覆盖测试取消一个不存在的库存占用抛出异常
     */
    @Test
    public void cancelOccupationThrowException() throws Exception {
//        try {
//            inventoryAppService.cancelOccupation("testCancelNotExist");
//        } catch (Exception e) {
//            Assert.assertTrue(e.getMessage().equals("库存占用不存在"));
//        }
    }

    /**
     * 覆盖测试取消已取消库存占用
     */
    @Test
    public void cancelOccupationHasCanceled() throws Exception {
        Occupation o = new Occupation();
        o.setContractNo("testHasCanceled");
        o.setAmount(2f);
        o.setBusinessType("YX");
        o.setStartDate(LocalDate.now());
        o.setEndDate(LocalDate.now());
        o.setCinemaCode("test121");
        o.setStatus(OccupationStatus.CANCEL);
        occupationRepository.save(o);
//        Occupation occupation = inventoryAppService.cancelOccupation("testHasCanceled");
//        Assert.assertTrue(occupation.getStatus() == OccupationStatus.CANCELLED);
    }

    /**
     * 测试更新营销点位的库存占用
     */
    @Test
    public void updateOccupationYx() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testUpdateYx");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testUpdateYx");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据,3天库存
        Inventory first = new Inventory();
        first.setCinemaCode("testUpdateYx");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);

        Inventory second = new Inventory();
        second.setCinemaCode("testUpdateYx");
        second.setDate(LocalDate.now().plusDays(1));
        second.setTotalMarketingPointLeasableArea(10f);
        second.setTotalOuterAreaLeasableArea(20f);
        second.setTotalFixedPointLeasableArea(20f);
        second.setTotalAdvertisingPointLeasableQuantity(10);
        second.setSoldMarketingPointLeasableArea(10f);
        second.setSoldOuterAreaLeasableArea(20f);
        second.setSoldFixedPointLeasableArea(20f);
        second.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(second);

        Inventory third = new Inventory();
        third.setCinemaCode("testUpdateYx");
        third.setDate(LocalDate.now().plusDays(2));
        third.setTotalMarketingPointLeasableArea(10f);
        third.setTotalOuterAreaLeasableArea(20f);
        third.setTotalFixedPointLeasableArea(20f);
        third.setTotalAdvertisingPointLeasableQuantity(10);
        third.setSoldMarketingPointLeasableArea(10f);
        third.setSoldOuterAreaLeasableArea(20f);
        third.setSoldFixedPointLeasableArea(20f);
        third.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(third);

        Occupation occupation = new Occupation();
        occupation.setContractNo("testUpdateYx");
        occupation.setAmount(5f);
        occupation.setBusinessType("YX");
        occupation.setStartDate(LocalDate.now());
        occupation.setEndDate(LocalDate.now());
        occupation.setCinemaCode("testUpdateYx");
        occupation.setStatus(OccupationStatus.ACTIVE);
        occupationRepository.save(occupation);
        UpdateOccupationCommand updateOccupationCommand = new UpdateOccupationCommand();
        updateOccupationCommand.setAmount(6f);
        updateOccupationCommand.setContractNo("testUpdateYx");
        updateOccupationCommand.setCinemaCode("testUpdateYx");
        updateOccupationCommand.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().plusDays(1)));
        updateOccupationCommand.setBusinessType("YX");
//        Occupation afterModified = inventoryAppService.updateOccupation(updateOccupationCommand);
//        Assert.assertTrue(afterModified.getAmount() == 6f);
    }


    /**
     * 测试更新固定点位库存占用
     */
    @Test
    public void updateOccupationGd() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testUpdateGd");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testUpdateGd");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据,3天库存
        Inventory first = new Inventory();
        first.setCinemaCode("testUpdateGd");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);

        Inventory second = new Inventory();
        second.setCinemaCode("testUpdateGd");
        second.setDate(LocalDate.now().plusDays(1));
        second.setTotalMarketingPointLeasableArea(10f);
        second.setTotalOuterAreaLeasableArea(20f);
        second.setTotalFixedPointLeasableArea(20f);
        second.setTotalAdvertisingPointLeasableQuantity(10);
        second.setSoldMarketingPointLeasableArea(10f);
        second.setSoldOuterAreaLeasableArea(20f);
        second.setSoldFixedPointLeasableArea(20f);
        second.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(second);

        Inventory third = new Inventory();
        third.setCinemaCode("testUpdateGd");
        third.setDate(LocalDate.now().plusDays(2));
        third.setTotalMarketingPointLeasableArea(10f);
        third.setTotalOuterAreaLeasableArea(20f);
        third.setTotalFixedPointLeasableArea(20f);
        third.setTotalAdvertisingPointLeasableQuantity(10);
        third.setSoldMarketingPointLeasableArea(10f);
        third.setSoldOuterAreaLeasableArea(20f);
        third.setSoldFixedPointLeasableArea(20f);
        third.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(third);

        Occupation occupation = new Occupation();
        occupation.setContractNo("testUpdateGd");
        occupation.setAmount(5f);
        occupation.setBusinessType("GD");
        occupation.setStartDate(LocalDate.now());
        occupation.setEndDate(LocalDate.now());
        occupation.setCinemaCode("testUpdateGd");
        occupation.setStatus(OccupationStatus.ACTIVE);
        occupationRepository.save(occupation);
        UpdateOccupationCommand updateOccupationCommand = new UpdateOccupationCommand();
        updateOccupationCommand.setAmount(6f);
        updateOccupationCommand.setContractNo("testUpdateGd");
        updateOccupationCommand.setCinemaCode("testUpdateGd");
        updateOccupationCommand.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().plusDays(1)));
        updateOccupationCommand.setBusinessType("GD");
//        Occupation afterModified = inventoryAppService.updateOccupation(updateOccupationCommand);
//        Assert.assertTrue(afterModified.getAmount() == 6f);
    }

    /**
     * 测试更新宣传点位的库存占用
     */
    @Test
    public void updateOccupationXc() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testUpdateXc");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testUpdateXc");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据,3天库存
        Inventory first = new Inventory();
        first.setCinemaCode("testUpdateXc");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);

        Inventory second = new Inventory();
        second.setCinemaCode("testUpdateXc");
        second.setDate(LocalDate.now().plusDays(1));
        second.setTotalMarketingPointLeasableArea(10f);
        second.setTotalOuterAreaLeasableArea(20f);
        second.setTotalFixedPointLeasableArea(20f);
        second.setTotalAdvertisingPointLeasableQuantity(10);
        second.setSoldMarketingPointLeasableArea(10f);
        second.setSoldOuterAreaLeasableArea(20f);
        second.setSoldFixedPointLeasableArea(20f);
        second.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(second);

        Inventory third = new Inventory();
        third.setCinemaCode("testUpdateXc");
        third.setDate(LocalDate.now().plusDays(2));
        third.setTotalMarketingPointLeasableArea(10f);
        third.setTotalOuterAreaLeasableArea(20f);
        third.setTotalFixedPointLeasableArea(20f);
        third.setTotalAdvertisingPointLeasableQuantity(10);
        third.setSoldMarketingPointLeasableArea(10f);
        third.setSoldOuterAreaLeasableArea(20f);
        third.setSoldFixedPointLeasableArea(20f);
        third.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(third);

        Occupation occupation = new Occupation();
        occupation.setContractNo("testUpdateXc");
        occupation.setAmount(5f);
        occupation.setBusinessType("XC");
        occupation.setStartDate(LocalDate.now());
        occupation.setEndDate(LocalDate.now());
        occupation.setCinemaCode("testUpdateXc");
        occupation.setStatus(OccupationStatus.ACTIVE);
        occupationRepository.save(occupation);
        UpdateOccupationCommand updateOccupationCommand = new UpdateOccupationCommand();
        updateOccupationCommand.setAmount(6f);
        updateOccupationCommand.setContractNo("testUpdateXc");
        updateOccupationCommand.setCinemaCode("testUpdateXc");
        updateOccupationCommand.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().plusDays(1)));
        updateOccupationCommand.setBusinessType("XC");
//        Occupation afterModified = inventoryAppService.updateOccupation(updateOccupationCommand);
//        Assert.assertTrue(afterModified.getAmount() == 6f);
    }

    /**
     * 测试更新外租点位的库存占用
     */
    @Test
    public void updateOccupationWz() throws Exception {
        //准备影院数据
        Cinema cinema = new Cinema();
        cinema.setCode("testUpdateWz");
        cinemaRepository.save(cinema);
        //准备资源数据
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("testUpdateWz");
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(20f);
        cinemaResource.setOuterAreaLeasableArea(20f);
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResourceRepository.save(cinemaResource);
        //准备测试数据,3天库存
        Inventory first = new Inventory();
        first.setCinemaCode("testUpdateWz");
        first.setDate(LocalDate.now());
        first.setTotalMarketingPointLeasableArea(10f);
        first.setTotalOuterAreaLeasableArea(20f);
        first.setTotalFixedPointLeasableArea(20f);
        first.setTotalAdvertisingPointLeasableQuantity(10);
        first.setSoldMarketingPointLeasableArea(10f);
        first.setSoldOuterAreaLeasableArea(20f);
        first.setSoldFixedPointLeasableArea(20f);
        first.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(first);

        Inventory second = new Inventory();
        second.setCinemaCode("testUpdateWz");
        second.setDate(LocalDate.now().plusDays(1));
        second.setTotalMarketingPointLeasableArea(10f);
        second.setTotalOuterAreaLeasableArea(20f);
        second.setTotalFixedPointLeasableArea(20f);
        second.setTotalAdvertisingPointLeasableQuantity(10);
        second.setSoldMarketingPointLeasableArea(10f);
        second.setSoldOuterAreaLeasableArea(20f);
        second.setSoldFixedPointLeasableArea(20f);
        second.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(second);

        Inventory third = new Inventory();
        third.setCinemaCode("testUpdateWz");
        third.setDate(LocalDate.now().plusDays(2));
        third.setTotalMarketingPointLeasableArea(10f);
        third.setTotalOuterAreaLeasableArea(20f);
        third.setTotalFixedPointLeasableArea(20f);
        third.setTotalAdvertisingPointLeasableQuantity(10);
        third.setSoldMarketingPointLeasableArea(10f);
        third.setSoldOuterAreaLeasableArea(20f);
        third.setSoldFixedPointLeasableArea(20f);
        third.setSoldAdvertisingPointLeasableQuantity(10);
        inventoryRepository.save(third);

        Occupation occupation = new Occupation();
        occupation.setContractNo("testUpdateWz");
        occupation.setAmount(5f);
        occupation.setBusinessType("WZ");
        occupation.setStartDate(LocalDate.now());
        occupation.setEndDate(LocalDate.now());
        occupation.setCinemaCode("testUpdateWz");
        occupation.setStatus(OccupationStatus.ACTIVE);
        occupationRepository.save(occupation);
        UpdateOccupationCommand updateOccupationCommand = new UpdateOccupationCommand();
        updateOccupationCommand.setAmount(6f);
        updateOccupationCommand.setContractNo("testUpdateWz");
        updateOccupationCommand.setCinemaCode("testUpdateWz");
        updateOccupationCommand.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now().plusDays(1)));
        updateOccupationCommand.setBusinessType("WZ");
//        Occupation afterModified = inventoryAppService.updateOccupation(updateOccupationCommand);
//        Assert.assertTrue(afterModified.getAmount() == 6f);
    }

    /**
     * 测试覆盖更新一个不存在的库存占用
     */
    @Test
    public void updateOccupationWhichNotExist() throws Exception {
        UpdateOccupationCommand updateOccupationCommand = new UpdateOccupationCommand();
        updateOccupationCommand.setAmount(20f);
        updateOccupationCommand.setContractNo("testNoNOExist");
        updateOccupationCommand.setCinemaCode("test121");
        updateOccupationCommand.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setBusinessType("YX");
//        try {
//            inventoryAppService.updateOccupation(updateOccupationCommand);
//        } catch (Exception e) {
//            Assert.assertTrue(e.getMessage().equals("库存占用不存在"));
//        }
    }

    /**
     * 测试覆盖更新一个已取消的库存占用
     */
    @Test
    public void updateOccupationHasCanceled() throws Exception {
        Occupation occupation = new Occupation();
        occupation.setContractNo("testNo00HasCanceled");
        occupation.setAmount(10f);
        occupation.setBusinessType("YX");
        occupation.setStartDate(LocalDate.now());
        occupation.setEndDate(LocalDate.now());
        occupation.setCinemaCode("test121");
        occupation.setStatus(OccupationStatus.CANCEL);
        occupationRepository.save(occupation);
        UpdateOccupationCommand updateOccupationCommand = new UpdateOccupationCommand();
        updateOccupationCommand.setAmount(20f);
        updateOccupationCommand.setContractNo("testNo00HasCanceled");
        updateOccupationCommand.setCinemaCode("test121");
        updateOccupationCommand.setStartDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setEndDate(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now()));
        updateOccupationCommand.setBusinessType("YX");
//        inventoryAppService.updateOccupation(updateOccupationCommand);
//        occupationRepository.findOne(Filter.create("contract_no", "testNo00HasCanceled")).ifPresent(o -> {
//            Assert.assertTrue(o.getAmount() == 20f);
//        });
    }

}