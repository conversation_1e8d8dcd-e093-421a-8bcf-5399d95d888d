package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.inventory.*;
import cmc.pad.resource.domain.resource.CinemaResource;
import cmc.pad.resource.domain.resource.CinemaResourceRepository;
import cmc.pad.resource.domain.resource.ResourceChangeRecordRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaResourceRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlInventoryRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlOccupationRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlResourceChangeRecordRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Filter;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;

import static org.mockito.Mockito.spy;

public class CinemaResourceAppServiceTest extends TestBase {

    private CinemaResourceRepository cinemaResourceRepository = new MysqlCinemaResourceRepository();
    private ResourceChangeRecordRepository resourceChangeRecordRepository = new MysqlResourceChangeRecordRepository();
    private InventoryRepository inventoryRepository = new MysqlInventoryRepository();
    private OccupationRepository occupationRepository = new MysqlOccupationRepository();
    private CinemaResourceAppService cinemaResourceAppService = spy(new CinemaResourceAppService(cinemaResourceRepository, resourceChangeRecordRepository,
            inventoryRepository, occupationRepository));

    @Test
    public void modifyCinemaResource() throws Exception {
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("test111");
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setOuterAreaLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(10f);
        cinemaResourceRepository.save(cinemaResource);

        Inventory inventory = new Inventory();
        inventory.setDate(LocalDate.now().plusDays(1));
        inventory.setCinemaCode("test111");
        inventory.setTotalAdvertisingPointLeasableQuantity(8);
        inventory.setTotalFixedPointLeasableArea(20f);
        inventory.setTotalOuterAreaLeasableArea(18f);
        inventory.setTotalMarketingPointLeasableArea(15f);
        inventory.setSoldAdvertisingPointLeasableQuantity(6);
        inventory.setSoldFixedPointLeasableArea(18f);
        inventory.setSoldOuterAreaLeasableArea(16f);
        inventory.setSoldMarketingPointLeasableArea(13f);
        inventoryRepository.save(inventory);

        Occupation yx = new Occupation();
        yx.setStatus(OccupationStatus.ACTIVE);
        yx.setCinemaCode("test111");
        yx.setBusinessType("YX");
        yx.setContractNo("1111111111111");
        yx.setAmount(2f);
        yx.setStartDate(LocalDate.now());
        yx.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(yx);

        Occupation gd = new Occupation();
        gd.setStatus(OccupationStatus.ACTIVE);
        gd.setCinemaCode("test111");
        gd.setBusinessType("GD");
        gd.setContractNo("2222222");
        gd.setAmount(2f);
        gd.setStartDate(LocalDate.now());
        gd.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(gd);

        Occupation wz = new Occupation();
        wz.setStatus(OccupationStatus.ACTIVE);
        wz.setCinemaCode("test111");
        wz.setBusinessType("WZ");
        wz.setContractNo("3333333333");
        wz.setAmount(2f);
        wz.setStartDate(LocalDate.now());
        wz.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(wz);

        Occupation xc = new Occupation();
        xc.setStatus(OccupationStatus.ACTIVE);
        xc.setCinemaCode("test111");
        xc.setBusinessType("XC");
        xc.setContractNo("444444444");
        xc.setAmount(2f);
        xc.setStartDate(LocalDate.now());
        xc.setEndDate(LocalDate.now().plusDays(1));
        occupationRepository.save(xc);

        UpdateCinemaResourceCommand command = new UpdateCinemaResourceCommand();
        command.setCinemaCode("test111");
        command.setAdvertisingPointLeasableQuantity(5);
        command.setMarketingPointLeasableArea(5f);
        command.setFixedPointLeasableArea(6f);
        command.setOuterAreaLeasableArea(3f);
        cinemaResourceAppService.modifyCinemaResource(command);
        cinemaResourceRepository.findOne(Filter.create("cinema_code", "test111")).ifPresent(resource -> {
            Assert.assertTrue(resource.getAdvertisingPointLeasableQuantity() == 5);
        });
    }

    @Test
    public void modifyCinemaResourceWhenNoDataChanged() throws Exception {
        CinemaResource cinemaResource = new CinemaResource();
        cinemaResource.setCinemaCode("test222");
        cinemaResource.setAdvertisingPointLeasableQuantity(10);
        cinemaResource.setMarketingPointLeasableArea(10f);
        cinemaResource.setOuterAreaLeasableArea(10f);
        cinemaResource.setFixedPointLeasableArea(10f);
        cinemaResourceRepository.save(cinemaResource);
        UpdateCinemaResourceCommand command = new UpdateCinemaResourceCommand();
        command.setCinemaCode("test222");
        command.setAdvertisingPointLeasableQuantity(10);
        command.setMarketingPointLeasableArea(10f);
        command.setFixedPointLeasableArea(10f);
        command.setOuterAreaLeasableArea(10f);
        cinemaResourceAppService.modifyCinemaResource(command);
    }

}