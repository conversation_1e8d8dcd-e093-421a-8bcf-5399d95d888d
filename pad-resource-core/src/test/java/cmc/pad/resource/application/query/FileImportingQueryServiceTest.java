package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.importing.*;
import com.alibaba.fastjson.JSON;
import mtime.lark.util.data.PageResult;
import org.junit.*;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDate;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:45
 * @Version 1.0
 */
public class FileImportingQueryServiceTest {

    private FileImportRepository repository = PowerMockito.mock(FileImportRepository.class);
    private FileImportingQueryService service = PowerMockito.spy(new FileImportingQueryService(repository));


    @Before
    public void before() {

        PageResult<FileImport> result = new PageResult<>();
        List<FileImport> list = new ArrayList<>();
        FileImport fileImport = new FileImport();
        list.add(fileImport);
        result.setTotalCount(1);
        result.setItems(list);
        PowerMockito.when(repository.findPage(any(), anyInt(), anyInt())).thenReturn(result);
        PowerMockito.when(repository.findMany(any())).thenReturn(Collections.EMPTY_LIST);
        PowerMockito.when(repository.list(any())).thenReturn(Collections.EMPTY_LIST);
        Optional<FileImport> optionalFileImport = Optional.of(new FileImport());
        PowerMockito.when(repository.findOne(any())).thenReturn(optionalFileImport);
        PowerMockito.when(repository.get(anyInt())).thenReturn(new FileImport());
    }

    @Test
    public void pageTest() {
        PageResult<FileImport> result = service.page(FileCategory.POINT_LOCATION, 1, 1);
        System.out.printf(" >>> " + JSON.toJSONString(result, true));
    }

    @Test
    public void listTest() {
        List<FileImport> list = service.list(FileCategory.POINT_LOCATION);
        System.out.printf(" >>> " + JSON.toJSONString(list, true));
    }

    @Test
    public void checkDuplicateFileTest() {
        boolean b = service.checkDuplicateFile(FileCategory.CINEMA_LEVEL, LocalDate.now());
        Assert.assertTrue(b);
    }

    @Test
    public void getTest() {
        FileImport fileImport = service.get(1);
        Assert.assertNotNull(fileImport);
    }
}
