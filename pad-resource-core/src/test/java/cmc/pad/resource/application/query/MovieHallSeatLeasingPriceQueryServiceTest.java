package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.MovieHallSeatPriceQuery;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPriceRepository;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
public class MovieHallSeatLeasingPriceQueryServiceTest {
    private MovieHallSeatLeasingPriceRepository repository = PowerMockito.mock(MovieHallSeatLeasingPriceRepository.class);
    private MovieHallSeatLeasingPriceQueryService service = PowerMockito.spy(new MovieHallSeatLeasingPriceQueryService(repository));

    @Before
    public void before() {

        PageResult<MovieHallSeatLeasingPrice> result = new PageResult<>();
        List<MovieHallSeatLeasingPrice> list = new ArrayList<>();
        MovieHallSeatLeasingPrice price = new MovieHallSeatLeasingPrice();
        list.add(price);
        result.setTotalCount(1);
        result.setItems(list);
        PowerMockito.when(repository.findPage(any(), anyInt(), anyInt())).thenReturn(result);
        PowerMockito.when(repository.findMany(any())).thenReturn(Collections.EMPTY_LIST);
        PowerMockito.when(repository.latestEffectiveDate()).thenReturn(LocalDate.now());
    }

    @Test
    public void effectivePageTest() {
        MovieHallSeatPriceQuery query = new MovieHallSeatPriceQuery();
        query.setMovieHallType("I");
        query.setPageIndex(1);
        query.setPageSize(10);
        PageResult<MovieHallSeatLeasingPrice> result = service.effectivePage(query);
        Assert.assertEquals(1, result.getTotalCount());
    }


    @Test
    public void listTest() {
        List<MovieHallSeatLeasingPrice> list = service.list("l1", "S", "I");
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = service.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }
}
