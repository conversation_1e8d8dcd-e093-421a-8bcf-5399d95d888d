package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.MovieHallSeatLeasingPrice;
import cmc.pad.resource.domain.price.MovieHallSeatLeasingPriceRepository;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
public class MovieHallSeatLeasingAppServiceTest {

    private MovieHallSeatLeasingPriceRepository repository = PowerMockito.mock(MovieHallSeatLeasingPriceRepository.class);
    private MovieHallSeatLeasingAppService service = spy(new MovieHallSeatLeasingAppService(repository));


    @Test
    public void importDataTest() {
        List<MovieHallSeatLeasingPrice> list = PowerMockito.mock(List.class);
        service.importData(list);
    }

    @Test
    public void discardTest() {
        service.discard(1);
    }
}
