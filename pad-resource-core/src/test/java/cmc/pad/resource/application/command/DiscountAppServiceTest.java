package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.discount.*;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlDiscountRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlDiscountRuleRepository;
import cmc.pad.resource.test.TestBase;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.Transaction;
import mtime.lark.util.lang.FaultException;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

public class DiscountAppServiceTest extends TestBase {

    private DiscountRepository discountRepository = new MysqlDiscountRepository();
    private DiscountRuleRepository discountRuleRepository = new MysqlDiscountRuleRepository();
    private DiscountAppService discountAppService = PowerMockito.spy(new DiscountAppService(discountRepository, discountRuleRepository));

    @Test
    public void insertDiscount() throws Exception {
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setDiscountType(1);
        saveDiscountCommand.setBusinessType("YX");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule rule = new DiscountRule();
        rule.setFactor(0.88f);
        rule.setComparisonSymbol(1);
        rule.setMin(1f);
        rule.setMax(5f);
        rules.add(rule);
        saveDiscountCommand.setDiscountRules(rules);
        discountAppService.saveDiscount(saveDiscountCommand);
        Assert.assertTrue(discountRepository.findOne(Filter.create("business_type", "YX").add("discount_type", 1)).isPresent());
    }

    /**
     * 覆盖测试添加同种业务类型同种折扣类型抛异常
     */
    @Test
    public void insertDiscountWhenDuplicate() throws Exception {
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setDiscountType(2);
        saveDiscountCommand.setBusinessType("YX");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule rule = new DiscountRule();
        rule.setFactor(0.88f);
        rule.setComparisonSymbol(1);
        rule.setMin(1f);
        rule.setMax(5f);
        rules.add(rule);
        saveDiscountCommand.setDiscountRules(rules);
        discountAppService.saveDiscount(saveDiscountCommand);
        try {
            discountAppService.saveDiscount(saveDiscountCommand);
        } catch (FaultException e) {
            Assert.assertTrue(e.getMessage().contains("已经存在"));
        }
    }

    /**
     * 覆盖测试更新折扣，在原来的折扣规则上改，并添加新的规则
     */
    @Test
    public void updateDiscountBaseOrginRule() throws Exception {
        final int[] discountId = {0};
        final long[] ruleIds = {0};
        discountAppService.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("WZ");
            discount.setDiscountType(DiscountType.AREA);
            discountId[0] = discountRepository.insertDiscount(tx, discount);
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(discountId[0]);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(11f);
            rule.setFactor(0.98f);
            DiscountRule rule2 = new DiscountRule();
            rule2.setDiscountId(discountId[0]);
            rule2.setComparisonSymbol(2);
            rule2.setMin(12f);
            rule2.setFactor(0.78f);
            ruleIds[0] = (long) tx.insert(rule).result(true).getKeys().get(0);
            tx.insert(rule).result(true).getKeys().get(0);
            discountRuleRepository.save(rule);
        });
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setId(discountId[0]);
        saveDiscountCommand.setDiscountType(2);
        saveDiscountCommand.setBusinessType("WZ");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule rule = new DiscountRule();//测试在原有规则的基础上修改，并添加一条新的规则
        rule.setId((int) ruleIds[0]);
        rule.setFactor(0.88f);
        rule.setComparisonSymbol(1);
        rule.setMin(1f);
        rule.setMax(5f);
        rules.add(rule);
        DiscountRule newRule = new DiscountRule();
        newRule.setFactor(0.88f);
        newRule.setComparisonSymbol(4);
        newRule.setMin(20f);
        rules.add(newRule);
        saveDiscountCommand.setDiscountRules(rules);
        discountAppService.saveDiscount(saveDiscountCommand);
        List<DiscountRule> ruleList = discountRuleRepository.findMany(Filter.create("discount_id", discountId[0]));
        Assert.assertTrue(ruleList.size() == 2 && ruleList.get(0).getMax() == 5 && ruleList.get(1).getMin() == 20);
    }

    /**
     * 覆盖测试更新折扣，删除原来的折扣规则，添加新的规则
     */
    @Test
    public void updateDiscountAddNew() throws Exception {
        final int[] discountId = {0};
        final long[] ruleIds = {0};
        discountAppService.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("GD");
            discount.setDiscountType(DiscountType.AREA);
            discountId[0] = discountRepository.insertDiscount(tx, discount);
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(discountId[0]);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(11f);
            rule.setFactor(0.98f);
            DiscountRule rule2 = new DiscountRule();
            rule2.setDiscountId(discountId[0]);
            rule2.setComparisonSymbol(2);
            rule2.setMin(12f);
            rule2.setFactor(0.78f);
            ruleIds[0] = (long) tx.insert(rule).result(true).getKeys().get(0);
            tx.insert(rule).result(true).getKeys().get(0);
            discountRuleRepository.save(rule);
        });
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setId(discountId[0]);
        saveDiscountCommand.setDiscountType(2);
        saveDiscountCommand.setBusinessType("GD");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule newRule = new DiscountRule();
        newRule.setFactor(0.88f);
        newRule.setComparisonSymbol(4);
        newRule.setMin(20f);
        rules.add(newRule);
        saveDiscountCommand.setDiscountRules(rules);
        discountAppService.saveDiscount(saveDiscountCommand);
        List<DiscountRule> ruleList = discountRuleRepository.findMany(Filter.create("discount_id", discountId[0]));
        Assert.assertTrue(ruleList.size() == 1 && ruleList.get(0).getFactor() == (0.88f));
    }

    /**
     * 测试覆盖添加新的规则存在重叠抛出异常
     */
    @Test
    public void updateDiscountWhenAddNewRuleAndOverlay() throws Exception {
        final int[] discountId = {0};
        discountAppService.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("XC");
            discount.setDiscountType(DiscountType.AREA);
            discountId[0] = discountRepository.insertDiscount(tx, discount);
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(discountId[0]);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(11f);
            rule.setFactor(0.98f);
            discountRuleRepository.save(rule);
        });
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setId(discountId[0]);
        saveDiscountCommand.setDiscountType(2);
        saveDiscountCommand.setBusinessType("XC");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule rule = new DiscountRule();
        rule.setFactor(0.88f);
        rule.setComparisonSymbol(2);
        rule.setMin(1f);
        rules.add(rule);
        DiscountRule rule2 = new DiscountRule();
        rule2.setFactor(0.88f);
        rule2.setComparisonSymbol(3);
        rule2.setMin(1f);
        rules.add(rule2);
        saveDiscountCommand.setDiscountRules(rules);
        try {
            discountAppService.saveDiscount(saveDiscountCommand);
        } catch (FaultException e) {
            Assert.assertEquals(e.getMessage(), "存在重叠的规则");
        }
    }

    @Test
    public void deleteDiscount() throws Exception {
        final int[] discountId = {0};
        discountAppService.getDatabase().begin((Transaction tx) -> {
            Discount discount = new Discount();
            discount.setBusinessType("GD");
            discount.setDiscountType(DiscountType.DURATION);
            discountId[0] = discountRepository.insertDiscount(tx, discount);
            DiscountRule rule = new DiscountRule();
            rule.setDiscountId(discountId[0]);
            rule.setComparisonSymbol(1);
            rule.setMin(1f);
            rule.setMax(10f);
            rule.setFactor(0.98f);
            discountRuleRepository.save(rule);
        });
        discountAppService.deleteDiscount(discountId[0]);
        Assert.assertFalse(discountRepository.findOne(Filter.create("id", discountId[0])).isPresent());

    }

    /**
     * 测试覆盖至多存在一种<=规则，否则抛出异常
     */
    @Test
    public void twoMoreLowsTest() throws Exception {
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setDiscountType(2);
        saveDiscountCommand.setBusinessType("XC");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule rule = new DiscountRule();
        rule.setFactor(0.88f);
        rule.setComparisonSymbol(3);
        rule.setMin(2f);
        rules.add(rule);
        DiscountRule rule2 = new DiscountRule();
        rule2.setFactor(0.88f);
        rule2.setComparisonSymbol(3);
        rule2.setMin(1f);
        rules.add(rule2);
        saveDiscountCommand.setDiscountRules(rules);
        try {
            discountAppService.saveDiscount(saveDiscountCommand);
        } catch (FaultException e) {
            Assert.assertEquals(e.getMessage(), ">=或者<=的折扣规则至多允许存在1个");
        }
    }

    /**
     * 测试覆盖至多存在一种>=规则，否则抛出异常
     */
    @Test
    public void twoMoreLargesTest() throws Exception {
        SaveDiscountCommand saveDiscountCommand = new SaveDiscountCommand();
        saveDiscountCommand.setDiscountType(1);
        saveDiscountCommand.setBusinessType("GD");
        List<DiscountRule> rules = new ArrayList<>();
        DiscountRule rule = new DiscountRule();
        rule.setFactor(0.88f);
        rule.setComparisonSymbol(4);
        rule.setMin(2f);
        rules.add(rule);
        DiscountRule rule2 = new DiscountRule();
        rule2.setFactor(0.88f);
        rule2.setComparisonSymbol(4);
        rule2.setMin(1f);
        rules.add(rule2);
        saveDiscountCommand.setDiscountRules(rules);
        try {
            discountAppService.saveDiscount(saveDiscountCommand);
        } catch (FaultException e) {
            Assert.assertEquals(e.getMessage(), ">=或者<=的折扣规则至多允许存在1个");
        }
    }

}