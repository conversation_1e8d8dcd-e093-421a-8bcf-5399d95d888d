package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.importing.FileImportRepository;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDate;

import static org.mockito.Matchers.any;
import static org.powermock.api.mockito.PowerMockito.spy;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 文件导入记录
 *
 * <AUTHOR>
 * @Date 2019/3/15 12:57
 * @Version 1.0
 */
public class FileImportingAppServiceTest {

    private FileImportRepository repository = PowerMockito.mock(FileImportRepository.class);
    private FileImportingAppService service = spy(new FileImportingAppService(repository));


    @Test
    public void importStartedTest() {
        when(repository.saveGenerateId(any())).thenReturn(1);
        SaveImportCommand command = new SaveImportCommand();
        command.setFileId("fileId");
        command.setFileName("fileName");
        command.setFileCategory(FileCategory.CINEMA_LEVEL);
        command.setVersion("version");
        command.setEffectiveDate(LocalDate.now());
        command.setImporter(1);
        int i = service.importStarted(command);
        Assert.assertEquals(1, i);
    }


    @Test
    public void importCompletedTest() {
        when(repository.get(any())).thenReturn(new FileImport());
        service.importCompleted(1);
    }

    @Test
    public void importFailedTest() {
        when(repository.get(any())).thenReturn(new FileImport());
        service.importFailed(1);
    }

    @Test
    public void discardFileTest() {
        when(repository.get(any())).thenReturn(new FileImport());
        service.discardFile(1);
    }

}
