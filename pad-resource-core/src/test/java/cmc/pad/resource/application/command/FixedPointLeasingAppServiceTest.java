package cmc.pad.resource.application.command;

import cmc.pad.resource.domain.price.FixedPointLeasingPrice;
import cmc.pad.resource.domain.price.FixedPointLeasingPriceRepository;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.powermock.api.mockito.PowerMockito.spy;

/**
 * <AUTHOR>
 * @Date 2019/3/19 15:16
 * @Version 1.0
 */
public class FixedPointLeasingAppServiceTest {
    private FixedPointLeasingPriceRepository repository = PowerMockito.mock(FixedPointLeasingPriceRepository.class);
    private FixedPointLeasingAppService service = spy(new FixedPointLeasingAppService(repository));

    @Test
    public void importDataTest() {
        List<FixedPointLeasingPrice> list = PowerMockito.mock(List.class);
        service.importData(list);
    }

    @Test
    public void discardTest() {
        service.discard(1);
    }
}
