package cmc.pad.resource.application.query;

import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlCinemaRepository;
import cmc.pad.resource.test.TestBase;
import org.junit.Assert;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

public class CinemaQueryServiceTest extends TestBase {

    private CinemaRepository repository = new MysqlCinemaRepository();
    private CinemaQueryService service = PowerMockito.spy(new CinemaQueryService(repository));

    @Test
    public void queryAllCinema() throws Exception {
        Cinema cinema = new Cinema();
        cinema.setCode("testNew");
        repository.save(cinema);
        List<Cinema> list = service.queryAllCinema();
        Assert.assertTrue(list.size() > 0);
    }

}