package cmc.pad.resource.application.query;

import cmc.pad.resource.application.query.data.MovieHallNamingQuery;
import cmc.pad.resource.domain.price.MovieHallNamingLeasingPriceRepository;
import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import mtime.lark.util.data.PageResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @Date 2019/3/19 17:47
 * @Version 1.0
 */
public class MovieHallNamingLeasingPriceQueryServiceTest {

    private MovieHallNamingLeasingPriceRepository repository = PowerMockito.mock(MovieHallNamingLeasingPriceRepository.class);
    private MovieHallNamingLeasingPriceQueryService service = PowerMockito.spy(new MovieHallNamingLeasingPriceQueryService(repository));


    @Before
    public void before() {

        PageResult<MovieHallNamingPrice> result = new PageResult<>();
        List<MovieHallNamingPrice> list = new ArrayList<>();
        MovieHallNamingPrice price = new MovieHallNamingPrice();
        list.add(price);
        result.setTotalCount(1);
        result.setItems(list);
        PowerMockito.when(repository.findPage(any(), anyInt(), anyInt())).thenReturn(result);
        PowerMockito.when(repository.findMany(any())).thenReturn(Collections.EMPTY_LIST);
        PowerMockito.when(repository.latestEffectiveDate()).thenReturn(LocalDate.now());
    }

    @Test
    public void effectivePageTest() {
        MovieHallNamingQuery query = new MovieHallNamingQuery();
        query.setCinemaCode("333");
        query.setMovieHallType("I");
        query.setPageIndex(1);
        query.setPageSize(10);
        PageResult<MovieHallNamingPrice> result = service.effectivePage(query);
        Assert.assertEquals(1, result.getTotalCount());
    }


    @Test
    public void listTest() {
        List<MovieHallNamingPrice> list = service.list("l1", "S");
        Assert.assertTrue(list.isEmpty());
    }

    @Test
    public void latestEffectiveDateTest() {
        LocalDate localDate = service.latestEffectiveDate();
        Assert.assertEquals(LocalDate.now(), localDate);
    }
}
