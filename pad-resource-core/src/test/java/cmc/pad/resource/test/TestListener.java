package cmc.pad.resource.test;

import org.powermock.core.spi.PowerMockTestListener;
import org.powermock.core.spi.testresult.TestMethodResult;
import org.powermock.core.spi.testresult.TestSuiteResult;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
public class TestListener implements PowerMockTestListener {

    @Override
    public void beforeTestSuiteStarted(Class<?> testClass, Method[] testMethods) throws Exception {
        DatabaseInitializer.init();
    }

    @Override
    public void beforeTestMethod(Object testInstance, Method method, Object[] arguments) throws Exception {
    }

    @Override
    public void afterTestMethod(Object testInstance, Method method, Object[] arguments, TestMethodResult testResult) throws Exception {

    }

    @Override
    public void afterTestSuiteEnded(Class<?> testClass, Method[] methods, TestSuiteResult testResult) throws Exception {

    }
}
