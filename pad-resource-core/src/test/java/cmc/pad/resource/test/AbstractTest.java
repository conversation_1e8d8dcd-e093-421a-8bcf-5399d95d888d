package cmc.pad.resource.test;

import mtime.lark.util.test.TestBase;
import mtime.lark.util.test.spring.SpringJUnit;
import org.junit.BeforeClass;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

public abstract class AbstractTest extends TestBase {

    @BeforeClass
    public static void init() {
        SpringJUnit.boot(Dummy.class, TestBootstrap.class);
    }

    /**
     * HACK: LOOP ENDLESS IF ANNOTATING ON AbstractTest DIRECTLY
     */
    @SpringBootApplication(exclude = MongoAutoConfiguration.class)
    public static class Dummy {
    }
}
