package cmc.pad.resource.test;

import ch.vorburger.exec.ManagedProcessException;
import ch.vorburger.mariadb4j.DB;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.config.ConfigProperties;
import mtime.lark.util.log.Logger;
import mtime.lark.util.log.LoggerManager;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Slf4j
class DatabaseInitializer {
    private static AtomicBoolean initialized = new AtomicBoolean(false);
    private static Logger logger = LoggerManager.getLogger(DatabaseInitializer.class);

    static void init() {
        if (initialized.compareAndSet(false, true)) {
            try {
                DB db = DB.newEmbeddedDB(Integer.valueOf(ConfigProperties.getProperty("mysql.port", "3307")));
                db.start();
                db.createDB("cmc_pad_resource");
                db.source("db/schema.sql", "root", null, "cmc_pad_resource");
            } catch (ManagedProcessException e) {
                logger.error("Failed to start mysql");
                throw new RuntimeException(e);
            }
        }
    }
}
