package cmc.pad.resource.test;

import mtime.lark.net.rpc.RpcApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@ComponentScan("cmc.pad")
public class TestBootstrap {


    public static void main(String[] args) {
        new RpcApplication(TestBootstrap.class, args).run();
    }


}
