package cmc.pad.resource.test;

import cmc.pad.resource.application.command.point.inventory.occupy.helper.RealOccupyData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cmc.pad.resource.application.command.point.inventory.occupy.alteration.contract.helper.AlterDataChecker.*;

/**
 * Created by fuwei on 2024/7/25.
 */
public class MergeOccupationDateRangeTest {
    public static void main(String[] args) {
        List<RealOccupyData.DateOccupyArea> dateOccupyAreas = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
                    LocalDate.of(2025, 1, 1),
                    LocalDate.of(2035, 1, 11),
                    1F
            ));
        }

//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 1),
//                LocalDate.of(2024, 1, 11),
//                20F
//        ));
//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 1),
//                LocalDate.of(2024, 1, 11),
//                10F
//        ));
//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 9),
//                LocalDate.of(2024, 1, 10),
//                20F
//        ));
//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 20),
//                LocalDate.of(2024, 2, 10),
//                30F
//        ));
//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 1),
//                LocalDate.of(2024, 2, 1),
//                10F
//        ));
//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 20),
//                LocalDate.of(2024, 2, 10),
//                30F
//        ));
//        dateOccupyAreas.add(RealOccupyData.DateOccupyArea.of(
//                LocalDate.of(2024, 1, 1),
//                LocalDate.of(2024, 2, 1),
//                10F
//        ));
        long start = System.currentTimeMillis();
        Map<LocalDate, Double> dayOccupListMap = dateOccupyAreas
                .stream()
                .flatMap(dateOccupyArea -> convert(dateOccupyArea).stream())
                .collect(Collectors.groupingBy(DayOccupyArea::getDate, Collectors.summingDouble(DayOccupyArea::getOccupyArea)));
        List<LocalDate> dayOccpList = dayOccupListMap.keySet().stream().sorted().collect(Collectors.toList());
        dayOccpList.forEach(dayOccp -> System.out.println(dayOccp + "->" + dayOccupListMap.get(dayOccp)));
        List<RealOccupyData.DateOccupyArea> dateOccupyAreaList = Lists.newArrayList();
        LocalDate tmpDay = dayOccpList.get(0);
        for (int i = 0; i < dayOccpList.size(); i++) {
            if (ChronoUnit.DAYS.between(dayOccpList.get(i), dayOccpList.get(i + 1)) != 1
                    || dayOccupListMap.get(dayOccpList.get(i)).doubleValue() != dayOccupListMap.get(dayOccpList.get(i + 1)).doubleValue()) {
                RealOccupyData.DateOccupyArea dateOccupyArea = new RealOccupyData.DateOccupyArea(tmpDay, dayOccpList.get(i), dayOccupListMap.get(dayOccpList.get(i)).floatValue());
                dateOccupyAreaList.add(dateOccupyArea);
                tmpDay = dayOccpList.get(i + 1);
            }
            if (i + 1 == dayOccpList.size() - 1) {
                if (dayOccpList.get(i + 1).isEqual(tmpDay))
                    dateOccupyAreaList.add(new RealOccupyData.DateOccupyArea(tmpDay, tmpDay, dayOccupListMap.get(dayOccpList.get(i + 1)).floatValue()));
                if (dayOccpList.get(0).isEqual(tmpDay))
                    dateOccupyAreaList.add(new RealOccupyData.DateOccupyArea(dayOccpList.get(0), dayOccpList.get(i + 1), dayOccupListMap.get(dayOccpList.get(i + 1)).floatValue()));
                break;
            }
        }
        dateOccupyAreaList.forEach(dateOccupyArea -> {
            System.out.println(dateOccupyArea.getStartDate() + " - " + dateOccupyArea.getEndDate() + " -> " + dateOccupyArea.getOccupyArea());
        });
        System.out.println(System.currentTimeMillis() - start);
//        System.out.println(dateOccupyAreaList);
//        System.out.println(">>>>>>>>>>>>>");
//        List<RealOccupyData.DateOccupyArea> newDateOccupyAreasList = foreachMergeDateRange(dateOccupyAreas);
//        System.out.println(">>>>最终>>>>");
//        newDateOccupyAreasList.sort(Comparator.comparing(RealOccupyData.DateOccupyArea::getStartDate));
//        newDateOccupyAreasList.forEach(dateOccupyArea -> {
//            System.out.println(dateOccupyArea.getStartDate() + " - " + dateOccupyArea.getEndDate() + " -> " + dateOccupyArea.getOccupyArea());
//        });
    }

    public static List<DayOccupyArea> convert(RealOccupyData.DateOccupyArea dateOccupyArea) {
        List<DayOccupyArea> dateList = new ArrayList<>();
        LocalDate current = dateOccupyArea.getStartDate();
        while (!current.isAfter(dateOccupyArea.getEndDate())) {
            dateList.add(new DayOccupyArea(current, dateOccupyArea.getOccupyArea()));
            current = current.plusDays(1);
        }
        return dateList;
    }

    @Data
    @AllArgsConstructor
    public static class DayOccupyArea {
        private LocalDate date;
        private Float occupyArea;
    }

    /*
    XXX
     */
    public static List<RealOccupyData.DateOccupyArea> foreachMergeDateRange(List<RealOccupyData.DateOccupyArea> dateOccupyAreas) {
        for (RealOccupyData.DateOccupyArea dateOccupyArea : dateOccupyAreas) {
            System.out.println(">>>相差" + ChronoUnit.DAYS.between(dateOccupyArea.getStartDate(), dateOccupyArea.getEndDate()) + "天 " + dateOccupyArea.getStartDate() + " - " + dateOccupyArea.getEndDate() + " -> " + dateOccupyArea.getOccupyArea());
        }
        System.out.println(dateOccupyAreas);
        List<RealOccupyData.DateOccupyArea> newMergedDateOccupyAreas = Lists.newArrayList();
        for (int i = 0; i < dateOccupyAreas.size(); i++) {
            if (newMergedDateOccupyAreas.isEmpty()) {
                newMergedDateOccupyAreas.add(dateOccupyAreas.get(i));
                continue;
            }
            RealOccupyData.DateOccupyArea newDateOccupyArea = dateOccupyAreas.get(i);
            System.out.println(">>>开始比较" + newDateOccupyArea.getStartDate() + " - " + newDateOccupyArea.getEndDate() + " -> " + newDateOccupyArea.getOccupyArea());
            if (newMergedDateOccupyAreas.stream().allMatch(newMergeredDateOccupyArea -> dateRangeIsNew(newDateOccupyArea, newMergeredDateOccupyArea))) {
                newMergedDateOccupyAreas.add(newDateOccupyArea);
                System.out.println(">>>没有交集" + newDateOccupyArea.getStartDate() + " - " + newDateOccupyArea.getEndDate() + " -> " + newDateOccupyArea.getOccupyArea());
                continue;
            }
            int index = 0;
            while (true) {
                RealOccupyData.DateOccupyArea newMergeredDateOccupyArea = newMergedDateOccupyAreas.get(index);
                List<RealOccupyData.DateOccupyArea> mergeDateOccupyAreaList = mergeDateRange(newDateOccupyArea, newMergeredDateOccupyArea);
                if (!mergeDateOccupyAreaList.isEmpty()) {
                    newMergedDateOccupyAreas.remove(index);
                    newMergedDateOccupyAreas.addAll(mergeDateOccupyAreaList);
                    break;
                }
                ++index;
            }
            System.out.println("当前遍历最终结果集:");
            newMergedDateOccupyAreas.stream().sorted(Comparator.comparing(RealOccupyData.DateOccupyArea::getStartDate)).forEach(date -> System.out.println("" + date.getStartDate() + " - " + date.getEndDate()));
        }
        return newMergedDateOccupyAreas;
    }

    public static List<RealOccupyData.DateOccupyArea> mergeDateRange(RealOccupyData.DateOccupyArea newDateOccupyArea, RealOccupyData.DateOccupyArea dateOccupyArea) {
        List<RealOccupyData.DateOccupyArea> newDateOccupyAreas = new ArrayList<>();
//        if (dateRangeIsNew(newDateOccupyArea, dateOccupyArea)) {
//            newDateOccupyAreas.add(dateOccupyArea);
//            newDateOccupyAreas.add(newDateOccupyArea);
//            return newDateOccupyAreas;
//        }
        if (dateRangeIsEqual(newDateOccupyArea, dateOccupyArea)) {
            System.out.println(">>相等, old" + dateOccupyArea + "new" + newDateOccupyArea);
            newDateOccupyArea.setOccupyArea(newDateOccupyArea.getOccupyArea() + dateOccupyArea.getOccupyArea());
            newDateOccupyAreas.add(newDateOccupyArea);
        }
        if (dateRangeShrink(newDateOccupyArea, dateOccupyArea)) {
            System.out.println(">>缩小, old" + dateOccupyArea + "new" + newDateOccupyArea);
            RealOccupyData.DateOccupyArea inner = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate(), newDateOccupyArea.getEndDate(), dateOccupyArea.getOccupyArea() + newDateOccupyArea.getOccupyArea());
            newDateOccupyAreas.add(inner);
            if (dateRangeAllShrink(newDateOccupyArea, dateOccupyArea)) {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate(), newDateOccupyArea.getStartDate().minusDays(1), dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getEndDate().plusDays(1), dateOccupyArea.getEndDate(), dateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
                newDateOccupyAreas.add(right);
            }
            if (dateRangeLeftShrink(newDateOccupyArea, dateOccupyArea)) {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate(), newDateOccupyArea.getStartDate().minusDays(1), dateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
            }
            if (dateRangeRightShrink(newDateOccupyArea, dateOccupyArea)) {
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getEndDate().plusDays(1), dateOccupyArea.getEndDate(), dateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(right);
            }
        }
        if (dateRangeExpand(newDateOccupyArea, dateOccupyArea)) {
            System.out.println(">>扩大 old" + dateOccupyArea + "new" + newDateOccupyArea);
            RealOccupyData.DateOccupyArea inner = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate(), dateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea() + dateOccupyArea.getOccupyArea());
            newDateOccupyAreas.add(inner);
            if (dateRangeAllExpand(newDateOccupyArea, dateOccupyArea)) {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate(), dateOccupyArea.getStartDate().minusDays(1), newDateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(dateOccupyArea.getEndDate().plusDays(1), newDateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
                newDateOccupyAreas.add(right);
            }
            if (dateRangeLeftExpand(newDateOccupyArea, dateOccupyArea)) {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate(), dateOccupyArea.getStartDate().minusDays(1), newDateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
            }
            if (dateRangeRightExpand(newDateOccupyArea, dateOccupyArea)) {
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(dateOccupyArea.getEndDate().plusDays(1), newDateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(right);
            }
        }
        if (dateLeftCrossing(newDateOccupyArea, dateOccupyArea)) {
            System.out.println(">>左交集, old" + dateOccupyArea + "new" + newDateOccupyArea);
            if (newDateOccupyArea.getEndDate().isEqual(dateOccupyArea.getStartDate())) {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate(), newDateOccupyArea.getEndDate().minusDays(1), newDateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea equ = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getEndDate(), newDateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea() + dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate().plusDays(1), dateOccupyArea.getEndDate(), dateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
                newDateOccupyAreas.add(equ);
                newDateOccupyAreas.add(right);
            } else {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate(), dateOccupyArea.getStartDate().minusDays(1), newDateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea inner = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate(), newDateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea() + dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getEndDate().plusDays(1), dateOccupyArea.getEndDate(), dateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
                newDateOccupyAreas.add(inner);
                newDateOccupyAreas.add(right);
            }
        }
        if (dateRightCrossing(newDateOccupyArea, dateOccupyArea)) {
            System.out.println(">>右交集, old" + dateOccupyArea + "new" + newDateOccupyArea);
            if (newDateOccupyArea.getStartDate().isEqual(dateOccupyArea.getEndDate())) {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate(), dateOccupyArea.getEndDate().minusDays(1), dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea equ = new RealOccupyData.DateOccupyArea(dateOccupyArea.getEndDate(), dateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea() + dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate().plusDays(1), newDateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
                newDateOccupyAreas.add(equ);
                newDateOccupyAreas.add(right);
            } else {
                RealOccupyData.DateOccupyArea left = new RealOccupyData.DateOccupyArea(dateOccupyArea.getStartDate(), newDateOccupyArea.getStartDate().minusDays(1), dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea inner = new RealOccupyData.DateOccupyArea(newDateOccupyArea.getStartDate(), dateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea() + dateOccupyArea.getOccupyArea());
                RealOccupyData.DateOccupyArea right = new RealOccupyData.DateOccupyArea(dateOccupyArea.getEndDate().plusDays(1), newDateOccupyArea.getEndDate(), newDateOccupyArea.getOccupyArea());
                newDateOccupyAreas.add(left);
                newDateOccupyAreas.add(inner);
                newDateOccupyAreas.add(right);
            }
        }
        System.out.println(">>>合并" + newDateOccupyArea.getStartDate() + " - " + newDateOccupyArea.getEndDate() + " 和 " + dateOccupyArea.getStartDate() + " - " + dateOccupyArea.getEndDate());
        System.out.println(">合并结果:");
        newDateOccupyAreas.stream().sorted(Comparator.comparing(RealOccupyData.DateOccupyArea::getStartDate)).forEach(date -> System.out.println(">" + date.getStartDate() + " - " + date.getEndDate()));
        return newDateOccupyAreas;
    }

    public static boolean dateRangeAllShrink(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return (newData.getStartDate().isAfter(oldData.getStartDate()) && newData.getEndDate().isBefore(oldData.getEndDate()));
    }

    public static boolean dateRangeLeftShrink(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return (newData.getStartDate().isAfter(oldData.getStartDate()) && newData.getEndDate().isEqual(oldData.getEndDate()));
    }

    public static boolean dateRangeRightShrink(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return (newData.getStartDate().isEqual(oldData.getStartDate()) && newData.getEndDate().isBefore(oldData.getEndDate()));
    }

    public static boolean dateRangeAllExpand(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return newData.getStartDate().isBefore(oldData.getStartDate()) && newData.getEndDate().isAfter(oldData.getEndDate());
    }

    public static boolean dateRangeLeftExpand(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return (newData.getStartDate().isBefore(oldData.getStartDate()) && newData.getEndDate().isEqual(oldData.getEndDate()));
    }

    public static boolean dateRangeRightExpand(RealOccupyData.DateOccupyArea newData, RealOccupyData.DateOccupyArea oldData) {
        return (newData.getStartDate().isEqual(oldData.getStartDate()) && newData.getEndDate().isAfter(oldData.getEndDate()));
    }
}