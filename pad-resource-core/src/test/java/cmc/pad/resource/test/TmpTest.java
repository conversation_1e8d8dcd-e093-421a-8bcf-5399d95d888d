package cmc.pad.resource.test;

import cmc.pad.resource.application.command.InventoryAppService;
import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.domain.inventory.OccupationStatus;
import com.google.common.collect.Lists;
import lombok.Data;
import org.junit.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by fuyuanpu on 2020/12/5.
 */
public class TmpTest {
    @Test
    public void test() {
        Occupation o1 = new Occupation();
        o1.setId(1);
        o1.setCinemaCode("820");
        o1.setContractNo("123");
        o1.setBusinessType("GD");
        o1.setAmount(12F);
        o1.setStartDate(LocalDate.of(2020, 9, 13));
        o1.setEndDate(LocalDate.of(2021, 9, 27));
        o1.setStatus(OccupationStatus.ACTIVE);

        Occupation o2 = new Occupation();
        o2.setId(2);
        o2.setCinemaCode("820");
        o2.setContractNo("123");
        o2.setBusinessType("GD");
        o2.setAmount(12F);
        o2.setStartDate(LocalDate.of(2020, 9, 13));
        o2.setEndDate(LocalDate.of(2021, 9, 27));
        o2.setStatus(OccupationStatus.ACTIVE);

        List<Occupation> list = Lists.newArrayList(o1, o2);
        //修改合同,检验新的明细通过，删除老的库存占用记录，再新增库存占用记录，记住老的合同各个明细占的库存
        List<InventoryPojo> oldPojoList = new ArrayList<>();
        for (Occupation occupation : list) {
            //每条明细占用的打散释放
            LocalDate start = occupation.getStartDate();
            LocalDate end = occupation.getEndDate();
            int between = (int) (end.toEpochDay() - start.toEpochDay());
            for (int i = 0; i <= between; i++) {
                InventoryPojo pojo = new InventoryPojo();
                pojo.setCinemaCode(occupation.getCinemaCode());
                pojo.setDate(start.plusDays(i));
                pojo.setCount(-occupation.getAmount());//释放每天的库存
                oldPojoList.add(pojo);
            }
        }
        Map<String, List<InventoryPojo>> oldOccupationMap = oldPojoList.stream().collect(Collectors.groupingBy(InventoryPojo::getCinemaCode));
        System.out.println(oldOccupationMap.get("820"));
    }

    @Data
    private static class InventoryPojo {
        private String cinemaCode;
        private LocalDate date;
        private Float count;
    }
}
