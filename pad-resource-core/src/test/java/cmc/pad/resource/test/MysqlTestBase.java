package cmc.pad.resource.test;

import ch.vorburger.exec.ManagedProcessException;
import ch.vorburger.mariadb4j.DB;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.config.ConfigProperties;
import org.junit.BeforeClass;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class MysqlTestBase {
    private static AtomicBoolean initialized = new AtomicBoolean(false);

    @BeforeClass
    public static void _beforeClass() throws Exception {
        if (initialized.compareAndSet(false, true)) {
            prepareMysql();
        }
    }

    private static void prepareMysql() throws ManagedProcessException {
        DB db = DB.newEmbeddedDB(Integer.valueOf(ConfigProperties.getProperty("mysql.port", "3307")));
        db.start();
        db.createDB("mobile");
        db.source("db/schema.sql", "root", null, "mobile");
    }
}
