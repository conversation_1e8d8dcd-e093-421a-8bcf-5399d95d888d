DROP TABLE IF EXISTS `city`;
CREATE TABLE `city` (
  `code`          VARCHAR(20) NOT NULL
  COMMENT '城市编码',
  `name`          VARCHAR(50) DEFAULT NULL
  COMMENT '城市名称',
  `region_code`   VARCHAR(20) DEFAULT NULL
  COMMENT '区域编码',
  `region_name`   VARCHAR(50) DEFAULT NULL
  COMMENT '区域名称',
  `superior_code` VARCHAR(20) DEFAULT NULL
  COMMENT '省/直辖市编码',
  `superior_name` VARCHAR(50) DEFAULT NULL
  COMMENT '省/直辖市名称',
  `city_level`    VARCHAR(10) DEFAULT NULL
  COMMENT '城市级别L1一线L2二线L3三线L4四线',
  `sync_time`     DATETIME    DEFAULT NULL
  COMMENT '同步时间',
  PRIMARY KEY (`code`)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `cinema`;
CREATE TABLE `cinema` (
  `code`        VARCHAR(20) NOT NULL
  COMMENT '影院内码',
  `name`        VARCHAR(50) DEFAULT NULL
  COMMENT '影院名称',
  `region_code` VARCHAR(20) DEFAULT NULL
  COMMENT '区域编码',
  `region_name` VARCHAR(50) DEFAULT NULL
  COMMENT '区域名称',
  `city_code`   VARCHAR(20) DEFAULT NULL
  COMMENT '城市编码',
  `sync_time`   DATETIME    DEFAULT NULL
  COMMENT '同步时间',
  PRIMARY KEY (`code`)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `cinema_resource`;
CREATE TABLE `cinema_resource` (
  `cinema_code`                         VARCHAR(20)  NOT NULL
  COMMENT '影院内码',
  `advertising_point_leasable_quantity` INT(11)      NOT NULL DEFAULT '0'
  COMMENT '宣传点位个数',
  `marketing_point_leasable_area`       FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '营销点位面积',
  `outer_area_leasable_area`            FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '外租区域面积',
  `fixed_point_leasable_area`           FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '固定点位面积',
  PRIMARY KEY (`cinema_code`)
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `resource_change_record`;
CREATE TABLE `resource_change_record` (
  `id`                                  INT(11)      NOT NULL AUTO_INCREMENT
  COMMENT '逻辑主键',
  `cinema_code`                         VARCHAR(20)  NOT NULL
  COMMENT '影院内码',
  `marketing_point_leasable_area`       FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '营销点位面积',
  `outer_area_leasable_area`            FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '外租区域面积',
  `fixed_point_leasable_area`           FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '固定点位面积',
  `advertising_point_leasable_quantity` INT(11)      NOT NULL DEFAULT '0'
  COMMENT '宣传点位个数',
  `update_time`                         DATETIME              DEFAULT NULL
  COMMENT '更新时间',
  `updator`                             VARCHAR(50)           DEFAULT NULL
  COMMENT '更新人',
  PRIMARY KEY (`id`)
)
  ENGINE = InnoDB
  AUTO_INCREMENT = 167
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `inventory`;
CREATE TABLE `inventory` (
  `cinema_code`                               VARCHAR(20)  NOT NULL,
  `date`                                      DATE         NOT NULL
  COMMENT '库存日期',
  `total_advertising_point_leasable_quantity` INT(11)      NOT NULL DEFAULT '0'
  COMMENT '宣传点位个数总量',
  `total_outer_area_leasable_area`            FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '外租区域面积总量',
  `total_fixed_point_leasable_area`           FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '固定点位面积总量',
  `total_marketing_point_leasable_area`       FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '营销点位面积总量',
  `sold_advertising_point_leasable_quantity`  INT(11)      NOT NULL DEFAULT '0'
  COMMENT '宣传点位个数剩余量',
  `sold_outer_area_leasable_area`             FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '外租区域面积剩余量',
  `sold_fixed_point_leasable_area`            FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '固定点位面积剩余量',
  `sold_marketing_point_leasable_area`        FLOAT(10, 2) NOT NULL DEFAULT '0.00'
  COMMENT '营销点位面积剩余量',
  PRIMARY KEY (`cinema_code`, `date`),
  KEY `cinema_code_index` (`cinema_code`),
  KEY `date_index` (`date`) USING BTREE
)
  ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `occupation`;
CREATE TABLE `occupation` (
  `id`            INT(11)      NOT NULL AUTO_INCREMENT
  COMMENT '逻辑ID',
  `cinema_code`   VARCHAR(20)  NOT NULL
  COMMENT '影院内码',
  `contract_no`   VARCHAR(50)  NOT NULL
  COMMENT '合同编号',
  `business_type` VARCHAR(10)  NOT NULL
  COMMENT '阵地业务类型编码',
  `amount`        FLOAT(10, 2) NOT NULL
  COMMENT '数量',
  `start_date`    DATE         NOT NULL
  COMMENT '合同开始日期',
  `end_date`      DATE         NOT NULL
  COMMENT '合同结束日期',
  `status`        INT(11)      NOT NULL
  COMMENT '占用状态1占用2取消占用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `contract_no_status_index` (`contract_no`, `status`) USING BTREE
)
  ENGINE = InnoDB
  AUTO_INCREMENT = 52
  DEFAULT CHARSET = utf8;


DROP TABLE IF EXISTS `discount`;
CREATE TABLE `discount` (
  `id`            INT(11)     NOT NULL AUTO_INCREMENT
  COMMENT '逻辑主键',
  `business_type` VARCHAR(10) NOT NULL
  COMMENT '阵地业务类型编码',
  `discount_type` INT(11)     NOT NULL
  COMMENT '折扣类型1时长折扣2面积折扣',
  `create_time`   DATETIME             DEFAULT NULL
  COMMENT '创建时间',
  `update_time`   DATETIME             DEFAULT NULL
  COMMENT '更新时间',
  `creator`       VARCHAR(50)          DEFAULT NULL
  COMMENT '创建用户',
  `updator`       VARCHAR(50)          DEFAULT NULL
  COMMENT '更新用户',
  PRIMARY KEY (`id`)
)
  ENGINE = InnoDB
  AUTO_INCREMENT = 73
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `discount_rule`;
CREATE TABLE `discount_rule` (
  `id`                INT(11)      NOT NULL AUTO_INCREMENT
  COMMENT '逻辑主键',
  `discount_id`       INT(11)      NOT NULL
  COMMENT '折扣主键ID',
  `comparison_symbol` INT(11)      NOT NULL
  COMMENT '比较符号1表示区间2表示==3表示<=4表示>=',
  `min`               FLOAT(10, 2) NOT NULL
  COMMENT '比较的最小值',
  `max`               FLOAT(10, 2)          DEFAULT NULL
  COMMENT '比较的最大值',
  `factor`            FLOAT(10, 2) NOT NULL
  COMMENT '折扣系数',
  PRIMARY KEY (`id`)
)
  ENGINE = InnoDB
  AUTO_INCREMENT = 182
  DEFAULT CHARSET = utf8;

  DROP TABLE IF EXISTS `advertising_point_leasing_price`;
  CREATE TABLE `advertising_point_leasing_price` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
    `city_level` varchar(255) NOT NULL DEFAULT '',
    `cinema_level` varchar(255) NOT NULL DEFAULT '',
    `min_area` int(11) NOT NULL DEFAULT '0',
    `min_total_price` int(11) NOT NULL DEFAULT '0',
    `expanded_unit_price` int(11) NOT NULL DEFAULT '0',
    `import_id` int(11) NOT NULL DEFAULT '0' COMMENT '导入记录ID',
    `updater` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `effective_date` date NOT NULL COMMENT '生效日期',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `cinema_level`;
  CREATE TABLE `cinema_level` (
    `inner_code` varchar(255) NOT NULL DEFAULT '' COMMENT '影城内码',
    `level` varchar(20) NOT NULL DEFAULT '' COMMENT '影城级别',
    `import_id` int(255) NOT NULL,
    PRIMARY KEY (`inner_code`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `file_import_record`;
  CREATE TABLE `file_import_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑ID',
    `file_id` varchar(50) NOT NULL DEFAULT '' COMMENT '文件ID',
    `file_name` varchar(50) NOT NULL DEFAULT '' COMMENT '文件名',
    `file_category` int(2) NOT NULL DEFAULT '0' COMMENT '文件类别',
    `version` varchar(100) NOT NULL DEFAULT '' COMMENT '版本信息',
    `effective_date` date NOT NULL COMMENT '生效日期',
    `status` int(11) NOT NULL COMMENT '上传状态',
    `begin_time` datetime NOT NULL COMMENT '上传时间',
    `end_time` datetime NOT NULL COMMENT '上传完成时间',
    `importer` int(11) NOT NULL COMMENT '上传人ID',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `fixed_point_leasing_price`;
  CREATE TABLE `fixed_point_leasing_price` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
    `city_level` varchar(255) NOT NULL DEFAULT '',
    `cinema_level` varchar(255) NOT NULL DEFAULT '',
    `unit_price` int(11) NOT NULL DEFAULT '0',
    `import_id` int(11) NOT NULL DEFAULT '0' COMMENT '导入记录ID',
    `updater` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `effective_date` date NOT NULL COMMENT '生效日期',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `marketing_point_leasing_price`;
  CREATE TABLE `marketing_point_leasing_price` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
    `city_level` varchar(255) NOT NULL DEFAULT '',
    `cinema_level` varchar(255) NOT NULL DEFAULT '',
    `unit_price_by_area` int(11) NOT NULL DEFAULT '0',
    `unit_price_by_quantity` int(11) NOT NULL DEFAULT '0',
    `import_id` int(11) NOT NULL DEFAULT '0' COMMENT '导入记录ID',
    `updater` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `effective_date` date NOT NULL COMMENT '生效日期',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `movie_hall_naming_price`;
  CREATE TABLE `movie_hall_naming_price` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
    `cinema_code` varchar(255) NOT NULL DEFAULT '',
    `movie_hall_type` varchar(255) NOT NULL DEFAULT '',
    `unit_price` int(11) NOT NULL DEFAULT '0',
    `import_id` int(11) NOT NULL DEFAULT '0' COMMENT '导入记录ID',
    `updater` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `effective_date` date NOT NULL COMMENT '生效日期',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `movie_hall_seat_leasing_price`;
  CREATE TABLE `movie_hall_seat_leasing_price` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
    `city_level` varchar(255) NOT NULL DEFAULT '',
    `cinema_level` varchar(255) NOT NULL DEFAULT '',
    `movie_hall_type` varchar(255) NOT NULL DEFAULT '',
    `min_hours` int(11) NOT NULL DEFAULT '0',
    `min_total_price` int(11) NOT NULL DEFAULT '0',
    `expanded_unit_price` int(11) NOT NULL DEFAULT '0',
    `import_id` int(11) NOT NULL DEFAULT '0' COMMENT '导入记录ID',
    `updater` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `effective_date` date NOT NULL COMMENT '生效日期',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

  DROP TABLE IF EXISTS `outer_area_leasing_price`;
  CREATE TABLE `outer_area_leasing_price` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '逻辑主键',
    `city_level` varchar(255) NOT NULL DEFAULT '',
    `cinema_level` varchar(255) NOT NULL DEFAULT '',
    `unit_price` int(11) NOT NULL DEFAULT '0',
    `import_id` int(11) NOT NULL DEFAULT '0' COMMENT '导入记录ID',
    `updater` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `effective_date` date NOT NULL COMMENT '生效日期',
    PRIMARY KEY (`id`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

INSERT INTO `cmc_pad_resource`.`cinema_level` (`inner_code`, `level`, `import_id`) VALUES ('137', 'S', '117');
INSERT INTO `cmc_pad_resource`.`file_import_record` (`id`, `file_id`, `file_name`, `file_category`, `version`, `effective_date`, `status`, `begin_time`, `end_time`, `importer`) VALUES ('105', '190402165043286179.xlsx', '影城级别导入模板.xlsx', '7', '', '2019-04-02', '3', '2019-04-02 16:50:43', '2019-04-02 16:50:44', '78220');
INSERT INTO `cmc_pad_resource`.`advertising_point_leasing_price` (`id`, `city_level`, `cinema_level`, `min_area`, `min_total_price`, `expanded_unit_price`, `import_id`, `updater`, `update_time`, `effective_date`) VALUES ('45', 'L1', 'S', '4', '3400', '400', '145', '78220', '2019-04-12 19:20:56', NOW());
INSERT INTO `cmc_pad_resource`.`fixed_point_leasing_price` (`id`, `city_level`, `cinema_level`, `unit_price`, `import_id`, `updater`, `update_time`, `effective_date`) VALUES ('74', 'L1', 'S', '100000', '188', '78220', '2019-04-19 18:06:13',  NOW());
INSERT INTO `cmc_pad_resource`.`marketing_point_leasing_price` (`id`, `city_level`, `cinema_level`, `unit_price_by_area`, `unit_price_by_quantity`, `import_id`, `updater`, `update_time`, `effective_date`) VALUES ('39', 'L1', 'S', '11', '0', '206', '78220', '2019-04-22 11:31:43',  NOW());
INSERT INTO `cmc_pad_resource`.`movie_hall_naming_price` (`id`, `cinema_code`, `movie_hall_type`, `unit_price`, `import_id`, `updater`, `update_time`, `effective_date`) VALUES ('114', '849', 'N', '300000', '198', '78220', '2019-04-19 18:45:20',  NOW());
INSERT INTO `cmc_pad_resource`.`movie_hall_seat_leasing_price` (`id`, `city_level`, `cinema_level`, `movie_hall_type`, `min_hours`, `min_total_price`, `expanded_unit_price`, `import_id`, `updater`, `update_time`, `effective_date`) VALUES ('59', 'L1', 'S', '', '4', '6600', '5500', '171', '78220', '2019-04-19 16:13:53',  NOW());
INSERT INTO `cmc_pad_resource`.`outer_area_leasing_price` (`id`, `city_level`, `cinema_level`, `unit_price`, `import_id`, `updater`, `update_time`, `effective_date`) VALUES ('53', 'L1', 'A', '10000', '184', '78220', '2019-04-19 17:48:15',  NOW());
INSERT INTO `cmc_pad_resource`.`cinema` (`code`, `name`, `region_code`, `region_name`, `city_code`, `sync_time`) VALUES ('124', '北京丰台万达广场店', '01', '北京', '290', '2019-04-25 03:00:03');
INSERT INTO `cmc_pad_resource`.`cinema_level` (`inner_code`, `level`, `import_id`) VALUES ('124', 'S', '333');
INSERT INTO `cmc_pad_resource`.`city` (`code`, `name`, `region_code`, `region_name`, `superior_code`, `superior_name`, `city_level`, `sync_time`) VALUES ('290', '北京', '01', '北京', '23', '北京', 'L1', '2019-04-25 02:00:01');


