<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pad-resource</artifactId>
        <groupId>cmc.pad</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>pad-resource-core</artifactId>
    <dependencies>
        <dependency>
            <groupId>mx.common</groupId>
            <artifactId>file-service-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>mx.common</groupId>
            <artifactId>mx-util-excel</artifactId>
        </dependency>
        <!--维数据-->
        <dependency>
            <groupId>cmc.basic</groupId>
            <artifactId>data-dict-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>mtime.lark</groupId>
            <artifactId>lark-util</artifactId>
        </dependency>
        <dependency>
            <groupId>mtime.lark</groupId>
            <artifactId>lark-db</artifactId>
        </dependency>
        <dependency>
            <groupId>cmc.mdm</groupId>
            <artifactId>location-front-service-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>cmc.mdm</groupId>
            <artifactId>location-admin-service-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>cmc.mdm</groupId>
            <artifactId>mdm-cinema-admin-contract</artifactId>
        </dependency>
        <dependency>
            <artifactId>cmc-copy-util</artifactId>
            <groupId>cmc.common</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito</artifactId>
            <version>1.7.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>1.7.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ch.vorburger.mariaDB4j</groupId>
            <artifactId>mariaDB4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cmc.tohdfs</groupId>
            <artifactId>cmc-tohdfs-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cmc.mdm</groupId>
            <artifactId>location-front-service-contract</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

</project>
