#set($cssUrls=[
        "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
        "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
        "${staticSiteUrl}/lib/select2/css/select2.min.css",
        "${staticSiteUrl}/lib/fileinput/css/fileinput.min.css"])
        #set($jsUrls=[
        "${staticSiteUrl}/lib/moment/moment-with-locales.js",
        "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
        "${staticSiteUrl}/mtime/datepicker/datepicker.js",
        "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
        "${staticSiteUrl}/mtime/validator/mtime.validator.js",
        "${staticSiteUrl}/mtime/form/mtime.form.js",
        "${staticSiteUrl}/lib/jquery.ui/js/jquery-ui.min.js",
        "${staticSiteUrl}/lib/jquery.ui/multiselect/js/jquery.multiselect.min.js",
        "${staticSiteUrl}/lib/jquery.ui/multiselect/js/jquery.multiselect.filter.min.js",
        "${staticSiteUrl}/lib/jquery.ui/multiselect/i18n/jquery.multiselect.zh-cn.js",
        "${staticSiteUrl}/mtime/multiselectable/multiselectable.ext.js",
        "${staticSiteUrl}/lib/select2/js/select2.min.js",
        "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
        "${staticSiteUrl}/mtime/selectable/selectable.js",
        "${staticSiteUrl}/lib/fileinput/js/fileinput.js",
        "${staticSiteUrl}/mtime/upload/upload.js",
        "/js/common/simpleFileupload.js",
        "/js/util/cmc_util.js",
        "/js/pointlocation/list.js"])
<style type="text/css">
.tit{
    width:100px;
    line-height:29px;
    text-align: right;
    display: inline-block;
    vertical-align: middle;
}
.add_hint{
	color: red;
}
.img_input{
    display: inline-block;
    vertical-align: middle;
    margin: -40px 1px 0 105px;
}
.media-left {
    width: 140px;
    text-align: right;
}



</style>
<dl id="containerDiv" class="cont-title-wrap">
<dt class="clearfix">
    查询
</dt>
<div class="clearfix pt10">
    <form id="searchForm">
        <input type="hidden" name="largeWard" id="largeWard" value="${largeWard}"/>
        <input type="hidden" name="category" value="10"/>
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                大区:
                <select class="form-control"
                        multiple
                        data-filter="true"
                        id="largeWardSelect"
                        name="largeWardSelect"
                ></select>
            </p>
            <p class="col-xs-12 col-sm-3 col-md-3">
                区域:
                <select name="region" id="region" class="form-control">
                    #if($user.rank == "CHAIN")
                    <option value="-1">全部</option>
                    #end
                    #if($user.rank == "AREA" || $user.rank == "CINEMA")
                    <option value="$user.area" selected="selected">$user.areaName</option>
                    #end
                </select>
            </p>
            <p class="col-xs-12 col-sm-3 col-md-3">
                影城:
                <select name="cinema" id="cinema" class="form-control">
                    #if($user.rank == "CHAIN"|| $user.rank == "AREA")
                    <option value="-1">全部</option>
                    #end
                    #if($user.rank == "CINEMA" )
                    <option value="$user.cinemaInnerCode" selected="selected">$user.cinemaName</option>
                    #end
                </select>
            </p>
            <p class="col-xs-12 col-sm-3 col-md-3">
                资源类型:
                <select name="businessTypeCode" class="form-control">
                    <option value="-1">全部</option>
                    #foreach($item in $!businessType.keySet())
                    <option value="$!{item}">$!{businessType.get($item)}</option>
                    #end
                </select>
            </p>
        </div>
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                资源归属:
                <select name="resourceOwnershipCode" class="form-control">
                    <option value="-1">全部</option>
                    #foreach($item in $!ownership.keySet())
                    <option value="$!{item}">$!{ownership.get($item)}</option>
                    #end
                </select>
            </p>
            <p class="col-xs-12 col-sm-3 col-md-3">
                资源编码:
                <input name="code" type="text" value=""/>
            </p>
            <p class="col-xs-12 col-sm-2 col-md-2">
                <a href="javascript:;" class="w-btn" name="queryBtn">
                    <i class="icon-check" for-datagrid="load"></i>查询
                </a>
            </p>
        </div>
    </form>
</div>
</dl>
<dl class="cont-title-wrap">
<dt class="clearfix">
    列表
</dt>
<dd>
    #if($perm.has("cmc.pad.resource.point_location.add"))
    <div class="btn-group">
        <a id="addResourceBtn" href="javascript:;" class="w-btn"><i class="icon-plus"></i>添加资源</a>
    </div>
    #end
    #if($perm.has("cmc.pad.resource.point_location.import"))
    <div class="btn-group">
        <a id="importBtn" href="javascript:;" class="w-btn"><i class="icon-updown"></i>导入资源</a>
    </div>
    #end
    <div class="btn-group">
        <a id="downloadTemplateBtn" href="javascript:;" class="w-btn"><i class="icon-down"></i>下载模板</a>
    </div>
    #if($perm.has("cmc.pad.resource.point_location.import.list"))
    <div class="btn-group">
        <a id="importResourceListBtn" href="javascript:;" class="w-btn"><i class="icon-check"></i>资源导入记录</a>
    </div>
    #end
    #if($perm.has("cmc.pad.resource.point_location.export"))
    <div class="btn-group">
        <a id="exportResourceBtn" href="javascript:;" class="w-btn"><i class="icon-down"></i>导出资源信息</a>
    </div>
    #end
    <div class="clearfix pt10 table-responsive">
        <table id="listTable"
               class="table table-bordered table-striped table-hover table-condensed"
               data-source="/pad/resource/point_location/list" data-search-form="#searchForm">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="regionName">区域</th>
                <th data-field-name="cinemaName">影城</th>
                <th data-field-name="businessTypeName">资源类型</th>
                <th data-field-name="resourceOwnershipName">资源归属</th>
                <th data-field-name="code">资源编码</th>
                <th data-field-name="availablePeriod">可用时段</th>
                <th data-field-name="floor">楼层</th>
                <th data-field-name="locationDesc">位置描述</th>
                <th data-field-name="planUse">规划用途</th>
                <th data-field-name="landingMode">落地方式</th>
                <th data-field-name="sellArea">可售面积(㎡)</th>
                <th data-field-name="floorHeight">层高</th>
                <th data-field-name="isSplittableText">是否可拆分</th>
                <th data-field-name="waterSupplyText">上下水</th>
                <th data-field-name="powerSupplyText">强电</th>
                <th data-field-name="fireFacilitiesText">消防设施</th>
                <th data-field-name="decoration">装修</th>
                <th data-column-type="oper" data-oper-btns="[
                    #if($perm.has("cmc.pad.resource.point_location.edit"))
                    {title:'编辑',txt:'icon-edit',handler:'edit'}
                    #end
                ]">
                操作
            </th>
        </tr>
    </table>
</div>
</dd>
        #parse("/view/point_location/edit.vm")
        #parse("/view/common/downloadList.vm")
        #parse("/view/common/simpleFileupload.vm")
        #parse("/view/common/exportFileForm.vm")
        </dl>