#set($cssUrls=[
    "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
    "${staticSiteUrl}/lib/select2/css/select2.min.css",
    "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
    "${staticSiteUrl}/mtime/loading/mtime.loading.css",
    "$!{staticSiteUrl}/css/wanda.css"])
#set($jsUrls=[
    "${staticSiteUrl}/lib/moment/moment-with-locales.js",
    "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
    "${staticSiteUrl}/lib/select2/js/select2.min.js",
    "${staticSiteUrl}/mtime/datepicker/datepicker.js",
    "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
    "${staticSiteUrl}/mtime/validator/mtime.validator.js",
    "${staticSiteUrl}/mtime/selectable/selectable.js",
    "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
    "${staticSiteUrl}/mtime/form/mtime.form.js",
    "${staticSiteUrl}/mtime/loading/mtime.loading.js",
    "${staticSiteUrl}/mtime/dialog/mtime.dialog.js",
    "/js/discount/list.js"])

<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    业务类型:
                    <select name="businessType" id="businessType"
                            class="form-control">
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                </p>
            </div>
        </form>
    </div>
</dl>
<dl class="cont-title-wrap">
    <dt class="clearfix">
        列表
    </dt>
    <dd>
        <div class="btn-group">
            <a href="javascript:;" class="w-btn" id="addDiscountBtn"><i class="icon-plus"></i>添加折扣系数</a>
        </div>
    <div class="clearfix pt10 table-responsive">
        <table id="discountTable"
               class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="businessType">业务类型</th>
                <th data-field-name="discountType">折扣类别</th>
                <th data-field-name="discountDesc" data-html="true" data-text-align="left">折扣系数</th>
                <th data-column-type="oper"
                    data-oper-btns="[{txt:'icon-edit',title:'编辑',condition:true,handler:'edit'},
                    {txt:'icon-del',title:'删除',condition:true,handler:'delete'}]" class="text-center">操作
                </th>
            </tr>
        </table>
    </div>
    </dd>
</dl>
<dl class="cont-title-wrap" style="display: none" id="editDl">
    <dt class="clearfix">
        编辑
    </dt>
    <div class="clearfix pt10">
        <p class="clearfix  row pl10">
        <p class="col-xs-12 col-sm-3 col-md-3">
            <a href="javascript:;" class="w-btn" id="saveBtn">
                <i class="icon-save"></i>保存
            </a>
            <a href="javascript:;" class="w-btn" id="closeBtn">
                <i class="icon-x"></i>关闭
            </a>
        </p>
        </p>
    </div>
    <div style="margin-left: -100px;">
        <form id="addPage" class="form-horizontal" role="form">
            <input type="hidden" name="id"/>
            <div class="clearfix">
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">阵地业务类型：</label>
                    <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4" style="margin-left: -20px;">
                        <select name="businessTypeToSave" id="businessTypeToSave"
                                class="form-control">
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-12 col-sm-3 col-md-3 col-lg-3 control-label">折扣类型：</label>
                    <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4" style="margin-left: -20px;">
                        <label class="radio-inline">
                            <input type="radio" name="discountType" value="1"/>时长折扣
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="discountType" value="2"/>面积折扣
                        </label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="clearfix pt10">
        <p class="clearfix  row pl10">
        <p class="col-xs-12 col-sm-12 col-md-12">
            <a href="javascript:;" class="w-btn" id="addRuleBtn">
                <i class="icon-plus"></i>添加
            </a>
            <span><font color="red">说明：按区间设置时，为前闭后开区间，即包含前面的数值，不包含后面的数值</font></span>
        </p>
        </p>
    </div>
    <div class="clearfix pt10" id="discountRuleDiv" style="margin-top: -10px;display: none;">
        <table class="table-condensed">
            <tbody id="discountRuleTbody">
            </tbody>
        </table>
    </div>
</dl>

