#set($cssUrls=[
        "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
        "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
        "${staticSiteUrl}/lib/select2/css/select2.min.css",
        "${staticSiteUrl}/lib/fileinput/css/fileinput.min.css"])
        #set($jsUrls=[
        "${staticSiteUrl}/lib/moment/moment-with-locales.js",
        "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
        "${staticSiteUrl}/mtime/datepicker/datepicker.js",
        "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
        "${staticSiteUrl}/mtime/validator/mtime.validator.js",
        "${staticSiteUrl}/mtime/form/mtime.form.js",
        "${staticSiteUrl}/lib/jquery.ui/js/jquery-ui.min.js",
        "${staticSiteUrl}/lib/jquery.ui/multiselect/js/jquery.multiselect.min.js",
        "${staticSiteUrl}/lib/jquery.ui/multiselect/js/jquery.multiselect.filter.min.js",
        "${staticSiteUrl}/lib/jquery.ui/multiselect/i18n/jquery.multiselect.zh-cn.js",
        "${staticSiteUrl}/mtime/multiselectable/multiselectable.ext.js",
        "${staticSiteUrl}/lib/select2/js/select2.min.js",
        "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
        "${staticSiteUrl}/mtime/selectable/selectable.js",
        "${staticSiteUrl}/lib/fileinput/js/fileinput.js",
        "${staticSiteUrl}/mtime/upload/upload.js",
        "/js/common/simpleFileupload.js",
        "/js/util/cmc_util.js",
        "/js/contract/list.js"])
<style type="text/css">
.tit{
    width:100px;
    line-height:29px;
    text-align: right;
    display: inline-block;
    vertical-align: middle;
}
.add_hint{
	color: red;
}
.img_input{
    display: inline-block;
    vertical-align: middle;
    margin: -40px 1px 0 105px;
}
.media-left {
    width: 140px;
    text-align: right;
}


</style>
<dl id="containerDiv" class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <input type="hidden" name="largeWard" id="largeWard" value="${largeWard}"/>
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    大区:
                    <select class="form-control"
                            multiple
                            data-filter="true"
                            id="largeWardSelect"
                            name="largeWardSelect"
                    ></select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    区域:
                    <select name="region" id="region" class="form-control">
                        #if($user.rank == "CHAIN")
                        <option value="-1">全部</option>
                        #end
                        #if($user.rank == "AREA" || $user.rank == "CINEMA")
                        <option value="$user.area" selected="selected">$user.areaName</option>
                        #end
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    填报人:<input name="operatorName" value="" type="text"/>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    计划收款日期:
                    <input id="startTime" name="startTime" data-init-element="false" type="text" style="width:30%"
                           data-date-type="date" role="datepicker" data-max="#endTime"/>
                    -
                    <input id="endTime" name="endTime" data-init-element="false" type="text" style="width:30%"
                           data-date-type="date" role="datepicker" data-min="#startTime"/>
                </p>
            </div>
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    客户名称:<input name="customer" value="" type="text"/>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    合同/申请单号:<input name="code" type="text" value=""/>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    合同状态:
                    <select name="contractState" class="form-control">
                        <option value="-1">全部</option>
                        <option value="0">未生效</option>
                        <option value="1" selected="selected">正常</option>
                        <option value="2">变更中</option>
                        <option value="3">作废中</option>
                        <option value="4">已作废</option>
                        <option value="5">关闭中</option>
                        <option value="6">已关闭</option>
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    逾期状态:
                    <select name="delayState" class="form-control">
                        <option value="-1">全部</option>
                        <option value="1">正常</option>
                        <option value="2" selected="selected">逾期</option>
                    </select>
                </p>
                <p class="col-xs-12 col-sm-2 col-md-2">
                    <a href="javascript:;" class="w-btn" name="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                    <a href="javascript:;" class="w-btn" name="cleanBtn">
                        <i class="icon-x"></i>重置
                    </a>
                </p>
            </div>
        </form>
    </div>
</dl>
<dl class="cont-title-wrap">
<dd>
    <div class="clearfix pt10 table-responsive">
        <table id="listTable"
               class="table table-bordered table-striped table-hover table-condensed"
               data-source="/pad/resource/contract/deContractCollectPlanList" data-search-form="#searchForm">
            <tr>
                <th data-field-name="contractNo">合同/申请单号</th>
                <th data-field-name="contractType" data-field-type="Enum[ContractType]">单据类型</th>
                <th data-field-name="areaName">所属区域</th>
                <th data-field-name="customerName">客户名称</th>
                <th data-field-name="totalAmount">合同金额</th>
                <th data-field-name="condition">收款计划</th>
                <th data-field-name="type" data-field-type="Enum[CollectType]">收款类型</th>
                <th data-field-name="planCollectionDate" data-field-type="Date[YYYY-MM-DD]">计划收款日期</th>
                <th data-field-name="planCollectionAmount">计划收款金额</th>
                <th data-field-name="accumulatedReceivableAmount">累计应收金额</th>
                <th data-field-name="claimedAmount">累计已认领金额</th>
                <th data-field-name="accumulatedUnpaidAmount">累计欠缴金额</th>
                <th data-field-name="overdueState" data-field-type="Enum[OverdueState]">逾期状态</th>
                <th data-field-name="operatorName">填报人</th>
                <th data-field-name="contractStartDate" data-field-type="Date[YYYY-MM-DD]">合同起始日期</th>
                <th data-field-name="contractEndDate" data-field-type="Date[YYYY-MM-DD]">合同终止日期</th>
                <th data-field-name="contractState" data-field-type="Enum[ContractState]">合同/申请单状态</th>
                <th data-field-name="operatorDate" data-field-type="Date[YYYY-MM-DD HH:mm:ss]">申请日期</th>
                <th data-field-name="updateTime" data-field-type="Date[YYYY-MM-DD HH:mm:ss]">最后修改日期</th>
            </tr>
        </table>
    </div>
</dd>
</dl>