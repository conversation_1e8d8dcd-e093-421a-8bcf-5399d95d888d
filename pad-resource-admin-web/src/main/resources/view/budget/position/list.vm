#set($cssUrls=[
    "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
    "${staticSiteUrl}/lib/select2/css/select2.min.css",
    "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
    "${staticSiteUrl}/mtime/loading/mtime.loading.css",
    "${staticSiteUrl}/lib/fileinput/css/fileinput.min.css",
    "/css/bootstrap-table.min.css",
    "/css/bootstrap-table-fixed-columns.min.css",
    "$!{staticSiteUrl}/css/wanda.css"])
#set($jsUrls=[
    "${staticSiteUrl}/lib/moment/moment-with-locales.js",
    "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
    "${staticSiteUrl}/lib/select2/js/select2.min.js",
    "${staticSiteUrl}/mtime/datepicker/datepicker.js",
    "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
    "${staticSiteUrl}/mtime/validator/mtime.validator.js",
    "${staticSiteUrl}/mtime/selectable/selectable.js",
    "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
    "${staticSiteUrl}/mtime/form/mtime.form.js",
    "${staticSiteUrl}/mtime/loading/mtime.loading.js",
    "${staticSiteUrl}/mtime/dialog/mtime.dialog.js",
    "${staticSiteUrl}/lib/fileinput/js/fileinput.js",
    "${staticSiteUrl}/mtime/upload/upload.js",
    "/js/bootstrap/bootstrap-table.min.js",
    "/js/bootstrap/bootstrap-table-locale-all.min.js",
    "/js/bootstrap/bootstrap-table-fixed-columns.min.js",
    "/js/budget/fileupload.js",
    "/js/budget/position/list.js"])
<script type="">
    function hasEditPermission() {
        return $perm.has("cmc.pad.resource.budget.point_location.edit");
    }
</script>
<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-2 col-md-2">
                    区域:
                    <select name="regionCode" id="regionCode"
                            class="form-control">
                        <option value="-1">全部</option>
                    </select>
                </p>

                <p class="col-xs-12 col-sm-2 col-md-2">
                    影城:
                    <select name="cinemaInnerCode" id="cinemaInnerCode" class="form-control">
                        <option value="-1">全部</option>
                    </select>
                </p>
                <p class="col-xs-12 col-sm-2 col-md-2">
                    资源类型:
                    <select name="resourceType" id="resourceType" class="form-control" style="width: 8rem">
                        <option value="-1">全部</option>
                        #foreach($item in $resourceTypeMap.entrySet())
                            <option value="$!{item.key}">$!{item.value}</option>
                        #end
                    </select>
                </p>
                <p class="col-xs-12 col-sm-2 col-md-2">
                    资源编码:
                    <input type="text" name="resourceCode" id="resourceCode">
                </p>

                <p class="col-xs-12 col-sm-2 col-md-2">
                    预算年份:
                    <select name="year" id="year" class="form-control" style="width: 8rem">
                        #set($yearList=[2022..2099])
                        #foreach($year in $yearList)
                            <option value="$year" #if($currentYear==$year)  selected="selected" #end >$year</option>
                        #end
                    </select>
                </p>
                <p class="col-xs-12 col-sm-2 col-md-2">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                    <a href="javascript:;" class="w-btn" id="btnReset"><i class="icon-x"></i>重置</a>
                </p>
            </div>
        </form>
    </div>
</dl>
<dl class="cont-title-wrap">
    <dt class="clearfix">
        列表
    </dt>
    <div class="clearfix pt10 table-responsive">
        #if($perm.has("cmc.pad.resource.budget.point_location.set"))
        <a href="javascript:;" class="w-btn" id="budgetImport" style="margin-left: 20px;">预算设定</a>
        #end
        <a href="javascript:;" id="budgetImportTemplate" style="margin-left: 10px;margin-right: 10px;">下载模板</a>
        <a href="javascript:;" class="w-btn" id="budgetImportLog">预算导入记录</a>
        <br/><br/>
        <table id="resourceTable1">
            <thead>
            <tr>
                <th data-field="id" data-formatter="index_formatter">NO.</th>
                <th data-field="regionName">区域</th>
                <th data-field="cinemaName">影城</th>
                <th data-field="resourceType">资源类型</th>
                <th data-field="resourceAffiliation">资源归属</th>
                <th data-field="resourceCode">资源编码</th>
                <th data-field="locationDescription">位置描述</th>
                <th data-field="usePlan">规划用途</th>
                <th data-field="landMode">落地方式</th>
                <th data-field="areaSize">可售面积</th>
                <th data-field="budgetYear">年度预算(元)</th>
                <th data-field="budgetMonth1">1月</th>
                <th data-field="budgetMonth2">2月</th>
                <th data-field="budgetMonth3">3月</th>
                <th data-field="budgetMonth4">4月</th>
                <th data-field="budgetMonth5">5月</th>
                <th data-field="budgetMonth6">6月</th>
                <th data-field="budgetMonth7">7月</th>
                <th data-field="budgetMonth8">8月</th>
                <th data-field="budgetMonth9">9月</th>
                <th data-field="budgetMonth10">10月</th>
                <th data-field="budgetMonth11">11月</th>
                <th data-field="budgetMonth12">12月</th>
                <th data-field="id" data-formatter="operate_formatter" class="text-center">操作
                </th>
            </tr>
            </thead>
        </table>
    </div>
</dl>
<dl class="cont-title-wrap" style="display: none" id="recordUpdate">
    <dt class="clearfix">
        预算更新记录
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                <a href="javascript:;" class="w-btn" id="closeRecordUpdateBtn">
                    <i class="icon-x"></i>关闭
                </a>
            </p>
        </div>
    </div>
    <div class="clearfix pt10 table-responsive">
        <table id="resourceChangeRecordTable" class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="regionName">区域</th>
                <th data-field-name="cinemaName">影城</th>
                <th data-field-name="resourceType">资源类型</th>
                <th data-field-name="budgetYear">年度预算(元)</th>
                <th data-field-name="budgetMonth1">1月</th>
                <th data-field-name="budgetMonth2">2月</th>
                <th data-field-name="budgetMonth3">3月</th>
                <th data-field-name="budgetMonth4">4月</th>
                <th data-field-name="budgetMonth5">5月</th>
                <th data-field-name="budgetMonth6">6月</th>
                <th data-field-name="budgetMonth7">7月</th>
                <th data-field-name="budgetMonth8">8月</th>
                <th data-field-name="budgetMonth9">9月</th>
                <th data-field-name="budgetMonth10">10月</th>
                <th data-field-name="budgetMonth11">11月</th>
                <th data-field-name="budgetMonth12">12月</th>
                <th data-field-name="createTime" data-field-type="Date">更新时间</th>
                <th data-field-name="userName">更新人</th>
            </tr>
        </table>
    </div>
</dl>
<dl class="cont-title-wrap" style="display: none" id="recordImport">
    <dt class="clearfix">
        预算导入记录
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                <a href="javascript:;" class="w-btn" id="closeRecordImportBtn">
                    <i class="icon-x"></i>关闭
                </a>
            </p>
        </div>
    </div>
    <div class="clearfix pt10 table-responsive">
        <table id="importTable" class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="fileName">导入文件名称</th>
                <th data-field-name="beginTime" data-field-type="Date">导入时间</th>
                <th data-field-name="importer">操作人</th>
                <th data-field-name="statusRemarkView">状态</th>
                <th data-column-type="oper"
                    data-oper-btns="[{txt:'icon-down',title:'下载',condition:true,handler:'download'}]">下载
                </th>
            </tr>
        </table>
    </div>
</dl>
#parse("/view/budget/fileupload.vm")
#parse("/view/budget/position/edit.vm")