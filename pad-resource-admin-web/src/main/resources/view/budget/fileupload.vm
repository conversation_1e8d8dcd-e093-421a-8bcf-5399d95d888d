<div class="modal fade" id="fileModal" tabindex="-1" aria-labelledby="fileModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 800px">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="fileModalLabel">预算批量设定</h4>
            </div>
            <div class="modal-body">
                <div class="clearfix row pl10 price">
                    <p class="col-xs-12 text-right col-sm-2 col-md-2">
                        预算年份:
                    </p>
                    <p class="col-xs-12 col-sm-7 col-md-7">
                        <select name="fileYear" id="fileYear" class="form-control" style="width: 8rem">
                            <option value="0">请选择</option>
                            <option value="$currentYear">本年</option>
                            #set($nextYear=$currentYear + 1)
                            <option value="$nextYear">明年</option>
                        </select>
                        <input type="hidden" value="0" id="category" name="category"/>
                    </p>
                </div>
                <div class="clearfix row pl10">
                    <p class="col-xs-12 text-right col-sm-2 col-md-2">
                        选择文件:
                    </p>
                    <p class="col-xs-12 col-sm-7 col-md-7">
                        <input style="width:70%" id="exFile" type="file" name="exFile" data-min-file-count="1"
                               class="form-control input-md"/>
                    </p>
                </div>
                <div class="clearfix row pl10">
                    <p class="col-xs-12 text-right col-sm-2 col-md-2"></p>
                    <p class="col-xs-12 col-sm-7 col-md-7" style="color: red">
                        请使用系统模板导入，在《预算导入记录》中查看导入结果
                    </p>
                </div>

            </div>
            <div class="modal-footer">
                <div class="cont-list no-icon">
                    <a href="javascript:;" class="w-btn" data-dismiss="modal" data-target="#fileModal"><i
                            class="icon-disable" for-datagrid="load"></i>取消</a>
                    <a href="javascript:;" class="w-btn upload"><i
                            class="icon-updown" for-datagrid="load"></i>上传</a>
                </div>
            </div>
        </div>
        <div id="errDiv">
        </div>
    </div>
</div>

