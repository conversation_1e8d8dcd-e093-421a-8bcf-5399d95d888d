#set($cssUrls=[
    "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
    "${staticSiteUrl}/lib/select2/css/select2.min.css",
    "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
    "${staticSiteUrl}/mtime/loading/mtime.loading.css",
    "${staticSiteUrl}/lib/fileinput/css/fileinput.min.css",
    "$!{staticSiteUrl}/css/wanda.css"])
#set($jsUrls=[
    "${staticSiteUrl}/lib/moment/moment-with-locales.js",
    "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
    "${staticSiteUrl}/lib/select2/js/select2.min.js",
    "${staticSiteUrl}/mtime/datepicker/datepicker.js",
    "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
    "${staticSiteUrl}/mtime/validator/mtime.validator.js",
    "${staticSiteUrl}/mtime/selectable/selectable.js",
    "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
    "${staticSiteUrl}/mtime/form/mtime.form.js",
    "${staticSiteUrl}/mtime/loading/mtime.loading.js",
    "${staticSiteUrl}/lib/fileinput/js/fileinput.js",
    "${staticSiteUrl}/mtime/upload/upload.js",
    "/js/common/fileupload.js",
    "/js/cinema/list.js"])

<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    地区级别:
                    <select name="cityDistrictLevel" id="cityDistrictLevel"
                            class="form-control">
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    影城级别:
                    <select name="cinemaLevel" id="cinemaLevel"
                            class="form-control">
                    </select>
                </p>

                <p class="col-xs-12 col-sm-3 col-md-3">
                    影城:
                    <select name="cinemaCode" id="cinemaInnerCode" class="form-control">
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                </p>
            </div>
        </form>
    </div>
    <dt class="clearfix">
        列表
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-12 col-md-12">
                <a href="javascript:;" class="w-btn" id="importBtn">
                    <i class="icon-updown" for-datagrid="load"></i>导入影城级别
                </a>
                <a href="#importTable" class="w-btn" id="importLogBtn">
                    <i class="icon-notes" for-datagrid="load"></i>导入记录
                </a>
                <a href="javascript:;" id="downloadDemo">
                    下载模版
                </a>
            </p>
        </div>
    </div>
    <div class="clearfix pt10 table-responsive">
        <table id="cinemaLevelTable"
               class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="regionName">区域</th>
                <th data-field-name="cinemaName">影城名称</th>
                <th data-field-name="cityName">城市</th>
                <th data-field-name="cityDistrictLevel">地区级别</th>
                <th data-field-name="cinemaLevel">影城级别</th>
            </tr>
        </table>
    </div>
</dl>
<dl id="fileImportDiv" class="cont-title-wrap hidden">
    <dt class="clearfix">
        <i class="close-file-import-log icon-x pull-right" style="cursor: pointer;"></i>
        影城级别导入记录
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                <a href="javascript:;" class="w-btn close-file-import-log">
                    <i class="icon-check" for-datagrid="load"></i>关闭
                </a>
            </p>
        </div>
    </div>
    <dd>
        <div class="cont-table-wrap clearfix">
            <div class="table-responsive">
                <table id="importTable"
                       class="table table-bordered table-striped table-hover table-condensed">
                    <tr>
                        <th data-column-type="index">NO.</th>
                        <th data-field-name="fileName">导入文件名称</th>
                        <th data-field-name="beginTime" data-field-type="Date">导入时间</th>
                        <th data-field-name="importer">操作人</th>
                        <th data-field-name="statusView">状态</th>
                        <th data-column-type="oper"
                            data-oper-btns="[{txt:'icon-down',title:'下载',condition:true,handler:'download'}]">下载
                        </th>
                    </tr>
                </table>
            </div>
        </div>
    </dd>
</dl>

#parse("/view/common/fileupload.vm")
