#set($cssUrls=[
    "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
    "${staticSiteUrl}/lib/select2/css/select2.min.css",
    "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
    "${staticSiteUrl}/mtime/loading/mtime.loading.css",
    "$!{staticSiteUrl}/css/wanda.css"])
#set($jsUrls=[
    "${staticSiteUrl}/lib/moment/moment-with-locales.js",
    "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
    "${staticSiteUrl}/lib/select2/js/select2.min.js",
    "${staticSiteUrl}/mtime/datepicker/datepicker.js",
    "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
    "${staticSiteUrl}/mtime/validator/mtime.validator.js",
    "${staticSiteUrl}/mtime/selectable/selectable.js",
    "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
    "${staticSiteUrl}/mtime/form/mtime.form.js",
    "${staticSiteUrl}/mtime/loading/mtime.loading.js",
    "${staticSiteUrl}/mtime/dialog/mtime.dialog.js",
    "/js/resource/list.js"])

<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    所属区域:
                    <select name="regionCode" id="region"
                            class="form-control">
                        <option value="-1">全部</option>
                    </select>
                </p>

                <p class="col-xs-12 col-sm-3 col-md-4">
                    影城:
                    <select name="cinemaCode" id="cinema" class="form-control">
                        <option value="-1">全部</option>
                    </select>
                </p>

                <p class="col-xs-12 col-sm-3 col-md-3">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                </p>
            </div>
        </form>
    </div>
</dl>
<dl class="cont-title-wrap">
    <dt class="clearfix">
        列表
    </dt>
    <div class="clearfix pt10 table-responsive">
        <table id="resourceTable"
               class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="regionName">区域</th>
                <th data-field-name="cinemaCode">影院内码</th>
                <th data-field-name="cinemaName">影城名称</th>
                <th data-field-name="marketingPointLeasableArea">营销点位面积(㎡)</th>
                <th data-field-name="outerAreaLeasableArea">外租区域面积(㎡)</th>
                <th data-field-name="fixedPointLeasableArea">固定点位面积(㎡)</th>
                <th data-field-name="advertisingPointLeasableQuantity">宣传点位个数(个)</th>
                <th data-column-type="oper"
                    data-oper-btns="[{txt:'icon-edit',title:'编辑',condition:true,handler:'edit'},{txt:'icon-check',title:'查看',condition:true,handler:'view'}]"
                    class="text-center">操作
                </th>
            </tr>
        </table>
    </div>
</dl>
<dl class="cont-title-wrap" style="display: none" id="recordDl">
    <dt class="clearfix">
        查看更新记录
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                <a href="javascript:;" class="w-btn" id="closeRecordBtn">
                    <i class="icon-x"></i>关闭
                </a>
            </p>
        </div>
    </div>
    <div class="clearfix pt10 table-responsive">
        <table id="resourceChangeRecordTable"
               class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="regionName">区域</th>
                <th data-field-name="cinemaName">影城名称</th>
                <th data-field-name="marketingPointLeasableArea">营销点位面积(㎡)</th>
                <th data-field-name="outerAreaLeasableArea">外租区域面积(㎡)</th>
                <th data-field-name="fixedPointLeasableArea">固定点位面积(㎡)</th>
                <th data-field-name="advertisingPointLeasableQuantity">宣传点位个数(个)</th>
                <th data-field-name="updateTimeStr">更新时间</th>
            </tr>
        </table>
    </div>
</dl>