<div class="modal fade" id="fileModal" tabindex="-1"
     aria-labelledby="fileModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 800px">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="fileModalLabel"></h4>
            </div>
            <div class="modal-body">
                <div class="clearfix row pl10 price">
                    <p class="col-xs-12 text-right col-sm-2 col-md-2">
                        版本名称:
                    </p>
                    <p class="col-xs-12 col-sm-7 col-md-7">
                        <input name="version" id="version" class="form-control">
                        </input>
                    </p>
                </div>
                <div class="clearfix row pl10">
                    <p class="col-xs-12 text-right col-sm-2 col-md-2">
                        选择文件:
                    </p>
                    <p class="col-xs-12 col-sm-7 col-md-7">
                        <input style="width:70%" id="exFile" type="file" name="exFile" data-min-file-count="1"
                               class="form-control input-md">
                    </p>
                </div>
                <div class="clearfix  row pl10 price">
                    <p class="col-xs-12 text-right col-sm-2 col-md-2">
                        生效日期:
                    </p>
                    <p class="col-xs-12 col-sm-7 col-md-7">
                        <input id="effectiveDate" type="text"
                               style="width:30%"/>
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="cont-list no-icon">
                    <a href="javascript:;" class="w-btn" data-dismiss="modal" data-target="#fileModal"><i
                            class="icon-disable" for-datagrid="load"></i>取消</a>
                    <a href="javascript:;" class="w-btn upload"><i
                            class="icon-updown" for-datagrid="load"></i>上传</a>
                </div>
            </div>
        </div>
        <div id="errDiv">
        </div>
    </div>
</div>

