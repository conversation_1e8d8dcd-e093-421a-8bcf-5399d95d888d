#set($cssUrls=[
    "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
    "${staticSiteUrl}/lib/select2/css/select2.min.css",
    "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
    "${staticSiteUrl}/mtime/loading/mtime.loading.css",
    "$!{staticSiteUrl}/css/wanda.css"])
#set($jsUrls=[
    "${staticSiteUrl}/lib/moment/moment-with-locales.js",
    "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
    "${staticSiteUrl}/lib/select2/js/select2.min.js",
    "${staticSiteUrl}/mtime/datepicker/datepicker.js",
    "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
    "${staticSiteUrl}/mtime/validator/mtime.validator.js",
    "${staticSiteUrl}/mtime/selectable/selectable.js",
    "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
    "${staticSiteUrl}/mtime/form/mtime.form.js",
    "${staticSiteUrl}/mtime/loading/mtime.loading.js",
    "${staticSiteUrl}/mtime/dialog/mtime.dialog.js",
    "/js/inventory/list.js"])

<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    所属区域:
                    <select name="regionCode" id="region"
                            class="form-control">
                        <option value="-1">全部</option>
                    </select>
                </p>

                <p class="col-xs-12 col-sm-3 col-md-3">
                    影城:
                    <select name="cinemaCode" id="cinema" class="form-control">
                        <option value="-1">全部</option>
                    </select>
                </p>

                <p class="col-xs-12 col-sm-4 col-md-4">
                    时&emsp;&emsp;间:
                    <input id="serviceStartTime" name="startDate" type="text"
                           data-max="#serviceEndTime" style="width: 28%" data-date-type="date"
                           data-init-element="false" role="datepicker"/>
                    至
                    <input id="serviceEndTime" name="endDate" type="text"
                           data-min="#serviceStartTime" style="width: 28%" data-date-type="date"
                           data-init-element="false" role="datepicker"/>
                </p>

                <p class="col-xs-12 col-sm-2 col-md-2">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                </p>
            </div>
        </form>
    </div>

</dl>
<dl class="cont-title-wrap">
    <dt class="clearfix">
        列表
    </dt>
    <dd>
        <div class="btn-group">
            <a href="javascript:;" class="w-btn" id="exportExcelBtn"><i class="icon-down"></i>导出</a>
        </div>
        <div class="clearfix pt10 table-responsive">
            <table id="inventoryTable"
                   class="table table-bordered table-striped table-hover table-condensed">
                <tr>
                    <th data-column-type="index">NO.</th>
                    <th data-field-name="regionName">区域</th>
                    <th data-field-name="cinemaCode">影院内码</th>
                    <th data-field-name="cinemaName">影院名称</th>
                    <th data-field-name="dateStr">日期</th>
                    <th data-field-name="marketingPointLeasableAreaStr">
                        <div class="text" style="visibility: visible;">
                            <p style="margin-bottom: 0"><span>营销点位（m²）</span></p>
                            <p style="margin-bottom: 0"><span>总量/剩余量</span></p>
                        </div>
                    </th>
                    <th data-field-name="outerAreaLeasableAreaStr">
                        <div class="text" style="visibility: visible;">
                            <p style="margin-bottom: 0"><span>外租区域（m²）</span></p>
                            <p style="margin-bottom: 0"><span>总量/剩余量</span></p>
                        </div>
                    </th>
                    <th data-field-name="fixedPointLeasableAreaStr">
                        <div class="text" style="visibility: visible;">
                            <p style="margin-bottom: 0"><span>固定点位（m²）</span></p>
                            <p style="margin-bottom: 0"><span>总量/剩余量</span></p>
                        </div>
                    </th>
                    <th data-field-name="advertisingPointLeasableQuantityStr">
                        <div class="text" style="visibility: visible;">
                            <p style="margin-bottom: 0"><span>宣传点位（个）</span></p>
                            <p style="margin-bottom: 0"><span>总量/剩余量</span></p>
                        </div>
                    </th>
                </tr>
            </table>
        </div>
    </dd>
</dl>
<dl class="cont-title-wrap" style="display: none" id="recordDl">
    <dt class="clearfix">
        库存变更记录
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                <a href="javascript:;" class="w-btn" id="closeRecordBtn">
                    <i class="icon-x"></i>关闭
                </a>
            </p>
        </div>
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-4 col-md-4 col-lg-4" id="cinemaP" style="padding-left: 100px;"></p>
            <p class="col-xs-12 col-sm-4 col-md-4 col-lg-4" id="dateP"></p>
            <p class="col-xs-12 col-sm-4 col-md-4 col-lg-4" id="businessP"></p>
        </div>
    </div>
    <div class="clearfix pt10 table-responsive">
        <table id="inventoryChangeRecordTable"
               class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="contractNo">单据编号</th>
                <th data-field-name="startDateStr">合同开始日期</th>
                <th data-field-name="endDateStr">合同结束日期</th>
                <th data-field-name="amount">数量</th>
                <th data-field-name="statusStr">占用状态</th>
            </tr>
        </table>
    </div>
</dl>