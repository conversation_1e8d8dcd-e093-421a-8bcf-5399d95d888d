#set($cssUrls=[
        "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
        "${staticSiteUrl}/lib/select2/css/select2.min.css",
        "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
        "${staticSiteUrl}/mtime/loading/mtime.loading.css",
        "$!{staticSiteUrl}/css/wanda.css"])
        #set($jsUrls=[
        "${staticSiteUrl}/lib/moment/moment-with-locales.js",
        "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
        "${staticSiteUrl}/lib/select2/js/select2.min.js",
        "${staticSiteUrl}/mtime/datepicker/datepicker.js",
        "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
        "${staticSiteUrl}/mtime/validator/mtime.validator.js",
        "${staticSiteUrl}/mtime/selectable/selectable.js",
        "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
        "${staticSiteUrl}/mtime/form/mtime.form.js",
        "${staticSiteUrl}/mtime/loading/mtime.loading.js",
        "${staticSiteUrl}/mtime/dialog/mtime.dialog.js",
        "/js/city/list.js"])

<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-4 col-md-4">
                    所属区域:
                    <select name="regionCode" id="region"
                            class="form-control"
                            role="selectable"
                            data-source="searchAllRegion"
                            data-auto="true"
                            data-tmpl-value="id"
                            data-tmpl-result="name"
                    >
                        <option value="0" selected="selected">全部</option>
                    </select>
                </p>
                <p class="col-xs-12 col-sm-4 col-md-4">
                    所属省/直辖市:
                    <select name="superiorCode" id="superior"
                            class="form-control"
                            data-source="searchAllProvince"
                            role="selectable"
                            data-auto="true"
                            data-tmpl-value="id"
                            data-tmpl-result="nameCN"
                    >
                        <option value="0" selected="selected">全部</option>
                    </select>
                </p>
                <p class="col-xs-12 col-sm-4 col-md-4">
                    所属城市:
                    <select name="cityCode" id="city"
                            class="form-control"
                            data-source="/pad/dict/city"
                            role="selectable"
                            data-auto="true"
                            data-parent="#superior"
                            data-parent-key="superior"
                            data-tmpl-value="code"
                            data-tmpl-result="name"
                    >
                        <option value="0">全部</option>
                    </select>
                </p>
                <p class="col-xs-12 col-sm-4 col-md-4">
                    地区编码:
                    <input name="districtCode" id="code" class="form-control">
                    </input>
                </p>
                <p class="col-xs-12 col-sm-4 col-md-4">
                    &emsp;&emsp;地区名称:
                    <input name="districtName" id="districtName" class="form-control">
                    </input>
                </p>
                <p class="col-xs-12 col-sm-4 col-md-4">
                    地区级别:
                    <select name="districtLevel" id="districtLevel" class="form-control">
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                </p>
            </div>
        </form>
    </div>
</dl>
<dl class="cont-title-wrap">
<dt class="clearfix">
    列表
</dt>
<div class="clearfix pt10 table-responsive">
    <table id="cityDistrictTable"
           class="table table-bordered table-striped table-hover table-condensed">
        <tr>
            <th data-column-type="index">NO.</th>
            <th data-field-name="code">编码</th>
            <th data-field-name="districtName">地区名称</th>
            <th data-field-name="cityName">所属城市</th>
            <th data-field-name="regionName">所属区域</th>
            <th data-field-name="superiorName">所属省/直辖市</th>
            <th data-field-name="level">级别</th>
            <th data-column-type="oper"
                data-oper-btns="[{txt:'icon-edit',title:'编辑',condition:true,handler:'edit'}]" class="text-center">操作
            </th>
        </tr>
    </table>
</div>
</dl>

<div class="modal fade" id="modify-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
<div class="modal-dialog" style="width: 850px;">
    <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h4 class="modal-title" id="myModalLabel">修改城市级别</h4>
        </div>
        <div class="modal-body">
            <ul class="nav nav-tabs" role="tablist">

            </ul>
            <div id="myTabContent" class="tab-content">
                <!-- 基本信息 begin -->
                <div class="tab-pane fade in active" id="jbxx">
                    <div style="min-width: 300px;display:block;" class="movie-hallbox">
                        <table width="100%" border="0" style=" background-color:#fbfbfb">
                            <tbody>
                            <tr style="display: none">
                                <td align="right" nowrap="nowrap">城市地区编码</td>
                                <td align="left" nowrap="nowrap">
                                    <input type="text" name="code" disabled class="txtinput_wid80">
                                </td>
                            </tr>
                            <tr>
                                <td align="right" nowrap="nowrap" style="width: 38%">级别:</td>
                                <td align="left" nowrap="nowrap">
                                    <select name="cityDistrictLevel" class="form-control">
                                    </select>
                                    <span id="tips"><font color="red">必须填写</font></span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!--   底部按钮 begin  -->
            <div class="modal-footer">
                <div class="cont-list">
                    <a href="javascript:;" class="w-btn save-btn" id="one-city-update"><i
                            class="icon-save"></i>保存</a>
                    <a href="javascript:;" class="w-btn cle-btn" data-dismiss="modal"><i class="icon-x"></i> 关闭</a>
                </div>
            </div>
            <!--   底部按钮 end  -->
        </div>
    </div>
    <!-- /.modal-content -->
</div>
</div>
