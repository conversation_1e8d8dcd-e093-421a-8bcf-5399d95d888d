#set($cssUrls=[
    "${staticSiteUrl}/lib/datetimepicker/css/bootstrap-datetimepicker.css",
    "${staticSiteUrl}/lib/select2/css/select2.min.css",
    "${staticSiteUrl}/mtime/datagrid/css/datagrid.css",
    "${staticSiteUrl}/mtime/loading/mtime.loading.css",
    "${staticSiteUrl}/lib/fileinput/css/fileinput.min.css",
    "$!{staticSiteUrl}/css/wanda.css"])
#set($jsUrls=[
    "${staticSiteUrl}/lib/moment/moment-with-locales.js",
    "${staticSiteUrl}/lib/datetimepicker/js/bootstrap-datetimepicker.js",
    "${staticSiteUrl}/lib/select2/js/select2.min.js",
    "${staticSiteUrl}/mtime/datepicker/datepicker.js",
    "${staticSiteUrl}/mtime/datagrid/js/datagrid.js",
    "${staticSiteUrl}/mtime/validator/mtime.validator.js",
    "${staticSiteUrl}/mtime/selectable/selectable.js",
    "${staticSiteUrl}/lib/select2/js/i18n/zh-CN.js",
    "${staticSiteUrl}/mtime/form/mtime.form.js",
    "${staticSiteUrl}/mtime/loading/mtime.loading.js",
    "${staticSiteUrl}/lib/fileinput/js/fileinput.js",
    "${staticSiteUrl}/mtime/upload/upload.js",
    "/js/common/fileupload.js",
    "/js/price/common.js",
    "/js/price/advertisingpoint/list.js",
    "/js/common/select/cinemaLevel.js",
    "/js/common/select/cityLevel.js"])

<dl class="cont-title-wrap">
    <dt class="clearfix">
        查询
    </dt>
    <div class="clearfix pt10">
        <form id="searchForm">
            <div class="clearfix  row pl10">
                <p class="col-xs-12 col-sm-3 col-md-3">
                    地区级别:
                    <select name="cityLevel" id="cityLevel"
                            class="form-control">
                    </select>
                </p>
                <p class="col-xs-12 col-sm-3 col-md-3">
                    影城级别:
                    <select name="cinemaLevel" id="cinemaLevel"
                            class="form-control">
                    </select>
                </p>

                <p class="col-xs-12 col-sm-3 col-md-3">
                    <a href="javascript:;" class="w-btn" id="queryBtn">
                        <i class="icon-check" for-datagrid="load"></i>查询
                    </a>
                </p>
            </div>
        </form>
    </div>
    <dt class="clearfix">
        列表
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-12 col-md-12">
                <a href="javascript:;" class="w-btn" id="importBtn">
                    <i class="icon-updown" for-datagrid="load"></i>导入刊例
                </a>
                <a href="#importTable" class="w-btn" id="importLogBtn">
                    <i class="icon-notes" for-datagrid="load"></i>刊例导入记录
                </a>
                <a href="javascript:;" id="downloadDemo">
                    下载模版
                </a>
            </p>
        </div>
    </div>
    <div class="clearfix pt10 table-responsive">
        <table id="priceTable"
               class="table table-bordered table-striped table-hover table-condensed">
            <tr>
                <th data-column-type="index">NO.</th>
                <th data-field-name="cityLevelView">地区级别</th>
                <th data-field-name="cinemaLevelView">影城级别</th>
                <th data-field-name="minTotalPrice">基础价(元/月)</th>
                <th data-field-name="minArea">基础面积(平米)</th>
                <th data-field-name="expandedUnitPrice">续价(元/平米/月)</th>
                <th data-column-type="oper"
                    data-oper-btns="[{txt:'icon-check',title:'查看更新记录',condition:true,handler:'priceRecord'}]">操作
                </th>
            </tr>
        </table>
    </div>
</dl>
<dl id="priceRecordDiv" class="cont-title-wrap hidden">
    <dt class="clearfix">
        <i class="close-file-import-log icon-x pull-right" style="cursor: pointer;"></i>
        查看价格记录
    </dt>
    <div class="clearfix pt10">
        <div class="clearfix  row pl10">
            <p class="col-xs-12 col-sm-3 col-md-3">
                <a href="javascript:;" class="w-btn close-file-import-log">
                    <i class="icon-check" for-datagrid="load"></i>关闭
                </a>
            </p>
        </div>
    </div>
    <dd>
        <div class="cont-table-wrap clearfix">
            <div class="table-responsive">
                <table id="priceRecordTable"
                       class="table table-bordered table-striped table-hover table-condensed">
                    <tr>
                        <th data-column-type="index">NO.</th>
                        <th data-field-name="cityLevel">地区级别</th>
                        <th data-field-name="cinemaLevel">影城级别</th>
                        <th data-field-name="minTotalPrice">基础价(元/月)</th>
                        <th data-field-name="minArea">基础面积(平米)</th>
                        <th data-field-name="expandedUnitPrice">续价(元/平米/月)</th>
                        <th data-field-name="effectiveDate" data-field-type="Date">生效日期</th>
                        <th data-field-name="updateTime" data-field-type="Date">更新时间</th>
                        <th data-field-name="status">状态</th>
                    </tr>
                </table>
            </div>
        </div>
    </dd>
</dl>

#parse("/view/price/importrecord.vm")
#parse("/view/common/fileupload.vm")
