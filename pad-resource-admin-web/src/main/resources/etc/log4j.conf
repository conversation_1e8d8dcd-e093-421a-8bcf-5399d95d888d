<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
    <appender name="File" class="mtime.lark.util.log.DailyFileAppender">
        <!-- 设置File参数：日志输出文件名 -->
        <param name="File" value="default.log"/>
        <!-- 设置日志存储目录, 如果不配置会从 global.conf 中加载 log.path 的设置 -->
        <param name="Dir" value="/home/<USER>/logs/${service.name}"/>
        <!-- 日志是否通过日期分割 -->
        <param name="SplitByDateDir" value="${log.date.split}"/>
        <!-- 设置输出文件项目和格式 -->
        <layout class="mtime.lark.util.log.CtxPatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss} %-5p [%A] (%c:%L) - %m%n"/>
        </layout>
    </appender>

    <appender name="Console" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss\} %-5p (%c:%L) - %m%n" />
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="AcceptOnMatch" value="true" />
        </filter>
    </appender>

    <!-- rpc的文本日志配置 -->
    <appender name="SOAFile" class="mtime.lark.util.log.DailyFileAppender">
        <param name="File" value="soa.log"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%m%n"/>
        </layout>
    </appender>

    <logger name="mtime.lark.net.rpc.client.$stats" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="SOAFile"/>
    </logger>

    <logger name="mtime.lark.db.jsd.debug" additivity="false">
        <level value="DEBUG" />
        <appender-ref ref="Console" />
    </logger>

    <root>
        <priority value="INFO"/>
        <appender-ref ref="File"/>
        <appender-ref ref="Console"/>
    </root>
</log4j:configuration>
