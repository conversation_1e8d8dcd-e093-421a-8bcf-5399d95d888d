# é»è®¤éç½®
app.platform=cmc
lark.global.profile=cmc-qas
app.auth.enabled=true
#
# çå¬ç«¯å£
server.port=8124
service.name=cmc.pad.resource.admin.portal
#globaléç½®æä»¶æå®
cmc.global.env=cmc
#
# Spring ç»æ
#
# èªå®ä¹éç½®å±æ§ å¼å§
#
cms.web.debug=true
# å¯ç¨æå¡åç°
cmc.rpc.discovery.enabled=true
# åå°éæèµæºå°å
cmc.admin.static.url=http://static.qas.cmc.com
#ç»ä¸è®¤è¯ç»å½å°å
cmc.passport.url=http://passport-qas.mx.com
#portalå°å
cmc.admin.portal.url=http://portal.qas.cmc.com
#è§åå¼æwebç«ç¹
ticket.rule.web.url=http://tkr.qas.cmc.com
#å®¡æ¹æµç³»ç»URLå°å
cmc.approval.url=http://approval.qas.cmc.com
#æ¥å¿æ¯å¦éè¿æ¥æåå²
log.date.split=true
#å¡ç±»åå®¡æ¹æµåæ®ç±»åid--é¢çº¿
cmc.pad.approval.cardManage=66074
#å¶å¡å®¡æ¹æµåæ®ç±»åid
cmc.pad.approval.makeCardDataTypeId=64737
#ææå¡å®¡æ¹æµåæ®ç±»åid
cmc.pad.approval.authorizedCardDataTypeId=64739
#æ¹éåå¡è´å²å å®¡æ¹æµç±»åID
cmc.approval.negative.recharge.order=118547
#æ¹éåå¡ï¼å®¡æ¹æµç±»åID
cmc.approval.sellorder=66080
#æ¯åºéè´­è®¾ç½®
everyPurchaseTicketNum=4
#æ¯æ¥éè´­è®¾ç½®
dayPurchaseTicketNum=6
#å¡ç±»ååå·å¨
cmc.pad.index.id=804
#ESéç½®
es.cluster.router=cmc-qa-es
#redisèç¹éç½®
#redisèç¹éç½®
cmc.redis.host.node1=**************
cmc.redis.port.node1=26309
cmc.redis.host.node2=**************
cmc.redis.port.node2=26309
cmc.redis.host.node3=**************
cmc.redis.port.node3=26309
cmc.redis.master.name=cmc-qa-6309
cmc.redis.password=9bA+7KctA2628w=Tb
#springbootä¸ä¼ æä»¶å¤§å°éå¶
multipart.maxFileSize=100Mb
multipart.maxRequestSize=1000Mb
