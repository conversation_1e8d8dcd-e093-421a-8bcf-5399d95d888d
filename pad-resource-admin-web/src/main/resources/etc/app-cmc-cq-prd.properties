# é»è®¤éç½®
#
# Spring å¼å§
#
# çå¬ç«¯å£
server.port=8124
service.name=cmc.pad.resource.admin.portal
#globaléç½®æä»¶æå®
cmc.global.env=cmc
#
# Spring ç»æ
#
# èªå®ä¹éç½®å±æ§ å¼å§
#
cms.web.debug=true
# å¯ç¨æå¡åç°
cmc.rpc.discovery.enabled=true
#å®¡æ¹æµç³»ç»URLå°å
cmc.approval.url=http://approval-prd-cmc.wandafilm.com
#è§åå¼æwebç«ç¹
ticket.rule.web.url=http://tkr-prd-cmc.wandafilm.com
#APP_NAME
#æ¥å¿æ¯å¦éè¿æ¥æåå²
log.date.split=true
#redisèç¹éç½®
cmc.redis.host.node1=hcmc-sentinel00.inc-mtime.com
cmc.redis.port.node1=27001
cmc.redis.host.node2=hcmc-sentinel01.inc-mtime.com
cmc.redis.port.node2=27001
cmc.redis.host.node3=hcmc-sentinel02.inc-mtime.com
cmc.redis.port.node3=27001
cmc.redis.host.node4=hcmc-sentinel03.inc-mtime.com
cmc.redis.port.node4=27001
cmc.redis.host.node5=hcmc-sentinel04.inc-mtime.com
cmc.redis.port.node5=27001
cmc.redis.master.name=redis-7028
cmc.redis.password=NTFkYjMyY2RjZmUw
#traceréç½®
tracer.enabled=true
tracer.payload.enabled=true
tracer.compression.enabled=true
#æ§å¶å°æ¥å¿éç½®
console.levelMin=off
console.levelMax=off
# mysqlæ°æ®åºéç½®
mx.mysql.port=8806
mx.mysql.host=hcmc-clu10-mysql-master.inc-mtime.com
mx.mysql.db=cmc_pad_resource
mx.mysql.pass.wr=es8xDnP52VyCJ4kcEuoq
mx.mysql.user.wr=cmc_pad_resource_rw