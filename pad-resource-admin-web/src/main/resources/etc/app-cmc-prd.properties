# é»è®¤éç½®
#
# Spring å¼å§
#
# çå¬ç«¯å£
server.port=8124
service.name=cmc.pad.resource.admin.portal
#globaléç½®æä»¶æå®
cmc.global.env=cmc
#
# Spring ç»æ
#
# èªå®ä¹éç½®å±æ§ å¼å§
#
cms.web.debug=true
# å¯ç¨æå¡åç°
cmc.rpc.discovery.enabled=true
#å®¡æ¹æµç³»ç»URLå°å
cmc.approval.url=http://approval-prd-cmc.wandafilm.com
#è§åå¼æwebç«ç¹
ticket.rule.web.url=http://tkr-prd-cmc.wandafilm.com
#APP_NAME
#æ¥å¿æ¯å¦éè¿æ¥æåå²
log.date.split=true
#å¡ç±»åå®¡æ¹æµåæ®ç±»åid--é¢çº¿
cmc.pad.approval.cardManage=66074
#å¶å¡å®¡æ¹æµåæ®ç±»åid
cmc.pad.approval.makeCardDataTypeId=64737
#ææå¡å®¡æ¹æµåæ®ç±»åid
cmc.pad.approval.authorizedCardDataTypeId=64739
#æ¹éåå¡ï¼å®¡æ¹æµç±»åID
cmc.approval.sellorder=66080
#æ¹éåå¡è´å²å å®¡æ¹æµç±»åID
cmc.approval.negative.recharge.order=118547
#æ¯åºéè´­è®¾ç½®
everyPurchaseTicketNum=4
#æ¯æ¥éè´­è®¾ç½®
dayPurchaseTicketNum=6
#å¡ç±»ååå·å¨
cmc.pad.index.id=804
#ESéç½®
es.cluster.router=cmc-es-voucher-card
#redisèç¹éç½®
cmc.redis.host.node1=************
cmc.redis.port.node1=27001
cmc.redis.host.node2=************
cmc.redis.port.node2=27001
cmc.redis.host.node3=*************
cmc.redis.port.node3=27001
cmc.redis.master.name=redis_7028
cmc.redis.password=NTFkYjMyY2RjZmUw
#springbootä¸ä¼ æä»¶å¤§å°éå¶
multipart.maxFileSize=100Mb
multipart.maxRequestSize=1000Mb
#traceréç½®
tracer.enabled=true
tracer.payload.enabled=true
tracer.compression.enabled=true
#æ§å¶å°æ¥å¿éç½®
console.levelMin=off
console.levelMax=off
# mysqlæ°æ®åºéç½®
mx.mysql.port=8806
mx.mysql.host=cmc-clu16-mysql-master.inc-mtime.com
mx.mysql.db=cmc_pad_resource
mx.mysql.pass.wr=ZfRUIYiH8rmT04KE7QSs
mx.mysql.user.wr=cmc_pad_resource_rw