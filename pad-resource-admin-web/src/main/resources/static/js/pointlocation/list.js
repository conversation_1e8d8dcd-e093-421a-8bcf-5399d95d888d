var listModule = (function() {
    var
        $largeWard              = $("#largeWard"),
        $largeWardSelect        = $("#largeWardSelect"),
        $region                 = $("#region"),
        $cinema                 = $("#cinema"),
        $searchForm             = $("#searchForm"),
        $queryBtn               = $searchForm.find('a[name="queryBtn"]'),
        $listTable              = $("#listTable"),
        $addResourceBtn         = $("#addResourceBtn"),
        $importResourceBtn      = $("#importResourceBtn"),
        $downloadTemplateBtn    = $("#downloadTemplateBtn"),
        $importResourceListBtn  = $("#importResourceListBtn"),
        $exportResourceBtn      = $("#exportResourceBtn");

    function init($container) {
        $largeWardSelect.multiselectable({
            source: "/pad/resource/common/getLargeWard",
            type: "post",   // ajax请求方式
            dataType: "json",
            async: false,
            valueField: "code",
            displayField: "name",
            auto: true, // 是否自动加载服务器端数据
            pageSize: 1000
        });
        $largeWardSelect.find('option[value="'+$largeWard.val()+'"]').attr('selected', true);
        $largeWardSelect.multiselect('refresh');

        $region.selectable({
            source: "/pad/resource/city/searchAllRegionByRank",
            tmplValue: "id",
            tmplResult: "name",
            parent: "#largeWardSelect",
            parentField: "parentId",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $cinema.selectable({
            source: "/pad/resource/resource/searchAllCinemaByRank",
            tmplValue: "code",
            tmplResult: "name",
            parent: "#region",
            parentField: "parentId",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $container.on('loadList', function(e) {
            reload();
        });
        $queryBtn.on('click', function() {
            $container.trigger('clear');
            $listTable.datagrid();
        }).click();
        $addResourceBtn.on('click', function() {
            $container.trigger('showEditModal', {type:'add'});
        });
        $downloadTemplateBtn.on('click', function() {
            window.location.href = "/files/point_location_template.xlsx";
        });
        $importResourceListBtn.on('click', function() {
            $container.trigger('showImportList');
        });
        $exportResourceBtn.on('click', function() {
            var fields = $searchForm.serializeArray();
            var obj = {}; //声明一个对象
            var largeWardSelect = [];
            $.each(fields, function(index, field) {
                if (field.name == 'largeWardSelect') {
                    largeWardSelect.push(field.value);
                } else {
                    obj[field.name] = field.value;
                }
            });
            obj['largeWardSelect'] = largeWardSelect;
            commonExportModule.exportFileAttachParam($('#exportFileForm'), null, '/pad/resource/exportExcelAttachParam', obj, '/pad/resource/exportStatus', '/pad/resource/download');
        });
        $listTable
          .dgOper("edit", function (e, data, btn) {
            $container.trigger('showEditModal', {type:'edit',id:data.id});
        });
        return true;
    }

    function reload() {
        $listTable.datagrid("reload");
    }

    return {
        init:init
    }
})();
var editModule = (function() {
    var $editDiv                = $('#editDiv'),
        $save                   = $editDiv.find('a[name="save"]'),
        $cancel                 = $editDiv.find('a[name="cancel"]'),
        $id                     = $editDiv.find('input[name="id"]'),
        $originSellArea         = $editDiv.find('input[name="originSellArea"]'),
        $cinemaInnerCodeSelectDiv = $('#cinemaInnerCodeSelectDiv'),
        $cinemaInnerCodeSelect  = $editDiv.find('select[name="cinemaInnerCode"]'),
        $cinemaInnerCodeInput   = $editDiv.find('input[name="cinemaInnerCode"]'),
        $cinemaName             = $editDiv.find('input[name="cinemaName"]'),
        $businessTypeCode       = $editDiv.find('input[name="businessTypeCode"]'),
        $resourceOwnershipCode  = $editDiv.find('input[name="resourceOwnershipCode"]'),
        $code                   = $editDiv.find('input[name="code"]'),
        $locationDesc           = $editDiv.find('input[name="locationDesc"]'),
        $planUse                = $editDiv.find('input[name="planUse"]'),
        $landingMode            = $editDiv.find('input[name="landingMode"]'),
        $sellArea               = $editDiv.find('input[name="sellArea"]'),
        $sellAreaAdjustDateDiv  = $editDiv.find('div[name="sellAreaAdjustDateDiv"]'),
        $sellAreaAdjustDate     = $editDiv.find('input[name="sellAreaAdjustDate"]'),
        $availablePeriod        = $editDiv.find('input[name="availablePeriod"]'),
        $floor                  = $editDiv.find('input[name="floor"]'),
        $floorHeight            = $editDiv.find('input[name="floorHeight"]'),
        $decoration             = $editDiv.find('input[name="decoration"]')
    ;

    function init($container) {
        $sellAreaAdjustDate.datepicker({format: 'YYYY-MM-DD'});
        $cinemaInnerCodeSelect.selectable({
            source: "/pad/resource/resource/searchAllCinemaByRank?parentId=-1&isEditPage=true",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });
        $container.on('showEditModal', function(e, param) {
            clear();
            $cinemaInnerCodeSelectDiv.hide();
            $cinemaName.hide();
            $sellAreaAdjustDateDiv.hide();
            $editDiv.modal('show');
            if (param.type === 'edit') {
                detail(param.id);
                $cinemaName.show();
                $sellAreaAdjustDateDiv.show();
                $code.prop("disabled", true);
                $sellAreaAdjustDate.val('').prop("disabled", true);
            }
            if (param.type === 'add') {
                $id.val(0);
                $originSellArea.val('');
                $.each($businessTypeCode, function(i) {
                    $(this).prop("checked", false);
                });
                $.each($resourceOwnershipCode, function(i) {
                    $(this).prop("checked", false);
                });
                $cinemaInnerCodeSelectDiv.show();
                $code.prop("disabled", false);
                $editDiv.find('select').val("");
            }
        });
        $sellArea.on('keyup', function(e) {
            if ($id.val() != 0) {
                console.info('%s_%s_%s', $originSellArea.val() * 1, $sellArea.val() * 1, sellAreaIsDiff($originSellArea, $sellArea));
                if (sellAreaIsDiff($originSellArea, $sellArea))
                    $sellAreaAdjustDate.val('').prop("disabled", false);
                else
                    $sellAreaAdjustDate.val('').prop("disabled", true);
            }
        });
        $save.on('click', function() {
            try {
                var param = buildSaveParam();
                console.info("点位保存参数:%o", param);
                checkSave(param);
                cmcAjaxUtil.postJson('/pad/resource/point_location/save', param, '保存失败, {errorMsg}', function(result) {
                    $cancel.click();
                    $container.trigger('loadList');
                });
            } catch (e) {
                $alert(e.message);
            }
        });
        $cancel.on('click', function() {
            $editDiv.modal('hide');
        });
    }
    function buildSaveParam() {
        if ($id.val() == 0) {
            var code = $cinemaInnerCodeSelect.find("option:selected").val();
            $cinemaInnerCodeInput.val(code);
        }
        return {
            id:$id.val(),
            cinemaInnerCode:$cinemaInnerCodeInput.val(),
            businessTypeCode:$businessTypeCode.filter(':checked').attr('val'),
            resourceOwnershipCode:$resourceOwnershipCode.filter(':checked').attr('val'),
            code:$code.val(),
            locationDesc:$locationDesc.val(),
            planUse:$planUse.val(),
            landingMode:$landingMode.val(),
            sellArea:$sellArea.val(),
            sellAreaAdjustDate:$sellAreaAdjustDate.val(),
            availablePeriod:$availablePeriod.val(),
            floor:$floor.val(),
            floorHeight:$floorHeight.val(),
            isSplittable:$editDiv.find('select[name="isSplittable"]').find("option:selected").val(),
            waterSupply:$editDiv.find('select[name="waterSupply"]').find("option:selected").val(),
            powerSupply:$editDiv.find('select[name="powerSupply"]').find("option:selected").val(),
            fireFacilities:$editDiv.find('select[name="fireFacilities"]').find("option:selected").val(),
            decoration:$decoration.val()
        }
    }
    function checkSave(param) {
        if ($.trim(param.cinemaInnerCode) == '')
            throw new CmcError('请选择影城');
        if (typeof param.businessTypeCode === 'undefined')
            throw new CmcError('请选择资源类型');
        if (typeof param.resourceOwnershipCode === 'undefined')
            throw new CmcError('请选择资源归属!');
        if ($.trim(param.code) == '')
            throw new CmcError('资源编码不能为空!');
        if ($.trim(param.sellArea) == '')
            throw new CmcError('可售面积不能为空!');
        if (!$.cmcValidateUtil.checkPrice(param.sellArea))
            throw new CmcError('可售面积只能是数字且如果包含小数仅支持两位小数!');
        if ($.trim(param.sellAreaAdjustDate) == '' && $id.val() != 0 && sellAreaIsDiff($originSellArea, $sellArea))
            throw new CmcError('面积调整日期不能为空!');
        if (param.floorHeight != '' && !$.cmcValidateUtil.checkPrice(param.floorHeight))
            throw new CmcError('层高只能是数字且如果包含小数仅支持两位小数!');
    }
    function sellAreaIsDiff($orgSellArea, $newSellArea) {
        return ($originSellArea.val() * 1) != ($sellArea.val() * 1);
    }
    function detail(id) {
        cmcAjaxUtil.get('/pad/resource/point_location/detail?id=' + id, {}, '获取详情错误', function(detail) {
            $id.val(detail.id);
            $cinemaInnerCodeInput.val(detail.cinemaInnerCode);
            $cinemaName.val(detail.cinemaName);
            $.each($businessTypeCode, function(i) {
                if ($(this).attr('val') === detail.businessTypeCode) $(this).prop("checked", true);
            });
            $.each($resourceOwnershipCode, function(i) {
                if ($(this).attr('val') === detail.resourceOwnershipCode) $(this).prop("checked", true);
            });
            $code.val(detail.code);
            $locationDesc.val(detail.locationDesc);
            $planUse.val(detail.planUse);
            $landingMode.val(detail.landingMode);
            $sellArea.val(detail.sellArea);
            $originSellArea.val(detail.sellArea);
            $availablePeriod.val(detail.availablePeriod);
            $floor.val(detail.floor);
            $floorHeight.val(detail.floorHeight);
            $editDiv.find('select[name="isSplittable"]').val(detail.isSplittable);
            $editDiv.find('select[name="waterSupply"]').val(detail.waterSupply);
            $editDiv.find('select[name="powerSupply"]').val(detail.powerSupply);
            $editDiv.find('select[name="fireFacilities"]').val(detail.fireFacilities);
            $decoration.val(detail.decoration);
        });
    }
    function clear() {
        $cinemaInnerCodeSelect.val('').trigger('change');
        $editDiv.find('input').val('');
    }
    return {
        init:init
    }
})();

var importResourceListModule = (function() {
    var
        $importListDiv  = $("#importListDiv"),
        $importList     = $("#importList"),
        $closeRecordBtn = $("#closeRecordBtn")
        $refreshBtn     = $('#refreshBtn')
        ;

    function init($container) {
        $container.on('clear.importList', function() {
            $importListDiv.hide();
        });
        $container.on('showImportList', function() {
            $.cmcUtil.toLocation($importListDiv.show());
            $importList.datagrid({
                source: "downList",
                auto: true
            });
        });
        $importList.dgOper("download", function (event, data, btn) {
            var fileName = encodeURI(data.fileName);
            window.open("download?fileId=" + data.fileId + "&filename=" + fileName);
        });
        $closeRecordBtn.on('click', function() {
            $container.trigger('clear.importList');
            $.cmcUtil.toLocation($container);
        });
        $refreshBtn.on('click', function() {
            $importList.datagrid('reload');
        });
    }
    return {
        init:init
    }
})();
var exportResourceModule = (function() {

})();
$(function() {
    var $container = $('#containerDiv');
    listModule.init($container);
    editModule.init($container);
    importResourceListModule.init($container);
    //初始化导入功能
    new FileUploadClient().init({
        url: "file/import",
        title: "导入资源信息",
        tip: "请使用系统模板导入, 在<<资源导入记录>>中查看导入结果",
        closeDiv: function () {
            $("#fileImportDiv .close-file-import-log").trigger("click");
        }
    }, $container);
});

function validateFee(obj){
    obj.value = obj.value.replace(/[^\d.]/g,""); //清除"数字"和"."以外的字符
    obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字而不是.
    obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
    obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
    obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3'); //只能输入两个小数
}