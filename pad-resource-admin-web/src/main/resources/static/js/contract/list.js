$(function() {
    var
        $largeWard              = $("#largeWard"),
        $largeWardSelect        = $("#largeWardSelect"),
        $region                 = $("#region"),
        $searchForm             = $("#searchForm"),
        $contractState          = $searchForm.find('select[name="contractState"]'),
        $delayState             = $searchForm.find('select[name="delayState"]'),
        $queryBtn               = $searchForm.find('a[name="queryBtn"]'),
        $cleanBtn               = $searchForm.find('a[name="cleanBtn"]'),
        $listTable              = $("#listTable");

        $largeWardSelect.multiselectable({
            source: "/pad/resource/common/getLargeWard",
            type: "post",   // ajax请求方式
            dataType: "json",
            async: false,
            valueField: "code",
            displayField: "name",
            auto: true, // 是否自动加载服务器端数据
            pageSize: 1000
        });
        $largeWardSelect.find('option[value="'+$largeWard.val()+'"]').attr('selected', true);
        $largeWardSelect.multiselect('refresh');

        $region.selectable({
            source: "/pad/resource/city/searchAllRegionByRank",
            tmplValue: "id",
            tmplResult: "name",
            parent: "#largeWardSelect",
            parentField: "parentId",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });
        $listTable
            .dgFieldTd("overdueState", function(value, extra, data) {
                var color = data.overdueState == 2 ? 'red' : '';
                return '<font color="' + color + '">' + OverdueState[data.overdueState] + '</font>';
            }).dgFieldTd("operatorName", function(value, extra, data) {
                return data.operatorName + (data.operatorWanXin == '' || data.operatorWanXin == null  ? '' : '(' + data.operatorWanXin + ')');
            });
        $queryBtn.on('click', function() {
            $listTable.datagrid();
        }).click();
        $cleanBtn.on('click', function() {
            $searchForm.find('input').val('');
            $largeWardSelect.val('').multiselect('refresh').trigger("change");
            $contractState.val(1);
            $delayState.val(2);
        });
});