$(function () {


    //新增文件上传
    $('#exFile').on('fileuploaded', function (event, data, previewId, index) {
        var response = data.response;
        $("#fileModal").modal("hide");
        if (response.state) {
            $alert("导入成功！", "导入成功");
            $("#resourceTable").datagrid("load");
            return true;
        } else {
            $alert(response.msg, "导入失败");
            return true;
        }

    });

    //上传按钮
    $("#fileModal").find(".upload").on("click", function () {
        if($("#fileYear").val()==0){
            $alert("请选择预算年份");
            return;
        }
        if (!$("#exFile").val()) {
            $alert("请选择文件");
            return;
        }
        $("#exFile").fileinput("upload");
        $("#fileModal").modal("hide");
    });

});
