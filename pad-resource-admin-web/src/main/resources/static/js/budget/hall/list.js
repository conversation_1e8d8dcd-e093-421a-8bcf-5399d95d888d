var controller = {

    init: function () {
        $("#resourceTable").datagrid({
            source: "list_hall",
            auto: true,
            searchForm: "#searchForm"
        });

        $("#resourceTable").dgOper("view", function (event, data, btn) {
            $("#recordUpdate").show();
            $("#resourceChangeRecordTable").datagrid({
                source: "list_hall_record",
                auto: true,
                data: {
                    "year": data.year,
                    "regionCode": data.regionCode,
                    "cinemaInnerCode": data.cinemaInnerCode,
                    "resourceType": data.resourceType
                },
                pagerOpts: false
            });
        })




        $("#importTable").dgOper("download", function (event, data, btn) {
            var fileName = encodeURI(data.fileName);
            window.open("download?fileId=" + data.fileId + "&filename=" + fileName);
        })

        $("#budgetImportTemplate").click(function () {
            window.location.href = "/files/template_hall.xlsx";
        });
        $("#btnReset").click(function () {
            $("#cinemaInnerCode").selectable("data", -1);
            $("#regionCode").selectable("data", -1);
            $("#resourceType").find("option").eq(0).prop("selected", true);
            $("#year").find("option").eq(0).prop("selected", true);

        });

        $("#budgetImport").click(function () {
            $("#category").val("8");
            $("#fileModal").modal("show");
            $("#fileYear").find("option").eq(0).prop("selected", true);
            $(".modal-backdrop").css("z-index", 1009);
            $("#fileModal").css("z-index", 1010);
            if ($(".fileinput-remove-button").length > 0) {
                $(".fileinput-remove-button").click();
            }
        });
        $("#btnEdit").click(function () {
            let id = $("#editId").val();
            let budgetMonth1 = $("#month1").val();
            let budgetMonth2 = $("#month2").val();
            let budgetMonth3 = $("#month3").val();
            let budgetMonth4 = $("#month4").val();
            let budgetMonth5 = $("#month5").val();
            let budgetMonth6 = $("#month6").val();
            let budgetMonth7 = $("#month7").val();
            let budgetMonth8 = $("#month8").val();
            let budgetMonth9 = $("#month9").val();
            let budgetMonth10 = $("#month10").val();
            let budgetMonth11 = $("#month11").val();
            let budgetMonth12 = $("#month12").val();
            $.ajax({
                url: "modify_hall",
                data: {
                    "id": id,
                    "month1": budgetMonth1,
                    "month2": budgetMonth2,
                    "month3": budgetMonth3,
                    "month4": budgetMonth4,
                    "month5": budgetMonth5,
                    "month6": budgetMonth6,
                    "month7": budgetMonth7,
                    "month8": budgetMonth8,
                    "month9": budgetMonth9,
                    "month10": budgetMonth10,
                    "month11": budgetMonth11,
                    "month12": budgetMonth12,
                },
                type: "GET",
                dataType: "json",
                success: function (result) {
                    if (result.state) {
                        $alert("修改成功！", "提示");
                        $("#resourceTable").datagrid("load");
                        $("#editModal").modal("hide");
                    }
                },
                error: function () {
                    $alert("修改失败！", "提示");
                }
            });
        });

        $("#closeRecordUpdateBtn").click(function () {
            $("#recordUpdate").hide();
        });
        $("#closeRecordImportBtn").click(function () {
            $("#recordImport").hide();
        });
        $("#budgetImportLog").click(function () {
            $("#recordImport").show();
            $("#importTable").datagrid({
                source: "file/list",
                auto: true
            });
        });
        $("#regionCode").selectable({
            source: "/pad/resource/city/searchAllRegion",
            tmplValue: "id",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#cinemaInnerCode").selectable({
            source: "/pad/resource/resource/searchAllCinema",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#regionCode").on('change', function () {
            var region = $("#regionCode").val();
            $.ajax({
                url: "/pad/resource/resource/searchAllCinema",
                data: {
                    regionCode: region
                },
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data) {
                        $("#cinemaInnerCode").empty();
                        var all = "<option auto-create='true' value='-1'>全部</option>";
                        $("#cinemaInnerCode").append(all);
                        jQuery.each(data, function (i, item) {
                            var option = "<option auto-create='true' value='" + item.code + "'>" + item.name + "</option>";
                            $("#cinemaInnerCode").append(option);
                        });
                    } else {
                        $alert("获取影院失败");
                    }
                }, error: function () {
                    $alert("获取影院失败");
                }
            });
        });

        $("#queryBtn").on('click', function () {
            $("#recordImport").hide();
            $("#recordUpdate").hide();
            $("#resourceTable").datagrid("load");
        });


        // 编辑
        $('#resourceTable').dgOper("edit", function (event, data, btn) {
            $("#editModal").modal("show");
            $(".modal-backdrop").css("z-index", 1009);
            $("#editModal").css("z-index", 1010);
            $("#editId").val(data.id);
            $("#editCinemaName").html(data.cinemaName);
            $("#editResourceType").html(data.resourceType);
            $("#editYear").html(data.year);
            $("#month1").val(data.budgetMonth1);
            $("#month2").val(data.budgetMonth2);
            $("#month3").val(data.budgetMonth3);
            $("#month4").val(data.budgetMonth4);
            $("#month5").val(data.budgetMonth5);
            $("#month6").val(data.budgetMonth6);
            $("#month7").val(data.budgetMonth7);
            $("#month8").val(data.budgetMonth8);
            $("#month9").val(data.budgetMonth9);
            $("#month10").val(data.budgetMonth10);
            $("#month11").val(data.budgetMonth11);
            $("#month12").val(data.budgetMonth12);

        });

        //销毁上传组件
        $('#exFile').fi("destroy");
        //初始化上传组件
        $('#exFile').fi({
            uploadUrl: "/pad/resource/budget/hall/upload",
            multi: false,
            showPreview: false,
            showUpload: false,
            showRemove: true,
            maxFileSize: 2048,
            maxFileCount: 1,
            minFileCount: 1,
            autoReplace: true,
            allowedFileExtensions: ["xls", "xlsx"],
            allowedPreviewTypes: [],
            uploadExtraData: function () {
                return {
                    category: $("#category").val(),
                    year: $("#fileYear").val()
                }
            }
        });
    }

};

$(function () {
    controller.init();
});
