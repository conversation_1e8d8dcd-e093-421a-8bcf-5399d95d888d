$.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['zh-CN']);
var controller = {
    init: function () {
        $("#resourceTable1").bootstrapTable({
            cache: false,
            pagination:true,
            paginationDetailHAlign:'right',
            pageNumber:1,
            pageSize: 10,
            pageList: [10, 25, 50, 100],
            sidePagination:'server',
            queryParams: function (params) {
                var temp = {
                    pageSize: params.limit,
                    pageNumber: (params.offset / params.limit) + 1,
                    year: $("#year").val(),
                    regionCode:$("#regionCode").val(),
                    cinemaInnerCode:$("#cinemaInnerCode").val(),
                    resourceType:$("#resourceType").val(),
                    resourceCode:$("#resourceCode").val()
                };
                return temp;
            },
            fixedColumns: true,
            fixedNumber: 10,
            url: 'list_data'
        });




        $("#importTable").dgOper("download", function (event, data, btn) {
            var fileName = encodeURI(data.fileName);
            window.open("download?fileId=" + data.fileId + "&filename=" + fileName);
        })

        $("#budgetImportTemplate").click(function () {
            window.location.href = "/files/template_position.xlsx";
        });
        $("#btnReset").click(function () {
            $("#cinemaInnerCode").selectable("data", -1);
            $("#regionCode").selectable("data", -1);
            $("#resourceType").find("option").eq(0).prop("selected", true);
            $("#year").find("option").eq(0).prop("selected", true);
            $("#resourceCode").val("");
        });

        $("#budgetImport").click(function () {
            $("#category").val("9");
            $("#fileModal").modal("show");
            $("#fileYear").find("option").eq(0).prop("selected", true);
            $(".modal-backdrop").css("z-index", 1009);
            $("#fileModal").css("z-index", 1010);
            if ($(".fileinput-remove-button").length > 0) {
                $(".fileinput-remove-button").click();
            }
        });
        $("#btnEdit").click(function () {
            let id = $("#editId").val();
            let budgetMonth1 = $("#month1").val();
            let budgetMonth2 = $("#month2").val();
            let budgetMonth3 = $("#month3").val();
            let budgetMonth4 = $("#month4").val();
            let budgetMonth5 = $("#month5").val();
            let budgetMonth6 = $("#month6").val();
            let budgetMonth7 = $("#month7").val();
            let budgetMonth8 = $("#month8").val();
            let budgetMonth9 = $("#month9").val();
            let budgetMonth10 = $("#month10").val();
            let budgetMonth11 = $("#month11").val();
            let budgetMonth12 = $("#month12").val();
            $.ajax({
                url: "modify_point_location",
                data: {
                    "id": id,
                    "month1": budgetMonth1,
                    "month2": budgetMonth2,
                    "month3": budgetMonth3,
                    "month4": budgetMonth4,
                    "month5": budgetMonth5,
                    "month6": budgetMonth6,
                    "month7": budgetMonth7,
                    "month8": budgetMonth8,
                    "month9": budgetMonth9,
                    "month10": budgetMonth10,
                    "month11": budgetMonth11,
                    "month12": budgetMonth12,
                },
                type: "GET",
                dataType: "json",
                success: function (result) {
                    if (result.state) {
                        $alert("修改成功！", "提示");
                        $("#resourceTable").datagrid("load");
                        $("#editModal").modal("hide");
                    }
                },
                error: function () {
                    $alert("修改失败！", "提示");
                }
            });
        });

        $("#closeRecordUpdateBtn").click(function () {
            $("#recordUpdate").hide();
        });
        $("#closeRecordImportBtn").click(function () {
            $("#recordImport").hide();
        });
        $("#budgetImportLog").click(function () {
            $("#recordImport").show();
            $("#importTable").datagrid({
                source: "file/list",
                auto: true
            });
        });
        $("#regionCode").selectable({
            source: "/pad/resource/city/searchAllRegion",
            tmplValue: "id",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#cinemaInnerCode").selectable({
            source: "/pad/resource/resource/searchAllCinema",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#regionCode").on('change', function () {
            var region = $("#regionCode").val();
            $.ajax({
                url: "/pad/resource/resource/searchAllCinema",
                data: {
                    regionCode: region
                },
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data) {
                        $("#cinemaInnerCode").empty();
                        var all = "<option auto-create='true' value='-1'>全部</option>";
                        $("#cinemaInnerCode").append(all);
                        jQuery.each(data, function (i, item) {
                            var option = "<option auto-create='true' value='" + item.code + "'>" + item.name + "</option>";
                            $("#cinemaInnerCode").append(option);
                        });
                    } else {
                        $alert("获取影院失败");
                    }
                }, error: function () {
                    $alert("获取影院失败");
                }
            });
        });

        $("#queryBtn").on('click', function () {
            $("#recordImport").hide();
            $("#recordUpdate").hide();
            $("#resourceTable1").bootstrapTable("refresh");
        });




        //销毁上传组件
        $('#exFile').fi("destroy");
        //初始化上传组件
        $('#exFile').fi({
            uploadUrl: "/pad/resource/budget/point_location/upload",
            multi: false,
            showPreview: false,
            showUpload: false,
            showRemove: true,
            maxFileSize: 2048,
            maxFileCount: 1,
            minFileCount: 1,
            autoReplace: true,
            allowedFileExtensions: ["xls", "xlsx"],
            allowedPreviewTypes: [],
            uploadExtraData: function () {
                return {
                    category: $("#category").val(),
                    year: $("#fileYear").val()
                }
            }
        });
    }

};

$(function () {
    controller.init();
});
function operate_formatter(value, row, index) {
    var html='';
    if (hasEditPermission()) {
        html=html+'<button class="btn btn-default t-icon" title="编辑" onclick=editDetail("'+ row.id +'","'+ row.year +'","'+ row.resourceType +'","'+ row.regionCode+'","'+ row.cinemaName+'","'+ row.resourceCode+'"';
        html=html+',"'+ row.budgetMonth1+'"';
        html=html+',"'+ row.budgetMonth2+'"';
        html=html+',"'+ row.budgetMonth3+'"';
        html=html+',"'+ row.budgetMonth4+'"';
        html=html+',"'+ row.budgetMonth5+'"';
        html=html+',"'+ row.budgetMonth6+'"';
        html=html+',"'+ row.budgetMonth7+'"';
        html=html+',"'+ row.budgetMonth8+'"';
        html=html+',"'+ row.budgetMonth9+'"';
        html=html+',"'+ row.budgetMonth10+'"';
        html=html+',"'+ row.budgetMonth11+'"';
        html=html+',"'+ row.budgetMonth12+'"';
        html=html+')><i class="icon-edit"></i></button>';
    }
    html=html+'<button class="btn btn-default t-icon" title="查看" onclick=viewDetail("'+ row.year +'","'+ row.regionCode+'","'+ row.cinemaInnerCode+'","'+ row.resourceCode+'")><i class="icon-check"></i></button>';
    return html;
}

function index_formatter(value, row, index){
    return index + 1;
}

function viewDetail(year,regionCode,cinemaInnerCode,resourceCode){
    $("#recordUpdate").show();
    $("#resourceChangeRecordTable").datagrid({
        source: "list_data_record",
        auto: true,
        data: {
            "year": year,
            "regionCode": regionCode,
            "cinemaInnerCode": cinemaInnerCode,
            "resourceCode": resourceCode
        },
        pagerOpts: false
    });
}

function editDetail(id,year,resourceType,regionCode,cinemaName,resourceCode,budgetMonth1,budgetMonth2,budgetMonth3,budgetMonth4,budgetMonth5,budgetMonth6,budgetMonth7,budgetMonth8,budgetMonth9,budgetMonth10,budgetMonth11,budgetMonth12){
    $("#editModal").modal("show");
    $(".modal-backdrop").css("z-index", 1009);
    $("#editModal").css("z-index", 1010);
    $("#editId").val(id);
    $("#editCinemaName").html(cinemaName);
    $("#editResourceType").html(resourceType);
    $("#editResourceCode").html(resourceCode);
    $("#editYear").html(year);
    $("#month1").val(budgetMonth1);
    $("#month2").val(budgetMonth2);
    $("#month3").val(budgetMonth3);
    $("#month4").val(budgetMonth4);
    $("#month5").val(budgetMonth5);
    $("#month6").val(budgetMonth6);
    $("#month7").val(budgetMonth7);
    $("#month8").val(budgetMonth8);
    $("#month9").val(budgetMonth9);
    $("#month10").val(budgetMonth10);
    $("#month11").val(budgetMonth11);
    $("#month12").val(budgetMonth12);
}