var controller = {

    init: function () {
        $("#cityDistrictTable").datagrid({
            source: "list",
            auto: true,
            searchForm: "#searchForm"
        });
//        $("#region").selectable({
//            source: "searchAllRegion",
//            tmplValue: "id",
//            tmplResult: "name",
//            auto: true,
//            pageSize: 1000,
//            term: "name",
//            selected: ""
//        });
//        $("#superior").selectable({
//            source: "searchAllProvince",
//            tmplValue: "id",
//            tmplResult: "nameCN",
//            auto: true,
//            pageSize: 1000,
//            term: "nameCN",
//            selected: ""
//        });
//        $("#city").selectable({
//            source: "/pad/dict/city",
//            tmplValue: "code",
//            tmplResult: "name",
//            auto: true,
//            pageSize: 1000,
//            term: "name",
//            selected: ""
//        });
        $("#districtLevel").selectable({
            source: "/pad/dict/city/levels",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });
        $("#modify-modal select[name='cityDistrictLevel']").selectable({
            source: "/pad/dict/city/levels",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });
        $("#queryBtn").on('click', function () {
            $("#cityDistrictTable").datagrid("load");
        });
        // 编辑
        $('#cityDistrictTable').dgOper("edit", function (event, data, btn) {
            controller.render(data.code);
            $('#modify-modal').modal();
        });
        //保存单个城市修改
        $("#one-city-update").click(function () {
            var code = $("#modify-modal input[name='code']").val();
            var cityLevel = $("#modify-modal select[name='cityDistrictLevel']").val();
            if (cityLevel == 0) {
                return;
            }
            var data = {"code": code, "cityDistrictLevel": cityLevel};
            controller.updateInfo(data);
        });
    },
    render: function (code) {
        controller.detail(code, function (cityLevel) {
            $("#modify-modal input[name='code']").val(code);
            if (cityLevel == "") {
                $("#modify-modal select[name='cityDistrictLevel']").selectable("data", 0);
            } else {
                $("#modify-modal select[name='cityDistrictLevel']").selectable("data", cityLevel);
            }
        });
    },

    detail: function (code, fun) {
        $.ajax({
            url: 'getCityDistrictLevel',
            data: {
                'code': code
            },
            type: 'GET',
            error: function () {
                alert('error');
            },
            success: function (data) {
                fun(data);
            }
        });
    },

    updateInfo: function (data) {
        $.ajax({
            url: 'updateCityDistrictLevel',
            data: data,
            type: 'post',
            error: function () {
                alert('error');
            },
            success: function (data) {
                $('#modify-modal').modal("hide");
                $("#cityDistrictTable").datagrid('reload');
            }
        });
    },
}

$(function () {
    controller.init();
});