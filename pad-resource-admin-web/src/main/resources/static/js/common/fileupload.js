function FileUploadClient() {
    //上传文件类型
    // 1: 宣传点位租赁
    // 2: 固定点位租赁
    // 3: 外租区域租赁
    // 4: 影厅座位租赁
    // 5: 冠名厅租赁
    // 6: 营销点位租赁
    // 7: 影院级别
    var category;
    this.init = function (options) {
        var closeDivFun = options.closeDiv;
        category = options.category;
        //是否显示版本,和生效日期
        if (options.price) {
            $("#fileModal").find(".price").removeClass("hidden");
        } else {
            $("#fileModal").find(".price").addClass("hidden");
        }

        if (options.title) {
            $("#fileModalLabel").text(options.title);
        }

        $("#effectiveDate").dp(
            {
                "dateType": "date",
                "initElement": false,
                "minDate": getTomorrowDate()
            }
        );

        function getTomorrowDate() {
            var date = new Date();
            date.setTime(date.getTime() + 24 * 60 * 60 * 1000);
            var year = date.getFullYear();    //获取完整的年份(4位,1970-????)
            var month = date.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
            var day = date.getDate();        //获取当前日(1-31)
            var clock = year + "-";
            if (month < 10)
                clock += "0";
            clock += month + "-";

            if (day < 10)
                clock += "0";
            clock += day;
            return clock;
        }

        //销毁上传组件
        $('#exFile').fi("destroy");
        //初始化上传组件
        $('#exFile').fi({
            uploadUrl: options.url,
            multi: false,
            showPreview: false,
            showUpload: false,
            showRemove: true,
            maxFileSize: 2048,
            maxFileCount: 1,
            minFileCount: 1,
            autoReplace: true,
            allowedFileExtensions: ["xls", "xlsx"],
            allowedPreviewTypes: [],
            uploadExtraData: function () {
                return {
                    category: category,
                    version: $("#version").val(),
                    effective_date: $("#effectiveDate").val()
                }
            }
        });
        //新增文件上传
        $('#exFile').on('fileuploaded', function (event, data, previewId, index) {
            var response = data.response;
            $("#fileModal").modal("hide");
            if (response.state) {
                $alert("导入成功！", "导入成功");
                return true;
            } else {
                $alert(response.msg, "导入失败");
                return true;
            }
        });

        //显示文件上传弹窗
        $("#importBtn").on('click', function () {
            closeDivFun();
            $("#version").val("");
            $("#effectiveDate").val("");
            $("#exFile").fileinput("reset");
            $("#fileModal").modal("show");
        });
        //上传按钮
        $("#fileModal").find(".upload").on("click", function () {
            if (options.price) {
                var version = $("#version").val();
                if (!version) {
                    $alert("请添加版本信息");
                    return;
                }
                var date = $("#effectiveDate").val();
                if (!date) {
                    $alert("请选择生效日期");
                    return;
                }
            }
            if (!$("#exFile").val()) {
                $alert("请选择文件");
                return;
            }
            $("#exFile").fileinput("upload");
            $("#fileModal").modal("hide");
        });
    }
};
