function FileUploadClient() {
    var category;
    this.init = function (options, $container) {
        var closeDivFun = options.closeDiv;
        category = options.category;
        if (options.title) {
            $("#fileModalLabel").text(options.title);
        }
        if (options.tip) {
            $("#tip").text(options.tip);
        }
        //销毁上传组件
        $('#exFile').fi("destroy");
        //初始化上传组件
        $('#exFile').fi({
            uploadUrl: options.url,
            multi: false,
            showPreview: false,
            showUpload: false,
            showRemove: true,
            maxFileSize: 2048,
            maxFileCount: 1,
            minFileCount: 1,
            autoReplace: true,
            allowedFileExtensions: ["xls", "xlsx"],
            allowedPreviewTypes: [],
            uploadExtraData: function () {
                return {
                    category: category
                }
            }
        });
        //新增文件上传
        $('#exFile').on('fileuploaded', function (event, data, previewId, index) {
            var response = data.response;
            $("#fileModal").modal("hide");
            if ($container != null)
                $container.trigger('showImportList');
            if (response.state) {
                $alert("开始导入, 进度请到导入列表查看！", "提示");
                return true;
            } else {
                $alert(response.msg, "导入失败");
                return true;
            }
        });
        //显示文件上传弹窗
        $("#importBtn").on('click', function () {
            closeDivFun();
            $("#exFile").fileinput("reset");
            $("#fileModal").modal("show");
        });
        //上传按钮
        $("#fileModal").find(".upload").on("click", function () {
            if (!$("#exFile").val()) {
                $alert("请选择文件");
                return;
            }
            $("#exFile").fileinput("upload");
            $("#fileModal").modal("hide");
        });
    }
};
