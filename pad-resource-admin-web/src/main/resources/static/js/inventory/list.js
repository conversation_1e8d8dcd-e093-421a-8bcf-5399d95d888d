var controller = {

    init: function () {
        $("#inventoryTable").datagrid({
            source: "list",
            auto: true,
            searchForm: "#searchForm"
        });

        $("#region").selectable({
            source: "/pad/resource/city/searchAllRegion",
            tmplValue: "id",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#cinema").selectable({
            source: "/pad/resource/resource/searchAllCinema",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#region").on('change', function () {
            var region = $("#region").val();
            $.ajax({
                url: "/pad/resource/resource/searchAllCinema",
                data: {
                    regionCode: region
                },
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data) {
                        $("#cinema").empty();
                        var all = "<option auto-create='true' value='-1'>全部</option>";
                        $("#cinema").append(all);
                        jQuery.each(data, function (i, item) {
                            var option = "<option auto-create='true' value='" + item.code + "'>" + item.name + "</option>";
                            $("#cinema").append(option);
                        });
                    } else {
                        $alert("获取影院失败");
                    }
                }, error: function () {
                    $alert("获取影院失败");
                }
            });
        });

        $("#queryBtn").on('click', function () {
            $("#recordDl").hide();
            $("#inventoryTable").datagrid("load");
        });

        $("#closeRecordBtn").on('click', function () {
            $("#recordDl").hide();
        });

        $("#inventoryTable").dgFieldTd("marketingPointLeasableAreaStr", function (fvalue, extra, data) {
            return "<a href='javascript:;' onclick='showInventoryOccupationList(" + '"' + data.cinemaCode + '"' + "," + '"' + data.dateStr + '"' + "," + '"yx"' + "," + '"' + data.cinemaName + '"' + ");'>" + data.marketingPointLeasableAreaStr + "</a>";
        });
        $("#inventoryTable").dgFieldTd("outerAreaLeasableAreaStr", function (fvalue, extra, data) {
            return "<a href='javascript:;' onclick='showInventoryOccupationList(" + '"' + data.cinemaCode + '"' + "," + '"' + data.dateStr + '"' + "," + '"wz"' + "," + '"' + data.cinemaName + '"' + ");'>" + data.outerAreaLeasableAreaStr + "</a>";
        });
        $("#inventoryTable").dgFieldTd("fixedPointLeasableAreaStr", function (fvalue, extra, data) {
            return "<a href='javascript:;' onclick='showInventoryOccupationList(" + '"' + data.cinemaCode + '"' + "," + '"' + data.dateStr + '"' + "," + '"gd"' + "," + '"' + data.cinemaName + '"' + ");'>" + data.fixedPointLeasableAreaStr + "</a>";
        });
        $("#inventoryTable").dgFieldTd("advertisingPointLeasableQuantityStr", function (fvalue, extra, data) {
            return "<a href='javascript:;' onclick='showInventoryOccupationList(" + '"' + data.cinemaCode + '"' + "," + '"' + data.dateStr + '"' + "," + '"xc"' + "," + '"' + data.cinemaName + '"' + ");'>" + data.advertisingPointLeasableQuantityStr + "</a>";
        });

        $("#exportExcelBtn").on('click', function () {
            var formObj = $("#searchForm").serialize();
            Mtime.Net.Ajax.post("exportInventory", formObj).encoder("form").timeout(100000).json(function (data) {
                console.log(data);
                if (data.success) {
                    $alert("文件正在后台生成，稍等片刻，请勿刷新页面或者离开该页面！", "提示");
                    loading2 = new Mtime.UI.Loading("body", {maskColor: "black", spinner: "spinner2"});
                    loading2.toggle();
                } else {
                    $alert("文件生成异常", "提示");
                    loading2.hide();
                }
            });


            var timer = setInterval(function () {
                geteExportStatus(timer);

            }, 5000);
        })

    }

};

function showInventoryOccupationList(cinemaCode, dateStr, type, cinemaName) {
    $("#recordDl").show();
    $("#cinemaP").html("影院名称：" + cinemaName);
    $("#dateP").html("库存日期：" + dateStr);
    var businessTypeStr;
    if (type == "yx") {
        businessTypeStr = "营销点位";
    } else if (type == "gd") {
        businessTypeStr = "固定点位";
    } else if (type == "wz") {
        businessTypeStr = "外租区域";
    } else if (type == "xc") {
        businessTypeStr = "宣传点位";
    }
    $("#businessP").html("业务类型：" + businessTypeStr);
    $("#inventoryChangeRecordTable").datagrid({
        source: "queryOccupationList",
        data: {
            "cinemaCode": cinemaCode,
            "date": dateStr,
            "businessType": type
        },
        pagerOpts: false
    });
    $('html, body').animate({scrollTop: $('#recordDl').offset().top}, 1000);
}

function getNowDate() {
    var myDate = new Date();
    var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
    var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
    var day = myDate.getDate();        //获取当前日(1-31)
    var clock = year + "-";
    if (month < 10)
        clock += "0";
    clock += month + "-";

    if (day < 10)
        clock += "0";
    clock += day;
    return clock;
}

function getEndDate() {
    var myDate = new Date();
    var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
    var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
    var day = myDate.getDate() + 30;        //获取当前日(1-31)
    var clock = year + "-";
    if (month < 10)
        clock += "0";
    clock += month + "-";

    if (day < 10)
        clock += "0";
    clock += day;
    return clock;
}

function geteExportStatus(timer) {
    Mtime.Net.Ajax.post("getExportStatus", "").json(function (data) {
        if (data.success) {
            var result = data.value;
            if (result == 'error') {
                $alert("文件生成异常", "提示");
                loading2.hide();
                clearInterval(timer);    //清除定时器
            } else {
                console.log("文件上传成功，开始下载", "提示")
                loading2.hide();
                //停止周期性的js调用
                clearInterval(timer);    //清除定时器
                getExcelFile();
            }
        }
    });
}

function getExcelFile() {
    $("#searchForm").attr("action", "downLoadInventory");
    $("#searchForm").submit();
}

$(function () {
    $("#serviceStartTime").datepicker("date", getNowDate());
    $("#serviceEndTime").datepicker("date", getEndDate());
    controller.init();
});
