function LeasingPriceClient() {
    // 1: 宣传点位租赁
    // 2: 固定点位租赁
    // 3: 外租区域租赁
    // 4: 影厅座位租赁
    // 5: 冠名厅租赁
    // 6: 营销点位租赁
    // 7: 影院级别
    var category;
    var priceRecordFunction;
    this.init = function (options) {
        category = options.category;
        priceRecordFunction = options.priceRecordFun;
        //初始化导入功能
        new FileUploadClient().init({
            url: "file/import",
            category: category,
            price: true,
            title: "批量导入刊例",
            closeDiv: closeDiv
        });

        $("#priceTable").datagrid({
            source: "list",
            auto: true,
            searchForm: "#searchForm"
        });


        $("#priceTable").dgOper("priceRecord", priceRecordFunction);

        //关闭价格记录
        $("#priceRecordDiv").on("click", ".close-file-import-log", function () {
            var downloadFileDiv = $("#priceRecordDiv");
            if (!downloadFileDiv.hasClass("hidden")) {
                downloadFileDiv.addClass("hidden");
            }
        });

        $("#queryBtn").on('click', function () {
            closeDiv();
            $("#priceTable").datagrid("reload");
        });

        //下载模版
        $("#downloadDemo").on('click', function () {
            window.open("demo");
        });

        $("#importTable").datagrid({
            source: "file/list",
            auto: true
        });

        $("#importTable").dgOper("download", function (event, data, btn) {
            var fileName = encodeURI(data.fileName);
            window.open("download?fileId=" + data.fileId + "&filename=" + fileName);
        });

        $("#importTable").dgOper("discard", function (event, data, btn) {
            $.ajax({
                url: "discard",
                data: {id: data.id},
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data.success) {
                        $("#importTable").datagrid("reload");
                        $alert("废弃成功")
                    } else {
                        $alert("废弃出错")
                    }
                }, error: function () {
                    $alert("废弃出错")
                }
            });
        });

        //导入记录
        $("#importLogBtn").on('click', function () {
            closeDiv();
            var downloadFileDiv = $("#fileImportDiv");
            if (downloadFileDiv.hasClass("hidden")) {
                downloadFileDiv.removeClass("hidden");
            }
            $("#importTable").datagrid("reload");
        });

        //关闭导入记录
        $("#fileImportDiv").on("click", ".close-file-import-log", function () {
            var downloadFileDiv = $("#fileImportDiv");
            if (!downloadFileDiv.hasClass("hidden")) {
                downloadFileDiv.addClass("hidden");
            }
        });

        function closeDiv() {
            $("#priceRecordDiv .close-file-import-log").trigger("click");
            $("#fileImportDiv .close-file-import-log").trigger("click");
        }
    }
};
