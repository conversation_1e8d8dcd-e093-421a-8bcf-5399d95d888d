$(function () {
    var options = {
        category: 5,
        priceRecordFun: function (event, data, btn) {
            $("#fileImportDiv .close-file-import-log").trigger("click");
            var downloadFileDiv = $("#priceRecordDiv");
            if (downloadFileDiv.hasClass("hidden")) {
                downloadFileDiv.removeClass("hidden");
            }
            $("#priceRecordTable").datagrid({
                source: "records",
                data: {
                    "cinemaCode": data.cinemaCode,
                    "movieHallType": data.movieHallType
                },
                pagerOpts: false
            });
            $('html, body').animate({scrollTop: $('#priceRecordDiv').offset().top}, 1000);
        }
    }
    new LeasingPriceClient().init(options);
});
