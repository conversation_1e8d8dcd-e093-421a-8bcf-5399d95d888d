var controller = {

    init: function () {
        $("#resourceTable").datagrid({
            source: "list",
            auto: true,
            searchForm: "#searchForm"
        });

        $("#region").selectable({
            source: "/pad/resource/city/searchAllRegion",
            tmplValue: "id",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#cinema").selectable({
            source: "/pad/resource/resource/searchAllCinema",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#region").on('change', function () {
            var region = $("#region").val();
            $.ajax({
                url: "/pad/resource/resource/searchAllCinema",
                data: {
                    regionCode: region
                },
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data) {
                        $("#cinema").empty();
                        var all = "<option auto-create='true' value='-1'>全部</option>";
                        $("#cinema").append(all);
                        jQuery.each(data, function (i, item) {
                            var option = "<option auto-create='true' value='" + item.code + "'>" + item.name + "</option>";
                            $("#cinema").append(option);
                        });
                    } else {
                        $alert("获取影院失败");
                    }
                }, error: function () {
                    $alert("获取影院失败");
                }
            });
        });

        $("#queryBtn").on('click', function () {
            $("#recordDl").hide();
            $("#resourceTable").datagrid("load");
        });

        $("#closeRecordBtn").on('click', function () {
            $("#recordDl").hide();
        });

        // 编辑
        $('#resourceTable').dgOper("edit", function (event, data, btn) {
            $("#recordDl").hide();
            $(event.currentTarget).parent().parent().children().eq(4).html("<input type='text' value='" + data.marketingPointLeasableArea + "'/>");
            $(event.currentTarget).parent().parent().children().eq(5).html("<input type='text' value='" + data.outerAreaLeasableArea + "'/>");
            $(event.currentTarget).parent().parent().children().eq(6).html("<input type='text' value='" + data.fixedPointLeasableArea + "'/>");
            $(event.currentTarget).parent().parent().children().eq(7).html("<input type='text' value='" + data.advertisingPointLeasableQuantity + "'/>");
            $(event.currentTarget).parent().html('<button class="btn btn-default t-icon" title="保存" data-oper-handler="save"><i class="icon-save"></i></button>' +
                '<button class="btn btn-default t-icon" title="取消" data-oper-handler="cancel""><i class="icon-x"></i></button>');

        });

        //保存
        $('#resourceTable').dgOper("save", function (event, data, btn) {
            var reg = /^([1-9]\d{0,9}|0)([.]?|(\.\d{1,2})?)$/;
            var xcReg = /^[0-9]\d*$/;//宣传点位个数正则，只能输入整数或0
            var cinemaCode = $(event.currentTarget).parent().parent().children().eq(2).html();
            var marketingPointLeasableAreaNew = $(event.currentTarget).parent().parent().children().eq(4).find("input").val();
            var outerAreaLeasableAreaNew = $(event.currentTarget).parent().parent().children().eq(5).find("input").val();
            var fixedPointLeasableAreaNew = $(event.currentTarget).parent().parent().children().eq(6).find("input").val();
            var advertisingPointLeasableQuantityNew = $(event.currentTarget).parent().parent().children().eq(7).find("input").val();
            if (!marketingPointLeasableAreaNew || !reg.test(marketingPointLeasableAreaNew)) {
                $alert("营销点位面积，请输入正数或0，至多两位小数", "提示", function (wind) {
                    wind.close();
                });
                return;
            }
            if (!outerAreaLeasableAreaNew || !reg.test(outerAreaLeasableAreaNew)) {
                $alert("外租区域面积，请输入正数或0，至多两位小数", "提示", function (wind) {
                    wind.close();
                });
                return;
            }
            if (!fixedPointLeasableAreaNew || !reg.test(fixedPointLeasableAreaNew)) {
                $alert("固定点位面积，请输入正数或0，至多两位小数", "提示", function (wind) {
                    wind.close();
                });
                return;
            }
            if (!advertisingPointLeasableQuantityNew || !xcReg.test(advertisingPointLeasableQuantityNew)) {
                $alert("宣传点位个数，请输入正整数或0", "提示", function (wind) {
                    wind.close();
                });
                return;
            }
            $.ajax({
                url: 'validateResource',
                data: {
                    "cinemaCode": cinemaCode,
                    "marketingPointLeasableArea": marketingPointLeasableAreaNew,
                    "outerAreaLeasableArea": outerAreaLeasableAreaNew,
                    "fixedPointLeasableArea": fixedPointLeasableAreaNew,
                    "advertisingPointLeasableQuantity": advertisingPointLeasableQuantityNew
                },
                type: 'post',
                success: function (data) {
                    if (data == "success") {
                        $.ajax({
                            url: 'updateResource',
                            data: {
                                "cinemaCode": cinemaCode,
                                "marketingPointLeasableArea": marketingPointLeasableAreaNew,
                                "outerAreaLeasableArea": outerAreaLeasableAreaNew,
                                "fixedPointLeasableArea": fixedPointLeasableAreaNew,
                                "advertisingPointLeasableQuantity": advertisingPointLeasableQuantityNew
                            },
                            type: 'post',
                            success: function (result) {
                                if (result.ack) {
                                    $alert("保存成功");
                                    $("#resourceTable").datagrid('load');
                                } else {
                                    $alert(result.msg);
                                }
                            }
                        });
                    } else {
                        var dlg = $confirm(data, "保存提示", function () {
                            $.ajax({
                                url: 'updateResource',
                                data: {
                                    "cinemaCode": cinemaCode,
                                    "marketingPointLeasableArea": marketingPointLeasableAreaNew,
                                    "outerAreaLeasableArea": outerAreaLeasableAreaNew,
                                    "fixedPointLeasableArea": fixedPointLeasableAreaNew,
                                    "advertisingPointLeasableQuantity": advertisingPointLeasableQuantityNew
                                },
                                type: 'post',
                                success: function (result) {
                                    if (result.ack) {
                                        $alert("保存成功");
                                        $("#resourceTable").datagrid('load');
                                    } else {
                                        $alert(result.msg);
                                    }
                                }
                            });
                            dlg.close();
                        });
                    }

                }
            });

        });

        //取消
        $('#resourceTable').dgOper("cancel", function (event, data, btn) {
            $(event.currentTarget).parent().parent().children().eq(4).html(data.marketingPointLeasableArea);
            $(event.currentTarget).parent().parent().children().eq(5).html(data.outerAreaLeasableArea);
            $(event.currentTarget).parent().parent().children().eq(6).html(data.fixedPointLeasableArea);
            $(event.currentTarget).parent().parent().children().eq(7).html(data.advertisingPointLeasableQuantity);
            $(event.currentTarget).parent().html('<button class="btn btn-default t-icon" title="编辑" data-oper-handler="edit"><i class="icon-edit"></i></button>' +
                '<button class="btn btn-default t-icon" title="查看" data-oper-handler="view""><i class="icon-check"></i></button>');

        });

        //查看影城资源更新记录
        $('#resourceTable').dgOper("view", function (event, data, btn) {
            var cinemaCode = $(event.currentTarget).parent().parent().children().eq(2).html();
            $("#recordDl").show();
            $("#resourceChangeRecordTable").datagrid({
                source: "queryChangeRecordList",
                data: {"cinemaCode": cinemaCode},
                pagerOpts: false
            });
            $('html, body').animate({scrollTop: $('#recordDl').offset().top}, 1000);
        });
    }

};

$(function () {
    controller.init();
});
