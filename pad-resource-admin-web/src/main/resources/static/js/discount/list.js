var controller = {

    init: function () {

        $("#discountTable").datagrid({
            source: "list",
            auto: true,
            searchForm: "#searchForm"
        });

        $("#businessType").selectable({
            source: "/pad/dict/businessType",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "code",
            selected: ""
        });

        $("#businessTypeToSave").selectable({
            source: "/pad/dict/businessType",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "code",
            selected: ""
        });

        $("#queryBtn").on('click', function () {
            $("#editDl").hide();
            $("#discountTable").datagrid("load");
        });

        $("#addDiscountBtn").on('click', function () {
            $("#editDl input[name='id']").val(null);
            $("#addPage").find("select").attr("disabled", false);
            $("#addPage").find("select").selectable("data", 0);
            $("#addPage").find("input:radio").attr("disabled", false);
            $("#addPage").find("input:radio").prop("checked", false);
            $("#discountRuleTbody").empty();
            $("#editDl").show();
            $('html, body').animate({scrollTop: $('#editDl').offset().top}, 1000);
        });

        $("#closeBtn").on('click', function () {
            $("#editDl").hide();
        });
        $("#saveBtn").on('click', function () {
            saveDiscount();
        });
        $("#addRuleBtn").on('click', function () {

            $("#discountRuleDiv").show();
            var content = '<tr>' +
                ' <td style="padding-left: 15px;">' +
                '<select class="form-control" style="width: 60px;" name="comparisonSymbol" onchange="showInput(this);">' +
                '<option value="1">区间</option>' +
                '<option value="2">==</option>' +
                '<option value="3"><=</option>' +
                '<option value="4">>=</option>' +
                '</select>' +
                '</td>' +
                '<td name="min">' +
                '<input name="min" class="form-control"/>' +
                '</td>' +
                '<td name="span">' +
                '<span>~</span>' +
                '</td>' +
                '<td name="max">' +
                '<input name="max" class="form-control"/>' +
                '</td>' +
                '<td>' +
                '<span>折扣系数：</span>' +
                '</td>' +
                '<td>' +
                '<input name="factor" class="form-control"/>' +
                '</td>' +
                '<td>' +
                '<a href="javascript:;" class="t-icon" id="addRuleBtn" title="删除" onclick="deleteTr(this);">' +
                '<i class="icon-del"></i>' +
                '</a>' +
                '</td>' +
                '</tr>';
            $("#discountRuleTbody").append(content);
            $('html, body').animate({scrollTop: $('#editDl').offset().top}, 1000);

        });
        // 编辑
        $('#discountTable').dgOper("edit", function (event, data, btn) {
            controller.render(data.id);
            $("#editDl").show();
            $('html, body').animate({scrollTop: $('#editDl').offset().top}, 1000);
        });
        // 删除
        $('#discountTable').dgOper("delete", function (event, data, btn) {
            var dlg = $confirm("确认删除此设置？", "提示", function () {
                $.ajax({
                    url: 'delete',
                    data: {
                        "id": data.id
                    },
                    type: 'post',
                    success: function (result) {
                        if (result.ack) {
                            $("#editDl").hide();
                            $("#discountTable").datagrid('load');
                        } else {
                            $alert(result.msg);
                        }
                    }
                });
                dlg.close();
            });
        });
    },

    render: function (id) {
        controller.detail(id, function (discount) {
            $("#editDl input[name='id']").val(discount.id);
            $("#editDl select[name='businessTypeToSave']").selectable("data", discount.businessType);
            $("#editDl select[name='businessTypeToSave']").attr("disabled", "disabled");
            $("#editDl input[name='discountType'][value='" + discount.discountType + "']").prop("checked", "checked");
            $("#editDl input[name='discountType']").attr("disabled", "disabled");
            $("#discountRuleTbody").empty();
            $("#discountRuleDiv").show();
            var discountRules = discount.discountRules;
            for (var i in discountRules) {
                var content = '<tr ruleId="' + discountRules[i].id + '" discountId="' + discountRules[i].discountId + '">' +
                    ' <td style="padding-left: 15px;">' +
                    '<select class="form-control" style="width: 60px;" name="comparisonSymbol" onchange="showInput(this);">';
                if (discountRules[i].comparisonSymbol == 1) {
                    content = content + '<option value="1" selected>区间</option>';
                    content = content + '<option value="2">==</option>';
                    content = content + '<option value="3"><=</option>';
                    content = content + '<option value="4">>=</option>';
                } else if (discountRules[i].comparisonSymbol == 2) {
                    content = content + '<option value="1">区间</option>';
                    content = content + '<option value="2" selected>==</option>';
                    content = content + '<option value="3">>=</option>';
                    content = content + '<option value="4"><=</option>';
                } else if (discountRules[i].comparisonSymbol == 3) {
                    content = content + '<option value="1">区间</option>';
                    content = content + '<option value="2">==</option>';
                    content = content + '<option value="3" selected><=</option>';
                    content = content + '<option value="4">>=</option>';
                } else if (discountRules[i].comparisonSymbol == 4) {
                    content = content + '<option value="1">区间</option>';
                    content = content + '<option value="2">==</option>';
                    content = content + '<option value="3"><=</option>';
                    content = content + '<option value="4" selected>>=</option>';
                }
                content = content +
                    '</select>' +
                    '</td>' +
                    '<td name="min">' +
                    '<input name="min" class="form-control" value="' + discountRules[i].min + '"/>' +
                    '</td>';
                if (discountRules[i].comparisonSymbol == 1) {
                    content = content +
                        '<td name="span">' +
                        '<span>~</span>' +
                        '</td>' +
                        '<td name="max">' +
                        '<input name="max" class="form-control" value="' + discountRules[i].max + '"/>' +
                        '</td>';
                } else {
                    content = content +
                        '<td name="span" style="display: none">' +
                        '<span>~</span>' +
                        '</td>' +
                        '<td name="max" style="display: none;">' +
                        '<input name="max" class="form-control" value="' + discountRules[i].max + '"/>' +
                        '</td>';
                }
                content = content +
                    '<td>' +
                    '<span>折扣系数：</span>' +
                    '</td>' +
                    '<td>' +
                    '<input name="factor" class="form-control" value="' + discountRules[i].factor + '"/>' +
                    '</td>' +
                    '<td>' +
                    '<a href="javascript:;" class="t-icon" id="addRuleBtn" title="删除" onclick="deleteTr(this);">' +
                    '<i class="icon-del"></i>' +
                    '</a>' +
                    '</td>' +
                    '</tr>';
                $("#discountRuleTbody").append(content);
            }
        });
    },

    detail: function (id, fun) {
        $.ajax({
            url: 'detail',
            data: {
                'id': id
            },
            type: 'GET',
            error: function () {
                alert('error');
            },
            success: function (data) {
                fun(data);
            }
        });
    },
};


function showInput(obj) {
    var comparisonSymbol = $(obj).val();
    $(obj).parent().parent().find("input[name='min']").val("");
    $(obj).parent().parent().find("input[name='max']").val("");
    $(obj).parent().parent().find("input[name='factor']").val("");
    if (comparisonSymbol != 1) {
        $(obj).parent().parent().find("td[name='span']").hide();
        $(obj).parent().parent().find("td[name='max']").hide();
    } else {
        $(obj).parent().parent().find("td[name='span']").show();
        $(obj).parent().parent().find("td[name='max']").show();
    }
}

function deleteTr(obj) {
    $(obj).parent().parent().remove();
}

function getDiscountInfo() {
    var discountInfo = {};
    var id = $("#addPage input[name='id']").val();
    var businessTypeToSave = $("#businessTypeToSave").val();
    var discountType = $("#addPage").find("input:radio:checked").val();
    if (businessTypeToSave == 0) {
        $alert("请选择阵地业务类型");
        return;
    }
    if (discountType == undefined) {
        $alert("请选择折扣类别");
        return;
    }
    discountInfo.id = id;
    discountInfo.businessType = businessTypeToSave;
    discountInfo.discountType = discountType;
    var discountRules = new Array();
    if ($("#discountRuleTbody").find("tr").length == 0) {
        $alert("请添加折扣规则");
        return;
    }
    var isFlag = false;
    var reg = /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/;
    try {
        $("#discountRuleTbody tr").each(function () {
            var discountRule = {};
            var id = $(this).attr("ruleId");
            var discountId = $(this).attr("discountId");
            var comparisonSymbol = $(this).find("td select").eq(0).val();
            var min = $(this).find("td").eq(1).find("input").val();
            var max = $(this).find("td").eq(3).find("input").val();
            var factor = $(this).find("td").eq(5).find("input").val();
            if (!min) {
                throw "输入框不能为空";
                return false;
            } else {
                if (!reg.test(min)) {
                    throw "输入框请输入正数，最多包括两位小数";
                    return false;
                }
            }
            if (!factor) {
                throw "折扣系数不能为空";
                return false;
            } else {
                if (!reg.test(factor)) {
                    throw "折扣系数请输入正数，最多包括两位小数";
                    return false;
                }
            }
            if (comparisonSymbol == 1) {
                if (!max) {
                    throw "最大值不能为空";
                    return false;
                } else {
                    if (!reg.test(max)) {
                        throw "最大值请输入正数，最多包括两位小数";
                        return false;
                    }
                }
                if (parseFloat(max) <= parseFloat(min)) {
                    throw "最大值不能小于最小值";
                    return false;
                }
            }
            discountRule.id = id;
            discountRule.discountId = discountId;
            discountRule.comparisonSymbol = comparisonSymbol;
            discountRule.min = min;
            discountRule.max = max;
            discountRule.factor = factor;
            discountRules.push(discountRule);
        });
        isFlag = true;
    } catch (e) {
        $alert(e);
        isFlag = false;
    }

    if (isFlag) {
        discountInfo.discountRules = discountRules;
        return discountInfo;
    }
}

function saveDiscount() {
    var discountInfo = getDiscountInfo();
    if (!discountInfo) {
        return;
    }
    $.ajax({
        url: "/pad//resource/discount/save",
        type: "post",
        data: JSON.stringify(discountInfo),
        contentType: 'application/json; charset=UTF-8',
        dataType: "json",
        success: function (result) {
            if (result.ack) {
                $("#editDl").hide();
                $("#discountTable").datagrid("load");
            } else {
                $alert(result.msg);
            }
        }
    });
}


$(function () {
    controller.init();
});