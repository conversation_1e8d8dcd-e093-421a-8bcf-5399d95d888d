function CinemaLevelClient() {
    this.init = function () {
        //init order table
        $("#cinemaLevelTable").datagrid({
            source: "list",
            auto: true,
            searchForm: "#searchForm"
        });

        $("#importTable").datagrid({
            source: "file/list",
            auto: true
        });

        $("#importTable").dgOper("download", function (event, data, btn) {
            var fileName = encodeURI(data.fileName);
            window.open("download?fileId=" + data.fileId + "&filename=" + fileName);
        })

        $("#cityDistrictLevel").selectable({
            source: "/pad/dict/city/levels",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#cinemaLevel").selectable({
            source: "/pad/dict/cinema/levels",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });

        $("#cinemaInnerCode").selectable({
            source: "/pad/dict/cinemas",
            tmplValue: "code",
            tmplResult: "name",
            auto: true,
            pageSize: 1000,
            term: "name",
            selected: ""
        });


        //bind query action
        $("#queryBtn").on('click', function () {
            closeDiv();
            $("#cinemaLevelTable").datagrid("reload");
        });
        //下载模版
        $("#downloadDemo").on('click', function () {
            window.open("demo");
        });


        //初始化导入功能
        new FileUploadClient().init({
            url: "file/import",
            category: 7,
            price: false,
            title: "批量导入影城级别",
            closeDiv: function () {
                $("#fileImportDiv .close-file-import-log").trigger("click");
            }
        });

        //导入记录
        $("#importLogBtn").on('click', function () {
            var downloadFileDiv = $("#fileImportDiv");
            if (downloadFileDiv.hasClass("hidden")) {
                downloadFileDiv.removeClass("hidden");
            }
            $("#importTable").datagrid("reload");
        });

        //关闭导入记录
        $("#fileImportDiv").on("click", ".close-file-import-log", function () {
            var downloadFileDiv = $("#fileImportDiv");
            if (!downloadFileDiv.hasClass("hidden")) {
                downloadFileDiv.addClass("hidden");
            }
        });

        function closeDiv() {
            $("#fileImportDiv .close-file-import-log").trigger("click");
        }
    }
};
$(function () {
    new CinemaLevelClient().init();
});
