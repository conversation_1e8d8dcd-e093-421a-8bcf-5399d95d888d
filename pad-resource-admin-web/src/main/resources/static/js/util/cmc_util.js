function CmcError(msg, obj) {
    if (typeof obj === 'undefined') {
        obj = {};
    }
    this.message = msg;
    this.obj = obj;
}
CmcError.prototype = new Error();

(function (root, factory) {
	'use strict';
	if (typeof define === 'function' && define.amd) {
		define(['jquery'], function ($) {
			return (root.cmcCurrencyUtil = factory($));
		});
	}
	else {
		root.cmcCurrencyUtil = root.cmcCurrencyUtil || factory(root.jQuery);
	}
}(this, function ($) {
	'use strict';
	function calDecimalTotalDigits(arg1, arg2) {//计算两个数的小数总位数
        var decimalTotalDigits = 0;
        try {
            decimalTotalDigits += arg1.split(".")[1].length
        } catch(e) {}
        try {
            decimalTotalDigits += arg2.split(".")[1].length
        } catch(e) {}
        return decimalTotalDigits;
    }
	return {
		fenToYuan:function(fen) {
            if (typeof fen === 'undefined' || fen == null)
                 return "";
             fen = fen + '';
             if (fen.length == 1) {
                 return "0.0" + fen;
             } else if (fen.length == 2) {
                 return "0." + fen;
             } else {
                 var front = fen.substring(0, fen.length - 2);
                 var after = fen.substring(fen.length - 2);
                 return front + "." + after;
             }
        },
        mul_yuan:function(num1, num2) {//参数单位为元
            var num1Str = num1.toString(), num2Str = num2.toString();
            var totalDigits = calDecimalTotalDigits(num1Str, num2Str);
            return (
                Math.round(Number(num1Str.replace(".","")) * Number(num2Str.replace(".",""))) / Math.pow(10, totalDigits)
            )
            .toFixed(2);
        }
	};
}));
(function (root, factory) {
	'use strict';
	if (typeof define === 'function' && define.amd) {
		define(['jquery'], function ($) {
			return (root.cmcUtil = factory($));
		});
	}
	else {
		root.cmcUtil = root.cmcUtil || factory(root.jQuery);
	}
}(this, function ($) {
	'use strict';
	function format(date, pos) {
        return ("0000" + date).slice(pos * -1);
    }
    function alertError(e, alertMsg) {
        var errorMsg = e.responseJSON.message,
            exceptionMatch = 'FaultException:',
            exceptionMatchIdx = errorMsg.lastIndexOf(exceptionMatch);
        if (exceptionMatchIdx === -1) {
           $alert(errorMsg);
           return;
        }
        errorMsg = errorMsg.substr(exceptionMatchIdx + exceptionMatch.length);
        typeof alertMsg === 'undefined' ? $alert(errorMsg) : $alert(alertMsg.replace('{errorMsg}', errorMsg));
    }
	return {
		lock:function(obj){
           if(!(obj instanceof jQuery)){
                  obj = $(obj);
           }
           obj.attr("lock","lock");
        },
        isLock:function(obj){
           if(!(obj instanceof jQuery)){
               obj = $(obj);
            }
           return obj.attr("lock") ==  "lock";
        },
        unLock:function(obj){
            if(!(obj instanceof jQuery)) {
                obj = $(obj);
            }
            obj.attr("lock","unlock");
        },
        encodeHtml:function(str) {
            return '<pre>' +
                        str.replace(/</g, '&lt;')
                           .replace(/>/g, '&gt;')
                           .replace(/'/g, '&#39;')
                           .replace(/"/g, '&quot;')
                   + '</pre>'
        },
        formatYYYY_MM_DD_HH_MM:function(v) {
            if (v == null || v == '')
                return '';
            return format(v.year, 4) + "-" + format(v.monthValue, 2) + "-" + format(v.dayOfMonth, 2) + " " + format(v.hour, 2) + ":" + format(v.minute, 2);
        },
        formatYYYY_MM_DD_HH_MM_SS:function(v) {
            if (v == null || v == '')
                return '';
            return format(v.year, 4) + "-" + format(v.monthValue, 2) + "-" + format(v.dayOfMonth, 2) + " " + format(v.hour, 2) + ":" + format(v.minute, 2) + ":" + format(v.second, 2);
        },
        checkSpecialChar:function(exclude, checkVal) {
            var regExpVar = "[\\\%_+\"`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]";
            if (typeof exclude !== 'undefined') {
                var excludeChars = exclude.split('||');
                for(var i = 0; i < excludeChars.length; i++) {
                    regExpVar = regExpVar.replace(excludeChars[i], '');
                }
            }
            var pattern = new RegExp(regExpVar);
            console.info(">>>%s", regExpVar);
            return (pattern.test(checkVal));
        },
        checkChar:function(checkVal) {
            return /[a-z]/i.test(checkVal);
        },
        checkPositiveInteger:function(checkVal) {
            return /^[0-9]+$/.test(checkVal);
        },
        checkPrice:function(checkVal) {
            return /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(checkVal);
        },
        includeNum:function(checkVal) {
            var flag = false;
            $.each(checkVal.split(''), function(i, item) {
                if (/^[0-9]+$/.test(item)) {
                    flag = true;
                    return false;
                }
            });
            return flag;
        },
        customError:function(e, alertMsg) {
            if (typeof alertMsg === 'undefined') {
                alertError(e, alertMsg);
                return;
            }
            if (alertMsg.search('{errorMsg}') == -1) {
                $alert(alertMsg);
                return;
            }
            alertError(e, alertMsg);
        },
        confirmDialog(msg, func) {
            var dlg = $confirm(msg, "提示", function() {
                func();
                dlg.close();
            });
        }
	};
}));

(function (root, factory) {
	'use strict';
	if (typeof define === 'function' && define.amd) {
		define(['jquery'], function ($) {
			return (root.cmcAjaxUtil = factory($));
		});
	}
	else {
		root.cmcAjaxUtil = root.cmcAjaxUtil || factory(root.jQuery);
	}
}(this, function ($) {
	'use strict';
    function customSuccess(result, alertMsg, callback) {
         if (!result) {
             $alert(alertMsg);
             return;
         }
         if (result.status !== 0) {
             $alert(result.msg);
             return;
         }
         callback(result.data);
    }

    function customError(e, alertMsg) {
        var errorMsgIdx = alertMsg.search('{errorMsg}');
        if (errorMsgIdx == -1) {
            $alert(alertMsg);
        } else {
           var errorMsg = e.responseJSON.message,
               exceptionMatch = 'FaultException:',
               exceptionMatchIdx = errorMsg.lastIndexOf(exceptionMatch);
           if (exceptionMatchIdx == -1)
               $alert(errorMsg);
           else
               $alert(alertMsg.replace('{errorMsg}', errorMsg.substr(exceptionMatchIdx + exceptionMatch.length)));
        }
    }

	return {
	    get:function(_url, param, alertMsg, callback) {
             $.ajax({
                 url: _url,
                 type: "GET",
                 data: param,
                 dataType: 'json',
                 success: function (result) {
                    customSuccess(result, alertMsg, callback)
                 }, error: function (e) {
                    customError(e, alertMsg);
                 }
             });
        },
		post:function(_url, param, alertMsg, callback, defaultAlert) {
             $.ajax({
                 url: _url,
                 type: "POST",
                 data: param,
                 dataType: 'json',
                 contentType:'application/x-www-form-urlencoded; charset=UTF-8',
                 success: function (result) {
                    if (typeof defaultAlert === 'undefined')
                        customSuccess(result, alertMsg, callback);
                    else
                        callback(result);
                 }, error: function (e) {
                    customError(e, alertMsg);
                 }
             });
        },
        postJson:function(_url, param, alertMsg, callback) {
             $.ajax({
                 url: _url,
                 type: "POST",
                 data: JSON.stringify(param),
                 dataType: 'json',
                 contentType: 'application/json;charset=utf-8',
                 success: function (result) {
                     callback(result);
                  }, error: function (e) {
                     customError(e, alertMsg);
                  }
             });
	    }
	}
}));
;(function($){
    var defaultOption = {
        type: "POST",
        url: "",
        data: {},
        success: function() {},
        error: function(XMLHttpRequest, textStatus, errorThrown) {
            alert("操作失败:msg=" + XMLHttpRequest.statusText);
        }
    };
    var cmcAjax = function(opt) {
        var setting = $.extend(defaultOption, opt);
        $.ajax(setting);
    };
    $.cmcAjax = cmcAjax;

    var cmcUtil = {
        confirmDialog:function(msg, func) {
            var dlg = $confirm(msg, "提示", function() {
                func();
                dlg.close();
            });
        },
        try_catch_chunk:function(func) {
            try {
                func();
            } catch(e) {
                $alert(e.message);
            }
        },
        toLocation:function($element) {
            $('html, body').animate({scrollTop: $element.offset().top}, 1000);
        }
    }
    $.cmcUtil = cmcUtil;

    var cmcValidateUtil = {
        format:function(date, pos) {
            return ("0000" + date).slice(pos * -1);
        },
        alertError:function(e, alertMsg) {
            var errorMsg = e.responseJSON.message,
                exceptionMatch = 'FaultException:',
                exceptionMatchIdx = errorMsg.lastIndexOf(exceptionMatch);
            if (exceptionMatchIdx === -1) {
               $alert(errorMsg);
               return;
            }
            errorMsg = errorMsg.substr(exceptionMatchIdx + exceptionMatch.length);
            typeof alertMsg === 'undefined' ? $alert(errorMsg) : $alert(alertMsg.replace('{errorMsg}', errorMsg));
        },
        lock:function(obj){
           if(!(obj instanceof jQuery)){
                  obj = $(obj);
           }
           obj.attr("lock","lock");
        },
        isLock:function(obj){
           if(!(obj instanceof jQuery)){
               obj = $(obj);
            }
           return obj.attr("lock") ==  "lock";
        },
        unLock:function(obj){
            if(!(obj instanceof jQuery)) {
                obj = $(obj);
            }
            obj.attr("lock","unlock");
        },
        encodeHtml:function(str) {
            return '<pre>' +
                        str.replace(/</g, '&lt;')
                           .replace(/>/g, '&gt;')
                           .replace(/'/g, '&#39;')
                           .replace(/"/g, '&quot;')
                   + '</pre>'
        },
        formatYYYY_MM_DD_HH_MM:function(v) {
            if (v == null || v == '')
                return '';
            return format(v.year, 4) + "-" + format(v.monthValue, 2) + "-" + format(v.dayOfMonth, 2) + " " + format(v.hour, 2) + ":" + format(v.minute, 2);
        },
        formatYYYY_MM_DD_HH_MM_SS:function(v) {
            if (v == null || v == '')
                return '';
            return format(v.year, 4) + "-" + format(v.monthValue, 2) + "-" + format(v.dayOfMonth, 2) + " " + format(v.hour, 2) + ":" + format(v.minute, 2) + ":" + format(v.second, 2);
        },
        checkSpecialChar:function(exclude, checkVal) {
            var regExpVar = "[\\\%_+\"`~!@#$^&*()=|{}':;',\\[\\].<>《》/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]";
            if (typeof exclude !== 'undefined') {
                var excludeChars = exclude.split('||');
                for(var i = 0; i < excludeChars.length; i++) {
                    regExpVar = regExpVar.replace(excludeChars[i], '');
                }
            }
            var pattern = new RegExp(regExpVar);
            console.info(">>>%s", regExpVar);
            return (pattern.test(checkVal));
        },
        checkChar:function(checkVal) {
            return /[a-z]/i.test(checkVal);
        },
        checkPositiveInteger:function(checkVal) {
            return /^[0-9]+$/.test(checkVal);
        },
        checkPrice:function(checkVal) {
            return /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(checkVal);
        },
        includeNum:function(checkVal) {
            var flag = false;
            $.each(checkVal.split(''), function(i, item) {
                if (/^[0-9]+$/.test(item)) {
                    flag = true;
                    return false;
                }
            });
            return flag;
        },
        customError:function(e, alertMsg) {
            if (typeof alertMsg === 'undefined') {
                alertError(e, alertMsg);
                return;
            }
            if (alertMsg.search('{errorMsg}') == -1) {
                $alert(alertMsg);
                return;
            }
            alertError(e, alertMsg);
        },
        confirmDialog(msg, func) {
            var dlg = $confirm(msg, "提示", function() {
                func();
                dlg.close();
            });
        }
    }
    $.cmcValidateUtil = cmcValidateUtil;

}($));
var commonExportModule = (function() {
    function exportFile($exportForm, $exportBtn, exportUrl, exportStatusUrl, downloadUrl) {
        $alert("文件正在后台生成，由于数据量过大，等待时间较长，请勿刷新页面或者离开该页面！", "提示");
        $.get(exportUrl)
            .error(function(e) {
                $alert("文件生成异常", "提示");
            })
            .success(function(exportResult) {
                var timer = setInterval(function () {
                        $.get(exportStatusUrl + "?id=" + exportResult.data)
                        .success(function(result) {
                            if (result.data.status == 1)
                                return;
                            if (result.data.status == 3) {
                                $alert("文件生成异常", "提示");
                                clearInterval(timer);
                                return;
                            }
                            clearInterval(timer);
                            $exportForm.find('input[name="id"]').val(result.data.id);
                            $exportForm.attr("action", downloadUrl);
                            $exportForm.submit();
                        })
                        .error(function(e) {
                            $alert("文件生成异常", "提示");
                            clearInterval(timer);
                        });
                    },
                    3000
                );
            });
    }
    function exportFileAttachParam($exportForm, $exportBtn, exportUrl, exportJsonParam, exportStatusUrl, downloadUrl) {
        $alert("文件正在后台生成，由于数据量过大，等待时间较长，请勿刷新页面或者离开该页面！", "提示");
//        $.post(exportUrl, exportJsonParam, function(){}, 'json')
        $.ajax({
            type:'POST',
            url: exportUrl,
            data: JSON.stringify(exportJsonParam),
            dataType: 'json',
            contentType : "application/json",
            async : false
        })
        .error(function(e) {
            $alert("文件生成异常", "提示");
        })
        .success(function(exportResult) {
            var timer = setInterval(function () {
                    $.get(exportStatusUrl + "?id=" + exportResult.data)
                    .success(function(result) {
                        if (result.data.status == 1)
                            return;
                        if (result.data.status == 3) {
                            $alert("文件生成异常", "提示");
                            clearInterval(timer);
                            return;
                        }
                        clearInterval(timer);
                        $exportForm.find('input[name="id"]').val(result.data.id);
                        $exportForm.attr("action", downloadUrl);
                        $exportForm.submit();
                    })
                    .error(function(e) {
                        $alert("文件生成异常", "提示");
                        clearInterval(timer);
                    });
                },
                3000
            );
        });
    }
    return {
        exportFile:exportFile,
        exportFileAttachParam:exportFileAttachParam
    }
}());
