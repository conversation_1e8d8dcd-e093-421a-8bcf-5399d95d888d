package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.admin.model.ResourceModel;
import cmc.pad.resource.admin.service.UserRankInfoService;
import cmc.pad.resource.application.command.CinemaResourceAppService;
import cmc.pad.resource.application.command.UpdateCinemaResourceCommand;
import cmc.pad.resource.application.query.CinemaResourceQueryService;
import cmc.pad.resource.application.query.InventoryQueryService;
import cmc.pad.resource.application.query.data.*;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.constant.Rank;
import cmc.portal.admin.service.facade.User;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.BasicFilter;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static mtime.lark.db.jsd.FilterType.IN;
import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/resource")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CinemaResourceController {

    private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private static final String REGION_CODE = "region_code";
    private final CinemaResourceAppService cinemaResourceAppService;
    private final CinemaResourceQueryService cinemaResourceQueryService;
    private final CinemaRepository cinemaRepository;
    private final InventoryQueryService inventoryQueryService;
    private final RegionService regionInfoService;
    private final UserRankInfoService userRankInfoService;

    /**
     * 跳转影城资源列表页面
     */
    @Authorize("cmc.pad.resource.resource.list")
    @RequestMapping(value = "toList", method = RequestMethod.GET)
    public ViewResult toList() {
        ViewResult viewResult = new ViewResult("/view/resource/list");
        viewResult.setMenuPath("/pad/resource/resource/toList");
        return viewResult;

    }

    /**
     * 影城资源查询
     */
    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<CinemaResourceData> list(ResourceModel.Query query) {
        CinemaResourceQueryParam queryParam = new CinemaResourceQueryParam();
        BeanUtils.copyProperties(query, queryParam);
        return this.cinemaResourceQueryService.queryCinemaResourceByPage(queryParam);
    }

    /**
     * 查询所有影城
     */
    @RequestMapping(value = "searchAllCinema", method = RequestMethod.POST)
    @ResponseBody
    public List<Cinema> searchAllCinema(String regionCode) {
        Filter filter = Filter.create();
        if (!"-1".equalsIgnoreCase(regionCode) && !Strings.isNullOrEmpty(regionCode)) {
            filter = filter.and(f("region_code", regionCode));
        }
        return this.cinemaRepository.findMany(filter);
    }

    /**
     * 查询所有影城
     */
    @RequestMapping(value = "searchAllCinemaByRank", method = RequestMethod.POST)
    @ResponseBody
    public List<Cinema> searchAllCinemaByRank(String parentId, boolean isEditPage) {
        log.info(">>>查询所有影城, parentId:{} isEditPage:{}", parentId, isEditPage);
        PointLocationModel.UserRankInfo userRankInfo = userRankInfoService.userRankInfo();
        log.info(">>>根据登录用户级别查询影城, user:{}", JSON.toJSONString(userRankInfo, true));
        BasicFilter f = f();
        if (userRankInfo.getRank() == Rank.CHAIN.value()) {
            if (!"-1".equalsIgnoreCase(parentId) && !Strings.isNullOrEmpty(parentId)) {
                f.add(REGION_CODE, parentId);
                return this.cinemaRepository.findMany(f);
            }
            String largeWardCode = userRankInfo.getLargeWardCode();
            if ("0".equals(largeWardCode) && !isEditPage) {//是院线时 影城条件不查询
                return Lists.newArrayList();
            }

            RegionDto.FindRegionsRequest request = new RegionDto.FindRegionsRequest();
            if ("0".equals(largeWardCode)) {//南北区院线管理员
                request.setLargeWards(Lists.newArrayList("1", "2"));
            } else {
                request.setLargeWard(largeWardCode);
            }
            RegionDto.FindRegionsResponse regions = regionInfoService.findRegions(request);
            List<String> regionIds = regions.getItems().stream().map(item -> item.getId()).collect(Collectors.toList());
            f.add(REGION_CODE, IN, regionIds.toArray(new String[regionIds.size()]));
            return this.cinemaRepository.findMany(f);

        }
        if (userRankInfo.getRank() == Rank.AREA.value()) {
            f.add(REGION_CODE, userRankInfo.getAreaCode());
            return this.cinemaRepository.findMany(f);
        }
        return Lists.newArrayList();
    }

    /**
     * 更新影院资源
     */
    @RequestMapping(value = "updateResource", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> update(HttpServletRequest request) {
        Map<String, Object> maps = new HashMap<>();
        User user = Users.currentUser();
        String cinemaCode = request.getParameter("cinemaCode");
        Float marketingPointLeasableArea = Float.parseFloat(request.getParameter("marketingPointLeasableArea"));
        Float outerAreaLeasableArea = Float.parseFloat(request.getParameter("outerAreaLeasableArea"));
        Float fixedPointLeasableArea;
        fixedPointLeasableArea = Float.parseFloat(request.getParameter("fixedPointLeasableArea"));
        Integer advertisingPointLeasableQuantity = Integer.parseInt(request.getParameter("advertisingPointLeasableQuantity"));
        UpdateCinemaResourceCommand command = new UpdateCinemaResourceCommand();
        command.setCinemaCode(cinemaCode);
        command.setAdvertisingPointLeasableQuantity(advertisingPointLeasableQuantity);
        command.setFixedPointLeasableArea(fixedPointLeasableArea);
        command.setOuterAreaLeasableArea(outerAreaLeasableArea);
        command.setMarketingPointLeasableArea(marketingPointLeasableArea);
        command.setUpdator(user.getUserName());
        try {
            cinemaResourceAppService.modifyCinemaResource(command);
            maps.put("ack", true);
        } catch (Exception e) {
            maps.put("ack", false);
            maps.put("msg", e.getMessage());
        }
        return maps;
    }

    /**
     * 影城资源更新记录列表查询
     */
    @ResponseBody
    @RequestMapping(value = "queryChangeRecordList", method = RequestMethod.POST)
    public List<ResourceChangeRecordData> queryChangeRecordList(String cinemaCode) {
        return this.cinemaResourceQueryService.queryResourceChangeRecordList(cinemaCode);
    }

    /**
     * 更新影院资源
     */
    @RequestMapping(value = "validateResource", method = RequestMethod.POST)
    @ResponseBody
    public String validateResource(HttpServletRequest request) {
        String cinemaCode = request.getParameter("cinemaCode");
        Float marketingPointLeasableArea = Float.parseFloat(request.getParameter("marketingPointLeasableArea"));
        Float outerAreaLeasableArea = Float.parseFloat(request.getParameter("outerAreaLeasableArea"));
        Float fixedPointLeasableArea;
        fixedPointLeasableArea = Float.parseFloat(request.getParameter("fixedPointLeasableArea"));
        Integer advertisingPointLeasableQuantity = Integer.parseInt(request.getParameter("advertisingPointLeasableQuantity"));
        List<ResourceSoldData> resourceSoldDataList = inventoryQueryService.queryEachTypeSoldAmountAfterEditDate(cinemaCode);
        for (ResourceSoldData data : resourceSoldDataList) {
            if (data.getAdvertisingPointLeasableQuantityHaveSoldAmount() > advertisingPointLeasableQuantity) {
                return "宣传点位个数小于" + sdf.format(data.getDate()) + "已售出的个数，确定保存？";
            }
            if (data.getFixedPointLeasableAreaHaveSoldAmount() > fixedPointLeasableArea) {
                return "固定点位面积小于" + sdf.format(data.getDate()) + "已售出的面积，确定保存？";
            }
            if (data.getMarketingPointLeasableAreaHaveSoldAmount() > marketingPointLeasableArea) {
                return "营销点位面积小于" + sdf.format(data.getDate()) + "已售出的面积，确定保存？";
            }
            if (data.getOuterAreaLeasableAreaHaveSoldAmount() > outerAreaLeasableArea) {
                return "外租区域面积小于" + sdf.format(data.getDate()) + "已售出的面积，确定保存？";
            }
        }
        return "success";
    }
}
