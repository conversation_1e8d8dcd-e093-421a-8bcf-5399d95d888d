package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.pad.resource.adapter.BusinessTypeServiceAdapter;
import cmc.pad.resource.admin.analyser.PointLocationAnalyser;
import cmc.pad.resource.admin.analyser.VerifyResult;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.model.AjaxResult;
import cmc.pad.resource.admin.model.FileModel;
import cmc.pad.resource.admin.service.UserRankInfoService;
import cmc.pad.resource.application.command.FileImportingAppService;
import cmc.pad.resource.application.command.point.PointLocationManageService;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.iface.AuthUserService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

import static cmc.pad.resource.domain.importing.FileCategory.POINT_LOCATION;
import static cmc.pad.resource.infrastructures.service.dictionary.DictionaryDomainServiceAdapter.RESOURCE_OWNER_SHIP_DICT_TYPE;

/**
 * Created by fuwei on 2022/1/11.
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/point_location")
public class PointLocationManageController extends BaseController {
    @Autowired
    private PointLocationManageService pointLocationManageService;
    @Autowired
    private BusinessTypeServiceAdapter businessTypeServiceAdapter;
    @Autowired
    private UserRankInfoService userRankInfoService;
    @Autowired
    private PointLocationAnalyser pointLocationAnalyser;

    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 1,
            20L, TimeUnit.MINUTES, new LinkedBlockingQueue<>());

    @Autowired
    PointLocationManageController(
            FileServiceFacade fileServiceFacade, FileImportingAppService fileImportingAppService,
            FileImportingQueryService fileImportingQueryService, DictionaryDomainService dictionaryService,
            AuthUserService authUserService, PointLocationAnalyser fileAnalyser) {
        super(fileServiceFacade, fileImportingAppService, fileImportingQueryService, dictionaryService, authUserService);
        threadPoolExecutor.allowCoreThreadTimeOut(true);
    }

    @RequestMapping(value = "page", method = RequestMethod.GET)
    @Authorize("cmc.pad.resource.point_location.page")
    public ViewResult page() {
        PointLocationModel.UserRankInfo userRankInfo = userRankInfoService.userRankInfo();
        ViewResult viewResult = new ViewResult("/view/point_location/list");
        Map<String, String> businessTypeMap = businessTypeServiceAdapter.pointLocationBusinessTypeMap();
        viewResult.addObject("businessType", businessTypeMap);
        viewResult.addObject("ownership", pointLocationManageService.dictMap(RESOURCE_OWNER_SHIP_DICT_TYPE));
        viewResult.addObject("largeWard", userRankInfo.getLargeWardCode());
        viewResult.addObject("areaCode", userRankInfo.getAreaCode());
        viewResult.addObject("cinemaInnerCode", userRankInfo.getCinemaInnerCode());
        viewResult.setMenuPath("/pad/resource/point_location/page");
        return viewResult;
    }

    @ResponseBody
    @RequestMapping(value = "save")
    public AjaxResult save(@RequestBody PointLocationModel.SaveParam param) {
        log.info(">>>保存点位信息, param:{}", JSON.toJSONString(param));
        param.setUserName(Users.currentUser().getUserName());
        pointLocationManageService.save(param);
        return AjaxResult.success();
    }

    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<PointLocationModel.ListDataParam> list(PointLocationModel.ListQueryParam param) {
        log.info(">>>点位信息列表, param:{}", JSON.toJSONString(param));
        param.setUserRankInfo(userRankInfoService.userRankInfo());
        log.info(">>>点位信息列表, param:{}", JSON.toJSONString(param));
        PageResult<PointLocationModel.ListDataParam> resultList = pointLocationManageService.list(param);
        return new PageResult<>(resultList.getItems(), resultList.getTotalCount());
    }

    @ResponseBody
    @RequestMapping(value = "detail", method = RequestMethod.GET)
    public AjaxResult<PointLocationModel.PointLocation> detail(int id) {
        log.info(">>>点位信息详情, id:{}", id);
        return AjaxResult.success(pointLocationManageService.get(id));
    }

    @ResponseBody
    @RequestMapping(value = "downList")
    public PageResult<FileModel.View> downList(int pageIndex, int pageSize) {
        log.info(">>>查询点位信息下载列表");
        PointLocationModel.UserRankInfo userRankInfo = userRankInfoService.userRankInfo();
        return fileRecordList(POINT_LOCATION,
                userRankInfo.getLargeWardCode(),
                userRankInfo.getAreaCode(),
                userRankInfo.getCinemaInnerCode(),
                pageIndex, pageSize);
    }

    @RequestMapping("file/import")
    @ResponseBody
    public Map<String, Object> importExcel(@RequestParam MultipartFile exFile) {
        log.info(">>>开始上传excel, name:{}", exFile.getOriginalFilename());
        Map<String, Object> result = new HashMap<>();
        if (existHanding(POINT_LOCATION)) {
            result.put("state", false);
            result.put("msg", "有上传中的任务正在执行,暂时不能上传");
            return result;
        }
        PointLocationModel.UserRankInfo userRankInfo = userRankInfoService.userRankInfo();
        int recordId = createImportRecord(POINT_LOCATION.value(), exFile.getOriginalFilename(), userRankInfo.getLargeWardCode(), userRankInfo.getAreaCode(), userRankInfo.getCinemaInnerCode());
        threadPoolExecutor.submit(new ImportTask(recordId, exFile, Users.currentUser().getUserName(), userRankInfo));
        result.put("state", true);
        return result;
    }

    class ImportTask implements Runnable {
        int recordId;
        MultipartFile exFile;
        String loginUserName;
        PointLocationModel.UserRankInfo userRankInfo;

        ImportTask(int recordId, MultipartFile exFile, String loginUserName, PointLocationModel.UserRankInfo userRankInfo) {
            this.exFile = exFile;
            this.recordId = recordId;
            this.loginUserName = loginUserName;
            this.userRankInfo = userRankInfo;
        }

        @Override
        public void run() {
            try {
                VerifyResult<PointLocationModel.ImportExcel> verifyResult = pointLocationAnalyser.verifyFile(exFile, userRankInfo);
                if (verifyResult.isResult()) {//成功导入
                    String fileId = fileServiceFacade.uploadFile(exFile.getBytes(), exFile.getOriginalFilename());
                    pointLocationManageService.batchSave(loginUserName, verifyResult.getList());
                    importCompleted(recordId, fileId);
                    log.info(">>>上传excel成功, name:{}", exFile.getOriginalFilename());
                } else {
                    //导入校验异常
                    String fileId = createExcelFileAndUpload(exFile.getOriginalFilename(), verifyResult.getList(), PointLocationModel.ImportExcel.class);
                    importFailed(recordId, fileId);
                    log.info(">>>excel数据异常, 导入数据失败, name:{}", exFile.getOriginalFilename());
                }
            } catch (Exception e) {
                log.error(">>>导入excel文件过程异常, name:{}", exFile.getOriginalFilename(), e);
                importFailed(recordId);
            }
        }
    }
}