package cmc.pad.resource.admin;

import cmc.admin.portal.web.integration.security.spring.PortalFormAuthenticationInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public class AdminFormAuthenticationInterceptor extends PortalFormAuthenticationInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        return request.getRequestURI().contains("/api/") || super.preHandle(request, response, handler);
    }
}
