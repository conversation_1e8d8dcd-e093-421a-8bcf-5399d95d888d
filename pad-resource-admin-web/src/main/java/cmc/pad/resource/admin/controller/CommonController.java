package cmc.pad.resource.admin.controller;

import cmc.pad.resource.admin.service.UserRankInfoService;
import com.google.common.collect.Lists;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by fuwei on 2022/7/12.
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/common")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonController {
    private final UserRankInfoService userRankInfoService;

    @RequestMapping(value = "getLargeWard", method = RequestMethod.POST)
    @ResponseBody
    public List<ResultData> getLargeWard() {
        String largeWardCode = userRankInfoService.userRankInfo().getLargeWardCode();
        if ("0".equals(largeWardCode)) {
            return Lists.newArrayList(
                    new ResultData("1", "北区"),
                    new ResultData("2", "南区")
            );
        }
        if ("1".equals(largeWardCode)) {
            return Lists.newArrayList(
                    new ResultData("1", "北区")
            );
        }
        if ("2".equals(largeWardCode)) {
            return Lists.newArrayList(
                    new ResultData("2", "南区")
            );
        }
        return Lists.newArrayList();
    }


    @Data
    @AllArgsConstructor
    public static class ResultData {
        private String code;
        private String name;
    }
}
