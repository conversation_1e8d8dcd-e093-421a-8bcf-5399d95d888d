package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.CinemaLevelModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class CinemaLevelAnalyser extends AbstractFileAnalyser<CinemaLevelModel.Excel> {
    @Autowired
    public CinemaLevelAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<CinemaLevelModel.Excel> verifyFile(MultipartFile file, Class<CinemaLevelModel.Excel> clazz) {
        List<CinemaLevelModel.Excel> dataList = analyseFile(file, clazz);
        dataList = dataList.stream().filter(excel ->
                StringUtils.isNotBlank(excel.getCode()) || StringUtils.isNotBlank(excel.getLevel())
        ).collect(Collectors.toList());
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        //获取所有影院内码
        List<String> cinemaCodes = allCinemaCodes();
        //获取所有影城级别
        List<String> cinemaLevelCodes = allCinemaLevelCodes();
        for (int i = 0; i < dataList.size(); i++) {
            CinemaLevelModel.Excel excel = dataList.get(i);
            String code = formatNumericalValue(excel.getCode().trim());
            excel.setCode(code);
            checkCinema(i + 1, cinemaCodes, code, reporter);
            String level = excel.getLevel().trim();
            checkCinemaLevel(i + 1, cinemaLevelCodes, level, reporter);
            String counterKey = code + ":" + level;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "影城编码+影城级别"));
        return getResult(dataList, reporter.toString());
    }
}
