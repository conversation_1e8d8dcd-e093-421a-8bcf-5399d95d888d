package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.pad.resource.admin.analyser.MovieHallNamingLeasingPriceAnalyser;
import cmc.pad.resource.admin.analyser.VerifyResult;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.admin.util.DateUtils;
import cmc.pad.resource.admin.util.MoneyUtils;
import cmc.pad.resource.application.command.FileImportingAppService;
import cmc.pad.resource.application.command.MovieHallNamingLeasingAppService;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.application.query.MovieHallNamingLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.MovieHallNamingQuery;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.price.MovieHallNamingPrice;
import cmc.pad.resource.domain.region.RegionDomainService;
import cmc.portal.admin.service.iface.AuthUserService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import mtime.lark.web.result.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Collections.EMPTY_LIST;

/**
 * 冠名厅刊例
 *
 * <AUTHOR>
 * @Date 2019/3/15 15:31
 * @Version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/price/mhn")
public class MovieHallNamingLeasingController extends BaseController {

    private final MovieHallNamingLeasingAppService appService;
    private final MovieHallNamingLeasingPriceQueryService queryService;
    private final RegionDomainService regionDomainService;
    private final CinemaQueryService cinemaQueryService;

    @Autowired
    MovieHallNamingLeasingController(FileServiceFacade fileServiceFacade,
                                     FileImportingAppService fileImportingAppService,
                                     FileImportingQueryService fileImportingQueryService,
                                     DictionaryDomainService dictionaryService,
                                     AuthUserService authUserService,
                                     MovieHallNamingLeasingPriceAnalyser priceAnalyser,
                                     MovieHallNamingLeasingAppService appService,
                                     MovieHallNamingLeasingPriceQueryService queryService,
                                     RegionDomainService regionDomainService,
                                     CinemaQueryService cinemaQueryService) {
        super(fileServiceFacade, fileImportingAppService, fileImportingQueryService, priceAnalyser, dictionaryService, authUserService);
        this.appService = appService;
        this.queryService = queryService;
        this.regionDomainService = regionDomainService;
        this.cinemaQueryService = cinemaQueryService;
    }


    @RequestMapping(value = "page", method = RequestMethod.GET)
    @Authorize("pad.resource.price.mhn.page")
    public ViewResult page() {
        ViewResult viewResult = new ViewResult("/view/price/moviehallname/list");
        viewResult.setMenuPath("/pad/resource/price/mhn/page");
        return viewResult;
    }

    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<PriceModel.MovieHallNaming> list(PriceModel.MovieHallNamingQuery query) {
        PageResult<PriceModel.MovieHallNaming> pageResult = new PageResult<>();
        MovieHallNamingQuery priceQuery = new MovieHallNamingQuery();
        priceQuery.setPageIndex(query.getPageIndex());
        priceQuery.setPageSize(query.getPageSize());
        priceQuery.setCinemaCode(query.getCinemaCode());
        priceQuery.setMovieHallType(query.getMovieHallType());
        PageResult<MovieHallNamingPrice> result = queryService.effectivePage(priceQuery);
        int totalCount = result.getTotalCount();
        pageResult.setTotalCount(totalCount);
        if (totalCount > 0) {
            Map<String, String> movieHallTypeDict = dictionaryService.allMovieHallTypeDict();
            Map<String, String> regionDict = regionDomainService.allRegionDict();
            Map<String, Cinema> cinemaDict = allCinemaDict();
            List<MovieHallNamingPrice> items = result.getItems();
            List<PriceModel.MovieHallNaming> viewList = items.stream().map(p -> dtoConvertView(p, null, cinemaDict, regionDict, movieHallTypeDict)).collect(Collectors.toList());
            pageResult.setItems(viewList);
        }
        return pageResult;
    }

    @ResponseBody
    @RequestMapping(value = "records", method = RequestMethod.POST)
    public List records(String cinemaCode, String movieHallType) {
        List<MovieHallNamingPrice> list = queryService.list(cinemaCode, movieHallType);
        if (Objects.nonNull(list) && !list.isEmpty()) {
            LocalDate latestEffectiveDate = queryService.latestEffectiveDate();
            Map<String, String> movieHallTypeDict = dictionaryService.allMovieHallTypeDict();
            Map<String, Cinema> cinemaDict = allCinemaDict();
            Map<String, String> regionDict = regionDomainService.allRegionDict();
            return list.stream().map(p -> dtoConvertView(p, latestEffectiveDate, cinemaDict, regionDict, movieHallTypeDict)).collect(Collectors.toList());
        }
        return EMPTY_LIST;
    }

    private PriceModel.MovieHallNaming dtoConvertView(MovieHallNamingPrice price, LocalDate currentEffectiveDate, Map<String, Cinema> cinemaDict, Map<String, String> regionDict, Map<String, String> movieHallTypeDict) {
        PriceModel.MovieHallNaming view = new PriceModel.MovieHallNaming();
        BeanUtils.copyProperties(price, view);
        String cinemaCode = price.getCinemaCode();
        Cinema cinema = cinemaDict.get(cinemaCode);
        String cinemaName = cinema.getName();
        String regionId = cinema.getRegionCode();
        String regionName = regionDict.get(regionId);
        view.setCinemaCode(cinemaCode);
        view.setCinemaName(cinemaName);
        view.setRegionName(regionName);
        String movieHallType = price.getMovieHallType();
        view.setMovieHallType(movieHallType);
        String movieHallTypeView = movieHallTypeDict.get(movieHallType);
        view.setMovieHallTypeView(movieHallTypeView);
        String unitPrice = MoneyUtils.centConvertYuan(price.getUnitPrice());
        view.setUnitPrice(unitPrice);
        PriceModel.PriceStatus status = determineStatus(currentEffectiveDate, price.getEffectiveDate());
        view.setStatus(status);
        return view;
    }

    private Map<String, Cinema> allCinemaDict() {
        return cinemaQueryService.queryAllCinema().stream().collect(Collectors.toMap(Cinema::getCode, o -> o));
    }

    /**
     * 文件上传
     */
    @RequestMapping("file/import")
    @ResponseBody
    public Map<String, Object> importExcel(@RequestParam MultipartFile exFile,
                                           @RequestParam(name = "version", required = false, defaultValue = "") String version,
                                           @RequestParam(name = "effective_date", required = false, defaultValue = "") String date) {
        Map<String, Object> result = new HashMap<>();
        //检查生效日期
        LocalDate effectiveDate = DateUtils.strToLocalDate(date);
        boolean duplicate = checkDuplicateFile(FileCategory.NAMED_MOVIE_HALL_LEASING_PRICE, effectiveDate);
        if (duplicate) {
            result.put("state", false);
            result.put("msg", "已存在该生效日期的文件，请勿重复上传");
            return result;
        }
        int recordId = uploadExcel(exFile, FileCategory.NAMED_MOVIE_HALL_LEASING_PRICE.value(), version, date);
        //校验数据
        try {
            VerifyResult<PriceModel.MovieHallNamingExcel> verifyResult = fileAnalyser.verifyFile(exFile, PriceModel.MovieHallNamingExcel.class);
            if (verifyResult.isResult()) {
                Integer userId = currentUserId();
                List<PriceModel.MovieHallNamingExcel> list = verifyResult.getList();
                List<MovieHallNamingPrice> priceList = list.stream().map(e -> {
                    MovieHallNamingPrice price = new MovieHallNamingPrice();
                    price.setCinemaCode(e.getCinemaCode());
                    price.setMovieHallType(e.getMovieHallType());
                    price.setUnitPrice(MoneyUtils.yuanConvertCent(e.getUnitPrice()));
                    price.setUpdater(userId);
                    price.setUpdateTime(LocalDateTime.now());
                    price.setEffectiveDate(effectiveDate);
                    price.setImportId(recordId);
                    return price;
                }).collect(Collectors.toList());
                appService.importData(priceList);
                importCompleted(recordId);
                result.put("state", true);
            } else {
                importFailed(recordId);
                result.put("state", false);
                result.put("msg", verifyResult.getError());
            }
        } catch (Exception e) {
            result.put("state", false);
            importFailed(recordId);
            result.put("msg", "上传失败");
            return result;
        }
        return result;
    }

    @RequestMapping("demo")
    public void download(HttpServletRequest request,
                         HttpServletResponse response) {
        String name = "冠名厅刊例导入模版.xlsx";
        byte[] bytes = generateDemo(name, Collections.singletonList(new PriceModel.MovieHallNamingExcel()), PriceModel.MovieHallNamingExcel.class);
        transferFile(name, request, response, bytes);
    }

    @ResponseBody
    @RequestMapping(value = "file/list", method = RequestMethod.POST)
    public List fileList() {
        return fileRecordList(FileCategory.NAMED_MOVIE_HALL_LEASING_PRICE);
    }

    /**
     * 导出资源库存
     */
    @ResponseBody
    @RequestMapping(value = "discard")
    public AjaxResult discard(@RequestParam("id") Integer id) {
        AjaxResult result = new AjaxResult();
        FileImport record = getFileImportRecord(id);
        if (Objects.nonNull(record) && LocalDate.now().isBefore(record.getEffectiveDate())) {
            appService.discard(id);
            deleteFile(id);
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }
        return result;
    }
}
