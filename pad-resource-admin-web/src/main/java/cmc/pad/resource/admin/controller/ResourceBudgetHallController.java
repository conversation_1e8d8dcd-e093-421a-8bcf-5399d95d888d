package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.pad.resource.admin.analyser.BudgetHallAnalyser;
import cmc.pad.resource.admin.analyser.VerifyResult;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.mapper.ResourceBudgetMapper;
import cmc.pad.resource.admin.model.BudgetExcelModel;
import cmc.pad.resource.admin.model.BudgetHallParam;
import cmc.pad.resource.application.command.*;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.domain.budget.ResourceBudgetHallChangeRecord;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImportStatus;
import cmc.portal.admin.service.facade.User;
import cmc.portal.admin.service.iface.AuthUserService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022/1/10 9:24
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/budget/hall")
public class ResourceBudgetHallController extends BaseController {

    @Autowired
    private FileServiceFacade fileServiceFacade;

    @Autowired
    private FileImportingAppService fileImportingAppService;

    @Autowired
    private BudgetHallAnalyser budgetHallAnalyser;

    @Autowired
    private ResourceBudgetHallAppService resourceBudgetHallAppService;

    @Autowired
    ResourceBudgetHallController(FileServiceFacade fileServiceFacade, FileImportingAppService fileImportingAppService, FileImportingQueryService fileImportingQueryService, BudgetHallAnalyser fileAnalyser, DictionaryDomainService dictionaryService, AuthUserService authUserService) {
        super(fileServiceFacade, fileImportingAppService, fileImportingQueryService, fileAnalyser, dictionaryService, authUserService);
    }

    @Authorize("cmc.pad.resource.budget.hall.list")
    @RequestMapping(value = "list", method = RequestMethod.GET)
    public ViewResult list() {
        Map<String, Object> data = new HashMap<>();
        data.put("currentYear", LocalDate.now().getYear());
        ViewResult viewResult = new ViewResult("/view/budget/hall/list", data);
        viewResult.addEnum(FileImportStatus.class, true);
        viewResult.setMenuPath("/pad/resource/budget/hall/list");
        return viewResult;

    }

    @RequestMapping(value = "list_hall")
    @ResponseBody
    public PageResult<ResourceBudgetHall> listHall(String year, String regionCode, String cinemaInnerCode, String resourceType, int pageSize, int pageIndex) {
        PageResult<ResourceBudgetHall> result = resourceBudgetHallAppService.getPageList(year, regionCode, cinemaInnerCode, resourceType, pageSize, pageIndex);
        return result;
    }

    @RequestMapping(value = "list_hall_record")
    @ResponseBody
    public List<ResourceBudgetHallChangeRecord> listHallRecord(String year, String regionCode, String cinemaInnerCode, String resourceType) {
        List<ResourceBudgetHallChangeRecord> result = resourceBudgetHallAppService.getChangeRecordList(year, regionCode, cinemaInnerCode, resourceType);
        return result;
    }

    @RequestMapping("upload")
    @ResponseBody
    public Map<String, Object> importExcel(@RequestParam MultipartFile exFile, @RequestParam int year, @RequestParam int category) {
        Map<String, Object> result = new HashMap<>();
        if (year == 0) {
            log.error("预算年份未选择!");
            result.put("state", false);
            result.put("msg", "预算年份未选择");
            return result;
        }
        if (category != FileCategory.BUDGET_HALL.value()) {
            log.error("文件导入选择的业务分类异常（category）!");
            result.put("state", false);
            result.put("msg", "文件导入选择的业务分类异常");
            return result;
        }
        User user = Users.currentUser();
        FileCategory fileCategory = FileCategory.valueOf(category);
        int recordId = 0;
        //1校验数据，2有错误写错误信息，3，上传文件服务器，4数据导入数据库
        Map<String, Cinema> cinemaMap = budgetHallAnalyser.getAllCinemas();
        //上传到文件服务器
        try {
            String filename = exFile.getOriginalFilename();
            //上传文件
            String fileId = fileServiceFacade.uploadFile(exFile.getBytes(), filename);
            //保存上传记录
            SaveImportCommand command = new SaveImportCommand();
            command.setFileId(fileId);
            command.setFileName(filename);
            command.setVersion(String.valueOf(System.nanoTime()));
            command.setEffectiveDate(LocalDate.now());
            command.setImporter(user.getId());
            command.setFileCategory(fileCategory);
            recordId = fileImportingAppService.importStarted(command);
        } catch (IOException e) {
            log.error("文件导入失败!:{}", e);
            result.put("state", false);
            result.put("msg", "文件导入失败");
        }
        //校验数据
        VerifyResult<BudgetExcelModel.Hall> verifyResult = new VerifyResult<>();
        verifyResult.setResult(false);
        verifyResult.setError("文件解析失败");
        try {
            verifyResult = budgetHallAnalyser.verifyFile(exFile, BudgetExcelModel.Hall.class, cinemaMap);
            if (verifyResult.isResult()) {
                fileImportingAppService.importCompleted(recordId);
                result.put("state", true);
            } else {
                fileImportingAppService.importFailed(recordId, verifyResult.getError());
                result.put("state", false);
                result.put("msg", verifyResult.getError());
            }
        } catch (Exception e) {
            fileImportingAppService.importFailed(recordId, verifyResult.getError());
            result.put("state", false);
            result.put("msg", "上传失败");
        }

        //导入数据
        if (verifyResult.isResult() && !CollectionUtils.isEmpty(verifyResult.getList())) {
            resourceBudgetHallAppService.importData(ResourceBudgetMapper.getBudgetHallsByExcel(verifyResult.getList(), String.valueOf(year), recordId, cinemaMap, user));
        }
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "file/list", method = RequestMethod.POST)
    public List fileList() {
        return fileRecordList(FileCategory.BUDGET_HALL);
    }

    @RequestMapping(value = "modify_hall")
    @ResponseBody
    public Map<String, Object> modifyBudget(BudgetHallParam param) {
        Map<String, Object> result = new HashMap<>();
        ResourceBudgetHall hall = resourceBudgetHallAppService.get(param.getId());
        if (hall == null) {
            result.put("state", false);
            result.put("msg", "没有这条数据");
            return result;
        }
        User user = Users.currentUser();
        hall.setUserName(user.getUserName());
        hall.setUpdateTime(LocalDateTime.now());
        hall.setUserId(user.getId());
        hall.setBudgetMonth1(param.getMonth1());
        hall.setBudgetMonth2(param.getMonth2());
        hall.setBudgetMonth3(param.getMonth3());
        hall.setBudgetMonth4(param.getMonth4());
        hall.setBudgetMonth5(param.getMonth5());
        hall.setBudgetMonth6(param.getMonth6());
        hall.setBudgetMonth7(param.getMonth7());
        hall.setBudgetMonth8(param.getMonth8());
        hall.setBudgetMonth9(param.getMonth9());
        hall.setBudgetMonth10(param.getMonth10());
        hall.setBudgetMonth11(param.getMonth11());
        hall.setBudgetMonth12(param.getMonth12());
        hall.setBudgetYear(hall.getBudgetMonth1().add(hall.getBudgetMonth2()).add(hall.getBudgetMonth3()).add(hall.getBudgetMonth4()).add(hall.getBudgetMonth5()).add(hall.getBudgetMonth6()).add(hall.getBudgetMonth7()).add(hall.getBudgetMonth8()).add(hall.getBudgetMonth9()).add(hall.getBudgetMonth10()).add(hall.getBudgetMonth11()).add(hall.getBudgetMonth12()));
        resourceBudgetHallAppService.editData(hall);
        result.put("state", true);
        result.put("msg", "修改成功");
        return result;
    }

}
