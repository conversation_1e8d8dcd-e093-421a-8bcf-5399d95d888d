package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.security.Users;
import cmc.pad.resource.admin.analyser.AbstractFileAnalyser;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.model.FileModel;
import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.application.command.FileImportingAppService;
import cmc.pad.resource.application.command.SaveImportCommand;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.importing.*;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.dto.AuthUserDto;
import cmc.portal.admin.service.iface.AuthUserService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.SortType;
import mtime.lark.db.jsd.Sorters;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.lang.FaultException;
import mx.common.excel.ExportExcel;
import mx.common.excel.bean.XBeanExport;
import mx.common.excel.bean.XBeanSheet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
abstract public class BaseController {
    final AbstractFileAnalyser fileAnalyser;
    final DictionaryDomainService dictionaryService;
    protected final FileServiceFacade fileServiceFacade;
    private final FileImportingAppService fileImportingAppService;
    private final FileImportingQueryService fileImportingQueryService;
    private final AuthUserService authUserService;


    BaseController(FileServiceFacade fileServiceFacade,
                   FileImportingAppService fileImportingAppService,
                   FileImportingQueryService fileImportingQueryService,
                   AbstractFileAnalyser fileAnalyser,
                   DictionaryDomainService dictionaryService,
                   AuthUserService authUserService) {
        this.fileServiceFacade = fileServiceFacade;
        this.fileImportingAppService = fileImportingAppService;
        this.fileImportingQueryService = fileImportingQueryService;
        this.fileAnalyser = fileAnalyser;
        this.dictionaryService = dictionaryService;
        this.authUserService = authUserService;
    }

    BaseController(FileServiceFacade fileServiceFacade,
                   FileImportingAppService fileImportingAppService,
                   FileImportingQueryService fileImportingQueryService,
                   DictionaryDomainService dictionaryService,
                   AuthUserService authUserService) {
        this.fileServiceFacade = fileServiceFacade;
        this.fileImportingAppService = fileImportingAppService;
        this.fileImportingQueryService = fileImportingQueryService;
        this.dictionaryService = dictionaryService;
        this.authUserService = authUserService;
        this.fileAnalyser = null;
    }

    FileImport getFileImportRecord(int id) {
        return fileImportingQueryService.get(id);
    }

    /**
     * 文件上传记录
     *
     * @param fileCategory 文件类型
     * @return 文件上传记录列表
     */
    List fileRecordList(FileCategory fileCategory) {
        List<FileImport> list = fileImportingQueryService.list(fileCategory);
        return transferFileImportRecord(fileCategory, list);
    }

    PageResult<FileModel.View> fileRecordList(FileCategory fileCategory, String largeWardCode, String regionCode, String cinemaInnerCode, int pageIndex, int pageSize) {
        PageResult<FileImport> page = fileImportingQueryService.page(
                fileCategory, largeWardCode, regionCode, cinemaInnerCode,
                new Sorters(SortType.DESC, "id"),
                pageSize,
                pageIndex);
        List<FileModel.View> viewList = transferFileImportRecord(fileCategory, page.getItems());
        return new PageResult(viewList, page.getTotalCount());
    }

    List<FileModel.View> transferFileImportRecord(FileCategory fileCategory, List<FileImport> list) {
        List<Integer> userIds = new ArrayList<>();
        for (FileImport fileImport : list) {
            userIds.add(fileImport.getImporter());
        }
        List resultList = Collections.EMPTY_LIST;
        Map usersMap = getUsersMap(userIds);
        if (Objects.nonNull(list) && !list.isEmpty()) {
            resultList = list.stream().map(fileImport -> {
                FileModel.View view = new FileModel.View();
                BeanUtils.copyProperties(fileImport, view);
                FileImportStatus status = fileImport.getStatus();
                view.setStatus(status.value());
                view.setStatusView(status.displayName());
                view.setImporter((String) usersMap.get(fileImport.getImporter()));
                if (org.springframework.util.StringUtils.isEmpty(fileImport.getRemark())) {
                    view.setStatusRemarkView(status.displayName());
                } else {
                    view.setStatusRemarkView(status.displayName() + ":" + fileImport.getRemark());
                }
                //判断有效期
                if (!FileCategory.CINEMA_LEVEL.equals(fileCategory)
                        && FileImportStatus.SUCCEEDED.equals(status)
                        && LocalDate.now().isBefore(fileImport.getEffectiveDate())) {
                    view.setCanDiscard(true);
                }
                return view;
            }).collect(Collectors.toList());
        }
        return resultList;
    }

    boolean checkDuplicateFile(FileCategory fileCategory, LocalDate effectiveDate) {
        return fileImportingQueryService.checkDuplicateFile(fileCategory, effectiveDate);
    }

    int uploadExcel(MultipartFile file, Integer category) {
        return uploadExcel(file, category, "", "");
    }


    void deleteFile(int recordId) {
        fileImportingAppService.discardFile(recordId);
    }

    public String createExcelFileAndUpload(String fileName, List list, Class clazz) throws IOException {
        XBeanExport xBean = ExportExcel.BeanExport(clazz);
        xBean.createBeanSheet(fileName.substring(0, fileName.indexOf(".")), null, PointLocationModel.ImportExcel.class).addData(list);
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            xBean.write(stream);
            xBean.dispose();
            return fileServiceFacade.uploadFile(stream.toByteArray(), fileName);
        }
    }

    boolean existHanding(FileCategory category) {
        return !fileImportingQueryService.list(category, FileImportStatus.UNDERWAY).isEmpty();
    }

    int createImportRecord(Integer category, String fileName) {
        FileCategory fileCategory = FileCategory.valueOf(category);
        //上传到文件服务器
        SaveImportCommand command = new SaveImportCommand();
        command.setFileId("");
        command.setFileName(fileName);
        command.setEffectiveDate(LocalDate.now());
        Integer userId = currentUserId();
        command.setImporter(userId);
        command.setFileCategory(fileCategory);
        command.setVersion("1");
        return fileImportingAppService.importStarted(command);
    }

    int createImportRecord(Integer category, String fileName, String largeWardCode, String regionCode, String cinemaInnerCode) {
        FileCategory fileCategory = FileCategory.valueOf(category);
        //上传到文件服务器
        SaveImportCommand command = new SaveImportCommand();
        command.setFileId("");
        command.setFileName(fileName);
        command.setEffectiveDate(LocalDate.now());
        Integer userId = currentUserId();
        command.setImporter(userId);
        command.setFileCategory(fileCategory);
        command.setVersion("1");
        command.setLargeWardCode(largeWardCode);
        command.setRegionCode(regionCode);
        command.setCinemaInnerCode(cinemaInnerCode);
        return fileImportingAppService.importStarted(command);
    }

    /**
     * 文件上传
     *
     * @param file     文件
     * @param category 上传文件类型
     * @param version  版本信息
     * @param date     生效日期
     * @return 文件记录ID
     */
    int uploadExcel(MultipartFile file, Integer category, String version, String date) {
        int recordId = 0;
        FileCategory fileCategory = FileCategory.valueOf(category);
        //上传到文件服务器
        try {
            String filename = file.getOriginalFilename();
            //上传文件
            String fileId = fileServiceFacade.uploadFile(file.getBytes(), filename);
            //保存上传记录
            SaveImportCommand command = new SaveImportCommand();
            command.setFileId(fileId);
            command.setFileName(filename);
            command.setVersion(version);
            LocalDate effectiveDate = StringUtils.isBlank(date) ? LocalDate.now() : LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            command.setEffectiveDate(effectiveDate);
            Integer userId = currentUserId();
            command.setImporter(userId);
            command.setFileCategory(fileCategory);
            recordId = fileImportingAppService.importStarted(command);
        } catch (IOException e) {
            log.error("文件导入失败!:{}", e);
        }
        return recordId;
    }


    void importCompleted(int recordId) {
        fileImportingAppService.importCompleted(recordId);
    }

    void importCompleted(int recordId, String fileId) {
        fileImportingAppService.importCompleted(recordId, fileId);
    }

    void importFailed(int recordId) {
        fileImportingAppService.importFailed(recordId);
    }

    void importFailed(int recordId, String fileId) {
        fileImportingAppService.importFailedFiled(recordId, fileId);
    }

    /**
     * 下载
     *
     * @param fileId   文件ID
     * @param filename 文件名
     */
    @RequestMapping("download")
    public void download(@RequestParam("fileId") String fileId,
                         @RequestParam("filename") String filename,
                         HttpServletRequest request,
                         HttpServletResponse response) {
        byte[] fileContent = fileServiceFacade.downloadFile(fileId);
        transferFile(filename, request, response, fileContent);
    }

    void transferFile(String filename, HttpServletRequest request, HttpServletResponse response, byte[] fileContent) {
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String header = request.getHeader("User-Agent");
        try {
            if (header.toUpperCase().contains("MSIE") || header.contains("Trident")) {
                filename = URLEncoder.encode(filename, "UTF-8");
            } else {
                filename = new String(filename.getBytes(), "ISO8859-1");
            }
        } catch (UnsupportedEncodingException e) {
            throw new FaultException("不支持的编码格式:{}", e);
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        try (BufferedInputStream bis = new BufferedInputStream(new ByteArrayInputStream(fileContent));
             BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream())) {
            byte[] buff = new byte[5120];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
                bos.flush();
            }
        } catch (final IOException e) {
            throw new FaultException("文件{}下载失败：{}", filename, e);
        }
    }

    /**
     * 生成模版
     *
     * @param name  模版名
     * @param data  模版数据
     * @param clazz 模版类型
     * @return 模版文件字节流
     */
    <T> byte[] generateDemo(String name, List<T> data, Class<T> clazz) {
        //创建xBean
        XBeanExport xbean = ExportExcel.BeanExport(clazz);
        XBeanSheet beanSheet = xbean.createBeanSheet(name, "", clazz);
        beanSheet.addData(data);
        try (ByteArrayOutputStream arrayStream = new ByteArrayOutputStream()) {
            xbean.write(arrayStream);
            return arrayStream.toByteArray();
        } catch (IOException e) {
            throw new FaultException("文件生成失败:{}", e);
        }
    }

    Integer currentUserId() {
        if (Users.currentUser() == null)
            return -1;
        return Users.currentUser().getId();
    }


    PriceModel.PriceStatus determineStatus(LocalDate currentEffectiveDate, LocalDate effectiveDate) {
        LocalDate today = LocalDate.now();
        if (Objects.isNull(currentEffectiveDate)) {
            return PriceModel.PriceStatus.PENDING_EFFECTIVE;
        }
        if (currentEffectiveDate.equals(effectiveDate)) {
            return PriceModel.PriceStatus.EFFECTIVE;
        }
        if (today.isBefore(effectiveDate)) {
            return PriceModel.PriceStatus.PENDING_EFFECTIVE;
        }
        if (today.isAfter(effectiveDate)) {
            return PriceModel.PriceStatus.EXPIRED;
        }
        return PriceModel.PriceStatus.UNDETERMINED;
    }

    /**
     * 查询用户列表并封装成map
     */
    private Map getUsersMap(List<Integer> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return null;
        }
        AuthUserDto.FindAuthUserListRequest request = new AuthUserDto.FindAuthUserListRequest();
        request.setIds(userIds);
        List<AuthUserDto.AuthUser> list = authUserService.findAuthUserList(request).getItems();
        Map map = new HashMap();
        if (list == null) return map;
        list.forEach(authUser -> map.put(authUser.getId(), authUser.getRealName()));
        return map;
    }
}

