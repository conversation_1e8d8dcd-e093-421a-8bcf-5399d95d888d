package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.pad.resource.admin.analyser.CinemaLevelAnalyser;
import cmc.pad.resource.admin.analyser.VerifyResult;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.model.CinemaLevelModel;
import cmc.pad.resource.application.command.CinemaLevelAppService;
import cmc.pad.resource.application.command.FileImportingAppService;
import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import cmc.pad.resource.application.query.data.CinemaLevelQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.portal.admin.service.iface.AuthUserService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 影城级别
 *
 * <AUTHOR>
 * @Date 2019/3/15 12:51
 * @Version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/cinema/level")
public class CinemaLevelController extends BaseController {
    private final CinemaLevelAppService cinemaLevelAppService;
    private final CinemaLevelQueryService cinemaLevelQueryService;

    @Autowired
    CinemaLevelController(FileServiceFacade fileServiceFacade,
                          FileImportingAppService fileImportingAppService,
                          FileImportingQueryService fileImportingQueryService,
                          DictionaryDomainService dictionaryService,
                          AuthUserService authUserService,
                          CinemaLevelAnalyser cinemaLevelAnalyser,
                          CinemaLevelAppService cinemaLevelAppService,
                          CinemaLevelQueryService cinemaLevelQueryService) {
        super(fileServiceFacade, fileImportingAppService, fileImportingQueryService, cinemaLevelAnalyser, dictionaryService, authUserService);
        this.cinemaLevelAppService = cinemaLevelAppService;
        this.cinemaLevelQueryService = cinemaLevelQueryService;
    }

    @RequestMapping(value = "page", method = RequestMethod.GET)
    @Authorize("pad.resource.cinema.level.page")
    public ViewResult page() {
        ViewResult viewResult = new ViewResult("/view/cinema/list");
        viewResult.setMenuPath("/pad/resource/cinema/level/page");
        return viewResult;
    }

    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<CinemaLevelModel.View> list(CinemaLevelModel.Query query) {
        PageResult<CinemaLevelModel.View> pageResult = new PageResult<>();
        Map<String, String> cinemaLevelDict = dictionaryService.allCinemaLevelDict();
        Map<String, String> cityDistrictLevelDict = dictionaryService.allCityLevelDict();
        CinemaLevelQuery cquery = new CinemaLevelQuery();
        BeanUtils.copyProperties(query, cquery);
        PageResult<CinemaLevelInfo> result = cinemaLevelQueryService.queryCinema(cquery);
        int totalCount = result.getTotalCount();
        pageResult.setTotalCount(totalCount);
        List<CinemaLevelInfo> items = result.getItems();
        if (totalCount > 0) {
            List<CinemaLevelModel.View> viewList = items.stream().map(v -> {
                CinemaLevelModel.View view = new CinemaLevelModel.View();
                BeanUtils.copyProperties(v, view);
                view.setCityDistrictLevel(cityDistrictLevelDict.get(v.getCityDistrictLevel()));
                String cinemaLevel = v.getCinemaLevel();
                view.setCinemaLevel(cinemaLevelDict.get(cinemaLevel));
                return view;
            }).collect(Collectors.toList());
            pageResult.setItems(viewList);
        }
        return pageResult;
    }


    @RequestMapping("file/import")
    @ResponseBody
    public Map<String, Object> importExcel(@RequestParam MultipartFile exFile) {
        Map<String, Object> result = new HashMap<>();
        int recordId = uploadExcel(exFile, FileCategory.CINEMA_LEVEL.value());
        //校验数据
        try {
            VerifyResult<CinemaLevelModel.Excel> verifyResult = fileAnalyser.verifyFile(exFile, CinemaLevelModel.Excel.class);
            if (verifyResult.isResult()) {
                List<CinemaLevelModel.Excel> list = verifyResult.getList();
                List<cmc.pad.resource.domain.cinema.CinemaLevel> cinemaLevels = list.stream().map(e -> {
                    cmc.pad.resource.domain.cinema.CinemaLevel cl = new cmc.pad.resource.domain.cinema.CinemaLevel();
                    cl.setInnerCode(e.getCode().trim());
                    cl.setLevel(e.getLevel().trim());
                    cl.setImportId(recordId);
                    return cl;
                }).collect(Collectors.toList());
                cinemaLevelAppService.importData(cinemaLevels);
                importCompleted(recordId);
                result.put("state", true);
            } else {
                importFailed(recordId);
                result.put("state", false);
                result.put("msg", verifyResult.getError());
            }
        } catch (Exception e) {
            importFailed(recordId);
            result.put("state", false);
            result.put("msg", "上传失败");
        }

        return result;
    }

    @RequestMapping("demo")
    public void download(HttpServletRequest request,
                         HttpServletResponse response) {
        String name = "影城级别导入模版.xlsx";
        byte[] bytes = generateDemo(name, Collections.singletonList(new CinemaLevelModel.Excel()), CinemaLevelModel.Excel.class);
        transferFile(name, request, response, bytes);
    }

    @ResponseBody
    @RequestMapping(value = "file/list", method = RequestMethod.POST)
    public List fileList() {
        return fileRecordList(FileCategory.CINEMA_LEVEL);
    }
}
