package cmc.pad.resource.admin.controller;

import cmc.location.front.service.dto.CityDto;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictEntry;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Controller
@RequestMapping(value = "pad/dict")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DictionaryController {

    private final DictionaryDomainService dictionaryService;
    private final CinemaQueryService cinemaQueryService;

    /**
     * 城市级别
     */
    @ResponseBody
    @RequestMapping(value = "city/levels")
    public List<DictEntry.CityLevel> cityLevels() {
        List<DictEntry.CityLevel> list = dictionaryService.allCityLevel();
        list.add(0, new DictEntry.CityLevel("0", "全部"));
        return list;
    }

    /**
     * 所属城市
     */
    @ResponseBody
    @RequestMapping(value = "city")
    public List<DictEntry.Data> city(String superior) {
        log.info(">>>superior:{}", superior);
        Stream<CityDto.City> stream = dictionaryService.allCity().stream();
        if (Strings.isNullOrEmpty(superior) || "0".equals(superior))
            return stream
                    .map(city -> new DictEntry.Data(String.valueOf(city.getId()), city.getNameCN()))
                    .collect(Collectors.toList());
        return stream
                .filter(city -> superior.equals(String.valueOf(city.getProvince())))
                .map(city -> new DictEntry.Data(String.valueOf(city.getId()), city.getNameCN()))
                .collect(Collectors.toList());
    }

    /**
     * 影城级别
     */
    @ResponseBody
    @RequestMapping(value = "cinema/levels")
    public List<DictEntry.CinemaLevel> cinemaLevels() {
        List<DictEntry.CinemaLevel> list = dictionaryService.allCinemaLevel();
        list.add(0, new DictEntry.CinemaLevel("0", "全部"));
        return list;
    }

    @ResponseBody
    @RequestMapping(value = "movie_hall/type")
    public List<DictEntry.MovieHallType> movieHallTypes() {
        List<DictEntry.MovieHallType> list = dictionaryService.allMovieHallTypes();
        list.add(0, new DictEntry.MovieHallType("0", "全部"));
        return list;
    }

    /**
     * 影城搜索
     */
    @ResponseBody
    @RequestMapping(value = "cinemas")
    public List<Cinema> cinemas() {
        List<Cinema> cinemas = cinemaQueryService.queryAllCinema();
        Cinema all = new Cinema();
        all.setCode("0");
        all.setName("全部");
        all.setRegionCode("");
        cinemas.add(0, all);
        return cinemas;
    }

    /**
     * 业务类型
     */
    @ResponseBody
    @RequestMapping(value = "businessType")
    public List<DictEntry.BusinessType> businessTypes() {
        List<DictEntry.BusinessType> list = dictionaryService.allBusinessTypes();
        list.add(0, new DictEntry.BusinessType("0", "全部"));
        return list;
    }
}
