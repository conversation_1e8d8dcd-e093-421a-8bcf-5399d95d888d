package cmc.pad.resource.admin.model;

import lombok.Data;

/**
 * Created by f<PERSON><PERSON> on 2018/5/21.
 */
@Data
public class AjaxResult<T> {
    private int status;
    private String msg;
    private T data;

    public static AjaxResult success() {
        return success("请求成功", null);
    }

    public static AjaxResult success(Object data) {
        return success("请求成功", data);
    }

    public static AjaxResult success(String message, Object data) {
        AjaxResult result = new AjaxResult();
        result.setStatus(0);
        result.setMsg(message);
        result.setData(data);
        return result;
    }

    public static AjaxResult simple(int statusCode, String message) {
        AjaxResult result = new AjaxResult();
        result.setStatus(statusCode);
        result.setMsg(message);
        return result;
    }

}