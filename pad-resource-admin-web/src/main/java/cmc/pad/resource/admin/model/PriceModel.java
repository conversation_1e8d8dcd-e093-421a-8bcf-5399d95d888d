package cmc.pad.resource.admin.model;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.util.lang.EnumDisplayNameSupport;
import mtime.lark.util.lang.EnumValueSupport;
import mx.common.excel.annotation.ExcelField;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2019/3/15 16:41
 * @Version 1.0
 */
public class PriceModel {

    @Setter
    @Getter
    protected static class Price {
        private Integer id;
        private Integer importId;
        private Integer updater;
        private LocalDateTime updateTime;
        private LocalDate effectiveDate;
        private PriceStatus status;

        public String getStatus() {
            if (Objects.isNull(this.status)) {
                return "";
            }
            return status.displayName();
        }
    }

    @Setter
    @Getter
    public static class Query {
        //城市级别
        private String cityLevel;
        //影城级别
        private String cinemaLevel;
        private int pageIndex;
        private int pageSize;
    }

    //------------------固定点位刊例---------------------//
    @Setter
    @Getter
    public static class FixedPointExcel {
        @ExcelField(title = "地区级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cityLevel;
        @ExcelField(title = "影院级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaLevel;
        @ExcelField(title = "基础价(元/平米/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String unitPrice;
    }

    @Setter
    @Getter
    public static class FixedPoint extends Price {
        private String cityLevel;
        private String cityLevelView;
        private String cinemaLevel;
        private String cinemaLevelView;
        private String unitPrice;
    }

    @Setter
    @Getter
    public static class FixedPointQuery extends Query {
    }

    //------------------外租区域刊例---------------------//
    @Setter
    @Getter
    public static class OuterAreaExcel {
        @ExcelField(title = "地区级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cityLevel;
        @ExcelField(title = "影院级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaLevel;
        @ExcelField(title = "基础价(元/平米/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String unitPrice;
    }

    @Setter
    @Getter
    public static class OuterArea extends Price {
        private String cityLevel;
        private String cityLevelView;
        private String cinemaLevel;
        private String cinemaLevelView;
        private String unitPrice;
    }

    @Setter
    @Getter
    public static class OuterAreaQuery extends Query {
    }

    //------------------宣传点位刊例---------------------//
    @Setter
    @Getter
    public static class AdvertisingPointExcel {
        @ExcelField(title = "地区级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cityLevel;
        @ExcelField(title = "影院级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaLevel;
        @ExcelField(title = "基础价(元/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String minTotalPrice;
        @ExcelField(title = "基础面积(平米)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private Integer minArea;
        @ExcelField(title = "续价(元/平米/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String expandedUnitPrice;
    }

    @Setter
    @Getter
    public static class LightBoxExcel {
        @ExcelField(title = "地区级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cityLevel;
        @ExcelField(title = "影院级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaLevel;
        @ExcelField(title = "基础价(元/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String minTotalPrice;
        @ExcelField(title = "基础面积(平米)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private Integer minArea;
        @ExcelField(title = "续价(元/平米/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String expandedUnitPrice;
    }

    @Setter
    @Getter
    public static class AdvertisingPoint extends Price {
        private String cityLevel;
        private String cityLevelView;
        private String cinemaLevel;
        private String cinemaLevelView;
        private String minTotalPrice;
        private Integer minArea;
        private String expandedUnitPrice;
    }

    @Setter
    @Getter
    public static class LightBox extends Price {
        private String cityLevel;
        private String cityLevelView;
        private String cinemaLevel;
        private String cinemaLevelView;
        private String minTotalPrice;
        private Integer minArea;
        private String expandedUnitPrice;
    }

    @Setter
    @Getter
    public static class AdvertisingPointQuery extends Query {
    }

    //------------------影厅租赁刊例---------------------//

    @Setter
    @Getter
    public static class MovieHallSeatExcel {
        @ExcelField(title = "地区级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cityLevel;
        @ExcelField(title = "影院级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaLevel;
        @ExcelField(title = "影厅类型", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String movieHallType;
        @ExcelField(title = "基础价(元/个)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String minTotalPrice;
        @ExcelField(title = "基础时长(小时)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private Integer minHours;
        @ExcelField(title = "续价(元/小时/个)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String expandedUnitPrice;
    }

    @Setter
    @Getter
    public static class MovieHallSeat extends Price {
        private String cityLevel;
        private String cityLevelView;
        private String cinemaLevel;
        private String cinemaLevelView;
        private String movieHallType;
        private String movieHallTypeView;
        private String minTotalPrice;
        private Integer minHours;
        private String expandedUnitPrice;
    }

    @Setter
    @Getter
    public static class MovieHallSeatQuery extends Query {
        private String movieHallType;
    }

    //------------------冠名厅刊例---------------------//
    @Setter
    @Getter
    public static class MovieHallNamingExcel {
        @ExcelField(title = "影城编码", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaCode;
        @ExcelField(title = "影城名称", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaName;
        @ExcelField(title = "影厅类型", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String movieHallType;
        @ExcelField(title = "基础价(元/月)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String unitPrice;
    }

    @Setter
    @Getter
    public static class MovieHallNaming extends Price {
        private String regionName;
        private String cinemaCode;
        private String cinemaName;
        private String movieHallType;
        private String movieHallTypeView;
        private String unitPrice;
    }

    @Setter
    @Getter
    public static class MovieHallNamingQuery {
        //城市级别
        private String cinemaCode;
        //影城级别
        private String movieHallType;
        private int pageIndex;
        private int pageSize;
    }
    //------------------营销点位刊例---------------------//

    @Setter
    @Getter
    public static class MarketingPointExcel {
        @ExcelField(title = "地区级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cityLevel;
        @ExcelField(title = "影院级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaLevel;
        @ExcelField(title = "数量单价(元/个)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String unitPriceByQuantity;
        @ExcelField(title = "面积单价(元/平米)", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String unitPriceByArea;
    }

    @Setter
    @Getter
    public static class MarketingPoint extends Price {
        private String cityLevel;
        private String cityLevelView;
        private String cinemaLevel;
        private String cinemaLevelView;
        private String unitPriceByQuantity;
        private String unitPriceByArea;
    }

    @Setter
    @Getter
    public static class MarketingPointQuery extends Query {
    }

    //------------------价格状态---------------------//
    public enum PriceStatus implements EnumValueSupport, EnumDisplayNameSupport {
        UNDETERMINED(1, "未确定"),
        PENDING_EFFECTIVE(2, "待生效"),
        EFFECTIVE(3, "生效中"),
        EXPIRED(4, "已过期");

        private int value;
        private String displayName;

        PriceStatus(int value, String displayName) {
            this.value = value;
            this.displayName = displayName;
        }

        /**
         * 获取枚举的 int 值,用于数据保存以及序列化
         *
         * @return 枚举的 int 值
         */
        @Override
        public int value() {
            return this.value;
        }

        /**
         * 获取枚举的显示名称
         *
         * @return 枚举的显示名称
         */
        @Override
        public String displayName() {
            return this.displayName;
        }

    }
}
