package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.BudgetExcelModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BudgetHallAnalyser extends AbstractFileAnalyser<BudgetExcelModel.Hall> {
    @Autowired
    public BudgetHallAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<BudgetExcelModel.Hall> verifyFile(MultipartFile file, Class<BudgetExcelModel.Hall> clazz) {
        List<BudgetExcelModel.Hall> dataList = analyseFile(file, clazz);
        dataList = dataList.stream().filter(excel ->
                StringUtils.isNotBlank(excel.getCinemaInnerCode()) && StringUtils.isNotBlank(excel.getResourceType())
        ).collect(Collectors.toList());
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        //获取所有影院内码
        List<String> cinemaCodes = allCinemaCodes();

        for (int i = 0; i < dataList.size(); i++) {
            BudgetExcelModel.Hall excel = dataList.get(i);
            String code = formatNumericalValue(excel.getCinemaInnerCode().trim());
            excel.setCinemaInnerCode(code);
            boolean res = checkCinema(i + 1, cinemaCodes, code, reporter);
            if (!res) {
                break;
            }
            String resourceType = excel.getResourceType().trim();
            String counterKey = code + ":" + resourceType;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "影城编码+资源类型"));
        return getResult(dataList, reporter.toString());
    }

    public VerifyResult<BudgetExcelModel.Hall> verifyFile(MultipartFile file, Class<BudgetExcelModel.Hall> clazz, Map<String, Cinema> cinemaMap) {
        List<BudgetExcelModel.Hall> dataList = analyseFile(file, clazz);
        dataList = dataList.stream().filter(excel ->
                StringUtils.isNotBlank(excel.getCinemaInnerCode()) && StringUtils.isNotBlank(excel.getResourceType())
        ).collect(Collectors.toList());
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();

        if (CollectionUtils.isEmpty(dataList)) {
            return getResult(dataList, "没有可用的有效数据");
        }
        for (int i = 0; i < dataList.size(); i++) {
            BudgetExcelModel.Hall excel = dataList.get(i);
            String code = formatNumericalValue(excel.getCinemaInnerCode().trim());
            excel.setCinemaInnerCode(code);
            boolean res = checkCinemaMap(i + 1, cinemaMap, code, reporter);
            if (!res) {
                break;
            }
            res = checkPrice(i + 1, reporter, excel.getMonth1(), excel.getMonth2(), excel.getMonth3(), excel.getMonth4(), excel.getMonth5(), excel.getMonth6(), excel.getMonth7(), excel.getMonth8(), excel.getMonth9(), excel.getMonth10(), excel.getMonth11(), excel.getMonth12());
            if (!res) {
                break;
            }
            String resourceType = excel.getResourceType().trim();
            String counterKey = code + ":" + resourceType;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "影城编码+资源类型"));
        return getResult(dataList, reporter.toString());
    }
}
