package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/3/15 17:13
 * @Version 1.0
 */
@Component
public class OuterAreaLeasingPriceAnalyser extends AbstractFileAnalyser<PriceModel.OuterAreaExcel> {
    @Autowired
    public OuterAreaLeasingPriceAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<PriceModel.OuterAreaExcel> verifyFile(MultipartFile file, Class<PriceModel.OuterAreaExcel> clazz) {
        List<PriceModel.OuterAreaExcel> dataList = analyseFile(file, clazz);
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        List<String> cinemaLevelCodes = allCinemaLevelCodes();
        List<String> cityLevelCodes = allCityLevelCodes();
        for (int i = 0; i < dataList.size(); i++) {
            PriceModel.OuterAreaExcel excel = dataList.get(i);
            String cinemaLevel = excel.getCinemaLevel().trim();
            checkCinemaLevel(i + 1, cinemaLevelCodes, cinemaLevel, reporter);
            String cityLevel = formatNumericalValue(excel.getCityLevel().trim());
            excel.setCityLevel(cityLevel);
            checkCityLevel(i + 1, cityLevelCodes, cityLevel, reporter);
            String unitPrice = excel.getUnitPrice();
            checkPrice(i + 1, unitPrice, reporter, "基础价格");
            String counterKey = cityLevel + ":" + cinemaLevel;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "城市级别+影院级别"));
        return getResult(dataList, reporter.toString());
    }
}
