package cmc.pad.resource.admin.service;

import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.cinema.CinemaRepository;
import cmc.pad.resource.infrastructures.service.region.RegionServiceAdapter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * Created by fuwei on 2022/7/13.
 */
@Slf4j
@Service
public class CinemaInfoQueryService {
    @Autowired
    private CinemaRepository cinemaRepository;
    @Autowired
    private RegionServiceAdapter regionServiceAdapter;

    public Set<String> allCinemaCodeSet() {
        return cinemaRepository.findMany(Filter.create()).stream().map(Cinema::getCode).collect(Collectors.toSet());
    }

    public CinemaInfo getCinemaInfo(Map<String, CinemaInfo> cache, String code) {
        if (cache.containsKey(code))
            return cache.get(code);
        Cinema cinema = cinemaRepository.findOne(f(Cinema.C_CODE, code)).get();
        String largeWardCode = regionServiceAdapter.getLargeWardCode(cinema.getRegionCode());
        CinemaInfo cinemaInfo = new CinemaInfo(cinema, largeWardCode);
        cache.put(code, cinemaInfo);
        return cinemaInfo;
    }

    private String getLargeWardCode(Map<String, String> cache, String regionCode) {
        if (cache.containsKey(regionCode))
            return cache.get(regionCode);
        String largeWardCode = regionServiceAdapter.getLargeWardCode(regionCode);
        cache.put(regionCode, largeWardCode);
        return largeWardCode;
    }

    public static class CinemaInfo {
        private Cinema cinema;
        @Getter
        private String largeWardCode;

        public CinemaInfo(Cinema cinema, String largeWardCode) {
            this.cinema = cinema;
            this.largeWardCode = largeWardCode;
        }

        public String getRegionCode() {
            return this.cinema.getRegionCode();
        }

        public String getCode() {
            return this.cinema.getCode();
        }

        public String getName() {
            return this.cinema.getName();
        }

        public String getRegionName() {
            return this.cinema.getRegionName();
        }

        public String getCityCode() {
            return this.cinema.getCityCode();
        }

        public String getCityDistrictCode() {
            return this.cinema.getCityDistrictCode();
        }
    }
}