package cmc.pad.resource.admin;

import cmc.admin.portal.web.integration.AdminWebApp;
import mtime.lark.util.config.ConfigProperties;
import mtime.lark.web.spring.security.FormAuthenticationInterceptor;
import org.apache.commons.lang.StringUtils;
import org.eclipse.jetty.server.Handler;
import org.eclipse.jetty.server.handler.ContextHandler;
import org.eclipse.jetty.server.handler.HandlerCollection;
import org.eclipse.jetty.server.handler.HandlerWrapper;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.context.embedded.EmbeddedServletContainerCustomizer;
import org.springframework.boot.context.embedded.jetty.JettyEmbeddedServletContainerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication(exclude = MongoAutoConfiguration.class)
@ComponentScan("cmc.pad")
public class AdminWebBootstrap {


    public static void main(String[] args) {
        new AdminWebApp(AdminWebBootstrap.class, args).run();
    }

    @Bean
    EmbeddedServletContainerCustomizer jettyCustomizer() {
        return container -> {
            if (container instanceof JettyEmbeddedServletContainerFactory) {
                JettyEmbeddedServletContainerFactory factory = (JettyEmbeddedServletContainerFactory) container;
                factory.addServerCustomizers(server -> {
                    setHandlerMaxHttpPostSize(getMaxHttpSize(), getMaxFormKeys(), server.getHandlers());
                });
            }
        };
    }

    @Bean
    public FormAuthenticationInterceptor formAuthenticationInterceptor() {
        return new AdminFormAuthenticationInterceptor();
    }

    private static int getMaxHttpSize() {
        String maxHttpSize = ConfigProperties.getProperty("server.max-http-post-size");
        if (StringUtils.isBlank(maxHttpSize)) {
            return 2000000;
        }
        maxHttpSize = maxHttpSize.toUpperCase();
        if (maxHttpSize.endsWith("MB")) {
            return parseMaxHttpSize(maxHttpSize, "MB") * 1024 * 1024;
        } else if (maxHttpSize.endsWith("KB")) {
            return parseMaxHttpSize(maxHttpSize, "KB") * 1024;
        } else if (maxHttpSize.endsWith("B")) {
            return parseMaxHttpSize(maxHttpSize, "B");
        } else {
            return Integer.parseInt(maxHttpSize);
        }
    }

    private static int getMaxFormKeys() {
        String maxFormKeys = ConfigProperties.getProperty("server.max-http-form-keys");
        if (StringUtils.isBlank(maxFormKeys)) {
            return 10000;
        }
        return Integer.parseInt(maxFormKeys);
    }

    private static int parseMaxHttpSize(String maxHttpSize, String unit) {
        return Integer.parseInt(maxHttpSize.substring(0, maxHttpSize.length() - unit.length()));
    }

    private static void setHandlerMaxHttpPostSize(int maxHttpPostSize, int maxFormKeys, Handler... handlers) {
        for (Handler handler : handlers) {
            if (handler instanceof ContextHandler) {
                ((ContextHandler) handler).setMaxFormContentSize(maxHttpPostSize);
                ((ContextHandler) handler).setMaxFormKeys(maxFormKeys);
            } else if (handler instanceof HandlerWrapper) {
                setHandlerMaxHttpPostSize(maxHttpPostSize, maxFormKeys, ((HandlerWrapper) handler).getHandler());
            } else if (handler instanceof HandlerCollection) {
                setHandlerMaxHttpPostSize(maxHttpPostSize, maxFormKeys, ((HandlerCollection) handler).getHandlers());
            }
        }
    }

}
