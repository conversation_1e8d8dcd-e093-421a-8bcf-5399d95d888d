package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/3/15 17:13
 * @Version 1.0
 */
@Component
public class MovieHallSeatLeasingPriceAnalyser extends AbstractFileAnalyser<PriceModel.MovieHallSeatExcel> {
    @Autowired
    public MovieHallSeatLeasingPriceAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<PriceModel.MovieHallSeatExcel> verifyFile(MultipartFile file, Class<PriceModel.MovieHallSeatExcel> clazz) {
        List<PriceModel.MovieHallSeatExcel> dataList = analyseFile(file, clazz);
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        List<String> cinemaLevelCodes = allCinemaLevelCodes();
        List<String> cityLevelCodes = allCityLevelCodes();
        List<String> movieHallTypeCodes = allMovieHallTypeCodes();
        for (int i = 0; i < dataList.size(); i++) {
            PriceModel.MovieHallSeatExcel excel = dataList.get(i);
            String cinemaLevel = excel.getCinemaLevel().trim();
            checkCinemaLevel(i + 1, cinemaLevelCodes, cinemaLevel, reporter);
            String cityLevel = formatNumericalValue(excel.getCityLevel().trim());
            excel.setCityLevel(cityLevel);
            checkCityLevel(i + 1, cityLevelCodes, cityLevel, reporter);
            String movieHallType = excel.getMovieHallType();
            if (StringUtils.isNotBlank(movieHallType)) {
                checkMovieHallType(i + 1, movieHallTypeCodes, movieHallType, reporter);
            } else {
                movieHallType = "";
                excel.setMovieHallType(movieHallType);
            }
            int lineNumber = i + 1;
            String unitPrice = excel.getMinTotalPrice();
            checkPrice(lineNumber, unitPrice, reporter, "基础价格");
            String expandedUnitPrice = excel.getExpandedUnitPrice();
            checkPrice(lineNumber, expandedUnitPrice, reporter, "续价");
            Integer minHours = excel.getMinHours();
            checkNumber(lineNumber, minHours, reporter, "基础时长");
            String counterKey = cityLevel + ":" + cinemaLevel + ":" + movieHallType;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "城市级别+影城级别+影厅编码"));
        return getResult(dataList, reporter.toString());
    }
}
