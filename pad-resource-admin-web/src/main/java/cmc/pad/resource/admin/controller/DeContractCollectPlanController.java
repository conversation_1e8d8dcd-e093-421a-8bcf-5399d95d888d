package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.pad.resource.admin.service.UserRankInfoService;
import cmc.pad.resource.application.query.contract.DeContractCollectionPlanQueryService;
import cmc.pad.resource.domain.contract.DeContractCollectionPlan;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.util.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageInfo;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * Created by fuwei on 2024/3/13.
 * 多经合同收款计划查询
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/contract")
public class DeContractCollectPlanController {
    @Autowired
    private UserRankInfoService userRankInfoService;
    @Autowired
    private DeContractCollectionPlanQueryService deContractCollectionPlanQueryService;

    @RequestMapping(value = "deContractCollectPlanListPage", method = RequestMethod.GET)
    @Authorize("cmc.pad.resource.de.contract.collect.plan.list")
    public ViewResult page() {
        PointLocationModel.UserRankInfo userRankInfo = userRankInfoService.userRankInfo();
        ViewResult viewResult = new ViewResult("/view/contract/list");
        viewResult.addEnum(DeContractCollectionPlan.ContractType.class, true);
        viewResult.addEnum(DeContractCollectionPlan.OverdueState.class, true);
        viewResult.addEnum(DeContractCollectionPlan.ContractState.class, true);
        viewResult.addEnum(DeContractCollectionPlan.CollectType.class, true);
        viewResult.addObject("largeWard", userRankInfo.getLargeWardCode());
        viewResult.addObject("areaCode", userRankInfo.getAreaCode());
        viewResult.setMenuPath("/pad/resource/contract/deContractCollectPlanListPage");
        return viewResult;
    }

    @ResponseBody
    @RequestMapping(value = "deContractCollectPlanList")
    public PageResult<DeContractCollectionPlan> list(ListParam param) {
//        param.setUserRankInfo(userRankInfoService.userRankInfo());
        log.info(">>>逾期合同收款计划列表, param:{}", JSON.toJSONString(param));
        return deContractCollectionPlanQueryService.
                pageList(param.getLargeWardSelect(), param.region, param.getOperatorName()
                        , param.getCustomer(), param.getCode(), param.getContractState()
                        , param.getDelayState()
                        , DateUtil.dateStr2StartLocalDateTime(param.getStartTime())
                        , DateUtil.dateStr2EndLocalDateTime(param.getEndTime())
                        , new PageInfo(param.pageIndex, param.pageSize));
    }

    @Data
    public static class ListParam {
        String largeWardSelect;
        String region;
        String operatorName;
        String startTime;
        String endTime;
        String customer;
        String code;
        Integer contractState;
        Integer delayState;
        int pageIndex;
        int pageSize;
    }
}
