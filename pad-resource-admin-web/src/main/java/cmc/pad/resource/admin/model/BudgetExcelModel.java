package cmc.pad.resource.admin.model;

import lombok.Getter;
import lombok.Setter;
import mx.common.excel.annotation.ExcelField;

import java.math.BigDecimal;

public class BudgetExcelModel {
    @Setter
    @Getter
    public static class Hall {
        @ExcelField(title = "*影城内码", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaInnerCode;
        @ExcelField(title = "影城名称", groups = 0, align = 2, sort = 2, width = 5000, fieldType = String.class)
        private String cinemaName;
        @ExcelField(title = "*资源类型", groups = 0, align = 2, sort = 3, width = 5000, fieldType = String.class)
        private String resourceType;
        @ExcelField(title = "*1月预算", groups = 0, align = 2, sort = 4, width = 5000, fieldType = BigDecimal.class)
        private String month1;
        @ExcelField(title = "*2月预算", groups = 0, align = 2, sort = 5, width = 5000, fieldType = BigDecimal.class)
        private String month2;
        @ExcelField(title = "*3月预算", groups = 0, align = 2, sort = 6, width = 5000, fieldType = BigDecimal.class)
        private String month3;
        @ExcelField(title = "*4月预算", groups = 0, align = 2, sort = 7, width = 5000, fieldType = BigDecimal.class)
        private String month4;
        @ExcelField(title = "*5月预算", groups = 0, align = 2, sort = 8, width = 5000, fieldType = BigDecimal.class)
        private String month5;
        @ExcelField(title = "*6月预算", groups = 0, align = 2, sort = 9, width = 5000, fieldType = BigDecimal.class)
        private String month6;
        @ExcelField(title = "*7月预算", groups = 0, align = 2, sort = 10, width = 5000, fieldType = BigDecimal.class)
        private String month7;
        @ExcelField(title = "*8月预算", groups = 0, align = 2, sort = 11, width = 5000, fieldType = BigDecimal.class)
        private String month8;
        @ExcelField(title = "*9月预算", groups = 0, align = 2, sort = 12, width = 5000, fieldType = BigDecimal.class)
        private String month9;
        @ExcelField(title = "*10月预算", groups = 0, align = 2, sort = 13, width = 5000, fieldType = BigDecimal.class)
        private String month10;
        @ExcelField(title = "*11月预算", groups = 0, align = 2, sort = 14, width = 5000, fieldType = BigDecimal.class)
        private String month11;
        @ExcelField(title = "*12月预算", groups = 0, align = 2, sort = 15, width = 5000, fieldType = BigDecimal.class)
        private String month12;
        @ExcelField(title = "错误信息", groups = 0, align = 2, sort = 16, width = 5000, fieldType = String.class)
        private String errorMessage;
    }


    @Setter
    @Getter
    public static class PointLocation {
        @ExcelField(title = "*影城内码", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String cinemaInnerCode;
        @ExcelField(title = "影城名称", groups = 0, align = 2, sort = 2, width = 5000, fieldType = String.class)
        private String cinemaName;
        @ExcelField(title = "*资源类型", groups = 0, align = 2, sort = 3, width = 5000, fieldType = String.class)
        private String resourceType;
        @ExcelField(title = "*资源编码", groups = 0, align = 2, sort = 3, width = 5000, fieldType = String.class)
        private String resourceCode;
        @ExcelField(title = "*1月预算", groups = 0, align = 2, sort = 4, width = 5000, fieldType = BigDecimal.class)
        private String month1;
        @ExcelField(title = "*2月预算", groups = 0, align = 2, sort = 5, width = 5000, fieldType = BigDecimal.class)
        private String month2;
        @ExcelField(title = "*3月预算", groups = 0, align = 2, sort = 6, width = 5000, fieldType = BigDecimal.class)
        private String month3;
        @ExcelField(title = "*4月预算", groups = 0, align = 2, sort = 7, width = 5000, fieldType = BigDecimal.class)
        private String month4;
        @ExcelField(title = "*5月预算", groups = 0, align = 2, sort = 8, width = 5000, fieldType = BigDecimal.class)
        private String month5;
        @ExcelField(title = "*6月预算", groups = 0, align = 2, sort = 9, width = 5000, fieldType = BigDecimal.class)
        private String month6;
        @ExcelField(title = "*7月预算", groups = 0, align = 2, sort = 10, width = 5000, fieldType = BigDecimal.class)
        private String month7;
        @ExcelField(title = "*8月预算", groups = 0, align = 2, sort = 11, width = 5000, fieldType = BigDecimal.class)
        private String month8;
        @ExcelField(title = "*9月预算", groups = 0, align = 2, sort = 12, width = 5000, fieldType = BigDecimal.class)
        private String month9;
        @ExcelField(title = "*10月预算", groups = 0, align = 2, sort = 13, width = 5000, fieldType = BigDecimal.class)
        private String month10;
        @ExcelField(title = "*11月预算", groups = 0, align = 2, sort = 14, width = 5000, fieldType = BigDecimal.class)
        private String month11;
        @ExcelField(title = "*12月预算", groups = 0, align = 2, sort = 15, width = 5000, fieldType = BigDecimal.class)
        private String month12;
        @ExcelField(title = "错误信息", groups = 0, align = 2, sort = 16, width = 5000, fieldType = String.class)
        private String errorMessage;
    }


}
