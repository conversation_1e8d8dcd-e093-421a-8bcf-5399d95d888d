package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.pad.resource.admin.analyser.FixedPointLeasingPriceAnalyser;
import cmc.pad.resource.admin.analyser.VerifyResult;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.admin.util.DateUtils;
import cmc.pad.resource.admin.util.MoneyUtils;
import cmc.pad.resource.application.command.FileImportingAppService;
import cmc.pad.resource.application.command.FixedPointLeasingAppService;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.application.query.FixedPointLeasingPriceQueryService;
import cmc.pad.resource.application.query.data.FixedPointPriceQuery;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.price.FixedPointLeasingPrice;
import cmc.portal.admin.service.iface.AuthUserService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import mtime.lark.web.result.AjaxResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Collections.EMPTY_LIST;

/**
 * 固定点位报价
 *
 * <AUTHOR>
 * @Date 2019/3/15 15:31
 * @Version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/price/fp")
public class FixedPointLeasingController extends BaseController {
    private final FixedPointLeasingAppService appService;
    private final FixedPointLeasingPriceQueryService queryService;

    @Autowired
    FixedPointLeasingController(FileServiceFacade fileServiceFacade,
                                FileImportingAppService fileImportingAppService,
                                FileImportingQueryService fileImportingQueryService,
                                DictionaryDomainService dictionaryService,
                                AuthUserService authUserService,
                                FixedPointLeasingPriceAnalyser priceAnalyser,
                                FixedPointLeasingAppService appService,
                                FixedPointLeasingPriceQueryService queryService) {
        super(fileServiceFacade, fileImportingAppService, fileImportingQueryService, priceAnalyser, dictionaryService, authUserService);
        this.appService = appService;
        this.queryService = queryService;
    }


    @RequestMapping(value = "page", method = RequestMethod.GET)
    @Authorize("pad.resource.price.fp.page")
    public ViewResult page() {
        ViewResult viewResult = new ViewResult("/view/price/fixedpoint/list");
        viewResult.setMenuPath("/pad/resource/price/fp/page");
        return viewResult;
    }

    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<PriceModel.FixedPoint> list(PriceModel.FixedPointQuery query) {
        PageResult<PriceModel.FixedPoint> pageResult = new PageResult<>();
        FixedPointPriceQuery priceQuery = new FixedPointPriceQuery();
        priceQuery.setPageIndex(query.getPageIndex());
        priceQuery.setPageSize(query.getPageSize());
        priceQuery.setCinemaLevel(query.getCinemaLevel());
        priceQuery.setCityLevel(query.getCityLevel());
        PageResult<FixedPointLeasingPrice> result = queryService.effectivePage(priceQuery);
        int totalCount = result.getTotalCount();
        pageResult.setTotalCount(totalCount);
        if (totalCount > 0) {
            Map<String, String> cinemaLevelDict = dictionaryService.allCinemaLevelDict();
            Map<String, String> cityLevelDict = dictionaryService.allCityLevelDict();
            List<FixedPointLeasingPrice> items = result.getItems();
            List<PriceModel.FixedPoint> viewList = items.stream().map(p -> dtoConvertView(p, null, cityLevelDict, cinemaLevelDict)).collect(Collectors.toList());
            pageResult.setItems(viewList);
        }
        return pageResult;
    }

    @ResponseBody
    @RequestMapping(value = "records", method = RequestMethod.POST)
    public List records(String cityLevel, String cinemaLevel) {
        List<FixedPointLeasingPrice> list = queryService.list(cityLevel, cinemaLevel);
        if (Objects.nonNull(list) && !list.isEmpty()) {
            LocalDate latestEffectiveDate = queryService.latestEffectiveDate();
            Map<String, String> cinemaLevelDict = dictionaryService.allCinemaLevelDict();
            Map<String, String> cityLevelDict = dictionaryService.allCityLevelDict();
            return list.stream().map(p -> dtoConvertView(p, latestEffectiveDate, cityLevelDict, cinemaLevelDict)).collect(Collectors.toList());
        }
        return EMPTY_LIST;
    }

    private PriceModel.FixedPoint dtoConvertView(FixedPointLeasingPrice price, LocalDate currentEffectiveDate, Map<String, String> cityLevelDict, Map<String, String> cinemaLevelDict) {
        PriceModel.FixedPoint view = new PriceModel.FixedPoint();
        BeanUtils.copyProperties(price, view);
        String cityLevel = price.getCityLevel();
        view.setCityLevelView(cityLevelDict.get(cityLevel));
        String cinemaLevel = price.getCinemaLevel();
        view.setCinemaLevelView(cinemaLevelDict.get(cinemaLevel));
        String priceYuan = MoneyUtils.centConvertYuan(price.getUnitPrice());
        view.setUnitPrice(priceYuan);
        PriceModel.PriceStatus status = determineStatus(currentEffectiveDate, price.getEffectiveDate());
        view.setStatus(status);
        return view;
    }

    /**
     * 文件上传
     */
    @RequestMapping("file/import")
    @ResponseBody
    public Map<String, Object> importExcel(@RequestParam MultipartFile exFile,
                                           @RequestParam(name = "version", required = false, defaultValue = "") String version,
                                           @RequestParam(name = "effective_date", required = false, defaultValue = "") String date) {
        Map<String, Object> result = new HashMap<>();
        //检查生效日期
        LocalDate effectiveDate = DateUtils.strToLocalDate(date);
        boolean duplicate = checkDuplicateFile(FileCategory.FIXED_POINT_LEASING_PRICE, effectiveDate);
        if (duplicate) {
            result.put("state", false);
            result.put("msg", "已存在该生效日期的文件，请勿重复上传");
            return result;
        }
        int recordId = uploadExcel(exFile, FileCategory.FIXED_POINT_LEASING_PRICE.value(), version, date);
        //校验数据
        try {
            VerifyResult<PriceModel.FixedPointExcel> verifyResult = fileAnalyser.verifyFile(exFile, PriceModel.FixedPointExcel.class);
            if (verifyResult.isResult()) {
                Integer userId = currentUserId();
                List<PriceModel.FixedPointExcel> list = verifyResult.getList();
                List<FixedPointLeasingPrice> priceList = list.stream().map(e -> {
                    FixedPointLeasingPrice price = new FixedPointLeasingPrice();
                    price.setCityLevel(e.getCityLevel());
                    price.setCinemaLevel(e.getCinemaLevel());
                    price.setUnitPrice(MoneyUtils.yuanConvertCent(e.getUnitPrice()));
                    price.setUpdater(userId);
                    price.setUpdateTime(LocalDateTime.now());
                    price.setEffectiveDate(effectiveDate);
                    price.setImportId(recordId);
                    return price;
                }).collect(Collectors.toList());
                appService.importData(priceList);
                importCompleted(recordId);
                result.put("state", true);
            } else {
                importFailed(recordId);
                result.put("state", false);
                result.put("msg", verifyResult.getError());
            }
        } catch (Exception e) {
            result.put("state", false);
            importFailed(recordId);
            result.put("msg", "上传失败");
            return result;
        }


        return result;
    }

    @RequestMapping("demo")
    public void download(HttpServletRequest request,
                         HttpServletResponse response) {
        String name = "固定点位刊例导入模版.xlsx";
        byte[] bytes = generateDemo(name, Collections.singletonList(new PriceModel.FixedPointExcel()), PriceModel.FixedPointExcel.class);
        transferFile(name, request, response, bytes);
    }

    @ResponseBody
    @RequestMapping(value = "file/list", method = RequestMethod.POST)
    public List fileList() {
        return fileRecordList(FileCategory.FIXED_POINT_LEASING_PRICE);
    }

    /**
     * 导出资源库存
     */
    @ResponseBody
    @RequestMapping(value = "discard")
    public AjaxResult discard(@RequestParam("id") Integer id) {
        AjaxResult result = new AjaxResult();
        FileImport record = getFileImportRecord(id);
        if (Objects.nonNull(record) && LocalDate.now().isBefore(record.getEffectiveDate())) {
            appService.discard(id);
            deleteFile(id);
            result.setSuccess(true);
        } else {
            result.setSuccess(false);
        }
        return result;
    }
}
