package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import lombok.RequiredArgsConstructor;
import mtime.lark.util.lang.FaultException;
import mx.common.excel.ImportExcel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文件校验器
 */
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
abstract public class AbstractFileAnalyser<T> {

    private static final Pattern cinemaCodePattern = Pattern.compile("^\\d{1,6}$");
    private static final Pattern cinemaLevelPattern = Pattern.compile("^[a-zA-Z]$");
    private static final Pattern cityLevelPattern = Pattern.compile("^[l,L]\\d+$");
    private static final Pattern movieHallTypePattern = Pattern.compile("^[a-zA-Z]+$");
    private static final Pattern numberPattern = Pattern.compile("^\\d+\\.0$");
    private static final Pattern moneyPattern = Pattern.compile("^([1-9]\\d{0,9}|0)([.]?|(\\.\\d{1,2})?)$");
    protected final DictionaryDomainService dictionaryService;
    private final CinemaQueryService cinemaQueryService;

    List<T> analyseFile(MultipartFile file, Class<T> clazz) {
        try {
            ImportExcel excel = new ImportExcel(file.getOriginalFilename(), file.getInputStream(), 0, 0);
            return excel.getDataList(clazz);
        } catch (Exception e) {
            throw new FaultException("解析文件出错:{}", e);
        }
    }

    List<String> allCinemaCodes() {
        return cinemaQueryService.queryAllCinema().stream().map(Cinema::getCode).collect(Collectors.toList());
    }

    Set<String> allCinemaCodeSet() {
        return cinemaQueryService.queryAllCinema().stream().map(Cinema::getCode).collect(Collectors.toSet());
    }

    List<Cinema> allCinemas(){
        return cinemaQueryService.queryAllCinema();
    }

    public Map<String,Cinema> getAllCinemas(){
        List<Cinema> cinemas= allCinemas();
        Map<String,Cinema> map=new HashMap<>();
        for (Cinema cinema : cinemas) {
            map.put(cinema.getCode(),cinema);
        }
        return map;
    }

    List<String> allCinemaLevelCodes() {
        return dictionaryService.allCinemaLevelCodes();
    }

    List<String> allCityLevelCodes() {
        return dictionaryService.allCityLevelCodes();
    }

    List<String> allMovieHallTypeCodes() {
        return dictionaryService.allMovieHallTypeCodes();
    }


    String formatNumericalValue(String numericalValue) {
        Matcher match = numberPattern.matcher(numericalValue);
        if (match.matches()) {
            return numericalValue.substring(0, numericalValue.length() - 2);
        }
        return numericalValue;
    }

    protected boolean checkContains(List<String> allCodes, String targetCode) {
        return (StringUtils.isBlank(targetCode) || !allCodes.contains(targetCode.trim().toUpperCase()));
    }

    protected boolean checkMapContains(Map<String,Cinema> cinemaMap, String targetCode) {
        return (StringUtils.isBlank(targetCode) || !cinemaMap.containsKey(targetCode.trim().toUpperCase()));
    }

    void duplicateCount(Map<String, Integer> counter, String key) {
        Integer count = counter.get(key);
        if (Objects.isNull(count)) {
            counter.put(key, 1);
        } else {
            counter.put(key, ++count);
        }
    }

    String checkDuplicate(Map<String, Integer> counter, String name) {
        StringBuilder reporter = new StringBuilder();
        counter.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getKey().trim()) && entry.getValue() > 1)
                .forEach(entry -> reporter.append(name).append(" ").append(entry.getKey()).append(" 数据重复").append(entry.getValue()).append("次</br>"));
        return reporter.toString();
    }

    boolean checkCinema(int lineNumber, List<String> allCode, String code, StringBuilder reporter) {
        Matcher match = cinemaCodePattern.matcher(code);
        if (!match.matches()) {
            reporter.append("第").append(lineNumber).append("行: ").append("影城内码:").append(code).append(" 格式错误</br>");
            return false;
        } else if (checkContains(allCode, code)) {
            reporter.append("第").append(lineNumber).append("行: ").append("影城内码:").append(code).append(" 不存在</br>");
            return false;
        }
        return true;
    }

    boolean checkCinemaMap(int lineNumber, Map<String,Cinema> cinemaMap, String code, StringBuilder reporter) {
        Matcher match = cinemaCodePattern.matcher(code);
        if (!match.matches()) {
            reporter.append("第").append(lineNumber).append("行: ").append("影城内码:").append(code).append(" 格式错误");
            return false;
        } else if (checkMapContains(cinemaMap, code)) {
            reporter.append("第").append(lineNumber).append("行: ").append("影城内码:").append(code).append(" 不存在");
            return false;
        }
        return true;
    }

    void checkCinemaLevel(int lineNumber, List<String> allCode, String code, StringBuilder reporter) {
        Matcher match = cinemaLevelPattern.matcher(code);
        if (!match.matches()) {
            reporter.append("第").append(lineNumber).append("行: ").append("影城级别:").append(code).append(" 格式错误</br>");
        } else if (checkContains(allCode, code)) {
            reporter.append("第").append(lineNumber).append("行: ").append("影城级别:").append(code).append(" 不存在</br>");
        }
    }


    void checkCityLevel(int lineNumber, List<String> allCode, String code, StringBuilder reporter) {
        Matcher match = cityLevelPattern.matcher(code);
        if (!match.matches()) {
            reporter.append("第").append(lineNumber).append("行: ").append("城市级别:").append(code).append(" 格式错误</br>");
        } else if (checkContains(allCode, code)) {
            reporter.append("第").append(lineNumber).append("行: ").append("城市级别:").append(code).append(" 不存在</br>");
        }
    }

    void checkMovieHallType(int lineNumber, List<String> allCode, String code, StringBuilder reporter) {
        Matcher match = movieHallTypePattern.matcher(code);
        if (!match.matches()) {
            reporter.append("第").append(lineNumber).append("行: ").append("影厅类型:").append(code).append(" 格式错误</br>");
        } else if (checkContains(allCode, code)) {
            reporter.append("第").append(lineNumber).append("行: ").append("影厅类型:").append(code).append(" 不存在</br>");
        }
    }

    void checkPrice(int lineNumber, String price, StringBuilder reporter, String name) {
        if (StringUtils.isBlank(price) || !(moneyPattern.matcher(price).matches())) {
            reporter.append("第").append(lineNumber).append("行: ").append(name).append("数据格式错误</br>");
        }
    }
    boolean checkPrice(int lineNumber, StringBuilder reporter, String... prices) {
        for (String price : prices) {
            if (StringUtils.isBlank(price) || !(moneyPattern.matcher(price).matches())) {
                reporter.append("第").append(lineNumber).append("行: ").append("金额的数据格式错误");
                return false;
            }
        }
        return true;
    }

    void checkNumber(int lineNumber, Integer price, StringBuilder reporter, String name) {
        if (Objects.isNull(price) || price <= 0) {
            reporter.append("第").append(lineNumber).append("行: ").append(name).append("数据格式错误</br>");
        }
    }


    VerifyResult<T> getResult(List<T> dataList, String report) {
        VerifyResult<T> result = new VerifyResult<>();
        if (StringUtils.isBlank(report)) {
            result.setResult(true);
            result.setList(dataList);
        } else {
            result.setResult(false);
            result.setError(report);
            result.setList(dataList);
        }
        return result;
    }

    public abstract VerifyResult<T> verifyFile(MultipartFile file, Class<T> clazz);

}
