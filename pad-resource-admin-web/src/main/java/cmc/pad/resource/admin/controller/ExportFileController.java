package cmc.pad.resource.admin.controller;

import cmc.pad.resource.adapter.FileServiceAdapter;
import cmc.pad.resource.admin.model.AjaxResult;
import cmc.pad.resource.admin.service.UserRankInfoService;
import cmc.pad.resource.application.command.common.FileExportAppService;
import cmc.pad.resource.domain.common.FileExportRecord;
import cmc.pad.resource.domain.importing.FileCategory;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import mtime.lark.util.msg.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Map;

import static cmc.pad.resource.constant.TopicName.EXPORT_FILE_TOPIC;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExportFileController {
    private final FileServiceAdapter fileServiceAdapter;
    private final FileExportAppService fileExportAppService;
    private final UserRankInfoService userRankInfoService;

    @ResponseBody
    @RequestMapping(value = "exportExcel")
    public AjaxResult exportExcel(int category) {
        log.info(">>>开始导出excel, category:{}", category);
        int id = fileExportAppService.save(new FileExportRecord(FileCategory.valueOf(category)));
        Publisher.get().publish(EXPORT_FILE_TOPIC, id);
        return AjaxResult.success(id);
    }

    @ResponseBody
    @RequestMapping(value = "exportExcelAttachParam")
    public AjaxResult exportExcelAttachParam(@RequestBody Map param) {
        log.info(">>>开始导出excel, param:{}", JSON.toJSONString(param));
        String category = (String) param.get("category");
        int id = fileExportAppService.save(new FileExportRecord(
                FileCategory.valueOf(Integer.valueOf(category)),
                JSON.toJSONString(param)
        ));
        Publisher.get().publish(EXPORT_FILE_TOPIC, id);
        return AjaxResult.success(id);
    }

    @ResponseBody
    @RequestMapping(value = "exportStatus")
    public AjaxResult exportStatus(int id) {
        int status = fileExportAppService.get(id).getStatus().value();
        log.info(">>>导出excel状态, id:{}, status:{}", id, status);
        Map<String, Integer> data = Maps.newHashMap();
        data.put("id", id);
        data.put("status", status);
        return AjaxResult.success(data);
    }

    @ResponseBody
    @RequestMapping(value = "download")
    public void download(int id, HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info(">>>下载excel, id:{}", id);
        FileExportRecord fileExportRecord = fileExportAppService.get(id);
        long start = System.currentTimeMillis();
        byte[] bytes = fileServiceAdapter.downloadFile(fileExportRecord.getFileId());
        log.info(">>>从文件服务器下载文件时间:{}", System.currentTimeMillis() - start);
        String filename = fileExportRecord.getFileId();
        InputStream is = new ByteArrayInputStream(bytes);
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String header = request.getHeader("User-Agent");
        if (header.toUpperCase().indexOf("MSIE") > 0 || header.contains("Trident")) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes(), "ISO8859-1");
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        ServletOutputStream out = response.getOutputStream();
        start = System.currentTimeMillis();
        try (BufferedInputStream bis = new BufferedInputStream(is);
             BufferedOutputStream bos = new BufferedOutputStream(out)) {
            byte[] buff = new byte[5120];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
                bos.flush();
            }
        } catch (final IOException e) {
            throw new FaultException("导出失败：{}", e);
        }
        log.info(">>>写输出流时间:{}", System.currentTimeMillis() - start);
        log.info(">>>下载excel完成, id:{}", id);
    }
}