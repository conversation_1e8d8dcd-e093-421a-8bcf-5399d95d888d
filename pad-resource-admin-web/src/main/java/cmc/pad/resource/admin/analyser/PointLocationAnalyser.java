package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.adapter.BusinessTypeServiceAdapter;
import cmc.pad.resource.admin.service.CinemaInfoQueryService;
import cmc.pad.resource.application.command.point.PointSaveChecker;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.constant.Rank;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import mx.common.excel.ImportExcel;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.text.DecimalFormat;
import java.util.*;

import static cmc.pad.resource.infrastructures.service.dictionary.DictionaryDomainServiceAdapter.RESOURCE_OWNER_SHIP_DICT_TYPE;

/**
 * <AUTHOR>
 * @Date 2019/3/15 17:13
 * @Version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PointLocationAnalyser {
    private final PointSaveChecker pointSaveChecker;
    private final BusinessTypeServiceAdapter businessTypeServiceAdapter;
    private final CinemaInfoQueryService cinemaInfoQueryService;
    private final DictionaryDomainService dictionaryService;

    List<PointLocationModel.ImportExcel> analyseFile(MultipartFile file, Class clazz) {
        try {
            ImportExcel excel = new ImportExcel(file.getOriginalFilename(), file.getInputStream(), 0, 0);
            return excel.getDataList(clazz);
        } catch (Exception e) {
            throw new FaultException("解析文件出错:{}", e);
        }
    }

    public VerifyResult<PointLocationModel.ImportExcel> verifyFile(MultipartFile file, PointLocationModel.UserRankInfo userRankInfo) {
        List<PointLocationModel.ImportExcel> dataList = analyseFile(file, PointLocationModel.ImportExcel.class);
        if (dataList.isEmpty())
            throw new FaultException("导入数据为空");
        Set<String> cinemaCodeSet = cinemaInfoQueryService.allCinemaCodeSet();
        Map<String, String> businessTypeMap = businessTypeServiceAdapter.pointLocationBusinessTypeMap();
        Map<String, String> resourceOwnershipMap = dictionaryService.findDictMap(RESOURCE_OWNER_SHIP_DICT_TYPE);
        Map<String, Integer> cinemaInnerCodeAndRCodeMap = Maps.newHashMap();
        Map<String, CinemaInfoQueryService.CinemaInfo> cinemaInfoCache = Maps.newHashMap();
        StringBuilder errorReporter = new StringBuilder();
        for (int i = 0; i < dataList.size(); i++) {
            StringBuilder reporter = new StringBuilder();
            PointLocationModel.ImportExcel item = dataList.get(i);
            item.setErrorMsg(null);
            int row = i + 1;
            String cinemaInnerCode = item.getCinemaInnerCode();
            try {
                checkCinemaInnerCode(cinemaCodeSet, cinemaInnerCode);
                CinemaInfoQueryService.CinemaInfo cinemaInfo = cinemaInfoQueryService.getCinemaInfo(cinemaInfoCache, cinemaInnerCode);
                checkCinemaOperatePermission(userRankInfo, cinemaInfo);
            } catch (CinemaInfoException e) {
                log.error(">>>>{}", e.getMessage());
                reporter.append(e.getMessage());
            } catch (CinemaOperatePermissionException e) {
                log.error(">>>>{}", e.getMessage());
                reporter.append("无该影城的操作权限,");
            }
            if (Strings.isNullOrEmpty(item.getResourceType())) {
                reporter.append("资源类型是空,");
            } else if (!businessTypeMap.containsKey(item.getResourceType())) {
                reporter.append("资源类型填写有误,");
            }
            String resourceOwnership = item.getResourceOwnership();
            if (Strings.isNullOrEmpty(resourceOwnership)) {
                reporter.append("资源归属是空,");
            } else {
                if (NumberUtils.isNumber(resourceOwnership)) {
                    log.info(">>>资源归属值:{}", resourceOwnership);
                    item.setResourceOwnership(String.valueOf(new Double(resourceOwnership).intValue()));
                    resourceOwnership = item.getResourceOwnership();
                    log.info(">>>资源归属值转换数值:{}", resourceOwnership);
                }
                if (!resourceOwnershipMap.containsKey(resourceOwnership)) {
                    reporter.append("资源归属填写有误,");
                    if (resourceOwnership.contains(".")) {
                        reporter.append("资源归属字段请设置文本类型,");
                    }
                }
            }
            if (Strings.isNullOrEmpty(item.getResourceCode())) {
                reporter.append("资源编码是空,");
            }
            if (Strings.isNullOrEmpty(item.getSellArea())) {
                reporter.append("可售面积是空,");
            } else if (!NumberUtils.isNumber(item.getSellArea())) {
                reporter.append("可售面积不是数字,");
            } else if (item.getSellArea().contains(".")) {
                try {
                    checkNumericWithTwoDecimalPlaces(item.getSellArea());
                } catch (FaultException e) {
                    reporter.append("可售面积").append(e.getMessage()).append(",");
                }
            }
            try {
                checkNumericWithTwoDecimalPlaces(item.getFloorHeight());
            } catch (FaultException e) {
                reporter.append("层高").append(e.getMessage()).append(",");
            }
            try {
                checkYesOrNo(item.getIsSplittable());
            } catch (FaultException e) {
                reporter.append("是否可拆分").append(e.getMessage()).append(",");
            }
            try {
                checkYesOrNo(item.getWaterSupply());
            } catch (FaultException e) {
                reporter.append("上下水").append(e.getMessage()).append(",");
            }
            try {
                checkYesOrNo(item.getPowerSupply());
            } catch (FaultException e) {
                reporter.append("强电").append(e.getMessage()).append(",");
            }
            try {
                checkYesOrNo(item.getFireFacilities());
            } catch (FaultException e) {
                reporter.append("消防设施").append(e.getMessage()).append(",");
            }
            if (cinemaInnerCodeAndRCodeMap.containsKey(item.getCinemaInnerCode() + "-" + item.getResourceCode())) {
                reporter.append("与 第" + cinemaInnerCodeAndRCodeMap.get(item.getCinemaInnerCode() + "-" + item.getResourceCode()) + "行数据重复");
            } else {
                cinemaInnerCodeAndRCodeMap.put(item.getCinemaInnerCode() + "-" + item.getResourceCode(), row);
            }
            try {
                pointSaveChecker.checkBusinessTypeCodeAndSellArea(item.getResourceCode(), item.getCinemaInnerCode(), item.getResourceType(), item.getSellArea());
            } catch (FaultException e) {
                reporter.append(e.getMessage());
            }
            if (!Strings.isNullOrEmpty(reporter.toString())) {
                reporter.insert(0, "第" + row + "行 ");
                item.setErrorMsg(reporter.toString());
                errorReporter.append(reporter);
            }
        }
        return getResult(dataList, errorReporter.toString());
    }

    public void checkYesOrNo(String txt) {
        if (Strings.isNullOrEmpty(txt))
            return;
        txt = txt.trim();
        if ("是".equals(txt) || "否".equals(txt))
            return;
        throw new FaultException("内容只允许填写“是”或者”否“");
    }

    public static void checkNumericWithTwoDecimalPlaces(String str) {
        if (StringUtils.isEmpty(str))
            return;
        try {
            double value = Double.parseDouble(str);
            // 使用 DecimalFormat 来格式化小数，保留两位小数
            DecimalFormat df = new DecimalFormat("#.##");
            String formatted = df.format(value);
            // 比较格式化后的字符串和原始字符串，如果不同，说明原始字符串的小数位数超过两位
            if (formatted.contains(".") && !formatted.equals(str))
                throw new FaultException("小数位数不能超过两位");
        } catch (NumberFormatException e) {
            throw new FaultException("不是数字");
        }
    }

    public static void main(String[] args) {
        try {
            checkNumericWithTwoDecimalPlaces("20");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        try {
            checkNumericWithTwoDecimalPlaces("20.0");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        try {
            checkNumericWithTwoDecimalPlaces("20.1");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        try {
            checkNumericWithTwoDecimalPlaces("20.11");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        try {
            checkNumericWithTwoDecimalPlaces("20.111");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    private void checkCinemaInnerCode(Set<String> cinemaCodeSet, String cinemaInnerCode) {
        if (Strings.isNullOrEmpty(cinemaInnerCode)) {
            throw new CinemaInfoException("影城内码是空,");
        } else if (!cinemaCodeSet.contains(cinemaInnerCode)) {
            String errorMsg = "影城内码填写有误,";
            if (cinemaInnerCode.contains(".")) {
                errorMsg += "影城内码字段请设置文本类型,";
            }
            throw new CinemaInfoException(errorMsg);
        }
    }

    private void checkCinemaOperatePermission(PointLocationModel.UserRankInfo userRankInfo, CinemaInfoQueryService.CinemaInfo cinemaInfo) {
        int rank = userRankInfo.getRank();
        String loginUserLargeWardCode = userRankInfo.getLargeWardCode();
        String loginUserAreaCode = userRankInfo.getAreaCode();
        String loginUserCinemaInnerCode = userRankInfo.getCinemaInnerCode();

        if (rank == Rank.CHAIN.value()) {
            if ("0".equals(loginUserLargeWardCode))
                return;
            if (!loginUserLargeWardCode.equals(cinemaInfo.getLargeWardCode())) {
                throw new CinemaOperatePermissionException("影城不属于该大区, 登录用户所属大区:{0}, 影城所属大区:{1}", loginUserLargeWardCode, cinemaInfo.getLargeWardCode());
            }
        }
        if (rank == Rank.AREA.value()) {
            if (!loginUserAreaCode.equals(cinemaInfo.getRegionCode())) {
                throw new CinemaOperatePermissionException("影城不属于该区域, 登录用户所属区域:{0}, 影城所属区域:{1}", loginUserAreaCode, cinemaInfo.getRegionCode());
            }
        }
        if (rank == Rank.CINEMA.value()) {
            if (!loginUserCinemaInnerCode.equals(cinemaInfo.getCode())) {
                throw new CinemaOperatePermissionException("影城错误, 登录用户所属影城:{0}, 影城:{1}", loginUserCinemaInnerCode, cinemaInfo.getCode());
            }
        }
    }

    public class CinemaOperatePermissionException extends FaultException {
        public CinemaOperatePermissionException(String messageFormat, Object... messageArgs) {
            super(messageFormat, messageArgs);
        }
    }

    public class CinemaInfoException extends FaultException {
        public CinemaInfoException(String messageFormat, Object... messageArgs) {
            super(messageFormat, messageArgs);
        }
    }

    VerifyResult<PointLocationModel.ImportExcel> getResult(List<PointLocationModel.ImportExcel> dataList, String report) {
        VerifyResult<PointLocationModel.ImportExcel> result = new VerifyResult<>();
        if (StringUtils.isBlank(report)) {
            result.setResult(true);
            result.setList(dataList);
        } else {
            result.setResult(false);
            result.setError(report);
            result.setList(dataList);
        }
        return result;
    }
}