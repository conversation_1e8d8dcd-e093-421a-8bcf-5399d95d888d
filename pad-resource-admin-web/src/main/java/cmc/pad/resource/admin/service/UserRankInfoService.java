package cmc.pad.resource.admin.service;

import cmc.admin.portal.web.integration.security.Users;
import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.admin.adapter.OrganizationServiceAdapter;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.constant.Rank;
import cmc.portal.admin.service.facade.User;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.lang.FaultException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by fuwei on 2022/7/12.
 */
@Slf4j
@Service
public class UserRankInfoService {
    @Autowired
    private RegionService regionInfoService;
    @Autowired
    private OrganizationServiceAdapter organizationServiceAdapter;

    public PointLocationModel.UserRankInfo userRankInfo() {
        User user = Users.currentUser();
        Rank rank = user.getRank();
        if (rank == Rank.CHAIN)
            return organizationServiceAdapter.getChainInfo(user);
        RegionDto.GetRegionRequest request = new RegionDto.GetRegionRequest();
        request.setId(user.getArea());
        String largeWard = regionInfoService.getRegion(request).getRegion().getLargeWard();
        if (rank == Rank.AREA)
            return new PointLocationModel.UserRankInfo(rank.value(), largeWard, user.getArea(), user.getCinemaInnerCode());
        if (rank == Rank.CINEMA)
            return new PointLocationModel.UserRankInfo(rank.value(), largeWard, user.getArea(), user.getCinemaInnerCode());
        throw new FaultException("获取登录用户级别异常");
    }
}
