package cmc.pad.resource.admin.model;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class FileModel {

    @Setter
    @Getter
    public static class SaveReq {
        //文件ID
        private String fileId;
        //文件名
        private String fileName;
        //版本信息
        private String version;
        //生效日期
        private LocalDateTime date;
    }

    @Setter
    @Getter
    public static class View {
        //ID
        private Integer id;
        //文件ID
        private String fileId;
        //文件名
        private String fileName;
        //版本
        private String version;
        //导入状态
        private int status;
        //状态名
        private String statusView;
        //生效日期
        private LocalDate effectiveDate;
        //上传时间
        private LocalDateTime beginTime;
        //导入人
        private String importer;
        //可以作废
        private boolean canDiscard;
        //状态名
        private String statusRemarkView;
    }
}
