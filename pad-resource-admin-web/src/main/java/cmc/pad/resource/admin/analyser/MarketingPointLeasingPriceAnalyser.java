package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/3/15 17:13
 * @Version 1.0
 */
@Component
public class MarketingPointLeasingPriceAnalyser extends AbstractFileAnalyser<PriceModel.MarketingPointExcel> {
    @Autowired
    public MarketingPointLeasingPriceAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<PriceModel.MarketingPointExcel> verifyFile(MultipartFile file, Class<PriceModel.MarketingPointExcel> clazz) {
        List<PriceModel.MarketingPointExcel> dataList = analyseFile(file, clazz);
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        List<String> cinemaLevelCodes = allCinemaLevelCodes();
        List<String> cityLevelCodes = allCityLevelCodes();
        for (int i = 0; i < dataList.size(); i++) {
            PriceModel.MarketingPointExcel excel = dataList.get(i);
            String cinemaLevel = excel.getCinemaLevel().trim();
            checkCinemaLevel(i + 1, cinemaLevelCodes, cinemaLevel, reporter);
            String cityLevel = formatNumericalValue(excel.getCityLevel().trim());
            excel.setCityLevel(cityLevel);
            checkCityLevel(i + 1, cityLevelCodes, cityLevel, reporter);
            int lineNumber = i + 1;
            String priceByArea = excel.getUnitPriceByArea();
            String priceByQuantity = excel.getUnitPriceByQuantity();
            //两者都为空报错
            if (StringUtils.isBlank(priceByArea) && StringUtils.isBlank(priceByQuantity)) {
                reporter.append("第").append(lineNumber).append("行数据,缺少价格设定</br>");
            }
            if (StringUtils.isNotBlank(priceByArea)) {
                checkPrice(lineNumber, priceByArea, reporter, "每平价格");
            } else {
                excel.setUnitPriceByArea("");
            }
            if (StringUtils.isNotBlank(priceByQuantity)) {
                checkPrice(lineNumber, priceByQuantity, reporter, "每个价格");
            } else {
                excel.setUnitPriceByQuantity("");
            }
            String counterKey = cityLevel + ":" + cinemaLevel;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "城市级别+影城级别"));
        return getResult(dataList, reporter.toString());
    }
}
