package cmc.pad.resource.admin.model;

import lombok.Getter;
import lombok.Setter;
import mx.common.excel.annotation.ExcelField;

public class CinemaLevelModel {
    @Setter
    @Getter
    public static class Excel {
        @ExcelField(title = "影城内码", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String code;
        @ExcelField(title = "影城名称", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String name;
        @ExcelField(title = "影城级别", groups = 0, align = 2, sort = 1, width = 5000, fieldType = String.class)
        private String level;
    }

    @Getter
    @Setter
    public static class Query {
        //城市级别
        private String cityDistrictLevel;
        //影城级别
        private String cinemaLevel;
        //影城编码
        private String cinemaCode;
        private int pageIndex;
        private int pageSize;
    }


    @Getter
    @Setter
    public static class View {
        //影城内码
        private String cinemaCode;
        //影城名称
        private String cinemaName;
        //区域
        private String regionName;
        //城市
        private String cityName;
        //城市级别
        private String cityLevel;
        //影城级别
        private String cinemaLevel;
        //城市地区名
        private String cityDistrictName;
        //城市地区级别
        private String cityDistrictLevel;
    }

}
