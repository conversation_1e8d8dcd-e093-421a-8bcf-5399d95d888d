package cmc.pad.resource.admin.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

public class DiscountModel {
    @Getter
    @Setter
    public static class Query {
        private String businessType;
        private int pageIndex;
        private int pageSize;
    }


    @Getter
    @Setter
    public static class View {
        private Integer id;
        private String businessType;
        private String discountType;
        private String discountDesc;
    }

    @Getter
    @Setter
    public static class SaveDiscount {
        private Integer id;
        private String businessType;
        private Integer discountType;
        private List<SaveDiscountRule> discountRules;
    }

    @Getter
    @Setter
    public static class SaveDiscountRule {
        private Integer id;
        private Integer discountId;
        private Integer comparisonSymbol;
        private Float min;
        private Float max;
        private Float factor;
    }

}
