package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.BudgetExcelModel;
import cmc.pad.resource.application.command.point.PointLocationManageService;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.resource.PointLocationModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class BudgetPointLocationAnalyser extends AbstractFileAnalyser<BudgetExcelModel.PointLocation> {

    @Autowired
    private PointLocationManageService pointLocationManageService;

    @Autowired
    public BudgetPointLocationAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    private Map<String, PointLocationModel.ListDataParam> getPointLocationMap(List<BudgetExcelModel.PointLocation> dataList) {
        Map<String, PointLocationModel.ListDataParam> pointLocationMap = new HashMap<>();
        if (CollectionUtils.isEmpty(dataList)) {
            return pointLocationMap;
        }

        Map<String, PointLocationModel.BatchQueryParam> paramMap = new HashMap<>();
        for (BudgetExcelModel.PointLocation pointLocation : dataList) {
            PointLocationModel.BatchQueryParam param = new PointLocationModel.BatchQueryParam();
            param.setCinemaInnerCode(pointLocation.getCinemaInnerCode());
            param.setCode(pointLocation.getResourceCode());
            paramMap.put(pointLocation.getCinemaInnerCode() + ":" + pointLocation.getResourceCode(), param);
        }
        List<PointLocationModel.BatchQueryParam> paramList = paramMap.values().stream().collect(Collectors.toList());
        List<PointLocationModel.ListDataParam> result = pointLocationManageService.batchQuery(paramList);
        for (PointLocationModel.ListDataParam param : result) {
            pointLocationMap.put(param.getCinemaInnerCode() + ":" + param.getCode(), param);
        }
        return pointLocationMap;
    }

    @Override
    public VerifyResult<BudgetExcelModel.PointLocation> verifyFile(MultipartFile file, Class<BudgetExcelModel.PointLocation> clazz) {
        List<BudgetExcelModel.PointLocation> dataList = analyseFile(file, clazz);
        dataList = dataList.stream().filter(excel ->
                StringUtils.isNotBlank(excel.getCinemaInnerCode()) && StringUtils.isNotBlank(excel.getResourceType())
        ).collect(Collectors.toList());
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        //获取所有影院内码
        List<String> cinemaCodes = allCinemaCodes();

        for (int i = 0; i < dataList.size(); i++) {
            BudgetExcelModel.PointLocation excel = dataList.get(i);
            String code = formatNumericalValue(excel.getCinemaInnerCode().trim());
            excel.setCinemaInnerCode(code);
            boolean res = checkCinema(i + 1, cinemaCodes, code, reporter);
            if (!res) {
                break;
            }
            String resourceCode = excel.getResourceCode().trim();
            String counterKey = code + ":" + resourceCode;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "影城编码+资源编码"));
        return getResult(dataList, reporter.toString());
    }

    public VerifyResult<BudgetExcelModel.PointLocation> verifyFile(MultipartFile file, Class<BudgetExcelModel.PointLocation> clazz, Map<String, Cinema> cinemaMap, Map<String, PointLocationModel.ListDataParam> pointLocationMap) {
        List<BudgetExcelModel.PointLocation> dataList = analyseFile(file, clazz);
        dataList = dataList.stream().filter(excel ->
                StringUtils.isNotBlank(excel.getCinemaInnerCode()) && StringUtils.isNotBlank(excel.getResourceType()) && StringUtils.isNotBlank(excel.getResourceCode())
        ).collect(Collectors.toList());
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();

        if (CollectionUtils.isEmpty(dataList)) {
            return getResult(dataList, "没有可用的有效数据");
        }
        pointLocationMap = getPointLocationMap(dataList);


        for (int i = 0; i < dataList.size(); i++) {
            BudgetExcelModel.PointLocation excel = dataList.get(i);
            String code = formatNumericalValue(excel.getCinemaInnerCode().trim());
            excel.setCinemaInnerCode(code);
            boolean res = checkCinemaMap(i + 1, cinemaMap, code, reporter);
            if (!res) {
                break;
            }
            res= chkResourceCode(i + 1,excel,pointLocationMap,reporter);
            if (!res) {
                break;
            }
            res = checkPrice(i + 1, reporter, excel.getMonth1(), excel.getMonth2(), excel.getMonth3(), excel.getMonth4(), excel.getMonth5(), excel.getMonth6(), excel.getMonth7(), excel.getMonth8(), excel.getMonth9(), excel.getMonth10(), excel.getMonth11(), excel.getMonth12());
            if (!res) {
                break;
            }
            String resourceCode = excel.getResourceCode().trim();
            String counterKey = code + ":" + resourceCode;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "影城编码+资源编码"));
        return getResult(dataList, reporter.toString());
    }

    private boolean chkResourceCode(int lineNumber,BudgetExcelModel.PointLocation excel,Map<String, PointLocationModel.ListDataParam> map, StringBuilder reporter){
        String key=excel.getCinemaInnerCode()+":"+excel.getResourceCode();
        boolean res= map.containsKey(key);
        if(!res){
            reporter.append("第").append(lineNumber).append("行: ").append("影城和资源编码不存在:").append(key).append(" 不存在");
        }
        return res;
    }

}
