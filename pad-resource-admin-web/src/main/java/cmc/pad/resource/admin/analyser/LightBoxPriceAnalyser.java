package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@Component
public class LightBoxPriceAnalyser extends AbstractFileAnalyser<PriceModel.LightBoxExcel> {
    @Autowired
    public LightBoxPriceAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<PriceModel.LightBoxExcel> verifyFile(MultipartFile file, Class<PriceModel.LightBoxExcel> clazz) {
        List<PriceModel.LightBoxExcel> dataList = analyseFile(file, clazz);
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        List<String> cinemaLevelCodes = allCinemaLevelCodes();
        List<String> cityLevelCodes = allCityLevelCodes();
        for (int i = 0; i < dataList.size(); i++) {
            PriceModel.LightBoxExcel excel = dataList.get(i);
            String cinemaLevel = excel.getCinemaLevel().trim();
            checkCinemaLevel(i + 1, cinemaLevelCodes, cinemaLevel, reporter);
            String cityLevel = formatNumericalValue(excel.getCityLevel().trim());
            excel.setCityLevel(cityLevel);
            checkCityLevel(i + 1, cityLevelCodes, cityLevel, reporter);
            int lineNumber = i + 1;
            String minTotalPrice = excel.getMinTotalPrice();
            checkPrice(lineNumber, minTotalPrice, reporter, "基础价格");
            String expandedUnitPrice = excel.getExpandedUnitPrice();
            checkPrice(lineNumber, expandedUnitPrice, reporter, "续价");
            checkNumber(lineNumber, excel.getMinArea(), reporter, "基础面积");
            String counterKey = cityLevel + ":" + cinemaLevel;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "城市级别+影城级别"));
        return getResult(dataList, reporter.toString());
    }
}
