package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.basic.datadict.service.dto.DataDictDto;
import cmc.basic.datadict.service.facade.DataDictServiceFacade;
import cmc.pad.resource.admin.model.DiscountModel;
import cmc.pad.resource.application.command.DiscountAppService;
import cmc.pad.resource.application.command.SaveDiscountCommand;
import cmc.pad.resource.application.query.DiscountQueryService;
import cmc.pad.resource.application.query.data.DiscountData;
import cmc.pad.resource.application.query.data.DiscountQueryParam;
import cmc.pad.resource.domain.discount.DiscountRule;
import cmc.portal.admin.service.facade.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/discount")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DiscountController {

    private final DiscountAppService discountAppService;
    private final DiscountQueryService discountQueryService;
    private final DataDictServiceFacade dataDictServiceFacade;

    @Authorize("cmc.pad.resource.discount.list")
    @RequestMapping(value = "toList", method = RequestMethod.GET)
    public ViewResult toList() {
        ViewResult viewResult = new ViewResult("/view/discount/list");
        viewResult.setMenuPath("/pad/resource/discount/toList");
        return viewResult;

    }


    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<DiscountModel.View> list(DiscountModel.Query query) {
        PageResult<DiscountModel.View> pageResult = new PageResult<>();
        DiscountQueryParam queryParam = new DiscountQueryParam();
        BeanUtils.copyProperties(query, queryParam);
        PageResult<DiscountData> discountData = this.discountQueryService.queryDiscountByPage(queryParam);
        DataDictDto.DataDict dataDict = dataDictServiceFacade.getDataDict("419");
        Map<String, DataDictDto.DataDictItem> map = dataDict.getDataDictItems().stream().collect(Collectors.toMap(DataDictDto.DataDictItem::getDictCode, a -> a));
        List<DiscountModel.View> viewList = new ArrayList<>();
        for (DiscountData data : discountData.getItems()) {
            DiscountModel.View view = new DiscountModel.View();
            view.setId(data.getId());
            view.setDiscountDesc(data.getDiscountDesc());
            DataDictDto.DataDictItem businessTypeItem = map.get(data.getBusinessType());
            view.setBusinessType(businessTypeItem != null ? businessTypeItem.getDictName() : "");
            view.setDiscountType(data.getDiscountType());
            viewList.add(view);
        }
        pageResult.setTotalCount(discountData.getTotalCount());
        pageResult.setItems(viewList);
        return pageResult;
    }

    @ResponseBody
    @RequestMapping(value = "save", method = {RequestMethod.GET, RequestMethod.POST})
    public Map<String, Object> save(@RequestBody DiscountModel.SaveDiscount saveDiscount) {
        Map<String, Object> maps = new HashMap<>();
        User user = Users.currentUser();
        SaveDiscountCommand command = new SaveDiscountCommand();
        command.setId(saveDiscount.getId());
        command.setBusinessType(saveDiscount.getBusinessType().trim());
        command.setDiscountType(saveDiscount.getDiscountType());
        command.setUpdator(user.getUserName());
        List<DiscountRule> ruleList = new ArrayList<>();
        saveDiscount.getDiscountRules().forEach(r -> {
            DiscountRule rule = new DiscountRule();
            BeanUtils.copyProperties(r, rule);
            ruleList.add(rule);
        });
        command.setDiscountRules(ruleList);
        try {
            discountAppService.saveDiscount(command);
            maps.put("ack", true);
        } catch (Exception e) {
            maps.put("ack", false);
            maps.put("msg", e.getMessage());
        }
        return maps;
    }

    @RequestMapping(value = "detail", method = RequestMethod.GET)
    @ResponseBody
    public DiscountData detail(Integer id) {
        return discountQueryService.findDiscountById(id);
    }

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> delete(Integer id) {
        Map<String, Object> maps = new HashMap<>();
        try {
            discountAppService.deleteDiscount(id);
            maps.put("ack", true);
        } catch (Exception e) {
            maps.put("ack", false);
            maps.put("msg", e.getMessage());
        }
        return maps;
    }

}
