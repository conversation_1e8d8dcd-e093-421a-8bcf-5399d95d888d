package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.basic.datadict.service.dto.DataDictDto;
import cmc.basic.datadict.service.facade.DataDictServiceFacade;
import cmc.location.front.service.dto.ProvinceDto;
import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.ProvinceService;
import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.admin.adapter.OrganizationServiceAdapter;
import cmc.pad.resource.admin.model.CityModel;
import cmc.pad.resource.application.command.CityAppService;
import cmc.pad.resource.application.query.data.ResultList;
import cmc.pad.resource.domain.city.*;
import cmc.portal.admin.service.constant.Rank;
import cmc.portal.admin.service.facade.User;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/city")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CityController {

    private final RegionService regionInfoService;
    private final ProvinceService provinceService;
    private final CityAppService cityAppService;
    private final DataDictServiceFacade dataDictServiceFacade;
    private final CityRepository cityRepository;
    private final CityDistrictRepository cityDistrictRepository;
    private final OrganizationServiceAdapter organizationServiceAdapter;

    /**
     * 跳转城市级别页面
     */
    @Authorize("cmc.pad.resource.city.list")
    @RequestMapping(value = "toList", method = RequestMethod.GET)
    public ViewResult toList() {
        ViewResult viewResult = new ViewResult("/view/city/list");
        viewResult.setMenuPath("/pad/resource/city/toList");
        return viewResult;
    }

    /**
     * 影城查询
     */
    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<CityDistrictListModel> list(CityModel.Query query) {
        DataDictDto.DataDict dataDict = dataDictServiceFacade.getDataDict("417");
        Map<String, DataDictDto.DataDictItem> map = dataDict.getDataDictItems().stream().collect(Collectors.toMap(DataDictDto.DataDictItem::getDictCode, a -> a));
        ResultList<CityDistrictListModel> result = cityDistrictRepository.find(
                query.getRegionCode(), query.getSuperiorCode(),
                query.getCityCode(), query.getDistrictCode(),
                query.getDistrictName(), query.getDistrictLevel(),
                query.getPageIndex(), query.getPageSize());
        result.getList().forEach(district -> {
            if (!Strings.isNullOrEmpty(district.getLevel()))
                district.setLevel(map.get(district.getLevel()).getDictName());
        });
        return new PageResult(result.getList(), result.getTotalCount());
    }

    /**
     * 查询区域
     */
    @RequestMapping(value = "searchAllRegion", method = RequestMethod.POST)
    @ResponseBody
    public List<RegionDto.Region> searchAllRegion() {
        RegionDto.FindRegionsRequest request = new RegionDto.FindRegionsRequest();
        return regionInfoService.findRegions(request).getItems();
    }

    /**
     * 查询区域
     */
    @RequestMapping(value = "searchAllRegionByRank", method = RequestMethod.POST)
    @ResponseBody
    public List<RegionDto.Region> searchAllRegionByRank(String[] parentId) {
        User user = Users.currentUser();
        Rank rank = user.getRank();
        log.info(">>>根据登录用户级别查询区域, user:{}", JSON.toJSONString(user, true));
        log.info(">>>根据登录用户级别查询区域, parentId:{}", JSON.toJSONString(parentId, true));
        RegionDto.FindRegionsRequest request = new RegionDto.FindRegionsRequest();
        if (rank == Rank.CHAIN) {
            request.setLargeWards(Lists.newArrayList(parentId));
            return regionInfoService.findRegions(request).getItems();
        }
        return Lists.newArrayList();
    }

    /**
     * 查询省/直辖市
     */
    @RequestMapping(value = "searchAllProvince", method = RequestMethod.POST)
    @ResponseBody
    public List<ProvinceDto.Province> searchAllProvince() {
        return provinceService.findProvincesByOldIds(new ProvinceDto.FindProvincesByOldIdsRequest())
                .getProvinces()
                .stream()
                .collect(Collectors.toList());
    }

    /**
     * 查询城市级别
     */
    @RequestMapping(value = "getCityLevel", method = RequestMethod.GET)
    @ResponseBody
    public Object getCityLevel(String code) {
        Optional<City> optional = this.cityRepository.findOne(Filter.create("code", code));
        if (optional.isPresent()) {
            City city = optional.get();
            return city.getCityLevel();
        } else {
            return null;
        }
    }

    /**
     * 查询城市级别
     */
    @RequestMapping(value = "getCityDistrictLevel", method = RequestMethod.GET)
    @ResponseBody
    public Object getCityDistrictLevel(String code) {
        Optional<CityDistrict> optional = this.cityDistrictRepository.findOne(Filter.create("code", code));
        return optional.isPresent() ? optional.get().getLevel() : null;
    }

    /**
     * 修改城市级别
     */
    @RequestMapping(value = "updateCityLevel", method = RequestMethod.POST)
    @ResponseBody
    public String updateCityLevel(HttpServletRequest request) {
        String code = request.getParameter("code");
        String cityLevel = request.getParameter("cityLevel");
        return cityRepository.updateCityLevelByCode(code, cityLevel) ? "success" : "false";
    }

    @RequestMapping(value = "updateCityDistrictLevel", method = RequestMethod.POST)
    @ResponseBody
    public void updateCityDistrictLevel(HttpServletRequest request) {
        cityDistrictRepository
                .updateLevelByCode(
                        request.getParameter("code"),
                        request.getParameter("cityDistrictLevel")
                );
    }


}
