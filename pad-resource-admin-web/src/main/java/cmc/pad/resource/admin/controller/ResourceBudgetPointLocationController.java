package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.pad.resource.admin.analyser.BudgetPointLocationAnalyser;
import cmc.pad.resource.admin.analyser.VerifyResult;
import cmc.pad.resource.admin.facade.FileServiceFacade;
import cmc.pad.resource.admin.mapper.ResourceBudgetMapper;
import cmc.pad.resource.admin.model.BudgetExcelModel;
import cmc.pad.resource.admin.model.BudgetHallParam;
import cmc.pad.resource.application.command.*;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.domain.budget.*;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImportStatus;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.facade.User;
import cmc.portal.admin.service.iface.AuthUserService;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.security.auth.Authorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2022/1/10 9:24
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/budget/point_location")
public class ResourceBudgetPointLocationController extends BaseController{

    @Autowired
    private FileServiceFacade fileServiceFacade;

    @Autowired
    private FileImportingAppService fileImportingAppService;

    @Autowired
    private BudgetPointLocationAnalyser budgetPointLocationAnalyser;

    @Autowired
    private ResourceBudgetPointLocationAppService resourceBudgetPointLocationAppService;

    @Autowired
    ResourceBudgetPointLocationController(FileServiceFacade fileServiceFacade, FileImportingAppService fileImportingAppService, FileImportingQueryService fileImportingQueryService, BudgetPointLocationAnalyser fileAnalyser, DictionaryDomainService dictionaryService, AuthUserService authUserService) {
        super(fileServiceFacade, fileImportingAppService, fileImportingQueryService, fileAnalyser, dictionaryService, authUserService);
    }

    @Authorize("cmc.pad.resource.budget.point_location.list")
    @RequestMapping(value = "list", method = RequestMethod.GET)
    public ViewResult list() {
        Map<String, Object> data = new HashMap<>();
        data.put("currentYear", LocalDate.now().getYear());
        data.put("resourceTypeMap",resourceBudgetPointLocationAppService.getBusinessMap());
        ViewResult viewResult = new ViewResult("/view/budget/position/list", data);
        viewResult.addEnum(FileImportStatus.class, true);
        viewResult.setMenuPath("/pad/resource/budget/point_location/list");
        return viewResult;
    }

    @RequestMapping(value = "list_data")
    @ResponseBody
    public Map<String,Object> listHall(String year, String regionCode, String cinemaInnerCode, String resourceType,String resourceCode, int pageSize, int pageNumber) {
        Map<String,Object> map=new HashMap<>();

        PageResult<ResourceBudgetPointLocationView> result = resourceBudgetPointLocationAppService.getPageList(year, regionCode, cinemaInnerCode, resourceType,resourceCode, pageSize, pageNumber);
        map.put("total",result.getTotalCount());
        map.put("rows",result.getItems());
        return map;
    }

    @RequestMapping(value = "list_data_record")
    @ResponseBody
    public List<ResourceBudgetPointLocationChangeRecordView> listHallRecord(String year, String regionCode, String cinemaInnerCode, String resourceCode) {
        List<ResourceBudgetPointLocationChangeRecordView> result = resourceBudgetPointLocationAppService.getChangeRecordList(year, regionCode, cinemaInnerCode, resourceCode);
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "file/list", method = RequestMethod.POST)
    public List fileList() {
        return fileRecordList(FileCategory.BUDGET_POSITION);
    }

    @RequestMapping("upload")
    @ResponseBody
    public Map<String, Object> importExcel(@RequestParam MultipartFile exFile, @RequestParam int year, @RequestParam int category) {
        Map<String, Object> result = new HashMap<>();
        if (year == 0) {
            log.error("预算年份未选择!");
            result.put("state", false);
            result.put("msg", "预算年份未选择");
            return result;
        }
        if (category != FileCategory.BUDGET_POSITION.value()) {
            log.error("文件导入选择的业务分类异常（category）!");
            result.put("state", false);
            result.put("msg", "文件导入选择的业务分类异常");
            return result;
        }
        User user = Users.currentUser();
        FileCategory fileCategory = FileCategory.valueOf(category);
        int recordId = 0;
        //1校验数据，2有错误写错误信息，3，上传文件服务器，4数据导入数据库
        Map<String, Cinema> cinemaMap = budgetPointLocationAnalyser.getAllCinemas();
        Map<String, PointLocationModel.ListDataParam> pointLocationMap=new HashMap<>();
        //上传到文件服务器
        try {
            String filename = exFile.getOriginalFilename();
            //上传文件
            String fileId = fileServiceFacade.uploadFile(exFile.getBytes(), filename);
            //保存上传记录
            SaveImportCommand command = new SaveImportCommand();
            command.setFileId(fileId);
            command.setFileName(filename);
            command.setVersion(String.valueOf(System.nanoTime()));
            command.setEffectiveDate(LocalDate.now());
            command.setImporter(user.getId());
            command.setFileCategory(fileCategory);
            recordId = fileImportingAppService.importStarted(command);
        } catch (IOException e) {
            log.error("文件导入失败!:{}", e);
            result.put("state", false);
            result.put("msg", "文件导入失败");
        }
        //校验数据
        VerifyResult<BudgetExcelModel.PointLocation> verifyResult = new VerifyResult<>();
        verifyResult.setResult(false);
        verifyResult.setError("文件解析失败");
        try {
            verifyResult = budgetPointLocationAnalyser.verifyFile(exFile, BudgetExcelModel.PointLocation.class, cinemaMap,pointLocationMap);
            if (verifyResult.isResult()) {
                fileImportingAppService.importCompleted(recordId);
                result.put("state", true);
            } else {
                fileImportingAppService.importFailed(recordId, verifyResult.getError());
                result.put("state", false);
                result.put("msg", verifyResult.getError());
            }
        } catch (Exception e) {
            fileImportingAppService.importFailed(recordId, verifyResult.getError());
            result.put("state", false);
            result.put("msg", "上传失败");
        }

        //导入数据
        if (verifyResult.isResult() && !CollectionUtils.isEmpty(verifyResult.getList())) {
            resourceBudgetPointLocationAppService.importData(ResourceBudgetMapper.getBudgetPointLocationsByExcel(verifyResult.getList(), String.valueOf(year), recordId, cinemaMap, user,pointLocationMap));
        }
        return result;
    }

    @RequestMapping(value = "modify_point_location")
    @ResponseBody
    public Map<String, Object> modifyBudget(BudgetHallParam param) {
        Map<String, Object> result = new HashMap<>();
        ResourceBudgetPointLocation pointLocation = resourceBudgetPointLocationAppService.get(param.getId());
        if (pointLocation == null) {
            result.put("state", false);
            result.put("msg", "没有这条数据");
            return result;
        }
        User user = Users.currentUser();
        pointLocation.setUserName(user.getUserName());
        pointLocation.setUpdateTime(LocalDateTime.now());
        pointLocation.setUserId(user.getId());
        pointLocation.setBudgetMonth1(param.getMonth1());
        pointLocation.setBudgetMonth2(param.getMonth2());
        pointLocation.setBudgetMonth3(param.getMonth3());
        pointLocation.setBudgetMonth4(param.getMonth4());
        pointLocation.setBudgetMonth5(param.getMonth5());
        pointLocation.setBudgetMonth6(param.getMonth6());
        pointLocation.setBudgetMonth7(param.getMonth7());
        pointLocation.setBudgetMonth8(param.getMonth8());
        pointLocation.setBudgetMonth9(param.getMonth9());
        pointLocation.setBudgetMonth10(param.getMonth10());
        pointLocation.setBudgetMonth11(param.getMonth11());
        pointLocation.setBudgetMonth12(param.getMonth12());
        pointLocation.setBudgetYear(pointLocation.getBudgetMonth1().add(pointLocation.getBudgetMonth2()).add(pointLocation.getBudgetMonth3()).add(pointLocation.getBudgetMonth4()).add(pointLocation.getBudgetMonth5()).add(pointLocation.getBudgetMonth6()).add(pointLocation.getBudgetMonth7()).add(pointLocation.getBudgetMonth8()).add(pointLocation.getBudgetMonth9()).add(pointLocation.getBudgetMonth10()).add(pointLocation.getBudgetMonth11()).add(pointLocation.getBudgetMonth12()));
        resourceBudgetPointLocationAppService.editData(pointLocation);
        result.put("state", true);
        result.put("msg", "修改成功");
        return result;
    }

}
