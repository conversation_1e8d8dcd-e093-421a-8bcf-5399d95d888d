package cmc.pad.resource.admin.adapter;

import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.constant.Rank;
import cmc.portal.admin.service.dto.OrganizationDto;
import cmc.portal.admin.service.facade.User;
import cmc.portal.admin.service.iface.OrganizationService;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by fuwei on 2022/7/8.
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrganizationServiceAdapter {
    private final OrganizationService organizationService;

    public PointLocationModel.UserRankInfo getChainInfo(User user) {
        log.info(">>>查询院线级所属大区 rank:{}-userId:{}-username:{}-orgId:{}", user.getRank(), user.getId(), user.getUserName(), user.getOrgId());
        OrganizationDto.FindListByRankRequest rankRequest = new OrganizationDto.FindListByRankRequest();
        rankRequest.setRank(Rank.CHAIN);
        OrganizationDto.FindListByRankResponse response = organizationService.findListByRank(rankRequest);
        log.info(">>>查询院线级所属大区 rank:{}-userId:{}-username:{}-orgId:{}\nresponse list:{}",
                user.getRank(), user.getId(), user.getUserName(), user.getOrgId(),
                response
        );
        OrganizationDto.Organization organization = response.getItems().stream().filter(item -> item.getId() == user.getOrgId().intValue()).findFirst().get();
        PointLocationModel.UserRankInfo userRankInfo = new PointLocationModel.UserRankInfo(organization.getRank().value(), organization.getDataId(), user.getArea(), user.getCinemaInnerCode());
        if ("createBySelf".equals(organization.getDataId())) {
            userRankInfo.setLargeWardCode("0");
        }
        log.info(">>>查询院线级所属大区 user:{}-{}-{}-{}\n过滤的大区:{}",
                user.getRank(), user.getId(), user.getUserName(), user.getOrgId(),
                JSON.toJSONString(userRankInfo)
        );
        return userRankInfo;
    }
}
