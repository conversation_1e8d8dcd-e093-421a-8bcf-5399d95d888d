package cmc.pad.resource.admin.mapper;

import cmc.pad.resource.admin.model.BudgetExcelModel;
import cmc.pad.resource.domain.budget.ResourceBudgetHall;
import cmc.pad.resource.domain.budget.ResourceBudgetPointLocation;
import cmc.pad.resource.domain.cinema.Cinema;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.portal.admin.service.facade.User;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022/1/14 13:51
 */
public class ResourceBudgetMapper {
    public static List<ResourceBudgetHall> getBudgetHallsByExcel(List<BudgetExcelModel.Hall> list, String year, int recordId, Map<String, Cinema> cinemas, User user) {
        List<ResourceBudgetHall> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (BudgetExcelModel.Hall excel : list) {
            ResourceBudgetHall hall = new ResourceBudgetHall();
            hall.setImportId(recordId);
            hall.setYear(year);
            hall.setBudgetMonth1(new BigDecimal(excel.getMonth1()));
            hall.setBudgetMonth2(new BigDecimal(excel.getMonth2()));
            hall.setBudgetMonth3(new BigDecimal(excel.getMonth3()));
            hall.setBudgetMonth4(new BigDecimal(excel.getMonth4()));
            hall.setBudgetMonth5(new BigDecimal(excel.getMonth5()));
            hall.setBudgetMonth6(new BigDecimal(excel.getMonth6()));
            hall.setBudgetMonth7(new BigDecimal(excel.getMonth7()));
            hall.setBudgetMonth8(new BigDecimal(excel.getMonth8()));
            hall.setBudgetMonth9(new BigDecimal(excel.getMonth9()));
            hall.setBudgetMonth10(new BigDecimal(excel.getMonth10()));
            hall.setBudgetMonth11(new BigDecimal(excel.getMonth11()));
            hall.setBudgetMonth12(new BigDecimal(excel.getMonth12()));
            hall.setBudgetYear(hall.getBudgetMonth1().add(hall.getBudgetMonth2()).add(hall.getBudgetMonth3()).add(hall.getBudgetMonth4()).add(hall.getBudgetMonth5()).add(hall.getBudgetMonth6()).add(hall.getBudgetMonth7()).add(hall.getBudgetMonth8()).add(hall.getBudgetMonth9()).add(hall.getBudgetMonth10()).add(hall.getBudgetMonth11()).add(hall.getBudgetMonth12()));
            hall.setCreateTime(LocalDateTime.now());
            Cinema cinema = cinemas.get(excel.getCinemaInnerCode());
            hall.setRegionCode(cinema.getRegionCode());
            hall.setRegionName(cinema.getRegionName());
            hall.setCinemaInnerCode(excel.getCinemaInnerCode());
            hall.setCinemaName(cinema.getName());
            hall.setResourceType(excel.getResourceType());
            hall.setUpdateTime(LocalDateTime.now());
            hall.setUserName(user.getUserName());
            hall.setUserId(user.getId());
            result.add(hall);
        }

        return result;
    }

    public static List<ResourceBudgetPointLocation> getBudgetPointLocationsByExcel(List<BudgetExcelModel.PointLocation> list, String year, int recordId, Map<String, Cinema> cinemas, User user, Map<String, PointLocationModel.ListDataParam> pointLocationMap) {
        List<ResourceBudgetPointLocation> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (BudgetExcelModel.PointLocation excel : list) {
            ResourceBudgetPointLocation location = new ResourceBudgetPointLocation();
            location.setImportId(recordId);
            location.setYear(year);
            location.setBudgetMonth1(new BigDecimal(excel.getMonth1()));
            location.setBudgetMonth2(new BigDecimal(excel.getMonth2()));
            location.setBudgetMonth3(new BigDecimal(excel.getMonth3()));
            location.setBudgetMonth4(new BigDecimal(excel.getMonth4()));
            location.setBudgetMonth5(new BigDecimal(excel.getMonth5()));
            location.setBudgetMonth6(new BigDecimal(excel.getMonth6()));
            location.setBudgetMonth7(new BigDecimal(excel.getMonth7()));
            location.setBudgetMonth8(new BigDecimal(excel.getMonth8()));
            location.setBudgetMonth9(new BigDecimal(excel.getMonth9()));
            location.setBudgetMonth10(new BigDecimal(excel.getMonth10()));
            location.setBudgetMonth11(new BigDecimal(excel.getMonth11()));
            location.setBudgetMonth12(new BigDecimal(excel.getMonth12()));
            location.setBudgetYear(location.getBudgetMonth1().add(location.getBudgetMonth2()).add(location.getBudgetMonth3()).add(location.getBudgetMonth4()).add(location.getBudgetMonth5()).add(location.getBudgetMonth6()).add(location.getBudgetMonth7()).add(location.getBudgetMonth8()).add(location.getBudgetMonth9()).add(location.getBudgetMonth10()).add(location.getBudgetMonth11()).add(location.getBudgetMonth12()));
            location.setCreateTime(LocalDateTime.now());
            Cinema cinema = cinemas.get(excel.getCinemaInnerCode());
            location.setRegionCode(cinema.getRegionCode());
            location.setRegionName(cinema.getRegionName());
            location.setCinemaInnerCode(excel.getCinemaInnerCode());
            location.setCinemaName(cinema.getName());
            location.setResourceCode(excel.getResourceCode());
            location.setUpdateTime(LocalDateTime.now());
            location.setUserName(user.getUserName());
            location.setUserId(user.getId());
            PointLocationModel.ListDataParam param = pointLocationMap.get(location.getCinemaInnerCode() + ":" + location.getResourceCode());
//            if (param != null) {
//                location.setResourceType(param.getBusinessTypeName());
//                location.setAreaSize(param.getSellArea());
//                location.setLandMode(param.getLandingMode());
//                location.setLocationDescription(param.getLocationDesc());
//                location.setResourceAffiliation(param.getResourceOwnershipName());
//                location.setUsePlan(param.getPlanUse());
//            }
            result.add(location);
        }

        return result;
    }

}
