package cmc.pad.resource.admin.analyser;

import cmc.pad.resource.admin.model.PriceModel;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.domain.dictionary.DictionaryDomainService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/3/15 17:13
 * @Version 1.0
 */
@Component
public class MovieHallNamingLeasingPriceAnalyser extends AbstractFileAnalyser<PriceModel.MovieHallNamingExcel> {
    @Autowired
    public MovieHallNamingLeasingPriceAnalyser(DictionaryDomainService dictionaryService, CinemaQueryService cinemaQueryService) {
        super(dictionaryService, cinemaQueryService);
    }

    @Override
    public VerifyResult<PriceModel.MovieHallNamingExcel> verifyFile(MultipartFile file, Class<PriceModel.MovieHallNamingExcel> clazz) {
        List<PriceModel.MovieHallNamingExcel> dataList = analyseFile(file, clazz);
        StringBuilder reporter = new StringBuilder();
        Map<String, Integer> counter = new HashMap<>();
        //获取所有影院内码
        List<String> cinemaCodes = allCinemaCodes();
        List<String> movieHallTypeCodes = allMovieHallTypeCodes();
        for (int i = 0; i < dataList.size(); i++) {
            PriceModel.MovieHallNamingExcel excel = dataList.get(i);
            String cinemaCode = formatNumericalValue(excel.getCinemaCode().trim());
            excel.setCinemaCode(cinemaCode);
            checkCinema(i + 1, cinemaCodes, cinemaCode, reporter);
            String movieHallType = excel.getMovieHallType();
            if (StringUtils.isNotBlank(movieHallType)) {
                checkMovieHallType(i + 1, movieHallTypeCodes, movieHallType, reporter);
            } else {
                movieHallType = "";
                excel.setMovieHallType(movieHallType);
            }
            checkPrice(i + 1, excel.getUnitPrice(), reporter, "基础价格");
            String counterKey = cinemaCode + ":" + movieHallType;
            duplicateCount(counter, counterKey);
        }
        reporter.append(checkDuplicate(counter, "影城编码+影厅编码"));
        return getResult(dataList, reporter.toString());
    }
}
