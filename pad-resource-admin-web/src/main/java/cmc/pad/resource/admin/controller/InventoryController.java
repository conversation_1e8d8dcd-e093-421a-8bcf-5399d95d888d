package cmc.pad.resource.admin.controller;

import cmc.admin.portal.web.integration.ViewResult;
import cmc.admin.portal.web.integration.security.Users;
import cmc.pad.resource.admin.model.InventoryModel;
import cmc.pad.resource.admin.model.OccupationModel;
import cmc.pad.resource.application.query.InventoryQueryService;
import cmc.pad.resource.application.query.data.InventoryData;
import cmc.pad.resource.application.query.data.InventoryQueryParam;
import cmc.pad.resource.domain.inventory.Occupation;
import cmc.pad.resource.domain.inventory.OccupationRepository;
import cmc.pad.resource.domain.inventory.OccupationStatus;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import mtime.lark.db.jsd.Filter;
import mtime.lark.db.jsd.FilterType;
import mtime.lark.pb.utils.StringUtils;
import mtime.lark.util.cache.Cache;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.lang.FaultException;
import mtime.lark.util.log.Logger;
import mtime.lark.util.log.LoggerManager;
import mtime.lark.util.msg.Publisher;
import mtime.lark.util.security.auth.Authorize;
import mtime.lark.web.result.AjaxResult;
import mx.common.file.service.dto.FileDto;
import mx.common.file.service.iface.FileService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static mtime.lark.db.jsd.Shortcut.f;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "pad/resource/inventory")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class InventoryController {

    private final static Logger excelLogger = LoggerManager.getLogger("ExportInventory");
    private final static String KEY_INVENTORY = "pad_resource_inventory";
    private final static String PUBLIC = "public";
    private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private final static DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final InventoryQueryService inventoryQueryService;
    private final OccupationRepository occupationRepository;
    private final FileService fileService;

    /**
     * 跳转资源库存列表页面
     */
    @Authorize("cmc.pad.resource.inventory.list")
    @RequestMapping(value = "toList", method = RequestMethod.GET)
    public ViewResult toList() {
        ViewResult viewResult = new ViewResult("/view/inventory/list");
        viewResult.setMenuPath("/pad/resource/inventory/toList");
        return viewResult;

    }

    /**
     * 资源库存查询
     */
    @ResponseBody
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public PageResult<InventoryModel.View> list(InventoryModel.Query query) {
        PageResult<InventoryModel.View> pageResult = new PageResult<>();
        InventoryQueryParam queryParam = new InventoryQueryParam();
        BeanUtils.copyProperties(query, queryParam);
        PageResult<InventoryData> queryResults = inventoryQueryService.queryInventoryByPage(queryParam);
        if (!CollectionUtils.isEmpty(queryResults.getItems())) {
            List<InventoryModel.View> viewList = new ArrayList<>();
            for (InventoryData data : queryResults.getItems()) {
                InventoryModel.View view = new InventoryModel.View();
                BeanUtils.copyProperties(data, view);
                view.setDateStr(sdf.format(data.getDate()));
                view.setMarketingPointLeasableAreaStr(data.getTotalMarketingPointLeasableArea() + "/" + data.getSoldMarketingPointLeasableArea());
                view.setAdvertisingPointLeasableQuantityStr(data.getTotalAdvertisingPointLeasableQuantity() + "/" + data.getSoldAdvertisingPointLeasableQuantity());
                view.setOuterAreaLeasableAreaStr(data.getTotalOuterAreaLeasableArea() + "/" + data.getSoldOuterAreaLeasableArea());
                view.setFixedPointLeasableAreaStr(data.getTotalFixedPointLeasableArea() + "/" + data.getSoldFixedPointLeasableArea());
                viewList.add(view);
            }
            pageResult.setItems(viewList);
            pageResult.setTotalCount(queryResults.getTotalCount());
        }
        return pageResult;
    }


    @ResponseBody
    @RequestMapping(value = "queryOccupationList", method = RequestMethod.POST)
    public List<OccupationModel.View> queryOccupationList(String cinemaCode, String date, String businessType) {
        Filter filter = Filter.create("cinema_code", cinemaCode);
        filter = filter.and(f("business_type", businessType.toUpperCase()));
        filter = filter.and(f("start_date", FilterType.LTE, date));
        filter = filter.and(f("end_date", FilterType.GTE, date));
        List<Occupation> occupations = occupationRepository.findMany(filter);
        List<OccupationModel.View> viewList = new ArrayList<>();
        for (Occupation occupation : occupations) {
            OccupationModel.View view = new OccupationModel.View();
            view.setContractNo(occupation.getContractNo());
            view.setStartDateStr(fmt.format(occupation.getStartDate()));
            view.setEndDateStr(fmt.format(occupation.getEndDate()));
            view.setAmount(occupation.getAmount().toString());
            if (occupation.getStatus() == OccupationStatus.ACTIVE) {
                view.setStatusStr("占用");
            } else if (occupation.getStatus() == OccupationStatus.CANCEL) {
                view.setStatusStr("取消占用");
            }
            viewList.add(view);
        }
        return viewList;
    }

    /**
     * 导出资源库存
     */
    @ResponseBody
    @RequestMapping(value = "exportInventory")
    public AjaxResult exportInventory(InventoryModel.Query reqParam) throws IOException {

        AjaxResult result = new AjaxResult();
        result.setSuccess(true);
        excelLogger.info("开始移除无用的文件信息");
        String fileKey = String.valueOf(Users.currentUser().getEhrUserId());
        fileKey = fileKey + KEY_INVENTORY;
        deleteFileInfo(fileKey);
        excelLogger.info("成功移除无用的文件信息");

        log.info("开始异步的发送mq消息");
        Map<String, String> data = new HashMap<>();
        reqParam.setAuthUser(Users.currentUser());
        String body = JSON.toJSONString(reqParam);
        data.put("data", body);
        String dataBody = JSON.toJSONString(data);
        Publisher.get().publish("PAD_RESOURCE_INVENTORY_EXPORT_TOPIC", dataBody);
        log.info("成功异步的发送mq消息");
        return result;
    }

    /**
     * 删除无用的文件信息
     */
    private void deleteFileInfo(String key) {
        log.info("开始删除无用的文件信息，key值为{}", key);
        String fieId = Cache.get(String.class, key);
        Cache.remove(key);
        log.info(Cache.get(String.class, key));
        if (fieId == null || fieId.isEmpty()) {
            log.info("成功删除无用的文件信息，key值为{}", key);
            return;
        }
        if ("error".equals(fieId)) {
            log.info("成功删除无用的文件信息，key值为{}", key);
            return;
        }
        FileDto.RemoveFileRequest request = new FileDto.RemoveFileRequest();
        request.setFileId(fieId);
        request.setStorageType(PUBLIC);
        fileService.removeFile(request);
        log.info("成功删除无用的文件信息，key值为{},field的id为{}", key, fieId);
    }

    @ResponseBody
    @RequestMapping(value = "getExportStatus")
    public AjaxResult getExportStatus() {
        AjaxResult result = new AjaxResult();
        String redisKey = String.valueOf(Users.currentUser().getEhrUserId());
        redisKey = redisKey + KEY_INVENTORY;
        //获取导出数据的状态
        String fieId = Cache.get(String.class, redisKey);
        if (StringUtils.isEmpty(fieId)) {
            result.setSuccess(false);
            return result;
        }
        result.setSuccess(true);
        result.setValue(fieId);
        return result;
    }

    @RequestMapping("downLoadInventory")
    public void downLoadInventory(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String redisKey = String.valueOf(Users.currentUser().getEhrUserId());
        redisKey = redisKey + KEY_INVENTORY;
        String fieId = Cache.get(String.class, redisKey);
        FileDto.DownloadFileRequest req = new FileDto.DownloadFileRequest();
        req.setFileId(fieId);
        req.setStorageType(PUBLIC);
        FileDto.DownloadFileResponse resp = fileService.downloadFile(req);
        byte[] fileContent = resp.getFileContent();
        InputStream is = new ByteArrayInputStream(fileContent);
        response.reset();
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String filename = "阵地广告资源库存列表.xls";
        String header = request.getHeader("User-Agent");
        if (header.toUpperCase().contains("MSIE") || header.contains("Trident")) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes(), "ISO8859-1");
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        ServletOutputStream out = response.getOutputStream();
        try (BufferedInputStream bis = new BufferedInputStream(is);
             BufferedOutputStream bos = new BufferedOutputStream(out)) {
            byte[] buff = new byte[5120];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
                bos.flush();
            }
            bos.close();
        } catch (final IOException e) {
            throw new FaultException("导出失败：{}", e);
        }

    }
}
