package cmc.pad.resource.admin.model;

import cmc.portal.admin.service.facade.User;
import lombok.Getter;
import lombok.Setter;

public class InventoryModel {
    @Getter
    @Setter
    public static class Query {
        private String regionCode;
        private String cinemaCode;
        private String startDate;
        private String endDate;
        private int pageIndex;
        private int pageSize;
        private User authUser;
    }


    @Getter
    @Setter
    public static class View {
        private String cinemaCode;
        private String regionName;
        private String cinemaName;
        private String dateStr;
        private String marketingPointLeasableAreaStr;
        private String outerAreaLeasableAreaStr;
        private String fixedPointLeasableAreaStr;
        private String advertisingPointLeasableQuantityStr;
    }

}
