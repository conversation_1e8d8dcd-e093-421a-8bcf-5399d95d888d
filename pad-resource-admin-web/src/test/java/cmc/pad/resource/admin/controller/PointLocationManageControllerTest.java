package cmc.pad.resource.admin.controller;

import cmc.location.front.service.iface.RegionService;
import cmc.pad.resource.adapter.FileServiceAdapter;
import cmc.pad.resource.application.query.CinemaQueryService;
import cmc.pad.resource.application.query.FileImportingQueryService;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImport;
import cmc.pad.resource.domain.resource.PointLocationModel;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlFileImportRepository;
import cmc.pad.resource.infrastructures.repository.mysql.MysqlPointLocationInfoRepository;
import cmc.portal.admin.service.constant.Rank;
import cmc.portal.admin.service.dto.OrganizationDto;
import cmc.portal.admin.service.iface.OrganizationService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import mtime.lark.db.jsd.SortType;
import mtime.lark.db.jsd.Sorters;
import mtime.lark.util.data.PageResult;
import mtime.lark.util.ioc.ServiceLocator;
import mx.common.excel.ExportExcel;
import mx.common.excel.bean.XBeanExport;
import org.junit.Test;
import org.springframework.web.multipart.MultipartFile;
import pad.AbstractCmcQasTest;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by fuwei on 2022/1/16.
 */
public class PointLocationManageControllerTest extends AbstractCmcQasTest {

    PointLocationManageController controller = ServiceLocator.current().getInstance(PointLocationManageController.class);
    MysqlPointLocationInfoRepository resp = ServiceLocator.current().getInstance(MysqlPointLocationInfoRepository.class);
    MysqlFileImportRepository fileImportResp = ServiceLocator.current().getInstance(MysqlFileImportRepository.class);
    FileServiceAdapter fileServiceAdapter = ServiceLocator.current().getInstance(FileServiceAdapter.class);
    FileImportingQueryService service = ServiceLocator.current().getInstance(FileImportingQueryService.class);
    CinemaQueryService cinemaQueryService = ServiceLocator.current().getInstance(CinemaQueryService.class);
    RegionService regionService = ServiceLocator.current().getInstance(RegionService.class);
    OrganizationService organizationService = ServiceLocator.current().getInstance(OrganizationService.class);

    @Test
    public void ss() {
        OrganizationDto.FindListByRankRequest rankRequest = new OrganizationDto.FindListByRankRequest();
        rankRequest.setRank(Rank.CHAIN);
        OrganizationDto.FindListByRankResponse response = organizationService.findListByRank(rankRequest);
        System.out.println(JSON.toJSONString(response, true));

        OrganizationDto.GetOrgnzByIdRequest request = new OrganizationDto.GetOrgnzByIdRequest();
        request.setId(722);
        System.out.printf(" >>> " + JSON.toJSONString(organizationService.getOrgnzById(request).getChainInfo().getName(), true));
    }

    @Test
    public void ttt() throws IOException {
        List list = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();
        cinemaQueryService.queryAllCinema().forEach(cinema -> {
            if (Integer.valueOf(cinema.getCode()) > 300 && Integer.valueOf(cinema.getCode()) < 900) {
                for (int j = 0; j < 100; j++) {
                    PointLocationModel.ImportExcel excel = new PointLocationModel.ImportExcel();
                    excel.setCinemaInnerCode(cinema.getCode());
                    excel.setCinemaName(cinema.getName());
                    excel.setResourceType("WBZY");
                    excel.setResourceOwnership("2");
                    excel.setResourceCode(String.format("RC-%s-%s", cinema.getCode(), j));
                    excel.setLocationDesc("位置描述");
                    excel.setPlanUse("规划用途");
                    excel.setLandingMode("落地方式");
                    excel.setSellArea("100.56");
                    System.out.println(" >>> " + (i.incrementAndGet()) + JSON.toJSONString(excel));
                    list.add(excel);
                }
            }
        });
        XBeanExport xbean = ExportExcel.BeanExport(PointLocationModel.ImportExcel.class);
        xbean.createBeanSheet("点位资源信息", null, PointLocationModel.ImportExcel.class).addData(list);
        FileOutputStream fileOutputStream = new FileOutputStream(new File("src/test/java/cmc/pad/resource/admin/controller/point_template1.xlsx"));
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            xbean.write(stream);
            xbean.dispose();
//            stream.writeTo(fileOutputStream);
            String fileId = fileServiceAdapter.uploadFile(stream.toByteArray(), "test.xlsx");
            System.out.println(">>>fileId:" + fileId);
            byte[] bytes = fileServiceAdapter.downloadFile(fileId);
            fileOutputStream.write(bytes);
            fileServiceAdapter.deleteFile(fileId);
        }
    }

    @Test
    public void testPointLocation() {
        List<PointLocationModel.BatchQueryParam> params = Lists.newArrayList();
        params.add(new PointLocationModel.BatchQueryParam("849", "Y12"));
        params.add(new PointLocationModel.BatchQueryParam("849", "Y130"));
        System.out.printf(" >>> " + JSON.toJSONString(resp.batchQuery(params), true));
    }

    @Test
    public void testImportExcel() {
        MultipartFile exFile = new MultipartFile() {
            @Override
            public String getName() {
                return "点位资源.xls";
            }

            @Override
            public String getOriginalFilename() {
                return "点位资源.xls";
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return new FileInputStream(new File("src/test/java/cmc/pad/resource/admin/controller/point_template.xlsx"));
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
        System.out.printf(" >>> " + JSON.toJSONString(controller.importExcel(exFile), true));
    }

    @Test
    public void testDownload() throws IOException {
        byte[] bytes = fileServiceAdapter.downloadFile("220120145809573253.xlsx");
        FileOutputStream fileOutputStream = new FileOutputStream(new File("src/test/java/cmc/pad/resource/admin/controller/point_template1.xlsx"));
        fileOutputStream.write(bytes);
        fileOutputStream.close();
    }

    @Test
    public void testFileImport() {
        PageResult<FileImport> page = service.page(FileCategory.POINT_LOCATION, new Sorters(SortType.DESC, "id"), 10, 1);
        System.out.printf(" >>> " + JSON.toJSONString(page, true));
        System.out.println(page.getTotalCount());
        List<FileImport> list = service.list(FileCategory.POINT_LOCATION);
        System.out.printf(" >>> " + JSON.toJSONString(list, true));
    }
}