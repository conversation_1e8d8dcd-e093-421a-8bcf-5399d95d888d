package cmc.pad.resource.admin.controller;

import cmc.pad.resource.domain.resource.PointLocationModel;
import com.alibaba.fastjson.JSON;
import mtime.lark.util.lang.FaultException;
import mx.common.excel.ExportExcel;
import mx.common.excel.ImportExcel;
import mx.common.excel.bean.XBeanExport;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by fuwei on 2022/1/16.
 */
public class TmpTest {
    @Test
    public void tt() {
        System.out.println(NumberUtils.isNumber("23"));
        System.out.println(NumberUtils.isNumber("23.12"));
        File file = new File("src/test/java/cmc/pad/resource/admin/controller/point_template.xlsx");
        System.out.println(file.getAbsoluteFile());
        try {
            ImportExcel excel = new ImportExcel(file.getName(), new FileInputStream(file), 0, 0);
            List<PointLocationModel.ImportExcel> dataList = excel.getDataList(PointLocationModel.ImportExcel.class);
            dataList.stream().forEach(data -> {
                System.out.println(JSON.toJSONString(data));
            });
        } catch (Exception e) {
            throw new FaultException("解析文件出错:{}", e);
        }
    }


    @Test
    public void getExcel() throws IOException {
        List list = new ArrayList<>();
        PointLocationModel.ImportExcel excel = new PointLocationModel.ImportExcel();
        excel.setCinemaInnerCode("849");
        excel.setCinemaName("123.0");
        excel.setResourceType("123");
        excel.setResourceOwnership("123");
        excel.setResourceCode("123");
        excel.setLocationDesc("123");
        excel.setPlanUse("123");
        excel.setLandingMode("123");
        excel.setSellArea("123");
        list.add(excel);
        XBeanExport xbean = ExportExcel.BeanExport(PointLocationModel.ImportExcel.class);
        xbean.createBeanSheet("点位资源信息", null, PointLocationModel.ImportExcel.class).addData(list);
        FileOutputStream fileOutputStream = new FileOutputStream(new File("src/test/java/cmc/pad/resource/admin/controller/point_template1.xlsx"));
        try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
            xbean.write(stream);
            xbean.dispose();
            stream.writeTo(fileOutputStream);
        }
    }
}