package pad;

import cmc.pad.resource.application.query.CinemaLevelQueryService;
import cmc.pad.resource.application.query.data.CinemaLevelInfo;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2020/10/19.
 */
public class CinemaLevelQueryServiceTest extends AbstractCmcQasTest {
    CinemaLevelQueryService cinemaLevelQueryService = ServiceLocator.current().getInstance(CinemaLevelQueryService.class);

    @Test
    public void test() {
        CinemaLevelInfo cinema = cinemaLevelQueryService.getCinema("124");
        System.out.println(cinema);
    }
}
