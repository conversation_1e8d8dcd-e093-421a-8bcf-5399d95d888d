package pad;

import org.junit.Test;

import java.util.regex.Pattern;

/**
 * Created by fuwei on 2020/12/17.
 */
public class TmpTest {
    @Test
    public void test() {
        Pattern cinemaCodePattern = Pattern.compile("^\\d{1,6}$");
        System.out.println(cinemaCodePattern.matcher("").matches());
        System.out.println(cinemaCodePattern.matcher("0").matches());
        System.out.println(cinemaCodePattern.matcher("01").matches());
        System.out.println(cinemaCodePattern.matcher("012").matches());
        System.out.println(cinemaCodePattern.matcher("0123").matches());
        System.out.println(cinemaCodePattern.matcher("01234").matches());
        System.out.println(cinemaCodePattern.matcher("012345").matches());
        System.out.println(cinemaCodePattern.matcher("0123456").matches());
        System.out.println(cinemaCodePattern.matcher("01234567").matches());
    }
}
