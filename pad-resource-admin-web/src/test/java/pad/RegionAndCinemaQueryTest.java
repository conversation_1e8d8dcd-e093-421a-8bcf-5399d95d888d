package pad;

import cmc.location.front.service.dto.RegionDto;
import cmc.location.front.service.iface.RegionService;
import com.alibaba.fastjson.JSON;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

/**
 * Created by fuwei on 2022/7/11.
 */
public class RegionAndCinemaQueryTest extends AbstractCmcQasTest {
    RegionService regionInfoService = ServiceLocator.current().getInstance(RegionService.class);

    @Test
    public void testRegion() {
        RegionDto.FindRegionsRequest request = new RegionDto.FindRegionsRequest();
//        request.setIds(Lists.newArrayList("01"));
        request.setLargeWard("2");
//        System.out.println(JSON.toJSONString(regionInfoService.findRegions(request), true));

        System.out.printf(" >>> " + JSON.toJSONString(
                regionInfoService.findRegions(request).getItems().stream().map(item -> item.getId()).toArray()
                , true));
    }
}
