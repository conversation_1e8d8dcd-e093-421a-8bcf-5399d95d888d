package pad;

import cmc.pad.resource.application.command.common.FileExportAppService;
import cmc.pad.resource.domain.common.FileExportRecord;
import cmc.pad.resource.domain.importing.FileCategory;
import cmc.pad.resource.domain.importing.FileImportStatus;
import mtime.lark.util.ioc.ServiceLocator;
import org.junit.Test;

import java.time.LocalDateTime;

/**
 * Created by fuwei on 2022/1/20.
 */
public class ExportFileTest extends AbstractCmcQasTest {
    FileExportAppService fileExportAppService = ServiceLocator.current().getInstance(FileExportAppService.class);

    @Test
    public void test() {
        FileExportRecord fileExportRecord = new FileExportRecord();
        fileExportRecord.setFileId("xxxx.xls");
        fileExportRecord.setStatus(FileImportStatus.UNDERWAY);
        fileExportRecord.setCategory(FileCategory.POINT_LOCATION);
        fileExportRecord.setCreateTime(LocalDateTime.now());

        System.out.println(fileExportAppService.save(fileExportRecord));
    }
}
