#!/bin/bash

# PointLocationQueryController验收测试运行脚本
# 作者: fu.wei
# 日期: 2025/09/15

echo "=========================================="
echo "PointLocationQueryController API 验收测试"
echo "=========================================="

# 检查服务是否运行
echo "检查服务状态..."
if ! curl -s http://localhost:8125/health > /dev/null 2>&1; then
    echo "警告: pad-resource-admin-api 服务 (端口8125) 未运行"
    echo "请先启动服务后再运行测试"
    exit 1
fi

echo "服务运行正常，开始执行测试..."

# 切换到项目目录
cd pad-resource-admin-api

# 运行PointLocationQueryController测试
echo "运行 PointLocationQueryControllerBasicTest..."
mvn test -Dtest=PointLocationQueryControllerBasicTest -DskipTests=false

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "✅ PointLocationQueryController 验收测试通过"
    echo "=========================================="
else
    echo "=========================================="
    echo "❌ PointLocationQueryController 验收测试失败"
    echo "请检查测试输出和服务日志"
    echo "=========================================="
    exit 1
fi

echo "测试完成！"