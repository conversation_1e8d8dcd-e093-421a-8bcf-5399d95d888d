# 价格字段BigDecimal类型重构

## Core Features

- 价格字段类型修改为BigDecimal

- RPC接口类型适配

- 业务逻辑兼容性保证

- 类型转换处理

- 接口文档更新

- 测试验证

## Tech Stack

{
  "Web": {
    "arch": "java",
    "component": null
  }
}

## Design

后端服务重构，无UI设计需求

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[/] 修改API模型类中的价格字段类型为BigDecimal

[ ] 修改RPC接口契约中的价格字段类型定义

[ ] 更新实体类和DTO中的extendedPrice、basePrice字段为BigDecimal类型

[ ] 修改四个Controller中的价格字段处理逻辑

[ ] 实现RPC服务层的价格字段类型转换和业务逻辑适配

[ ] 添加类型转换工具方法和兼容性处理

[ ] 更新接口文档中的字段类型说明

[ ] 重启服务并执行LeasingControllerTestSuite测试验证
