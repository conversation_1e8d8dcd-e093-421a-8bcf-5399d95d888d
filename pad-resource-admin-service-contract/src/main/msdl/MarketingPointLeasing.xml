<?xml version="1.0" encoding="utf-8" ?>
<root javaPackageBase="cmc.pad.resource.admin.service">
    <services description="营销点位租赁价格服务">
        <service name="MarketingPointLeasing" description="营销点位租赁价格服务">
            <method name="QueryPrices" description="查询营销点位租赁价格">
                <request description="查询营销点位租赁价格请求参数">
                    <field name="cityLevel" type="string" description="城市级别编码" order="1"/>
                    <field name="cinemaLevel" type="string" description="影院级别编码" order="2"/>
                    <field name="cinemaCode" type="string"
                           description="影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效" order="3"/>
                    <field name="leaseMethod" type="string" description="租赁方式编码" order="4"/>
                </request>
                <response description="查询营销点位租赁价格响应结果">
                    <field modifier="repeated" name="priceInfos" type="PriceInfo" description="价格信息列表" order="1"/>
                </response>
            </method>
        </service>
    </services>
    <dataTypes>
        <dataType name="PriceInfo" description="营销点位租赁价格信息">
            <field name="cityLevel" type="string" description="城市级别编码" order="1"/>
            <field name="cinemaLevel" type="string" description="影院级别编码" order="2"/>
            <field name="leaseMethod" type="string" description="租赁方式编码" order="3"/>
            <field name="basePrice" type="string" description="基础价，按面积租赁时单位：元/平米/天,按数量租赁时单位：元/个/天" order="4"/>
        </dataType>
    </dataTypes>
</root>
