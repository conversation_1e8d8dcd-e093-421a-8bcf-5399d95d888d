<?xml version="1.0" encoding="utf-8" ?>
<root javaPackageBase="cmc.pad.resource.admin.service">
    <services description="折扣服务">
        <service name="Discount" description="折扣服务">
            <method name="MatchCoefficient" description="匹配折扣系数">
                <request description="匹配折扣系数请求参数">
                    <field name="businessType" type="string" description="广告业务类型" order="1"/>
                    <field name="discountMethod" type="string" description="折扣方式" order="2"/>
                    <field name="area" type="float" description="需要计算折扣的面积" order="3"/>
                    <field name="duration" type="float" description="需要计算折扣的时长" order="4"/>
                </request>
                <response description="匹配折扣系数响应结果">
                    <field modifier="repeated" name="coefficientInfos" type="CoefficientInfo" description="折扣系数信息列表" order="1"/>
                </response>
            </method>
        </service>
    </services>
    <dataTypes>
        <dataType name="CoefficientInfo" description="折扣系数信息">
            <field name="discountMethod" type="string" description="折扣方式" order="1"/>
            <field name="coefficient" type="float" description="折扣系数" order="2"/>
        </dataType>
    </dataTypes>
</root>