<?xml version="1.0" encoding="utf-8" ?>
<root javaPackageBase="cmc.pad.resource.admin.service">
    <services description="库存占用">
        <service name="ContractCollect" description="合同数据收集服务">
            <method name="ReceiveDataAndSendMsg" description="接收并发送消息">
                <request>
                    <field modifier="repeated" type="Contract" name="contract" order="1" description="合同"/>
                </request>
            </method>
        </service>
    </services>
    <dataTypes>
        <dataType name="Contract" description="多种经营合同基本信息实体类">
            <field name="contractNo" type="string" description="合同/申请单号" order="1"/>
            <field name="contractType" type="int32" description="单据类型（目前只保存：3-无合同多种经营收入，4-有合同多种经营）" order="2"/>
            <field name="areaCode" type="string" description="所属区域编码" order="3"/>
            <field name="areaName" type="string" description="所属区域名称" order="4"/>
            <field name="customerName" type="string" description="客户名称" order="5"/>
            <field name="totalAmount" type="string" description="合同金额（元）" order="6"/>
            <field name="claimedAmount" type="string" description="累计已认领金额（元）" order="7"/>
            <field name="claimedBondAmount" type="string" description="累计已认领保证金（元）" order="8"/>
            <field name="operatorName" type="string" description="填报人" order="9"/>
            <field name="operatorWanXin" type="string" description="填报人万信号" order="10"/>
            <field name="contractStartDate" type="string" description="合同起始日期yyyyMMdd" order="11"/>
            <field name="contractEndDate" type="string" description="合同终止日期yyyyMMdd" order="12"/>
            <field name="contractState" type="int32" description="合同/申请单状态（0-未生效，1-正常，2-变更中，3-作废中，4-已作废，5-关闭中，6-已关闭）"
                   order="13"/>
            <field name="operatorDate" type="string" description="合同/申请日期yyyy-MM-dd HH:mm:ss" order="14"/>
            <field name="updateTime" type="string" description="最后修改日期yyyy-MM-dd HH:mm:ss" order="15"/>
            <field modifier="repeated" name="contractCollectionPlanList" type="ContractCollectionPlan"
                   description="合同收款计划" order="16"/>
        </dataType>
        <dataType name="ContractCollectionPlan" description="多种经营合同收款计划实体类">
            <field name="contractNo" type="string" description="合同/申请单号" order="1"/>
            <field name="condition" type="string" description="收款计划条件" order="2"/>
            <field name="type" type="int32" description="收款类型 1:保证金 2:合同款" order="3"/>
            <field name="planCollectionDate" type="string" description="计划收款日期yyyy-MM-dd" order="4"/>
            <field name="planCollectionAmount" type="string" description="计划收款金额（元）" order="5"/>
        </dataType>
    </dataTypes>
</root>