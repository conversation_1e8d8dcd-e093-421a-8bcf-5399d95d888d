<?xml version="1.0" encoding="utf-8" ?>
<root javaPackageBase="cmc.pad.resource.admin.service">
    <services description="点位库存管理服务">
        <service name="PointLocationInventory" description="点位库存管理服务">
            <!-- 提交合同库存占用 -->
            <method name="occupy" description="提交合同库存占用">
                <request name="InventoryOccupationContractParam">
                    <field name="contractType" type="int32" description="合同类型：1-新提交合同 2-变更已审核过的合同" order="1"/>
                    <field name="contractNo" type="string" description="合同编号" order="2"/>
                    <field name="businessTypeCode" type="string" description="业务类型编码" order="3"/>
                    <field modifier="repeated" name="details" type="OccupationDetail" description="占用明细列表" order="4"/>
                </request>
                <response name="OccupyResponse">
                    <field name="success" type="bool" description="操作是否成功" order="1"/>
                </response>
            </method>
            
            <!-- 更新库存状态 -->
            <method name="updateStatus" description="更新库存状态">
                <request name="UpdateStatusParam">
                    <field name="contractNo" type="string" description="合同编号" order="1"/>
                    <field name="status" type="int32" description="状态：1-扣减 2-取消" order="2"/>
                </request>
                <response name="UpdateStatusResponse">
                    <field name="success" type="bool" description="操作是否成功" order="1"/>
                </response>
            </method>
        </service>
    </services>
    
    <dataTypes>
        <!-- 占用明细 -->
        <dataType name="OccupationDetail" description="占用明细">
            <field name="cinemaInnerCode" type="string" description="影院内码" order="1"/>
            <field name="pointLocationId" type="int32" description="点位ID" order="2"/>
            <field name="amount" type="float" description="占用面积" order="3"/>
            <field name="startDate" type="int64" javaType="LocalDate" description="开始日期" order="4"/>
            <field name="endDate" type="int64" javaType="LocalDate" description="结束日期" order="5"/>
            <field name="id" type="string" description="明细唯一标识" order="6"/>
            <field name="status" type="int32" description="状态：1-占用（包含不变的和修改的） 2-新增 3-作废" order="7"/>
        </dataType>
    </dataTypes>
</root>
