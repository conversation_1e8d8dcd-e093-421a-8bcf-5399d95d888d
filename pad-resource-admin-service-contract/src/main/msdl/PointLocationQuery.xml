<?xml version="1.0" encoding="utf-8" ?>
<root javaPackageBase="cmc.pad.resource.admin.service">
    <services description="点位查询服务">
        <service name="PointLocationQuery">
            <!-- 查询点位可用面积数据 -->
            <method name="queryPointLocationUsableArea" description="查询点位可用面积数据">
                <request name="QueryPointLocationUsableAreaRequest">
                    <field modifier="optional" name="pageIndex" type="int32" description="页码" order="1"/>
                    <field modifier="optional" name="pageSize" type="int32" description="页大小" order="2"/>
                    <field modifier="optional" name="code" type="string" description="点位编码" order="3"/>
                    <field modifier="optional" name="cinemaInnerCode" type="string" description="影城内码" order="4"/>
                    <field modifier="optional" name="businessTypeCode" type="string" description="业务类型编码" order="5"/>
                    <field modifier="optional" name="start" type="int64" javaType="LocalDate" description="开始日期" order="6"/>
                    <field modifier="optional" name="end" type="int64" javaType="LocalDate" description="结束日期" order="7"/>
                </request>
                <response name="QueryPointLocationUsableAreaResponse">
                    <field modifier="required" name="pageResult" type="PageResult" order="1"
                           description="分页结果"/>
                </response>
            </method>
        </service>
    </services>
    <dataTypes>
        <dataType name="PageResult" description="点位可用面积信息返回值">
            <field modifier="repeated" name="items" type="PointLocationUsableAreaInfo" order="1"/>
            <field modifier="required" name="totalCount" type="int64" order="2"/>
        </dataType>
        <!-- 点位可用面积信息 -->
        <dataType name="PointLocationUsableAreaInfo" description="点位可用面积信息">
            <field name="id" type="int32" description="点位ID" order="1"/>
            <field name="code" type="string" description="点位编码" order="2"/>
            <field name="usableArea" type="float" description="可用面积" order="3"/>
            <field name="businessTypeCode" type="string" description="业务类型编码" order="4"/>
            <field name="cinemaInnerCode" type="string" description="影城内码" order="5"/>
            <field name="resourceOwnershipCode" type="string" description="资源归属编码" order="6"/>
            <field name="locationDesc" type="string" description="位置描述" order="7"/>
            <field name="planUse" type="string" description="规划用途" order="8"/>
            <field name="landingMode" type="string" description="落地方式" order="9"/>
        </dataType>
    </dataTypes>
</root>