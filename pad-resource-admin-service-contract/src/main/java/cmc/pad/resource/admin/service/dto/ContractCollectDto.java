package cmc.pad.resource.admin.service.dto;

import lombok.Getter;
import lombok.Setter;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

import java.util.*;

/**
 * Dto
 * mscgenVersion: 0.5.3
 */
public class ContractCollectDto {

    private static final String CONTRACT_NO = "contractNo: ";
    private static final String CONTRACT_TYPE = "contractType: ";
    private static final String AREA_CODE = "areaCode: ";
    private static final String AREA_NAME = "areaName: ";
    private static final String CUSTOMER_NAME = "customerName: ";
    private static final String TOTAL_AMOUNT = "totalAmount: ";
    private static final String CLAIMED_AMOUNT = "claimedAmount: ";
    private static final String CLAIMED_BOND_AMOUNT = "claimedBondAmount: ";
    private static final String OPERATOR_NAME = "operatorName: ";
    private static final String OPERATOR_WAN_XIN = "operatorWanXin: ";
    private static final String CONTRACT_START_DATE = "contractStartDate: ";
    private static final String CONTRACT_END_DATE = "contractEndDate: ";
    private static final String CONTRACT_STATE = "contractState: ";
    private static final String OPERATOR_DATE = "operatorDate: ";
    private static final String UPDATE_TIME = "updateTime: ";
    private static final String CONTRACT_COLLECTION_PLAN_LIST = "contractCollectionPlanList: ";
    private static final String CONDITION = "condition: ";
    private static final String TYPE = "type: ";
    private static final String PLAN_COLLECTION_DATE = "planCollectionDate: ";
    private static final String PLAN_COLLECTION_AMOUNT = "planCollectionAmount: ";
    private static final String CONTRACT = "contract: ";

    private ContractCollectDto() {

    }

    /**
     * 多种经营合同基本信息实体类
     */
    @Setter
    @Getter
    @ProtoMessage(description = "多种经营合同基本信息实体类")
    public static class Contract {
        /**
         * 合同/申请单号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "合同/申请单号")
        private String contractNo;
        /**
         * 单据类型（目前只保存：3-无合同多种经营收入，4-有合同多种经营）
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = false, description = "单据类型（目前只保存：3-无合同多种经营收入，4-有合同多种经营）")
        private int contractType;
        /**
         * 所属区域编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "所属区域编码")
        private String areaCode;
        /**
         * 所属区域名称
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "所属区域名称")
        private String areaName;
        /**
         * 客户名称
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "客户名称")
        private String customerName;
        /**
         * 合同金额（元）
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "合同金额（元）")
        private String totalAmount;
        /**
         * 累计已认领金额（元）
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "累计已认领金额（元）")
        private String claimedAmount;
        /**
         * 累计已认领保证金（元）
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "累计已认领保证金（元）")
        private String claimedBondAmount;
        /**
         * 填报人
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "填报人")
        private String operatorName;
        /**
         * 填报人万信号
         */
        @ProtoField(order = 10, type = FieldType.STRING, required = false, description = "填报人万信号")
        private String operatorWanXin;
        /**
         * 合同起始日期yyyyMMdd
         */
        @ProtoField(order = 11, type = FieldType.STRING, required = false, description = "合同起始日期yyyyMMdd")
        private String contractStartDate;
        /**
         * 合同终止日期yyyyMMdd
         */
        @ProtoField(order = 12, type = FieldType.STRING, required = false, description = "合同终止日期yyyyMMdd")
        private String contractEndDate;
        /**
         * 合同/申请单状态（0-未生效，1-正常，2-变更中，3-作废中，4-已作废，5-关闭中，6-已关闭）
         */
        @ProtoField(order = 13, type = FieldType.INT32, required = false, description = "合同/申请单状态（0-未生效，1-正常，2-变更中，3-作废中，4-已作废，5-关闭中，6-已关闭）")
        private int contractState;
        /**
         * 合同/申请日期yyyy-MM-dd HH:mm:ss
         */
        @ProtoField(order = 14, type = FieldType.STRING, required = false, description = "合同/申请日期yyyy-MM-dd HH:mm:ss")
        private String operatorDate;
        /**
         * 最后修改日期yyyy-MM-dd HH:mm:ss
         */
        @ProtoField(order = 15, type = FieldType.STRING, required = false, description = "最后修改日期yyyy-MM-dd HH:mm:ss")
        private String updateTime;
        /**
         * 合同收款计划
         */
        @ProtoField(order = 16, type = FieldType.OBJECT, required = false, description = "合同收款计划")
        private List<ContractCollectionPlan> contractCollectionPlanList;


        /**
         * add item method
         * @param item
         */
        public void addContractCollectionPlanList(ContractCollectionPlan item) {
            ensureContractCollectionPlanList();
            this.contractCollectionPlanList.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addContractCollectionPlanList(Collection<? extends ContractCollectionPlan> items) {
            ensureContractCollectionPlanList();
            this.contractCollectionPlanList.addAll(items);
        }

        private void ensureContractCollectionPlanList() {
            if (contractCollectionPlanList == null) {
                this.contractCollectionPlanList = new ArrayList<>();
            }
        }

		@Override
		public String toString() {
			String s = getClass().getSimpleName() + ":\n";
			s +=CONTRACT_NO + this.contractNo + "\n";
			s +=CONTRACT_TYPE + this.contractType + "\n";
			s +=AREA_CODE + this.areaCode + "\n";
			s +=AREA_NAME + this.areaName + "\n";
			s +=CUSTOMER_NAME + this.customerName + "\n";
			s +=TOTAL_AMOUNT + this.totalAmount + "\n";
			s +=CLAIMED_AMOUNT + this.claimedAmount + "\n";
			s +=CLAIMED_BOND_AMOUNT + this.claimedBondAmount + "\n";
			s +=OPERATOR_NAME + this.operatorName + "\n";
			s +=OPERATOR_WAN_XIN + this.operatorWanXin + "\n";
			s +=CONTRACT_START_DATE + this.contractStartDate + "\n";
			s +=CONTRACT_END_DATE + this.contractEndDate + "\n";
			s +=CONTRACT_STATE + this.contractState + "\n";
			s +=OPERATOR_DATE + this.operatorDate + "\n";
			s +=UPDATE_TIME + this.updateTime + "\n";
			s +=CONTRACT_COLLECTION_PLAN_LIST + this.contractCollectionPlanList + "\n";
			return s;

		}
    }


    /**
     * 多种经营合同收款计划实体类
     */
    @Setter
    @Getter
    @ProtoMessage(description = "多种经营合同收款计划实体类")
    public static class ContractCollectionPlan {
        /**
         * 合同/申请单号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "合同/申请单号")
        private String contractNo;
        /**
         * 收款计划条件
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "收款计划条件")
        private String condition;
        /**
         * 收款类型 1:保证金 2:合同款
         */
        @ProtoField(order = 3, type = FieldType.INT32, required = false, description = "收款类型 1:保证金 2:合同款")
        private int type;
        /**
         * 计划收款日期yyyy-MM-dd
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "计划收款日期yyyy-MM-dd")
        private String planCollectionDate;
        /**
         * 计划收款金额（元）
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "计划收款金额（元）")
        private String planCollectionAmount;

		@Override
		public String toString() {
			String s = getClass().getSimpleName() + ":\n";
			s +=CONTRACT_NO + this.contractNo + "\n";
			s +=CONDITION + this.condition + "\n";
			s +=TYPE + this.type + "\n";
			s +=PLAN_COLLECTION_DATE + this.planCollectionDate + "\n";
			s +=PLAN_COLLECTION_AMOUNT + this.planCollectionAmount + "\n";
			return s;

		}
    }


    /**
     * ReceiveDataAndSendMsg 请求参数
     */
    @Setter
    @Getter
    @ProtoMessage(description = "ReceiveDataAndSendMsg 请求参数")
    public static class ReceiveDataAndSendMsgRequest {
        /**
         * 合同
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "合同")
        private List<Contract> contract;


        /**
         * add item method
         * @param item
         */
        public void addContract(Contract item) {
            ensureContract();
            this.contract.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addContract(Collection<? extends Contract> items) {
            ensureContract();
            this.contract.addAll(items);
        }

        private void ensureContract() {
            if (contract == null) {
                this.contract = new ArrayList<>();
            }
        }

		@Override
		public String toString() {
			String s = getClass().getSimpleName() + ":\n";
			s +=CONTRACT + this.contract + "\n";
			return s;

		}
    }



}