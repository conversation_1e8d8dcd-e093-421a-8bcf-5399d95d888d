package cmc.pad.resource.admin.service.dto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class MovieHallNamingLeasingDto {

    private MovieHallNamingLeasingDto() {

    }

    /**
     * 冠名厅租赁价格信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "冠名厅租赁价格信息")
    public static class PriceInfo {
        /**
         * 影院编码(内码)
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "影院编码(内码)")
        private String cinemaCode;
        /**
         * 影厅类型编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "影厅类型编码")
        private String movieHallType;
        /**
         * 基础价，单位：元/年
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "基础价，单位：元/年")
        private String basePrice;


    }


    /**
     * 查询冠名厅租赁价格请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "查询冠名厅租赁价格请求参数")
    public static class QueryPricesRequest {
        /**
         * 影院编码(内码)
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "影院编码(内码)")
        private String cinemaCode;
        /**
         * 影厅类型编码，见cmc维数据影厅类型
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "影厅类型编码，见cmc维数据影厅类型")
        private String movieHallType;


    }


    /**
     * 查询冠名厅租赁价格响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "查询冠名厅租赁价格响应结果")
    public static class QueryPricesResponse {
        /**
         * 价格信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "价格信息列表")
        private List<PriceInfo> priceInfos;


        /**
         * add item method
         * @param item
         */
        public void addPriceInfos(PriceInfo item) {
            ensurePriceInfos();
            this.priceInfos.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addPriceInfos(Collection<? extends PriceInfo> items) {
            ensurePriceInfos();
            this.priceInfos.addAll(items);
        }

        private void ensurePriceInfos() {
            if (priceInfos == null) {
                this.priceInfos = new ArrayList<>();
            }
        }


    }



}