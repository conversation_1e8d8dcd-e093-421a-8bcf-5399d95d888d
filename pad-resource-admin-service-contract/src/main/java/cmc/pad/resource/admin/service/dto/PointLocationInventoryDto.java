package cmc.pad.resource.admin.service.dto;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class PointLocationInventoryDto {

    private PointLocationInventoryDto() {

    }

    /**
     * 占用明细
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "占用明细")
    public static class OccupationDetail {
        /**
         * 影院内码
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "影院内码")
        private String cinemaInnerCode;
        /**
         * 点位ID
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = false, description = "点位ID")
        private int pointLocationId;
        /**
         * 占用面积
         */
        @ProtoField(order = 3, type = FieldType.FLOAT, required = false, description = "占用面积")
        private float amount;
        /**
         * 开始日期
         */
        @ProtoField(order = 4, type = FieldType.INT64, required = false, description = "开始日期")
        private LocalDate startDate;
        /**
         * 结束日期
         */
        @ProtoField(order = 5, type = FieldType.INT64, required = false, description = "结束日期")
        private LocalDate endDate;
        /**
         * 明细唯一标识
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "明细唯一标识")
        private String id;
        /**
         * 状态：1-占用（包含不变的和修改的） 2-新增 3-作废
         */
        @ProtoField(order = 7, type = FieldType.INT32, required = false, description = "状态：1-占用（包含不变的和修改的） 2-新增 3-作废")
        private int status;


    }


    /**
     * occupy 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "occupy 请求参数")
    public static class OccupyRequest {
        /**
         * 合同类型：1-新提交合同 2-变更已审核过的合同
         */
        @ProtoField(order = 1, type = FieldType.INT32, required = false, description = "合同类型：1-新提交合同 2-变更已审核过的合同")
        private int contractType;
        /**
         * 合同编号
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "合同编号")
        private String contractNo;
        /**
         * 业务类型编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "业务类型编码")
        private String businessTypeCode;
        /**
         * 占用明细列表
         */
        @ProtoField(order = 4, type = FieldType.OBJECT, required = false, description = "占用明细列表")
        private List<OccupationDetail> details;


        /**
         * add item method
         * @param item
         */
        public void addDetails(OccupationDetail item) {
            ensureDetails();
            this.details.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addDetails(Collection<? extends OccupationDetail> items) {
            ensureDetails();
            this.details.addAll(items);
        }

        private void ensureDetails() {
            if (details == null) {
                this.details = new ArrayList<>();
            }
        }


    }


    /**
     * occupy 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "occupy 响应结果")
    public static class OccupyResponse {
        /**
         * 操作是否成功
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = false, description = "操作是否成功")
        private boolean success;


    }


    /**
     * updateStatus 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "updateStatus 请求参数")
    public static class UpdateStatusRequest {
        /**
         * 合同编号
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "合同编号")
        private String contractNo;
        /**
         * 状态：1-扣减 2-取消
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = false, description = "状态：1-扣减 2-取消")
        private int status;


    }


    /**
     * updateStatus 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "updateStatus 响应结果")
    public static class UpdateStatusResponse {
        /**
         * 操作是否成功
         */
        @ProtoField(order = 1, type = FieldType.BOOL, required = false, description = "操作是否成功")
        private boolean success;


    }



}