package cmc.pad.resource.admin.service.spring;

import cmc.pad.resource.admin.service.iface.*;
import mtime.lark.net.rpc.RpcClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * 提供 Spring 自动注入默认服务代理功能。
 */
@Configuration
@Lazy
@Order(Ordered.LOWEST_PRECEDENCE + 10)
public class PadAdminServiceProxyAutoConfig {

    private String SERVER = "cmc.pad.resource.admin.service";

    @Bean
    @ConditionalOnMissingBean
    public ContractCollectService AuthorizedCardManageServiceProxy() {
        return RpcClient.get(SERVER, ContractCollectService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public AdvertisingPointLeasingService AdvertisingPointLeasingServiceProxy() {
        return RpcClient.get(SERVER, AdvertisingPointLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public FixedPointLeasingService fixedPointLeasingServiceProxy() {
        return RpcClient.get(SERVER, FixedPointLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public LightBoxPriceService LightBoxPriceServiceProxy() {
        return RpcClient.get(SERVER, LightBoxPriceService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public MarketingPointLeasingService marketingPointLeasingServiceProxy() {
        return RpcClient.get(SERVER, MarketingPointLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public MovieHallNamingLeasingService movieHallNamingLeasingServiceProxy() {
        return RpcClient.get(SERVER, MovieHallNamingLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public MovieHallSeatLeasingService movieHallSeatLeasingServiceProxy() {
        return RpcClient.get(SERVER, MovieHallSeatLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public OuterAreaLeasingService outerAreaLeasingServiceProxy() {
        return RpcClient.get(SERVER, OuterAreaLeasingService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public DiscountService discountServiceProxy() {
        return RpcClient.get(SERVER, DiscountService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public PointLocationQueryService pointLocationQueryServiceProxy() {
        return RpcClient.get(SERVER, PointLocationQueryService.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public PointLocationInventoryService pointLocationInventoryServiceProxy() {
        return RpcClient.get(SERVER, PointLocationInventoryService.class);
    }
}
