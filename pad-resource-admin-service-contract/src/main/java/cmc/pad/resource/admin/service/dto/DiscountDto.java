package cmc.pad.resource.admin.service.dto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class DiscountDto {

    private DiscountDto() {

    }

    /**
     * 折扣系数信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "折扣系数信息")
    public static class CoefficientInfo {
        /**
         * 折扣方式
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "折扣方式")
        private String discountMethod;
        /**
         * 折扣系数
         */
        @ProtoField(order = 2, type = FieldType.FLOAT, required = false, description = "折扣系数")
        private float coefficient;


    }


    /**
     * 匹配折扣系数请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "匹配折扣系数请求参数")
    public static class MatchCoefficientRequest {
        /**
         * 广告业务类型
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "广告业务类型")
        private String businessType;
        /**
         * 折扣方式
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "折扣方式")
        private String discountMethod;
        /**
         * 需要计算折扣的面积
         */
        @ProtoField(order = 3, type = FieldType.FLOAT, required = false, description = "需要计算折扣的面积")
        private float area;
        /**
         * 需要计算折扣的时长
         */
        @ProtoField(order = 4, type = FieldType.FLOAT, required = false, description = "需要计算折扣的时长")
        private float duration;


    }


    /**
     * 匹配折扣系数响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "匹配折扣系数响应结果")
    public static class MatchCoefficientResponse {
        /**
         * 折扣系数信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "折扣系数信息列表")
        private List<CoefficientInfo> coefficientInfos;


        /**
         * add item method
         * @param item
         */
        public void addCoefficientInfos(CoefficientInfo item) {
            ensureCoefficientInfos();
            this.coefficientInfos.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addCoefficientInfos(Collection<? extends CoefficientInfo> items) {
            ensureCoefficientInfos();
            this.coefficientInfos.addAll(items);
        }

        private void ensureCoefficientInfos() {
            if (coefficientInfos == null) {
                this.coefficientInfos = new ArrayList<>();
            }
        }


    }



}