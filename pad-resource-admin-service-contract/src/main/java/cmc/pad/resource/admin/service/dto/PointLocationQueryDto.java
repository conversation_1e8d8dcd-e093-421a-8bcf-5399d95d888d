package cmc.pad.resource.admin.service.dto;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class PointLocationQueryDto {

    private PointLocationQueryDto() {

    }

    /**
     * 点位可用面积信息返回值
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "点位可用面积信息返回值")
    public static class PageResult {
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "")
        private List<PointLocationUsableAreaInfo> items;
        @ProtoField(order = 2, type = FieldType.INT64, required = true, description = "")
        private long totalCount;


        /**
         * add item method
         * @param item
         */
        public void addItems(PointLocationUsableAreaInfo item) {
            ensureItems();
            this.items.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addItems(Collection<? extends PointLocationUsableAreaInfo> items) {
            ensureItems();
            this.items.addAll(items);
        }

        private void ensureItems() {
            if (items == null) {
                this.items = new ArrayList<>();
            }
        }


    }


    /**
     * 点位可用面积信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "点位可用面积信息")
    public static class PointLocationUsableAreaInfo {
        /**
         * 点位ID
         */
        @ProtoField(order = 1, type = FieldType.INT32, required = false, description = "点位ID")
        private int id;
        /**
         * 点位编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "点位编码")
        private String code;
        /**
         * 可用面积
         */
        @ProtoField(order = 3, type = FieldType.FLOAT, required = false, description = "可用面积")
        private float usableArea;
        /**
         * 业务类型编码
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "业务类型编码")
        private String businessTypeCode;
        /**
         * 影城内码
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "影城内码")
        private String cinemaInnerCode;
        /**
         * 资源归属编码
         */
        @ProtoField(order = 6, type = FieldType.STRING, required = false, description = "资源归属编码")
        private String resourceOwnershipCode;
        /**
         * 位置描述
         */
        @ProtoField(order = 7, type = FieldType.STRING, required = false, description = "位置描述")
        private String locationDesc;
        /**
         * 规划用途
         */
        @ProtoField(order = 8, type = FieldType.STRING, required = false, description = "规划用途")
        private String planUse;
        /**
         * 落地方式
         */
        @ProtoField(order = 9, type = FieldType.STRING, required = false, description = "落地方式")
        private String landingMode;


    }


    /**
     * queryPointLocationUsableArea 请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "queryPointLocationUsableArea 请求参数")
    public static class QueryPointLocationUsableAreaRequest {
        /**
         * 页码
         */
        @ProtoField(order = 1, type = FieldType.INT32, required = false, description = "页码")
        private int pageIndex;
        /**
         * 页大小
         */
        @ProtoField(order = 2, type = FieldType.INT32, required = false, description = "页大小")
        private int pageSize;
        /**
         * 点位编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "点位编码")
        private String code;
        /**
         * 影城内码
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "影城内码")
        private String cinemaInnerCode;
        /**
         * 业务类型编码
         */
        @ProtoField(order = 5, type = FieldType.STRING, required = false, description = "业务类型编码")
        private String businessTypeCode;
        /**
         * 开始日期
         */
        @ProtoField(order = 6, type = FieldType.INT64, required = false, description = "开始日期")
        private LocalDate start;
        /**
         * 结束日期
         */
        @ProtoField(order = 7, type = FieldType.INT64, required = false, description = "结束日期")
        private LocalDate end;


    }


    /**
     * queryPointLocationUsableArea 响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "queryPointLocationUsableArea 响应结果")
    public static class QueryPointLocationUsableAreaResponse {
        /**
         * 分页结果
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = true, description = "分页结果")
        private PageResult pageResult;


    }



}