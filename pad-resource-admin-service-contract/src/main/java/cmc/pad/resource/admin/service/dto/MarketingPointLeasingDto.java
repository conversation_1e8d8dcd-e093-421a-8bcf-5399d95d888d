package cmc.pad.resource.admin.service.dto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import mtime.lark.pb.FieldType;
import mtime.lark.pb.annotation.ProtoField;
import mtime.lark.pb.annotation.ProtoMessage;

/**
 * Dto
 * mscgenVersion: 0.5.4
 */
public class MarketingPointLeasingDto {

    private MarketingPointLeasingDto() {

    }

    /**
     * 营销点位租赁价格信息
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "营销点位租赁价格信息")
    public static class PriceInfo {
        /**
         * 城市级别编码
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "城市级别编码")
        private String cityLevel;
        /**
         * 影院级别编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "影院级别编码")
        private String cinemaLevel;
        /**
         * 租赁方式编码
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "租赁方式编码")
        private String leaseMethod;
        /**
         * 基础价，按面积租赁时单位：元/平米/天,按数量租赁时单位：元/个/天
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "基础价，按面积租赁时单位：元/平米/天,按数量租赁时单位：元/个/天")
        private String basePrice;


    }


    /**
     * 查询营销点位租赁价格请求参数
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "查询营销点位租赁价格请求参数")
    public static class QueryPricesRequest {
        /**
         * 城市级别编码
         */
        @ProtoField(order = 1, type = FieldType.STRING, required = false, description = "城市级别编码")
        private String cityLevel;
        /**
         * 影院级别编码
         */
        @ProtoField(order = 2, type = FieldType.STRING, required = false, description = "影院级别编码")
        private String cinemaLevel;
        /**
         * 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效
         */
        @ProtoField(order = 3, type = FieldType.STRING, required = false, description = "影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效")
        private String cinemaCode;
        /**
         * 租赁方式编码
         */
        @ProtoField(order = 4, type = FieldType.STRING, required = false, description = "租赁方式编码")
        private String leaseMethod;


    }


    /**
     * 查询营销点位租赁价格响应结果
     */
    @Setter
    @Getter
    @ToString
    @ProtoMessage(description = "查询营销点位租赁价格响应结果")
    public static class QueryPricesResponse {
        /**
         * 价格信息列表
         */
        @ProtoField(order = 1, type = FieldType.OBJECT, required = false, description = "价格信息列表")
        private List<PriceInfo> priceInfos;


        /**
         * add item method
         * @param item
         */
        public void addPriceInfos(PriceInfo item) {
            ensurePriceInfos();
            this.priceInfos.add(item);
        }

        /**
         * add collection method
         * @param items
         */
        public void addPriceInfos(Collection<? extends PriceInfo> items) {
            ensurePriceInfos();
            this.priceInfos.addAll(items);
        }

        private void ensurePriceInfos() {
            if (priceInfos == null) {
                this.priceInfos = new ArrayList<>();
            }
        }


    }



}