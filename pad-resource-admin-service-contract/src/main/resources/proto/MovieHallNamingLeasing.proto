
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.pad.resource.admin.service.dto";
option java_outer_classname = "MovieHallNamingLeasingProto";

// 冠名厅租赁价格信息
message	PriceInfo {
	// 影院编码(内码)	
	 string cinemaCode = 1;
	// 影厅类型编码	
	 string movieHallType = 2;
	// 基础价，单位：元/年	
	 string basePrice = 3;

}

// 查询冠名厅租赁价格请求参数
message	QueryPricesRequest {
	// 影院编码(内码)
  	 string cinemaCode = 1;
	// 影厅类型编码，见cmc维数据影厅类型
  	 string movieHallType = 2;

}

// 查询冠名厅租赁价格响应结果 
message	QueryPricesResponse {
	// 价格信息列表	
	repeated PriceInfo priceInfos = 1;

}
