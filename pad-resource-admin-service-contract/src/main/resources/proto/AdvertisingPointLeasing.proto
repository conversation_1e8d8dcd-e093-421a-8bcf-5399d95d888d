
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.pad.resource.admin.service.dto";
option java_outer_classname = "AdvertisingPointLeasingProto";

// 宣传点位租赁价格信息
message	PriceInfo {
	// 城市级别编码	
	 string cityLevel = 1;
	// 影院级别编码	
	 string cinemaLevel = 2;
	// 基础价，单位：元/月	
	 string basePrice = 3;
	// 基础面积，单位：平米	
	 int32 baseArea = 4;
	// 续价，单位：元/平米/月	
	 string extendedPrice = 5;

}

// 查询宣传点位租赁价格请求参数
message	QueryPricesRequest {
	// 城市级别编码
  	 string cityLevel = 1;
	// 影院级别编码
  	 string cinemaLevel = 2;
	// 影院编码(内码),当影院编码被赋值时，城市级别和影院级别参数将失效
  	 string cinemaCode = 3;

}

// 查询宣传点位租赁价格响应结果 
message	QueryPricesResponse {
	// 价格信息列表	
	repeated PriceInfo priceInfos = 1;

}
