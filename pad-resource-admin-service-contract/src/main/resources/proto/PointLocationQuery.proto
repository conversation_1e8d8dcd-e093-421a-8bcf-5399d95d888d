
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.pad.resource.admin.service.dto";
option java_outer_classname = "PointLocationQueryProto";

// 点位可用面积信息返回值
message	PageResult {
	// 	
	repeated PointLocationUsableAreaInfo items = 1;
	// 	
	required int64 totalCount = 2;

}

// 点位可用面积信息
message	PointLocationUsableAreaInfo {
	// 点位ID	
	 int32 id = 1;
	// 点位编码	
	 string code = 2;
	// 可用面积	
	 float usableArea = 3;
	// 业务类型编码	
	 string businessTypeCode = 4;
	// 影城内码	
	 string cinemaInnerCode = 5;
	// 资源归属编码	
	 string resourceOwnershipCode = 6;
	// 位置描述	
	 string locationDesc = 7;
	// 规划用途	
	 string planUse = 8;
	// 落地方式	
	 string landingMode = 9;

}

// 
message	queryPointLocationUsableAreaRequest {
	// 页码
  	optional int32 pageIndex = 1;
	// 页大小
  	optional int32 pageSize = 2;
	// 点位编码
  	optional string code = 3;
	// 影城内码
  	optional string cinemaInnerCode = 4;
	// 业务类型编码
  	optional string businessTypeCode = 5;
	// 开始日期
  	optional int64 start = 6;
	// 结束日期
  	optional int64 end = 7;

}

//  
message	queryPointLocationUsableAreaResponse {
	// 分页结果	
	required PageResult pageResult = 1;

}
