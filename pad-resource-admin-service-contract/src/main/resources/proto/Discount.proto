
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.pad.resource.admin.service.dto";
option java_outer_classname = "DiscountProto";

// 折扣系数信息
message	CoefficientInfo {
	// 折扣方式	
	 string discountMethod = 1;
	// 折扣系数	
	 float coefficient = 2;

}

// 匹配折扣系数请求参数
message	MatchCoefficientRequest {
	// 广告业务类型
  	 string businessType = 1;
	// 折扣方式
  	 string discountMethod = 2;
	// 需要计算折扣的面积
  	 float area = 3;
	// 需要计算折扣的时长
  	 float duration = 4;

}

// 匹配折扣系数响应结果 
message	MatchCoefficientResponse {
	// 折扣系数信息列表	
	repeated CoefficientInfo coefficientInfos = 1;

}
