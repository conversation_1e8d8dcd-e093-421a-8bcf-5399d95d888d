
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.pad.resource.admin.service.dto";
option java_outer_classname = "ContractCollectProto";

// 多种经营合同基本信息实体类
message	Contract {
	// 合同/申请单号	
	 string contractNo = 1;
	// 单据类型（目前只保存：3-无合同多种经营收入，4-有合同多种经营）	
	 int32 contractType = 2;
	// 所属区域编码	
	 string areaCode = 3;
	// 所属区域名称	
	 string areaName = 4;
	// 客户名称	
	 string customerName = 5;
	// 合同金额（元）	
	 string totalAmount = 6;
	// 累计已认领金额（元）	
	 string claimedAmount = 7;
	// 累计已认领保证金（元）	
	 string claimedBondAmount = 8;
	// 填报人	
	 string operatorName = 9;
	// 填报人万信号	
	 string operatorWanXin = 10;
	// 合同起始日期yyyyMMdd	
	 string contractStartDate = 11;
	// 合同终止日期yyyyMMdd	
	 string contractEndDate = 12;
	// 合同/申请单状态（0-未生效，1-正常，2-变更中，3-作废中，4-已作废，5-关闭中，6-已关闭）	
	 int32 contractState = 13;
	// 合同/申请日期yyyy-MM-dd HH:mm:ss	
	 string operatorDate = 14;
	// 最后修改日期yyyy-MM-dd HH:mm:ss	
	 string updateTime = 15;
	// 合同收款计划	
	repeated ContractCollectionPlan contractCollectionPlanList = 16;

}

// 多种经营合同收款计划实体类
message	ContractCollectionPlan {
	// 合同/申请单号	
	 string contractNo = 1;
	// 收款计划条件	
	 string condition = 2;
	// 收款类型 1:保证金 2:合同款	
	 int32 type = 3;
	// 计划收款日期yyyy-MM-dd	
	 string planCollectionDate = 4;
	// 计划收款金额（元）	
	 string planCollectionAmount = 5;

}

// 
message	ReceiveDataAndSendMsgRequest {
	// 合同
  	repeated Contract contract = 1;

}
