
// Generated by the mtime code generator.  DO NOT EDIT!
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.

option java_package = "cmc.pad.resource.admin.service.dto";
option java_outer_classname = "PointLocationInventoryProto";

// 占用明细
message	OccupationDetail {
	// 影院内码	
	 string cinemaInnerCode = 1;
	// 点位ID	
	 int32 pointLocationId = 2;
	// 占用面积	
	 float amount = 3;
	// 开始日期	
	 int64 startDate = 4;
	// 结束日期	
	 int64 endDate = 5;
	// 明细唯一标识	
	 string id = 6;
	// 状态：1-占用（包含不变的和修改的） 2-新增 3-作废	
	 int32 status = 7;

}

// 
message	occupyRequest {
	// 合同类型：1-新提交合同 2-变更已审核过的合同
  	 int32 contractType = 1;
	// 合同编号
  	 string contractNo = 2;
	// 业务类型编码
  	 string businessTypeCode = 3;
	// 占用明细列表
  	repeated OccupationDetail details = 4;

}

//  
message	occupyResponse {
	// 操作是否成功	
	 bool success = 1;

}

// 
message	updateStatusRequest {
	// 合同编号
  	 string contractNo = 1;
	// 状态：1-扣减 2-取消
  	 int32 status = 2;

}

//  
message	updateStatusResponse {
	// 操作是否成功	
	 bool success = 1;

}
