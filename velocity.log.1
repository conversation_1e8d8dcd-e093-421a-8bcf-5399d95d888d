2025-09-01 16:56:25,131 - Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2025-09-01 16:56:25,131 - Initializing Velocity, Calling init()...
2025-09-01 16:56:25,131 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 16:56:25,131 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 16:56:25,131 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 16:56:25,131 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 16:56:25,131 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 16:56:25,131 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 16:56:25,132 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 16:56:25,134 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 16:56:25,135 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 16:56:25,135 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 16:56:25,135 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 16:56:25,136 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 16:56:25,136 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 16:56:25,136 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 16:56:25,136 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 16:56:25,136 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 16:56:25,136 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 16:56:25,143 - Created '20' parsers.
2025-09-01 16:56:25,144 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 16:56:25,144 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 16:56:25,144 - Velocimacro : Default library not found.
2025-09-01 16:56:25,144 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 16:56:25,144 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 16:56:25,144 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 16:56:25,144 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 16:56:25,148 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 16:58:49,953 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 16:58:49,953 - Initializing Velocity, Calling init()...
2025-09-01 16:58:49,953 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 16:58:49,954 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 16:58:49,954 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 16:58:49,954 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 16:58:49,954 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 16:58:49,954 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 16:58:49,954 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 16:58:49,956 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 16:58:49,957 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 16:58:49,957 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 16:58:49,957 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 16:58:49,957 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 16:58:49,957 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 16:58:49,958 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 16:58:49,958 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 16:58:49,958 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 16:58:49,958 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 16:58:49,964 - Created '20' parsers.
2025-09-01 16:58:49,964 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 16:58:49,965 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 16:58:49,965 - Velocimacro : Default library not found.
2025-09-01 16:58:49,965 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 16:58:49,965 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 16:58:49,965 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 16:58:49,965 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 16:58:49,969 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:06:55,058 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 17:06:55,058 - Initializing Velocity, Calling init()...
2025-09-01 17:06:55,058 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 17:06:55,058 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 17:06:55,058 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 17:06:55,058 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 17:06:55,058 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:06:55,058 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:06:55,058 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:06:55,060 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 17:06:55,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 17:06:55,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 17:06:55,061 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 17:06:55,062 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 17:06:55,062 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 17:06:55,062 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 17:06:55,062 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 17:06:55,062 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 17:06:55,062 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 17:06:55,068 - Created '20' parsers.
2025-09-01 17:06:55,069 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 17:06:55,069 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 17:06:55,069 - Velocimacro : Default library not found.
2025-09-01 17:06:55,069 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 17:06:55,069 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 17:06:55,069 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 17:06:55,070 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 17:06:55,075 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:09:33,036 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 17:09:33,036 - Initializing Velocity, Calling init()...
2025-09-01 17:09:33,036 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 17:09:33,036 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 17:09:33,036 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 17:09:33,036 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 17:09:33,036 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:09:33,036 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:09:33,036 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:09:33,038 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 17:09:33,039 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 17:09:33,039 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 17:09:33,039 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 17:09:33,040 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 17:09:33,040 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 17:09:33,040 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 17:09:33,040 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 17:09:33,040 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 17:09:33,040 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 17:09:33,046 - Created '20' parsers.
2025-09-01 17:09:33,047 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 17:09:33,047 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 17:09:33,047 - Velocimacro : Default library not found.
2025-09-01 17:09:33,047 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 17:09:33,047 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 17:09:33,047 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 17:09:33,047 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 17:09:33,052 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:09:39,118 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 17:09:39,118 - Initializing Velocity, Calling init()...
2025-09-01 17:09:39,118 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 17:09:39,118 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 17:09:39,118 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 17:09:39,118 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 17:09:39,118 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:09:39,118 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:09:39,119 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:09:39,121 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 17:09:39,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 17:09:39,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 17:09:39,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 17:09:39,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 17:09:39,122 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 17:09:39,123 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 17:09:39,123 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 17:09:39,123 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 17:09:39,123 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 17:09:39,129 - Created '20' parsers.
2025-09-01 17:09:39,130 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 17:09:39,130 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 17:09:39,130 - Velocimacro : Default library not found.
2025-09-01 17:09:39,130 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 17:09:39,130 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 17:09:39,130 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 17:09:39,130 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 17:09:39,135 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:13:06,009 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 17:13:06,009 - Initializing Velocity, Calling init()...
2025-09-01 17:13:06,009 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 17:13:06,009 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 17:13:06,009 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 17:13:06,009 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 17:13:06,009 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:13:06,009 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:13:06,010 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:13:06,012 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 17:13:06,013 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 17:13:06,013 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 17:13:06,013 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 17:13:06,013 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 17:13:06,013 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 17:13:06,014 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 17:13:06,014 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 17:13:06,014 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 17:13:06,014 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 17:13:06,020 - Created '20' parsers.
2025-09-01 17:13:06,021 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 17:13:06,021 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 17:13:06,021 - Velocimacro : Default library not found.
2025-09-01 17:13:06,021 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 17:13:06,021 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 17:13:06,021 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 17:13:06,021 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 17:13:06,026 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:15:01,817 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 17:15:01,817 - Initializing Velocity, Calling init()...
2025-09-01 17:15:01,817 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 17:15:01,817 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 17:15:01,817 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 17:15:01,817 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 17:15:01,817 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:15:01,817 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:15:01,818 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:15:01,820 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 17:15:01,821 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 17:15:01,821 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 17:15:01,821 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 17:15:01,821 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 17:15:01,821 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 17:15:01,822 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 17:15:01,822 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 17:15:01,822 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 17:15:01,822 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 17:15:01,828 - Created '20' parsers.
2025-09-01 17:15:01,829 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 17:15:01,829 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 17:15:01,829 - Velocimacro : Default library not found.
2025-09-01 17:15:01,829 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 17:15:01,829 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 17:15:01,829 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 17:15:01,829 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 17:15:01,833 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:41:32,263 - Log4JLogChute initialized using file 'velocity.log'
2025-09-01 17:41:32,263 - Initializing Velocity, Calling init()...
2025-09-01 17:41:32,263 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-01 17:41:32,263 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-01 17:41:32,263 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-01 17:41:32,263 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-01 17:41:32,263 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:41:32,263 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-01 17:41:32,263 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-01 17:41:32,265 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-01 17:41:32,266 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-01 17:41:32,266 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-01 17:41:32,266 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-01 17:41:32,266 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-01 17:41:32,267 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-01 17:41:32,267 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-01 17:41:32,267 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-01 17:41:32,267 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-01 17:41:32,267 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-01 17:41:32,273 - Created '20' parsers.
2025-09-01 17:41:32,274 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-01 17:41:32,274 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-01 17:41:32,274 - Velocimacro : Default library not found.
2025-09-01 17:41:32,274 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-01 17:41:32,274 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-01 17:41:32,274 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-01 17:41:32,274 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-01 17:41:32,278 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-02 11:22:15,293 - Log4JLogChute initialized using file 'velocity.log'
2025-09-02 11:22:15,293 - Initializing Velocity, Calling init()...
2025-09-02 11:22:15,293 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-02 11:22:15,293 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-02 11:22:15,293 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-02 11:22:15,293 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-02 11:22:15,293 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-02 11:22:15,293 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-02 11:22:15,294 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-02 11:22:15,297 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-02 11:22:15,297 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-02 11:22:15,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-02 11:22:15,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-02 11:22:15,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-02 11:22:15,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-02 11:22:15,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-02 11:22:15,298 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-02 11:22:15,299 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-02 11:22:15,299 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-02 11:22:15,305 - Created '20' parsers.
2025-09-02 11:22:15,306 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-02 11:22:15,306 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-02 11:22:15,307 - Velocimacro : Default library not found.
2025-09-02 11:22:15,307 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-02 11:22:15,307 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-02 11:22:15,307 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-02 11:22:15,307 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-02 11:22:15,311 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-02 11:27:48,517 - Log4JLogChute initialized using file 'velocity.log'
2025-09-02 11:27:48,518 - Initializing Velocity, Calling init()...
2025-09-02 11:27:48,518 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-02 11:27:48,518 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-02 11:27:48,518 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-02 11:27:48,518 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-02 11:27:48,518 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-02 11:27:48,518 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-02 11:27:48,519 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-02 11:27:48,522 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-02 11:27:48,523 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-02 11:27:48,523 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-02 11:27:48,523 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-02 11:27:48,524 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-02 11:27:48,524 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-02 11:27:48,524 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-02 11:27:48,524 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-02 11:27:48,525 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-02 11:27:48,525 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-02 11:27:48,532 - Created '20' parsers.
2025-09-02 11:27:48,533 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-02 11:27:48,533 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-02 11:27:48,533 - Velocimacro : Default library not found.
2025-09-02 11:27:48,533 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-02 11:27:48,533 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-02 11:27:48,533 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-02 11:27:48,533 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-02 11:27:48,539 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-04 10:02:22,160 - Log4JLogChute initialized using file 'velocity.log'
2025-09-04 10:02:22,160 - Initializing Velocity, Calling init()...
2025-09-04 10:02:22,160 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-04 10:02:22,160 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-04 10:02:22,160 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-04 10:02:22,160 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-04 10:02:22,160 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-04 10:02:22,160 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-04 10:02:22,161 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-04 10:02:22,164 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-04 10:02:22,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-04 10:02:22,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-04 10:02:22,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-04 10:02:22,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-04 10:02:22,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-04 10:02:22,166 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-04 10:02:22,166 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-04 10:02:22,166 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-04 10:02:22,166 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-04 10:02:22,174 - Created '20' parsers.
2025-09-04 10:02:22,175 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-04 10:02:22,175 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-04 10:02:22,175 - Velocimacro : Default library not found.
2025-09-04 10:02:22,175 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-04 10:02:22,175 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-04 10:02:22,175 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-04 10:02:22,175 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-04 10:02:22,180 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-04 10:56:25,828 - Log4JLogChute initialized using file 'velocity.log'
2025-09-04 10:56:25,829 - Initializing Velocity, Calling init()...
2025-09-04 10:56:25,829 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-04 10:56:25,829 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-04 10:56:25,829 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-04 10:56:25,829 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-04 10:56:25,829 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-04 10:56:25,829 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-04 10:56:25,829 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-04 10:56:25,832 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-04 10:56:25,833 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-04 10:56:25,833 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-04 10:56:25,833 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-04 10:56:25,833 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-04 10:56:25,834 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-04 10:56:25,834 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-04 10:56:25,834 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-04 10:56:25,834 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-04 10:56:25,834 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-04 10:56:25,841 - Created '20' parsers.
2025-09-04 10:56:25,842 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-04 10:56:25,842 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-04 10:56:25,842 - Velocimacro : Default library not found.
2025-09-04 10:56:25,842 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-04 10:56:25,842 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-04 10:56:25,842 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-04 10:56:25,843 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-04 10:56:25,847 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-05 13:15:10,442 - Log4JLogChute initialized using file 'velocity.log'
2025-09-05 13:15:10,443 - Initializing Velocity, Calling init()...
2025-09-05 13:15:10,443 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-05 13:15:10,443 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-05 13:15:10,443 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-05 13:15:10,443 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-05 13:15:10,443 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-05 13:15:10,443 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-05 13:15:10,444 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-05 13:15:10,450 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-05 13:15:10,451 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-05 13:15:10,452 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-05 13:15:10,452 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-05 13:15:10,452 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-05 13:15:10,453 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-05 13:15:10,453 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-05 13:15:10,454 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-05 13:15:10,454 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-05 13:15:10,455 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-05 13:15:10,471 - Created '20' parsers.
2025-09-05 13:15:10,473 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-05 13:15:10,474 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-05 13:15:10,474 - Velocimacro : Default library not found.
2025-09-05 13:15:10,474 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-05 13:15:10,474 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-05 13:15:10,474 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-05 13:15:10,474 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-05 13:15:10,491 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 11:23:46,057 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 11:23:46,058 - Initializing Velocity, Calling init()...
2025-09-08 11:23:46,058 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 11:23:46,058 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 11:23:46,058 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 11:23:46,058 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 11:23:46,058 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 11:23:46,058 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 11:23:46,059 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 11:23:46,065 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 11:23:46,066 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 11:23:46,066 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 11:23:46,067 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 11:23:46,067 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 11:23:46,067 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 11:23:46,068 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 11:23:46,068 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 11:23:46,069 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 11:23:46,070 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 11:23:46,082 - Created '20' parsers.
2025-09-08 11:23:46,084 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 11:23:46,085 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 11:23:46,085 - Velocimacro : Default library not found.
2025-09-08 11:23:46,085 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 11:23:46,085 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 11:23:46,085 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 11:23:46,085 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 11:23:46,100 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 11:31:52,683 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 11:31:52,683 - Initializing Velocity, Calling init()...
2025-09-08 11:31:52,683 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 11:31:52,683 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 11:31:52,683 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 11:31:52,683 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 11:31:52,683 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 11:31:52,683 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 11:31:52,685 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 11:31:52,690 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 11:31:52,692 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 11:31:52,692 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 11:31:52,692 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 11:31:52,692 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 11:31:52,692 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 11:31:52,693 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 11:31:52,693 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 11:31:52,694 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 11:31:52,694 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 11:31:52,708 - Created '20' parsers.
2025-09-08 11:31:52,709 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 11:31:52,710 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 11:31:52,710 - Velocimacro : Default library not found.
2025-09-08 11:31:52,710 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 11:31:52,710 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 11:31:52,710 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 11:31:52,710 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 11:31:52,726 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:32,173 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 12:05:32,173 - Initializing Velocity, Calling init()...
2025-09-08 12:05:32,174 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 12:05:32,174 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 12:05:32,174 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 12:05:32,174 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 12:05:32,174 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:32,174 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:32,175 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:32,180 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 12:05:32,181 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 12:05:32,181 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 12:05:32,182 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 12:05:32,182 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 12:05:32,182 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 12:05:32,183 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 12:05:32,183 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 12:05:32,184 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 12:05:32,184 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 12:05:32,197 - Created '20' parsers.
2025-09-08 12:05:32,198 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 12:05:32,199 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 12:05:32,199 - Velocimacro : Default library not found.
2025-09-08 12:05:32,199 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 12:05:32,199 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 12:05:32,199 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 12:05:32,200 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 12:05:32,217 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:37,292 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 12:05:37,292 - Initializing Velocity, Calling init()...
2025-09-08 12:05:37,292 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 12:05:37,292 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 12:05:37,292 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 12:05:37,292 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 12:05:37,292 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:37,292 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:37,293 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:37,299 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 12:05:37,300 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 12:05:37,301 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 12:05:37,301 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 12:05:37,301 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 12:05:37,302 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 12:05:37,302 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 12:05:37,303 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 12:05:37,303 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 12:05:37,303 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 12:05:37,315 - Created '20' parsers.
2025-09-08 12:05:37,317 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 12:05:37,317 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 12:05:37,317 - Velocimacro : Default library not found.
2025-09-08 12:05:37,317 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 12:05:37,317 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 12:05:37,317 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 12:05:37,317 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 12:05:37,334 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:43,025 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 12:05:43,025 - Initializing Velocity, Calling init()...
2025-09-08 12:05:43,025 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 12:05:43,025 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 12:05:43,025 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 12:05:43,025 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 12:05:43,025 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:43,025 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:43,027 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:43,032 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 12:05:43,033 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 12:05:43,034 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 12:05:43,034 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 12:05:43,034 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 12:05:43,034 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 12:05:43,035 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 12:05:43,036 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 12:05:43,036 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 12:05:43,036 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 12:05:43,049 - Created '20' parsers.
2025-09-08 12:05:43,050 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 12:05:43,051 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 12:05:43,051 - Velocimacro : Default library not found.
2025-09-08 12:05:43,051 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 12:05:43,051 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 12:05:43,051 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 12:05:43,051 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 12:05:43,067 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:48,924 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 12:05:48,925 - Initializing Velocity, Calling init()...
2025-09-08 12:05:48,925 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 12:05:48,925 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 12:05:48,925 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 12:05:48,925 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 12:05:48,925 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:48,925 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 12:05:48,926 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 12:05:48,931 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 12:05:48,933 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 12:05:48,933 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 12:05:48,933 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 12:05:48,933 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 12:05:48,933 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 12:05:48,934 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 12:05:48,934 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 12:05:48,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 12:05:48,936 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 12:05:48,948 - Created '20' parsers.
2025-09-08 12:05:48,950 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 12:05:48,951 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 12:05:48,951 - Velocimacro : Default library not found.
2025-09-08 12:05:48,951 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 12:05:48,951 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 12:05:48,951 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 12:05:48,951 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 12:05:48,966 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:16:53,115 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:16:53,116 - Initializing Velocity, Calling init()...
2025-09-08 13:16:53,116 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:16:53,116 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:16:53,116 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:16:53,116 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:16:53,116 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:16:53,116 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:16:53,118 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:16:53,123 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:16:53,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:16:53,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:16:53,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:16:53,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:16:53,125 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:16:53,126 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:16:53,127 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:16:53,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:16:53,128 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:16:53,140 - Created '20' parsers.
2025-09-08 13:16:53,142 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:16:53,142 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:16:53,142 - Velocimacro : Default library not found.
2025-09-08 13:16:53,142 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:16:53,142 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:16:53,142 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:16:53,142 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:16:53,159 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:17:02,627 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:17:02,628 - Initializing Velocity, Calling init()...
2025-09-08 13:17:02,628 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:17:02,628 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:17:02,628 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:17:02,628 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:17:02,628 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:17:02,628 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:17:02,629 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:17:02,635 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:17:02,637 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:17:02,637 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:17:02,637 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:17:02,638 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:17:02,638 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:17:02,638 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:17:02,639 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:17:02,641 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:17:02,641 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:17:02,654 - Created '20' parsers.
2025-09-08 13:17:02,656 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:17:02,656 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:17:02,656 - Velocimacro : Default library not found.
2025-09-08 13:17:02,656 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:17:02,656 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:17:02,656 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:17:02,656 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:17:02,672 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:17:09,155 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:17:09,156 - Initializing Velocity, Calling init()...
2025-09-08 13:17:09,156 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:17:09,156 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:17:09,156 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:17:09,156 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:17:09,156 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:17:09,156 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:17:09,157 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:17:09,163 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:17:09,164 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:17:09,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:17:09,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:17:09,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:17:09,165 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:17:09,166 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:17:09,166 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:17:09,167 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:17:09,167 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:17:09,179 - Created '20' parsers.
2025-09-08 13:17:09,181 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:17:09,181 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:17:09,181 - Velocimacro : Default library not found.
2025-09-08 13:17:09,181 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:17:09,181 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:17:09,181 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:17:09,181 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:17:09,196 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:17:16,662 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:17:16,663 - Initializing Velocity, Calling init()...
2025-09-08 13:17:16,663 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:17:16,663 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:17:16,663 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:17:16,663 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:17:16,663 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:17:16,663 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:17:16,664 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:17:16,669 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:17:16,671 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:17:16,671 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:17:16,671 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:17:16,671 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:17:16,672 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:17:16,672 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:17:16,673 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:17:16,673 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:17:16,674 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:17:16,685 - Created '20' parsers.
2025-09-08 13:17:16,687 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:17:16,687 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:17:16,687 - Velocimacro : Default library not found.
2025-09-08 13:17:16,687 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:17:16,687 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:17:16,687 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:17:16,687 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:17:16,702 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:18:05,260 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:18:05,260 - Initializing Velocity, Calling init()...
2025-09-08 13:18:05,260 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:18:05,260 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:18:05,260 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:18:05,260 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:18:05,260 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:18:05,260 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:18:05,262 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:18:05,267 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:18:05,268 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:18:05,269 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:18:05,269 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:18:05,269 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:18:05,269 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:18:05,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:18:05,270 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:18:05,271 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:18:05,272 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:18:05,284 - Created '20' parsers.
2025-09-08 13:18:05,285 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:18:05,286 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:18:05,286 - Velocimacro : Default library not found.
2025-09-08 13:18:05,286 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:18:05,286 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:18:05,286 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:18:05,286 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:18:05,303 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:18:16,762 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:18:16,763 - Initializing Velocity, Calling init()...
2025-09-08 13:18:16,763 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:18:16,763 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:18:16,763 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:18:16,763 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:18:16,763 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:18:16,763 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:18:16,764 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:18:16,770 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:18:16,771 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:18:16,772 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:18:16,772 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:18:16,772 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:18:16,772 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:18:16,773 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:18:16,774 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:18:16,774 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:18:16,774 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:18:16,788 - Created '20' parsers.
2025-09-08 13:18:16,790 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:18:16,790 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:18:16,790 - Velocimacro : Default library not found.
2025-09-08 13:18:16,790 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:18:16,790 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:18:16,790 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:18:16,790 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:18:16,807 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:18:54,348 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:18:54,348 - Initializing Velocity, Calling init()...
2025-09-08 13:18:54,348 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:18:54,348 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:18:54,348 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:18:54,349 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:18:54,349 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:18:54,349 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:18:54,350 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:18:54,355 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:18:54,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:18:54,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:18:54,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:18:54,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:18:54,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:18:54,358 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:18:54,359 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:18:54,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:18:54,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:18:54,372 - Created '20' parsers.
2025-09-08 13:18:54,374 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:18:54,374 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:18:54,374 - Velocimacro : Default library not found.
2025-09-08 13:18:54,374 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:18:54,374 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:18:54,374 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:18:54,374 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:18:54,392 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:19:03,023 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:19:03,023 - Initializing Velocity, Calling init()...
2025-09-08 13:19:03,023 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:19:03,023 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:19:03,023 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:19:03,023 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:19:03,023 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:19:03,023 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:19:03,025 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:19:03,028 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:19:03,031 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:19:03,032 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:19:03,032 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:19:03,032 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:19:03,033 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:19:03,034 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:19:03,035 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:19:03,035 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:19:03,036 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:19:03,048 - Created '20' parsers.
2025-09-08 13:19:03,049 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:19:03,050 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:19:03,050 - Velocimacro : Default library not found.
2025-09-08 13:19:03,050 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:19:03,050 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:19:03,050 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:19:03,050 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:19:03,064 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:20:01,412 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:20:01,412 - Initializing Velocity, Calling init()...
2025-09-08 13:20:01,413 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:20:01,413 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:20:01,413 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:20:01,413 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:20:01,413 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:20:01,413 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:20:01,414 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:20:01,419 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:20:01,420 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:20:01,420 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:20:01,421 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:20:01,421 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:20:01,421 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:20:01,422 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:20:01,423 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:20:01,423 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:20:01,423 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:20:01,435 - Created '20' parsers.
2025-09-08 13:20:01,437 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:20:01,438 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:20:01,438 - Velocimacro : Default library not found.
2025-09-08 13:20:01,438 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:20:01,438 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:20:01,438 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:20:01,438 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:20:01,452 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:26:53,230 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 13:26:53,231 - Initializing Velocity, Calling init()...
2025-09-08 13:26:53,231 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 13:26:53,231 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 13:26:53,231 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 13:26:53,231 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 13:26:53,231 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:26:53,231 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 13:26:53,232 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 13:26:53,238 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 13:26:53,240 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 13:26:53,240 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 13:26:53,240 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 13:26:53,240 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 13:26:53,240 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 13:26:53,242 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 13:26:53,242 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 13:26:53,243 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 13:26:53,243 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 13:26:53,257 - Created '20' parsers.
2025-09-08 13:26:53,258 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 13:26:53,259 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 13:26:53,259 - Velocimacro : Default library not found.
2025-09-08 13:26:53,259 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 13:26:53,259 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 13:26:53,259 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 13:26:53,259 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 13:26:53,273 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:33,913 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 15:48:33,913 - Initializing Velocity, Calling init()...
2025-09-08 15:48:33,913 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 15:48:33,913 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 15:48:33,913 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 15:48:33,913 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 15:48:33,913 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:33,913 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:33,915 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:33,921 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 15:48:33,922 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 15:48:33,923 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 15:48:33,923 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 15:48:33,923 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 15:48:33,923 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 15:48:33,924 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 15:48:33,924 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 15:48:33,925 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 15:48:33,925 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 15:48:33,937 - Created '20' parsers.
2025-09-08 15:48:33,939 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 15:48:33,940 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 15:48:33,940 - Velocimacro : Default library not found.
2025-09-08 15:48:33,940 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 15:48:33,940 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 15:48:33,940 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 15:48:33,940 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 15:48:33,956 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:43,718 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 15:48:43,719 - Initializing Velocity, Calling init()...
2025-09-08 15:48:43,719 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 15:48:43,719 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 15:48:43,719 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 15:48:43,719 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 15:48:43,719 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:43,719 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:43,720 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:43,726 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 15:48:43,727 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 15:48:43,727 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 15:48:43,727 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 15:48:43,728 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 15:48:43,728 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 15:48:43,728 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 15:48:43,729 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 15:48:43,730 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 15:48:43,730 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 15:48:43,743 - Created '20' parsers.
2025-09-08 15:48:43,744 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 15:48:43,745 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 15:48:43,745 - Velocimacro : Default library not found.
2025-09-08 15:48:43,745 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 15:48:43,746 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 15:48:43,746 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 15:48:43,746 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 15:48:43,760 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:47,348 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 15:48:47,348 - Initializing Velocity, Calling init()...
2025-09-08 15:48:47,348 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 15:48:47,348 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 15:48:47,348 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 15:48:47,348 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 15:48:47,349 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:47,349 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:47,350 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:47,355 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-09-08 15:48:47,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-09-08 15:48:47,357 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-09-08 15:48:47,358 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-09-08 15:48:47,358 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-09-08 15:48:47,358 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-09-08 15:48:47,359 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-09-08 15:48:47,360 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-09-08 15:48:47,361 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-09-08 15:48:47,361 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-09-08 15:48:47,375 - Created '20' parsers.
2025-09-08 15:48:47,380 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-09-08 15:48:47,384 - Could not load resource 'VM_global_library.vm' from ResourceLoader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader: ClasspathResourceLoader Error: cannot find resource VM_global_library.vm
2025-09-08 15:48:47,384 - Velocimacro : Default library not found.
2025-09-08 15:48:47,384 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-09-08 15:48:47,384 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-09-08 15:48:47,384 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-09-08 15:48:47,384 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-09-08 15:48:47,399 - ResourceManager : found xsl/interface.vm with loader org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
2025-09-08 15:48:51,534 - Log4JLogChute initialized using file 'velocity.log'
2025-09-08 15:48:51,535 - Initializing Velocity, Calling init()...
2025-09-08 15:48:51,535 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-09-08 15:48:51,535 - Default Properties File: org/apache/velocity/runtime/defaults/velocity.properties
2025-09-08 15:48:51,535 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-09-08 15:48:51,535 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/format/Formatter).  Falling back to next log system...
2025-09-08 15:48:51,535 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:51,535 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-09-08 15:48:51,536 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader
